[WalmartTech_CentOS_7_OS]
name = CentOS 7 OS
baseurl = http://ark-repos.wal-mart.com/ark/yum/published/centos/7Server/direct/soe/noenv/baseos
enabled = 1
metadata_expire = 1
enabled_metadata = 1
gpgcheck = 0

[WalmartTech_CentOS_7_Updates]
name = CentOS 7 Updates
baseurl = http://ark-repos.wal-mart.com/ark/yum/published/centos/7Server/direct/soe/noenv/updates
enabled = 1
metadata_expire = 1
enabled_metadata = 1
gpgcheck = 0

[WalmartTech_CentOS_7_SCLO]
name = CentOS 7 SCLO
baseurl = http://ark-repos.wal-mart.com/ark/yum/published/centos/7Server/direct/soe/noenv/sclo
enabled = 1
metadata_expire = 1
enabled_metadata = 1
gpgcheck = 0

[WalmartTech_CentOS_7_SCLO_RH]
name = CentOS 7 SCLO RH
baseurl = http://ark-repos.wal-mart.com/ark/yum/published/centos/7Server/direct/soe/noenv/sclo-rh
enabled = 1
metadata_expire = 1
enabled_metadata = 1
gpgcheck = 0

[WalmartTech_Misc_EPEL_7]
name = EPEL 7
baseurl = http://ark-repos.wal-mart.com/ark/yum/published/centos/7Server/direct/soe/noenv/EPEL
enabled = 1
metadata_expire = 1
enabled_metadata = 1
gpgcheck = 0

[WalmartTech_Misc_Third_Party_RHEL_7]
name = Third Party RHEL 7
baseurl = http://ark-repos.wal-mart.com/ark/yum/published/centos/7Server/direct/soe/noenv/third-party
enabled = 0
metadata_expire = 1
enabled_metadata = 1
gpgcheck = 0

[WalmartTech_Misc_Walmart_Apps_RHEL_7]
name = Walmart Apps RHEL 7
baseurl = http://ark-repos.wal-mart.com/ark/yum/published/centos/7Server/direct/soe/noenv/wm-apps
enabled = 0
metadata_expire = 1
enabled_metadata = 1
gpgcheck = 0