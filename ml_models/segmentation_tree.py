import pandas as pd
from sklearn.tree import DecisionTreeClassifier

import pandas as pd
from sklearn.tree import DecisionTreeClassifier
import logging
logger = logging.getLogger(__name__)
def segment_workloads(df, feature_cols, label_col="segment_label", max_depth=3):
    """
    Segments workloads into 'low' and 'high' categories based on resource usage.

    - Adds `segment_label` column with 'low' or 'high' as string labels.
    - Adds `segment_label_encoded` column with 0 (low) or 1 (high) as numeric for modeling.

    Parameters:
        df (pd.DataFrame): Input features dataframe.
        feature_cols (List[str]): Features used for segmentation.
        label_col (str): Name of the string label column to add.
        max_depth (int): Decision tree depth for segmentation.

    Returns:
        Tuple[pd.DataFrame, DecisionTreeClassifier]: DataFrame with labels, fitted model.
    """
    # Log missing info before segmentation
    logger.info(f"[segment_workloads] 🚧 Input shape: {df.shape}")
    logger.info(f"[segment_workloads] 🚧 Missing values before segmentation:\n{df[feature_cols].isnull().sum()}")

    median_label = (df[feature_cols].sum(axis=1) > df[feature_cols].sum(axis=1).median()).astype(int)
    clf = DecisionTreeClassifier(max_depth=max_depth)
    clf.fit(df[feature_cols], median_label)

    # Predict and assign labels
    try:
        df[label_col] = clf.predict(df[feature_cols])
        df[label_col] = df[label_col].replace({0: "low", 1: "high"})  # this line is optional

        # For modeling: keep both versions
        df["segment_label_str"] = df[label_col]  # keep for inspection
        df["segment_label"] = df[label_col].map({"low": 0, "high": 1})  # numeric for model
        logger.info(f"[segment_workloads] ✅ Segmentation complete. Unique labels: {df[label_col].unique()}")
        logger.info(f"[segment_workloads] 🔍 Nulls in segment_label: {df[label_col].isnull().sum()}")

    except Exception as e:
        logger.error(f"[segment_workloads] ❌ Failed to assign segment_label: {e}")
        raise

    return df, clf
def segment_workloads_v2(df, feature_cols, label_col="segment_label", max_depth=3):
    """
    Segments workloads into 'low' and 'high' usage categories based on input features.

    Returns:
        df with new columns: segment_label ("low"/"high"), segment_label_str, segment_label (numeric).
    """
    logger.info(f"[segment_workloads] 🚧 Input shape: {df.shape}")
    logger.info(f"[segment_workloads] 🚧 Missing values before segmentation:\n{df[feature_cols].isnull().sum()}")

    median_label = (df[feature_cols].sum(axis=1) > df[feature_cols].sum(axis=1).median()).astype(int)
    clf = DecisionTreeClassifier(max_depth=max_depth)
    clf.fit(df[feature_cols], median_label)

    try:
        df[label_col] = clf.predict(df[feature_cols])
        logger.info(f"Predicted segment raw values: {df[label_col].unique()}")

        df[label_col] = df[label_col].replace({0: "low", 1: "high"})
        df["segment_label_str"] = df[label_col]
        df["segment_label"] = df[label_col].map({"low": 0, "high": 1})
        logger.info(f"[segment_workloads] ✅ Segmentation complete. Unique labels: {df[label_col].unique()}")
    except Exception as e:
        logger.error(f"[segment_workloads] ❌ Failed to assign segment_label: {e}")
        raise

    return df, clf