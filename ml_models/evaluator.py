import pandas as pd
from sklearn.metrics import r2_score, mean_absolute_error

def compare_rule_vs_ml(df, ml_preds: pd.Series, visualize=False):
    df["ml_prediction"] = ml_preds
    r2 = r2_score(df["actual_min_pods"], df["ml_prediction"])
    mae = mean_absolute_error(df["actual_min_pods"], df["ml_prediction"])
    return {
        "ml": {
            "r2": r2,
            "mae": mae
        }
    }
