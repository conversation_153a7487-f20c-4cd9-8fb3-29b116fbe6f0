# train_min_pods_model.py

import pandas as pd
import joblib
import os
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from typing import Tuple, List, Optional

try:
    from xgboost import XGBRegressor
    HAS_XGBOOST = True
except ImportError:
    HAS_XGBOOST = False

def train_min_pods_model(
    X: pd.DataFrame,
    y: pd.Series,
    model_path: str = "models/min_pods_predictor.pkl",
    test_size: float = 0.2,
    random_state: int = 42,
    hpo: bool = True,
    save_model: bool = True,
    model_type: str = "rf"  # "rf" or "xgb"
) -> Tuple[object, dict, List[str]]:
    """
    Trains a RandomForestRegressor or XGBRegressor to predict min pod recommendations.

    Args:
        X (pd.DataFrame): Feature set
        y (pd.Series): Labels (min pod recommendations)
        model_path (str): Path to save the trained model
        test_size (float): Fraction of data to use for testing
        random_state (int): Seed for reproducibility
        hpo (bool): Whether to perform hyperparameter optimization
        save_model (bool): Whether to persist the trained model
        model_type (str): Model to use - "rf" (RandomForest) or "xgb" (XGBoost)

    Returns:
        Tuple: Trained model, evaluation metrics, and feature list
    """
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state
    )

    features_used = list(X.columns)

    if model_type == "xgb" and not HAS_XGBOOST:
        raise ImportError("XGBoost is not installed. Please install with `pip install xgboost`.")

    if model_type == "rf":
        if hpo:
            param_grid = {
                "n_estimators": [100, 200],
                "max_depth": [5, 10, None],
                "min_samples_split": [2, 5],
                "min_samples_leaf": [1, 2]
            }
            grid = GridSearchCV(RandomForestRegressor(random_state=random_state), param_grid, cv=3, scoring="neg_mean_absolute_error")
            grid.fit(X_train, y_train)
            model = grid.best_estimator_
        else:
            model = RandomForestRegressor(n_estimators=100, max_depth=None, random_state=random_state)
            model.fit(X_train, y_train)

    elif model_type == "xgb":
        if hpo:
            param_grid = {
                "n_estimators": [100, 200],
                "max_depth": [3, 5, 10],
                "learning_rate": [0.01, 0.1, 0.2]
            }
            grid = GridSearchCV(XGBRegressor(objective="reg:squarederror", random_state=random_state), param_grid, cv=3, scoring="neg_mean_absolute_error")
            grid.fit(X_train, y_train)
            model = grid.best_estimator_
        else:
            model = XGBRegressor(objective="reg:squarederror", n_estimators=100, random_state=random_state)
            model.fit(X_train, y_train)

    y_pred_train = model.predict(X_train)
    y_pred_test = model.predict(X_test)

    metrics = {
        "train_mae": mean_absolute_error(y_train, y_pred_train),
        "test_mae": mean_absolute_error(y_test, y_pred_test),
        "train_rmse": mean_squared_error(y_train, y_pred_train, squared=False),
        "test_rmse": mean_squared_error(y_test, y_pred_test, squared=False),
        "test_r2": r2_score(y_test, y_pred_test),
        "model_path": model_path,
        "model_type": model_type
    }

    if save_model:
        os.makedirs(os.path.dirname(model_path), exist_ok=True)
        feature_metadata = {
            "features_used": features_used,
            "tagged_columns": [col for col in X.columns if "is_outlier" in col or "segment_label" in col]
        }
        joblib.dump((model, feature_metadata), model_path)

    return model, metrics, features_used
