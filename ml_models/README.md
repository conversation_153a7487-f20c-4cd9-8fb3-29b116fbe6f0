MinPods Prediction Architecture Flow

📦 Input: Cluster-wise historical metrics data
 └─ "cluster_id" → {peak_df, offpeak_df, latency_df, throttling_df, error_rate_df, rule_min_pods, historical_hpa}

 1️⃣ Build Training Dataset
 └─ Uses: peak_df + offpeak_df + optional metrics (latency, throttling, error)
 └─ Output: Features (X), Target (y = rule-based final_recommended_min_pods)

 2️⃣ Preprocessing
   ├─ Autoencoder-based outlier tagging → adds recon_error + is_outlier_autoencoder
   └─ Workload segmentation (DecisionTree) → adds segment_label

 3️⃣ Feature Filtering
 └─ Extract `used_features` = only numeric columns (int/float) including encoded segment_label

 4️⃣ Train Model
 └─ Model: RandomForestRegressor
 └─ Input: X[used_features], y
 └─ Output: Trained model + metrics + used_features list

 5️⃣ Predict
 └─ Input: Full X + used_features
 └─ Output: ml_predicted_min_pods (rounded, clipped)

 6️⃣ Evaluation & SHAP Explanation
 ├─ Compare ML vs rule-based predictions (MAE, R²)
 └─ Use SHAP to explain feature contributions

 7️⃣ Result Packaging
 └─ Save: metrics, comparisons, predictions, SHAP summaries per cluster

 Summary
 cluster_data → build_training_dataset → preprocess (outliers + segment) 
→ used_features → train_model → predict_min_pods 
→ evaluate → explain (SHAP) → results per cluster


over view
Rule-Based Logic (MID)
               ↓
   ┌──────── build_training_dataset ────────┐
   │     Features (X) + Rule Targets (y)   │
   └────────────────────┬──────────────────┘
                        ↓
       Outlier Tagging + Segmentation
                        ↓
         Train RandomForest (MIR model)
                        ↓
             Predict ml_predicted_min_pods
                        ↓
    Compare ML vs Rule-based → Evaluate & Explain
                        ↓
              Return final results per cluster

