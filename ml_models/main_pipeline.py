import os
import numpy as np
import pandas as pd
import logging
from ml_models.data_prep import build_training_dataset
from ml_models.train_min_pods_model import train_min_pods_model
from ml_models.predict_min_pods import predict_min_pods
from ml_models.evaluator import compare_rule_vs_ml
from ml_models.autoencoder_tagging import tag_outliers_with_autoencoder
from ml_models.segmentation_tree import segment_workloads
from ml_models.shap_explainer import explain_model_with_shap

logger = logging.getLogger(__name__)

def run_cluster_model_pipeline(cluster_data: dict, model_dir="ml_models/clusters", cluster_col="cluster_id",
                               namespace_col="namespace", app_col="app_id") -> dict:
    os.makedirs(model_dir, exist_ok=True)
    cluster_results = {}

    for cluster_id, dfs in cluster_data.items():
        print(f"\n🔧 Processing cluster: {cluster_id}")
        try:
            # 1. Build dataset
            X, y = build_training_dataset(
                peak_df=dfs.get("peak"), offpeak_df=dfs.get("offpeak"),
                latency_df=dfs.get("latency"), throttling_df=dfs.get("throttling"),
                error_rate_df=dfs.get("error_rate"), rule_min_pods_df=dfs.get("rule_min_pods"),
                historical_hpa_df=dfs.get("historical_hpa"),
                namespace_col=namespace_col, app_col=app_col
            )
            logger.info("1. Build features and labels done")
            if isinstance(X.index, pd.MultiIndex):
                X = X.reset_index()
                y = y.reset_index(drop=True)
            logger.info(f"🔍 Dataset shape: {X.shape}")
            logger.info(f"🔍 Unique apps: {X[[namespace_col, app_col]].drop_duplicates().shape[0]}")

            # 2. Preprocessing
            X = tag_outliers_with_autoencoder(X, feature_cols=["avg_cpu_off", "avg_traffic_off"])[0]
            X = segment_workloads(X, feature_cols=["avg_cpu_off", "avg_traffic_off"])[0]
            logger.info("2. Preprocessing complete")
            logger.info(f"   Data shape after preprocessing: {X.shape}")

            # Encode segment_label before training
            if "segment_label" in X.columns:
                X["segment_label"] = X["segment_label"].map({"low": 0, "high": 1})

            # 3. Train model
            used_features = [col for col in X.columns if X[col].dtype in [np.float64, np.int64]]
            model_path = os.path.join(model_dir, f"min_pods_model_{cluster_id}.pkl")
            model, metrics, _used_features = train_min_pods_model(X, y, model_path=model_path, used_features=used_features)

            # 4. Predict
            preds = predict_min_pods(model, X,expected_features=_used_features)
            logger.info(f"📈 Predictions done: {len(preds)} rows")

            # 5. Prediction output
            rule_df = X.copy()
            rule_df["actual_min_pods"] = y
            rule_df["ml_predicted_min_pods"] = preds.values

            if dfs.get("rule_min_pods") is not None:
                rule_df = rule_df.merge(
                    dfs["rule_min_pods"][[namespace_col, app_col, "final_recommended_min_pods"]],
                    on=[namespace_col, app_col],
                    how="left"
                )
                rule_df = rule_df.rename(columns={"final_recommended_min_pods": "rule_based_min_pods"})
            else:
                rule_df["rule_based_min_pods"] = y

            # 6. Evaluate and explain
            #comparison = compare_rule_vs_ml(rule_df, preds, visualize=False)
            shap_summary = explain_model_with_shap(model, X[used_features])

            logger.info("6. Evaluation complete")
            logger.info(f"   MAE: {metrics['test_mae']:.4f}, R²: {metrics['test_r2']}")
            #logger.info(f"   Rule vs ML → MAE: {comparison['ml']['mae']:.4f}, R²: {comparison['ml']['r2']}")
            if shap_summary is not None:
                logger.info(f"   SHAP summary shape: {getattr(shap_summary, 'values', np.empty(0)).shape}")

            # 7. Save result
            cluster_results[cluster_id] = {
                "metrics": metrics,
               # "comparison": comparison,
                "shap": shap_summary,
                "predictions": rule_df
            }

        except Exception as e:
            logger.exception(e)
            cluster_results[cluster_id] = {"error": str(e)}
            print(f"❌ Error in cluster {cluster_id}: {e}")

    return cluster_results
