import pandas as pd
def encode_boolean_and_label_features(X: pd.DataFrame) -> pd.DataFrame:
    """
    Encodes categorical or boolean string features like 'true'/'false', 'high'/'low' into numeric values.

    - "true"/"false" → 1/0
    - "high"/"low"   → 1/0
    - Boolean True/False → 1/0

    Args:
        X (pd.DataFrame): Input DataFrame with mixed types

    Returns:
        pd.DataFrame: Cleaned DataFrame with encoded numeric columns
    """
    X = X.copy()

    binary_mappings = {
        "true": 1, "false": 0,
        "True": 1, "False": 0,
        True: 1, False: 0,
        "high": 1, "low": 0,
        "High": 1, "Low": 0
    }

    for col in X.columns:
        if X[col].dtype == object or X[col].dtype == bool:
            unique_vals = X[col].dropna().unique().tolist()

            # Check if all values are binary-like
            if set(unique_vals).issubset(set(binary_mappings.keys())):
                X[col] = X[col].map(binary_mappings).astype("int")

    return X
import matplotlib.pyplot as plt
from sklearn.tree import plot_tree

def show_and_save_tree_plot(model, feature_names, output_path="tree_plot.png", max_depth=3):
    """
    Displays and saves a decision tree plot.

    Args:
        model: Trained RandomForestRegressor or DecisionTreeRegressor.
        feature_names: List of feature names.
        output_path: Path to save the figure (e.g., 'tree.png').
        max_depth: Max tree depth to visualize.
    """
    tree = model.estimators_[0]  # For RandomForest

    fig, ax = plt.subplots(figsize=(16, 8))
    plot_tree(tree, feature_names=feature_names, filled=True, max_depth=max_depth, ax=ax)
    plt.title("Decision Tree")

    # Save to file
    fig.savefig(output_path, format=output_path.split('.')[-1])
    print(f"✅ Tree saved to: {output_path}")

    # Show GUI
    plt.show()

import lightgbm as lgb
def show_lightgbm_tree(model, output_path="lgb_tree.png"):
    """
    Plots and saves the first decision tree from a LightGBM model.

    Args:
        model: A trained LGBMRegressor model.
        output_path (str): File path to save the tree image.
    """
    lgb.plot_tree(
        model,
        tree_index=0,
        figsize=(20, 10),
        show_info=["split_gain", "internal_value", "leaf_count"]
    )
    plt.tight_layout()
    plt.savefig(output_path)
    plt.show()
    print(f"✅ LightGBM tree saved to {output_path}")