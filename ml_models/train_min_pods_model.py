import joblib
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score
import logging
from ml_models.utils import encode_boolean_and_label_features
# from xgboost import XGBRegressor
# from lightgbm import LGBMRegressor
# from ml_models.utils import show_and_save_tree_plot,show_lightgbm_tree
logger = logging.getLogger(__name__)




def train_min_pods_model(X, y, model_path,drop_cols, used_features=None):
    """
    Trains a Random Forest model to predict minPods.

    Parameters:
        X (pd.DataFrame): Feature set.
        y (pd.Series): Target values.
        model_path (str): Path to save the trained model.
        drop_cols: drop feature
        used_features (List[str], optional): List of features to use for training.

    Returns:
        model (RandomForestRegressor): Trained model.
        metrics (dict): Evaluation metrics (MAE, R²).
        used_features (List[str]): Actual features used in the model.
    """
    X = X.copy()

    # Filter out non-feature columns if present in used_features
    if used_features:
        used_features = [f for f in used_features if f not in drop_cols]
        X = X[used_features]
    else:
        used_features = [col for col in X.columns if col not in drop_cols]
        X = X[used_features]

    X = encode_boolean_and_label_features(X)
    used_features = [col for col in X.columns if X[col].dtype in [float, int]]

    logger.info(f"[train_min_pods_model] 🔍 Training on features:\n{X.columns.tolist()}")

    # Train-test split
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42
    )

    model = RandomForestRegressor(n_estimators=100, random_state=42)
    # model = XGBRegressor(
    #     n_estimators=100,
    #     learning_rate=0.1,
    #     max_depth=5,
    #     subsample=0.8,
    #     colsample_bytree=0.8,
    #     random_state=42
    # )
    # model = LGBMRegressor(
    #     n_estimators=100,
    #     learning_rate=0.1,
    #     max_depth=5,
    #     random_state=42
    # )
    model.fit(X_train, y_train)

    y_pred = model.predict(X_test)
    metrics = {
        "test_mae": mean_absolute_error(y_test, y_pred),
        "test_r2": r2_score(y_test, y_pred)
    }

    joblib.dump(model, model_path)
    logger.info(f"[train_min_pods_model] ✅ Model saved to {model_path}")
    # show_lightgbm_tree(model, output_path="tree_output.png")
    return model, metrics, used_features
