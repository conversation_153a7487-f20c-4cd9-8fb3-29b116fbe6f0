import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from tensorflow.keras.models import Model
from tensorflow.keras.layers import Input, Dense
from tensorflow.keras.optimizers import Adam

def build_autoencoder(input_dim, encoding_dim=4):
    input_layer = Input(shape=(input_dim,))
    encoded = Dense(encoding_dim, activation='relu')(input_layer)
    decoded = Dense(input_dim, activation='linear')(encoded)
    autoencoder = Model(input_layer, decoded)
    autoencoder.compile(optimizer=Adam(learning_rate=0.001), loss='mse')
    return autoencoder

def tag_outliers_with_autoencoder(df, feature_cols, quantile_thresh=0.99, epochs=50):
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(df[feature_cols])
    autoencoder = build_autoencoder(X_scaled.shape[1])
    autoencoder.fit(X_scaled, X_scaled, epochs=epochs, verbose=0)
    recon = autoencoder.predict(X_scaled)
    errors = np.mean(np.square(X_scaled - recon), axis=1)
    threshold = np.quantile(errors, quantile_thresh)
    df["recon_error"] = errors
    df["is_outlier_autoencoder"] = errors > threshold
    return df, autoencoder, scaler
def tag_outliers_with_autoencoder_v2(df, feature_cols, quantile_thresh=0.99, epochs=10):
    """
    Detects outliers using a simple autoencoder model on the provided feature columns.

    Returns the dataframe with two new columns:
    - 'recon_error': reconstruction error per row
    - 'is_outlier_autoencoder': boolean flag if the row is an outlier
    """
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(df[feature_cols])
    input_layer = Input(shape=(X_scaled.shape[1],))
    encoded = Dense(4, activation='relu')(input_layer)
    decoded = Dense(X_scaled.shape[1], activation='linear')(encoded)
    autoencoder = Model(input_layer, decoded)
    autoencoder.compile(optimizer=Adam(learning_rate=0.001), loss='mse')
    autoencoder.fit(X_scaled, X_scaled, epochs=epochs, verbose=0)

    recon = autoencoder.predict(X_scaled, verbose=0)
    errors = ((X_scaled - recon) ** 2).mean(axis=1)
    threshold = pd.Series(errors).quantile(quantile_thresh)

    df["recon_error"] = errors
    df["is_outlier_autoencoder"] = errors > threshold
    return df, autoencoder, scaler

