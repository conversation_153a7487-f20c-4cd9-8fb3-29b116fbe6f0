import pandas as pd
import numpy as np
import random
import logging
from datetime import datetime, timedelta
from ml_models.main_pipeline import run_cluster_model_pipeline

# Sample size configuration
TOTAL_SAMPLES = 500
AUX_SAMPLES = 1500
NUM_CLUSTERS = 2
NUM_APPS = 3


def generate_synthetic_cluster_data(num_clusters=2, num_apps=3, total_samples=500, start_time=datetime(2025, 5, 1)):
    data = []
    for cluster_id in [f"cluster-{i}" for i in range(num_clusters)]:
        for namespace, app_id in zip([f"namespace-{i}" for i in range(num_apps)], [f"app-{i}" for i in range(num_apps)]):
            for i in range(total_samples // (num_clusters * num_apps)):
                timestamp = int((start_time + timedelta(minutes=i)).timestamp())
                dt = start_time + timedelta(minutes=i)
                hour = dt.hour
                tps_scale = 1.5 if 9 <= hour <= 17 else 1.0
                spike = 5.0 if random.random() < 0.1 else 1.0
                cpu = round(np.random.uniform(10, 90), 2)
                memory = round(np.random.uniform(100, 500), 2)
                traffic = round(np.random.uniform(10, 100) * tps_scale * spike, 2)
                pods = max(1, round(traffic / 50))
                latency = round(np.random.uniform(50, 200) / pods, 2)
                data.append([timestamp, cluster_id, namespace, app_id, cpu, dt.strftime("%A"), memory, traffic, pods, latency])
    df = pd.DataFrame(data, columns=['timestamp', 'cluster_id', 'namespace', 'app_id', 'cpu', 'day', 'memory', 'traffic', 'pods', 'latency_ms'])
    df = df.astype({'timestamp': 'int64', 'cluster_id': 'category', 'namespace': 'category', 'app_id': 'category',
                    'cpu': 'float64', 'day': 'object', 'memory': 'float64', 'traffic': 'float64', 'pods': 'float64', 'latency_ms': 'float64'})
    return df


def generate_aux_data(base_df, value_col, low=0, high=100, total_samples=1500):
    df = base_df[["timestamp", "cluster_id", "namespace", "app_id"]].copy()
    df = df.head(total_samples) if len(df) >= total_samples else df.sample(n=total_samples, replace=True).reset_index(drop=True)
    df[value_col] = np.random.uniform(low, high, size=len(df))
    return df


if __name__ == "__main__":
    # Set up logging
    log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    formatter = logging.Formatter(fmt='%(asctime)s %(filename)18s %(funcName)25s %(lineno)3d %(levelname)10s: %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
    stream_handler = logging.StreamHandler()
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)

    clusters = {}

    for i in range(NUM_CLUSTERS):
        cluster_id = f"cluster-{i}"
        peak_df = generate_synthetic_cluster_data(num_clusters=1, num_apps=NUM_APPS, total_samples=TOTAL_SAMPLES)
        offpeak_df = generate_synthetic_cluster_data(num_clusters=1, num_apps=NUM_APPS, total_samples=TOTAL_SAMPLES,
                                                     start_time=datetime(2025, 5, 1, 1, 0))

        latency_df = generate_aux_data(offpeak_df, "latency_ms", low=100, high=700, total_samples=AUX_SAMPLES)
        throttling_df = generate_aux_data(offpeak_df, "throttle_pct", low=3, high=10, total_samples=AUX_SAMPLES)
        error_df = generate_aux_data(offpeak_df, "error_pct", low=2, high=18, total_samples=AUX_SAMPLES)

        clusters[cluster_id] = {
            "peak": peak_df,
            "offpeak": offpeak_df,
            "latency": None,
            "throttling": None,
            "error_rate": None,
            "rule_min_pods": None,
            "historical_hpa": None
        }

    # Run pipeline
    results = run_cluster_model_pipeline(clusters)

    # Print error summary
    print("\n❌ Errors Summary:")
    for cid, res in results.items():
        if "error" in res:
            print(f" - Cluster: {cid} | Error: {res['error']}")

    # Collect and print predictions
    all_preds = []
    for cid, res in results.items():
        preds_df = res.get("predictions")
        if preds_df is not None and not preds_df.empty:
            preds_df = preds_df.copy()
            preds_df["cluster_id"] = cid
            all_preds.append(preds_df[["cluster_id", "namespace", "app_id", "ml_predicted_min_pods"]])

    if all_preds:
        summary_df = pd.concat(all_preds).sort_values(by="ml_predicted_min_pods")
        print("\n📊 🔍 Aggregated Predicted Minimum Pods Across All Clusters:")
        print(summary_df.to_string(index=False))
        # Optional: Save to file
        summary_df.to_csv("predicted_min_pods.csv", index=False)
        print("\n📁 Saved predictions to 'predicted_min_pods.csv'")
    else:
        print("⚠️ No predictions to display.")
