import shap
import matplotlib.pyplot as plt


def explain_model_with_shap(model, features_df, top_n=10):
    # 1. Select only numeric columns
    features_df = features_df.select_dtypes(include=["number"])

    # 2. Drop rows or columns with NaNs
    features_df = features_df.dropna(axis=1, how="any").dropna(axis=0, how="any")

    # 3. Ensure float64 dtype
    features_df = features_df.astype("float64")

    if len(features_df) < 2:
        print("⚠️ Not enough valid samples to compute SHAP.")
        return None

    explainer = shap.Explainer(model, features_df)
    shap_values = explainer(features_df)
    shap.summary_plot(shap_values, features_df, plot_type="bar", max_display=top_n)
    return shap_values

