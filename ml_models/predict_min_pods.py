import pandas as pd
import joblib
from typing import Optional
from ml_models.utils import encode_boolean_and_label_features
def load_model(model_path: str):
    return joblib.load(model_path)

def predict_min_pods(model, features_df: pd.DataFrame,drop_cols, expected_features: Optional[list] = None,
                     round_up: bool = True, clip_min: Optional[int] = 1, clip_max: Optional[int] = None) -> pd.Series:
    """
    Predicts the minimum number of pods using a trained model.

    Parameters:
        model: Trained ML model.
        features_df (pd.DataFrame): Input features for prediction.
        drop_cols: Drop columns
        expected_features (List[str], optional): Expected column order/features used during training.
        round_up (bool): If True, rounds predictions to nearest int.
        clip_min (int, optional): Lower bound on predictions.
        clip_max (int, optional): Upper bound on predictions.

    Returns:
        pd.Series: Predicted min pod counts with same index as input.
    """
    features_df = features_df.copy()

    # Drop non-numeric training-time extras if not already handled upstream
    features_df = features_df.drop(columns=[col for col in drop_cols if col in features_df.columns])

    features_df = encode_boolean_and_label_features(features_df)
    # Use only the expected features passed during training
    if expected_features:
        missing = set(expected_features) - set(features_df.columns)
        if missing:
            raise ValueError(f"Missing expected features: {missing}")
        features_df = features_df[expected_features]

    # Make predictions
    preds = model.predict(features_df)
    if round_up:
        preds = [int(round(p)) if not isinstance(p, int) else p for p in preds]
    preds = pd.Series(preds, index=features_df.index, name="ml_predicted_min_pods")

    if clip_min is not None:
        preds = preds.clip(lower=clip_min)
    if clip_max is not None:
        preds = preds.clip(upper=clip_max)

    return preds
