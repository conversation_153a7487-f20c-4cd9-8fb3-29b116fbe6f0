import pandas as pd
import os
import logging
from math import ceil
from sklearn.tree import plot_tree
import matplotlib.pyplot as plt
from ml_models.train_min_pods_model import train_min_pods_model
from ml_models.predict_min_pods import predict_min_pods
from ml_models.evaluator import compare_rule_vs_ml
from ml_models.shap_explainer import explain_model_with_shap
from libs.analysis import clean_and_merge_metric_dataframes_generic
from io import BytesIO
from ml_models.segmentation_tree import segment_workloads_v2
from ml_models.autoencoder_tagging import tag_outliers_with_autoencoder_v2
import numpy as np
from libs.util import get_peak_and_offpeak_range, check_range_in_bounds, log_and_drop_na

# from ml_models.data_prep import build_training_dataset_raw
logger = logging.getLogger(__name__)


def build_training_dataset_raw(df, latency_df=None, throttling_df=None, error_rate_df=None) -> tuple[
    pd.DataFrame, pd.Series]:
    """
    Enhances the input dataframe with derived features and merges auxiliary metrics.
    Constructs the feature matrix X and label vector y for training.

    Args:
        df (pd.DataFrame): Base metrics including ['cpu', 'traffic', 'pods'] at minimum.
        latency_df (pd.DataFrame, optional): Latency metrics to join.
        throttling_df (pd.DataFrame, optional): Throttling metrics to join.
        error_rate_df (pd.DataFrame, optional): Error rate metrics to join.

    Returns:
        tuple[pd.DataFrame, pd.Series]: Feature matrix X, target label y
    """

    for optional_df in [latency_df, throttling_df, error_rate_df]:
        if optional_df is not None:
            df = df.merge(optional_df, on=["timestamp", "namespace", "app_id", "cluster_id"], how="left")

    # Drop rows where label is missing
    df = df.dropna(subset=["final_recommended_min_pods"])

    # Derived features
    df["cpu_per_req"] = df["cpu"] / df["traffic"].replace(0, np.nan)
    # df["cpu_per_pod"] = df["cpu"] / df["pods"].replace(0, np.nan)
    df["tps_per_pod"] = df["traffic"] / df["pods"].replace(0, np.nan)

    # Drop rows with any NaNs (e.g., from divide-by-zero replacements)
    nan_rows = df[df.isna().any(axis=1)]
    if len(nan_rows) > 0:
        logger.info(f"[Data Cleanup] 🚨 Dropping {len(nan_rows)} rows with NaNs after feature derivation.")
        if len(nan_rows) >= 10:
            logger.info("[Sample NaN Rows]\n" + nan_rows.sample(10).to_string(index=False))
        else:
            logger.info("[Sample NaN Rows]\n" + nan_rows.to_string(index=False))
    df = df.dropna()

    drop_columns = ["pods", "day"]
    X = df.drop(columns=[col for col in drop_columns if col in df.columns] + ["final_recommended_min_pods"])
    y = df["final_recommended_min_pods"]

    return X, y


def preprocess_features(X):
    """
    Applies autoencoder-based outlier tagging and decision tree-based segmentation.

    Args:
        X (pd.DataFrame): Feature matrix

    Returns:
        pd.DataFrame: Updated feature matrix with new features
    """
    X = tag_outliers_with_autoencoder_v2(X, feature_cols=["cpu", "traffic"])[0]
    X = segment_workloads_v2(X, feature_cols=["cpu", "traffic"])[0]
    logger.info("2. Preprocessing complete")
    logger.info(f"   Data shape after preprocessing: {X.shape}")

    return X


def prepare_training_inputs_from_prometheus_data(dat: dict) -> tuple[
    pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame]:
    """
    Converts and merges Prometheus metric results for peak and off-peak data.

    Args:
        dat (dict): Dict containing keys 'peak' and 'offpeak', each with metric DataFrames.

    Returns:
        tuple[pd.DataFrame, pd.DataFrame]:
            - merged_df: Combined peak + off-peak metrics.
            - current_pods_df: Extracted and renamed pod count labels.
    """

    peak_df = clean_and_merge_metric_dataframes_generic(
        metric_data=dat["peak"],
        # merge_keys=["timestamp", "namespace", "app_id", "cluster_id"]
    )

    offpeak_df = clean_and_merge_metric_dataframes_generic(
        metric_data=dat["offpeak"],
        # merge_keys=["timestamp", "namespace", "app_id", "cluster_id"]
    )

    merged_df = pd.concat([peak_df, offpeak_df], ignore_index=True)
    current_pods_df = merged_df[["timestamp", "namespace", "app_id", "cluster_id", "pods"]].copy()
    current_pods_df = current_pods_df.rename(columns={"pods": "current_pods"})

    logger.info("Removing NAN rows for final_peak_df")
    peak_df = log_and_drop_na(peak_df)

    logger.info("Removing NAN rows for offpeak_df")
    offpeak_df = log_and_drop_na(offpeak_df)

    logger.info("Removing NAN rows for merged_df")
    merged_df = log_and_drop_na(merged_df)

    logger.info("Removing NAN rows for current_pods_df")
    current_pods_df = log_and_drop_na(current_pods_df)

    return merged_df, peak_df, offpeak_df, current_pods_df


def generate_debug_metadata(model, X, deployment_id, used_features):
    """
    Generates debug info including decision tree image and feature importance.

    Args:
        model: Trained sklearn RandomForest model.
        X (pd.DataFrame): Feature matrix.
        deployment_id (str): Identifier for the deployment.
        used_features (list[str]): Feature column names used in training.

    Returns:
        dict: Dictionary containing 'tree_image_bytes' and 'feature_importance' DataFrame.
    """
    debug_info = {}

    if hasattr(model, "estimators_"):
        first_tree = model.estimators_[0]
        fig, ax = plt.subplots(figsize=(12, 6))
        plot_tree(first_tree, feature_names=used_features, filled=True, max_depth=3, ax=ax)
        plt.title(f"Decision Tree Sample for {deployment_id}")
        tree_buffer = BytesIO()
        plt.savefig(tree_buffer, format="png")
        plt.close()
        tree_buffer.seek(0)
        debug_info["tree_image_bytes"] = tree_buffer.read()

        feature_importance = model.feature_importances_
        importance_df = pd.DataFrame({"feature": used_features, "importance": feature_importance})
        debug_info["feature_importance"] = importance_df
        logger.info(
            f"🔍 Feature Importance for {deployment_id}:{importance_df.sort_values(by='importance', ascending=False).to_string(index=False)}")

    return debug_info


def orchestrate_by_deployment(merged_df,
                              latency_df=None, throttling_df=None, error_rate_df=None,
                              namespace_col="namespace", app_col="app_id", cluster_col="cluster_id",
                              model_dir="models/deployments",
                              include_debug=False,
                              ) -> dict:
    """
    Trains and evaluates ML models per unique (namespace, app_id, cluster_id) deployment using merged metric data.

    Args:
        merged_df (pd.DataFrame): Combined feature set.
        latency_df (pd.DataFrame): Optional latency metrics.
        throttling_df (pd.DataFrame): Optional throttling metrics.
        error_rate_df (pd.DataFrame): Optional error rate metrics.
        namespace_col (str): Column name for namespace.
        app_col (str): Column name for app ID.
        cluster_col (str): Column name for cluster ID.
        model_dir (str): Directory to store model artifacts.
        include_debug (bool): If True, includes decision tree image and feature importance in results.

    Returns:
        dict: Mapping of deployment_id to model metrics, predictions, SHAP explanations, and optionally debug info.
    """
    os.makedirs(model_dir, exist_ok=True)
    results = {}
    drop_cols = ["namespace", "app_id", "cluster_id"]
    unique_keys = merged_df[[namespace_col, app_col, cluster_col]].drop_duplicates()

    for _, row in unique_keys.iterrows():
        ns, app, cid = row[namespace_col], row[app_col], row[cluster_col]
        deployment_id = f"{ns}||{app}||{cid}"
        model_path = os.path.join(model_dir, f"min_pods_{ns}_{app}_{cid}.pkl")

        logger.info(f"🚀 Processing deployment: {deployment_id}")

        df_subset = merged_df[(merged_df[namespace_col] == ns) &
                              (merged_df[app_col] == app) &
                              (merged_df[cluster_col] == cid)]

        latency = latency_df[(latency_df[namespace_col] == ns) & (latency_df[app_col] == app) & (
                    latency_df[cluster_col] == cid)] if latency_df is not None else None
        throttling = throttling_df[(throttling_df[namespace_col] == ns) & (throttling_df[app_col] == app) & (
                    throttling_df[cluster_col] == cid)] if throttling_df is not None else None
        error_rate = error_rate_df[(error_rate_df[namespace_col] == ns) & (error_rate_df[app_col] == app) & (
                    error_rate_df[cluster_col] == cid)] if error_rate_df is not None else None
        # current_pods = current_pods_df[(current_pods_df[namespace_col] == ns) & (current_pods_df[app_col] == app) & (current_pods_df[cluster_col] == cid)] if current_pods_df is not None else None

        try:
            X, y = build_training_dataset_raw(df_subset, latency, throttling, error_rate)
            #y=df_subset["pods"]
            logger.info(f"📊 Data shape for {deployment_id}: {X.shape}, Labels: {y.shape}")
            X = preprocess_features(X)

            #used_features = [col for col in X.columns if X[col].dtype in [float, int]]

            model, metrics, _used_features = train_min_pods_model(X, y, model_path=model_path,drop_cols=drop_cols)

            preds = predict_min_pods(model, X, expected_features=_used_features,drop_cols=drop_cols)
            rule_df = X.copy()
            # rule_df["actual_min_pods"] = y
            rule_df["ml_predicted_min_pods"] = preds.values
            rule_df["rule_based_min_pods"] = y
            # only for comparison purpose
            rule_df["current_pods"] = df_subset["pods"].values

            # comparison = compare_rule_vs_ml(rule_df, preds, visualize=False)
            # shap_summary = explain_model_with_shap(model, X[used_features])
            min_hpa_replicas = rule_df["ml_predicted_min_pods"].min()
            logger.info(f"🟢 Suggested min HPA replicas for deployment: {min_hpa_replicas}")
            result_payload = {
                "metrics": metrics,
                # "comparison": comparison,
                # "shap": shap_summary,
                "predictions": rule_df,
                "suggested_min_hpa": int(min_hpa_replicas)
            }

            if include_debug:
                debug_metadata = generate_debug_metadata(model, X, deployment_id, _used_features)
                result_payload.update(debug_metadata)

            results[deployment_id] = result_payload

        except Exception as e:
            logger.exception(f"❌ Error in deployment {deployment_id}: {e}")
            results[deployment_id] = {"error": str(e)}

    return results


def safe_ceil_div(numerator, denominator):
    if denominator == 0:
        return 1
    return max(ceil(numerator / denominator), 1)
