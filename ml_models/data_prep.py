from autoencoder_tagging import tag_outliers_with_autoencoder
from ml_models.segmentation_tree import segment_workloads
from libs.analysis import calculate_min_pods_peak_off_peak
import logging
import pandas as pd
logger = logging.getLogger(__name__)
def build_training_dataset(
    peak_df, offpeak_df,
    latency_df=None, throttling_df=None, error_rate_df=None,
    rule_min_pods_df=None, historical_hpa_df=None,
    namespace_col="namespace", app_col="app_id"
):
    peak_agg = peak_df.groupby([namespace_col, app_col],observed=True).agg({
        'cpu': 'mean', 'memory': 'mean', 'traffic': 'mean'
    }).rename(columns={'cpu': 'avg_cpu_peak', 'memory': 'avg_memory_peak', 'traffic': 'avg_traffic_peak'})

    offpeak_agg = offpeak_df.groupby([namespace_col, app_col],observed=True).agg({
        'cpu': 'mean', 'memory': 'mean', 'traffic': 'mean'
    }).rename(columns={'cpu': 'avg_cpu_off', 'memory': 'avg_memory_off', 'traffic': 'avg_traffic_off'})

    df = peak_agg.join(offpeak_agg, how='inner')

    for optional_df, col in [(latency_df, "latency_ms"), (throttling_df, "throttle_pct"), (error_rate_df, "error_pct")]:
        if optional_df is not None and not optional_df.empty:
            agg = optional_df.groupby([namespace_col, app_col],observed=True).agg({col: "mean"})
            df = df.join(agg, how="left")

    if historical_hpa_df is not None:
        df = df.join(historical_hpa_df.set_index([namespace_col, app_col]).rename(columns={"min_pods": "historical_min_pods"}))
    if rule_min_pods_df is None:
        rule_min_pods_df = calculate_min_pods_peak_off_peak(
            peak_df=peak_df,
            offpeak_df=offpeak_df,
            throttling_df=throttling_df,
            latency_df=latency_df,
            error_rate_df=error_rate_df,
            buffer=1.2,
            max_cpu_per_pod=0.7
        )[[namespace_col, app_col, "final_recommended_min_pods"]]

    if rule_min_pods_df is not None:
        df = df.join(rule_min_pods_df.set_index([namespace_col, app_col]))

    df, _, _ = tag_outliers_with_autoencoder(df, feature_cols=['avg_cpu_off', 'avg_memory_off', 'avg_traffic_off'])
    df, _ = segment_workloads(df, feature_cols=['avg_cpu_off', 'avg_traffic_off'])

    logger.info(f"[build_training_dataset] 🔍 Null summary:\n{df.isnull().sum()}")
    # Final checks before dropping NaNs or returning
    for col in ['avg_cpu_off', 'avg_memory_off', 'avg_traffic_off']:
        if col not in df.columns:
            logger.error(f"[build_training_dataset] ❌ Missing column: {col}")
        elif df[col].isnull().all():
            logger.error(f"[build_training_dataset] ❌ All values are NaN in column: {col}")
        elif df[col].isnull().any():
            logger.warning(f"[build_training_dataset] ⚠️ Some NaNs in column: {col}")

    df = df.dropna(subset=["final_recommended_min_pods"])
    X = df.drop(columns=["final_recommended_min_pods"])
    y = df["final_recommended_min_pods"]

    return X, y

def build_training_dataset_raw(peak_df, offpeak_df, latency_df=None, throttling_df=None, error_rate_df=None,
                                current_pods_df=None) -> tuple[pd.DataFrame, pd.Series]:
    df = pd.concat([peak_df, offpeak_df])

    for optional_df in [latency_df, throttling_df, error_rate_df, current_pods_df]:
        if optional_df is not None:
            df = df.merge(optional_df, on=["timestamp", "namespace", "app_id", "cluster_id"], how="left")

    df = df.dropna(subset=["current_pods"])
    X = df.drop(columns=["current_pods"])
    y = df["current_pods"]
    return X, y