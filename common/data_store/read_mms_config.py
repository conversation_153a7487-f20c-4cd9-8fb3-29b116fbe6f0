import os
import logging
import json
import time

import settings
from ruamel.yaml import YAM<PERSON>, YAMLError
import yaml as ryaml
from libs import shell
from git_service import exceptions
from git_service.git import Git
from collections import OrderedDict
from common.data_store.data_processor import Data
from template_engine import compile

logger = logging.getLogger(__name__)
ALERT_TYPE = {"app-owner", "sre"}
CONVERT_NAME = {"alert_team": "alert_team_name", "mms_xmatters_group": "xmatters_group",
                "mms_slack_channel": "slack_channel", "mms_email": "team_email"}
THRESHOLDS = "_threshold_"
THRESHOLDS_KEY = "thresholds"
PREFIX = "international-tech/intl-sre/golden-signals/rules/production"
ALERT_CONFIG_URL = "https://gecgithub01.walmart.com/intl-ecomm-svcs/mms-config"
ALERT_REPO_NAME = "mms-config"
ALERT_OWNER = "intl-ecomm-svcs"


class ExtractFileFields(Data):
    def __init__(self, template, is_sre_slas=True):
        super().__init__(template)
        self.is_sre_slas = is_sre_slas
        self.mapper_location = os.path.join(self.templates_repo, "data_store", "sla", "user")
        self.mapper_data = self.get_configs_data(self.mapper_location)

        self.cloned_to_location = settings.BASE_DIR + "/" + str(int(time.time() * 1000))
        self.root_location = os.path.join(self.cloned_to_location, ALERT_REPO_NAME)
        self.git = Git(ALERT_CONFIG_URL, cloned_to_location=self.cloned_to_location,
                       project=ALERT_OWNER)
        self.alert_sla_names = self.get_template_data(template)

    def get_configs_data(self, location):
        return ExtractFileFields.read_data(location, self.template_file_name)

    @staticmethod
    def read_data(location, template_file_name):
        """
        Reads yml data as dict
        """
        values = dict()
        try:
            _file = f"{location}/{template_file_name}"
            logger.info(f"Reading yaml file {_file}")
            file_exists = os.path.isfile(_file)
            if not file_exists:
                logger.warning(f"{_file} file not fount ")
                return values

            yaml = YAML(typ='rt')
            with open(_file) as file:
                values = yaml.load(file)

            # values = yaml.load(open(_file))
            # values = yaml.load(open(_file), Loader=yaml.FullLoader)
        except ryaml.constructor.DuplicateKeyError as exc:

            logger.exception(f"Error reading YAML file Duplicate {_file}: {exc}")
            return values
        except YAMLError as exc:
            logger.exception(f"Error reading YAML file YAMLError {_file}: {exc}")
            return values
        except Exception as exc:
            logger.exception(f"Error reading YAML file {_file}: {exc}")
            return values
        if values is None:
            return dict()
        return values

    def load_files(self, _path):
        files = OrderedDict()

        files_list = os.listdir(_path)
        for each_file in files_list:
            files[each_file] = self.read_yaml(f"{_path}/{each_file}")

        return files

    def read_yaml(self, _yaml_path):
        values = OrderedDict()

        try:
            logger.info(f"Reading yaml file {_yaml_path}")
            file_exists = os.path.isfile(_yaml_path)
            if not file_exists:
                logger.warning(f"{_yaml_path} file not found ")
                return values
            yaml = YAML(typ='rt')
            with open(_yaml_path) as file:
                values = yaml.load(file)
            # values = yaml.safe_load(open(_yaml_path))
        except YAMLError as exc:
            logger.exception(exc)
            return values
        if values is None:
            return OrderedDict()

        return values

    def convert_to_json_file(self, alert_type):
        list1 = list(self.result.values())
        file_name = alert_type + "_data.json"
        with open(file_name, 'w') as f:
            json.dump(list1, f)

    @staticmethod
    def get_template_data(template_name):
        _time = int(time.time())
        template_data = compile.get_template_variables_and_default_vars(template_name)
        alert_sla_names = []
        for group in template_data.get('content', {}).get('groups', []):
            for rule in group.get('rules', []):
                alert_sla_name = rule.get('labels', {}).get('alert_sla_name')
                if alert_sla_name:
                    alert_sla_names.append(alert_sla_name)
        return alert_sla_names

    def extract_fields2(self, alert_path, alert_file, _keys, template_name):
        pass

    @staticmethod
    def check_key_and_get_value_for_key(key, key_map):
        '''
        For a given key , exists then gets the value for key_map.get(key)
        '''
        if key in key_map:
            return True, key_map.get(key)
        logger.warning(f"Key 'alert_sla_name' not found in rule_dict for {key} not in {key_map}")
        return False, None

    def check_communication_key(self, key, missing_keys, rule_dict):
        if key in missing_keys:
            is_exits, value = self.check_key_and_get_value_for_key('alert_sla_name', rule_dict)
            if is_exits:
                return True
        return False

    def extraxt_rule(self, rule, key_mapper, required_keys, user_provided_params_map=None):
        if not rule:
            return False, {}
        response_data = dict()
        thresholds = dict()
        filter_strings = dict()
        missing_all_communications_keys_alerts = False
        missing_xmatters_group, missing_slack_rules, missing_email_rules = False, False, False
        for key, val in rule['labels'].items():
            if 'threshold' in key:
                thresholds[key] = str(val)
                continue
        response_data["thresholds"] = thresholds
        for key, val in rule.get('annotations', {}).items():
            if key.endswith("_filter_string"):
                if val is not None:
                    s_val = str(val).strip()
                    if s_val and s_val.lower() != "empty":
                        filter_strings[key] = str(val)
        response_data["filter_strings"] = filter_strings
        added_keys = self.extract_labels_and_add_juno_names(rule['labels'])
        if user_provided_params_map:
            added_keys = {**added_keys, **user_provided_params_map}
        missing_keys = [key for key in required_keys if key not in added_keys]
        ExtractFileFields.add_any_new_key_handling(added_keys, key_mapper)

        if missing_keys:
            if len(missing_keys) == len(required_keys):
                is_exits, value = self.check_key_and_get_value_for_key('alert_sla_name', added_keys)
                if is_exits:
                    missing_all_communications_keys_alerts = True
            else:
                missing_xmatters_group = self.check_communication_key("xmatters_group", missing_keys, added_keys)
                missing_slack_rules = self.check_communication_key("slack_channel", missing_keys, added_keys)
                missing_email_rules = self.check_communication_key("team_email", missing_keys, added_keys)

        response_data["missing_keys"] = missing_all_communications_keys_alerts
        response_data["missing_xmatters_group"] = missing_xmatters_group
        response_data["missing_slack_channel"] = missing_slack_rules
        response_data["missing_team_email"] = missing_email_rules
        response_data["data"] = added_keys
        return True, response_data

    @staticmethod
    def extract_labels_and_add_juno_names(labels_data):
        juno_name = {**labels_data}
        if "mms_xmatters_group" in labels_data:
            juno_name.update({"xmatters_group": labels_data.get("mms_xmatters_group")})
        if "mms_slack_channel" in labels_data:
            juno_name.update({"slack_channel": labels_data.get("mms_slack_channel")})
        if "mms_email" in labels_data:
            juno_name.update({"team_email": labels_data.get("mms_email")})

        return juno_name

    @staticmethod
    def add_any_new_key_handling(data, new_keys_map):
        """
        new_keys_map:{new_key: old_key}
        """
        # Add new keys with values from corresponding old keys
        if new_keys_map:
            for new_key, old_key in new_keys_map.items():
                if old_key in data:  # Check if the old key exists in the original dictionary
                    data[new_key] = data[old_key]

    @staticmethod
    def get_communication_from_yaml(status, key, process_rule, communication):
        if not status:
            _status, value = ExtractFileFields.check_key_and_get_value_for_key(key, process_rule)
            if _status:
                communication[key] = value
            return _status
        return status

    @staticmethod
    def aggregate_skipp_rules(processed_rules):

        communication = {"xmatters_group": None,
                         "slack_channel": None,
                         "team_email": None}
        xmatters_group_status = False
        slack_channel = False
        team_email = False
        skip_rules = set()
        skip_xmatters = set()
        skip_slack = set()
        skip_email = set()
        thresholds = dict()
        all_data = dict()
        all_filter_strings = dict()

        for process_rule in processed_rules:
            if not xmatters_group_status:
                xmatters_group_status = ExtractFileFields.get_communication_from_yaml(xmatters_group_status,
                                                                                      "xmatters_group",
                                                                                      process_rule.get("data"),
                                                                                      communication)
            if not slack_channel:
                slack_channel = ExtractFileFields.get_communication_from_yaml(slack_channel, "slack_channel",
                                                                              process_rule.get("data"), communication)
            if not team_email:
                team_email = ExtractFileFields.get_communication_from_yaml(team_email, "team_email",
                                                                           process_rule.get("data"), communication)

            logger.debug(process_rule)
            alert_sla_name = process_rule.get("data", {}).get('alert_sla_name')
            if process_rule.get('missing_keys'):
                skip_rules.add(alert_sla_name)
            if process_rule.get('missing_xmatters_group'):
                skip_xmatters.add(alert_sla_name)
            if process_rule.get('missing_slack_channel'):
                skip_slack.add(alert_sla_name)
            if process_rule.get('missing_team_email'):
                skip_email.add(alert_sla_name)
            thresholds.update(**process_rule.get("thresholds", dict()))
            all_filter_strings.update(**process_rule.get("filter_strings", dict()))
            all_data.update(**process_rule.get("data", dict()))
        data_dict = {"skip_rules": list(skip_rules), "skip_xmatters": list(skip_xmatters),
                     "skip_slack": list(skip_slack), "skip_email": list(skip_email)}
        all_data.update(**communication)
        return all_data, thresholds, data_dict, communication, all_filter_strings

    @staticmethod
    def filter_and_validate_alert_rules_data(rules_count, all_data, skipp_data,
                                             mandatory_required_keys, communication_required_keys,
                                             communication_dict):
        # If all the communication channels are commented for all the rules, communication_required_keys will be none
        #         So, exclude communication_keys from mandatory_required_keys
        mandatory_keys_without_communication_keys = list(
            set(mandatory_required_keys) - set(communication_required_keys))

        # MODIFIED: Further filter mandatory_keys_without_communication_keys to exclude filter strings
        final_mandatory_keys_to_check = [
            key for key in mandatory_keys_without_communication_keys
            if not key.endswith("_filter_string")
        ]

        # Check all mandatory parameter present in all_data, using the filtered list
        is_all_mandatory_keys_present = all(
            elem in all_data.keys() for elem in final_mandatory_keys_to_check)

        # case1: Check all alerts are disabled,all rules count is same as skip_rules. Means all alerts disabled
        all_alerts_disabled = rules_count == len(list(set(skipp_data.get('skip_rules'))))

        # Better way is all_data[key], but using all_data.get(key, "empty"). All alerts disabled not of the
        # communication channel data presents all_data. So adding empty
        filtered_rule_dict = {key: all_data.get(key, "empty") for key in final_mandatory_keys_to_check if
                              key in final_mandatory_keys_to_check}
        # append communication, required in inventory file
        filtered_rule_dict.update(**communication_dict)
        return is_all_mandatory_keys_present, all_alerts_disabled, filtered_rule_dict

    @staticmethod
    def get_alert_names(groups_data):
        alert_names = list()
        for group in groups_data:
            for rule in group.get("rules", list()):
                if not rule:
                    continue
                labels = rule.get("labels", dict()).keys()
                data = [label for label in labels if "_threshold_" in label]
                if "alert_sla_name" in rule.get("labels"):
                    alert_names.append(rule.get("labels").get("alert_sla_name"))
                elif len(data) > 0:
                    alert_names.append(data[0])
        return alert_names

    @staticmethod
    def get_entire_disabled_rules_and_add_to_skip_rules(groups_data, template_alert_sla_names, skipp_data):
        alert_sla_names_in_mms_yaml = ExtractFileFields.get_alert_names(groups_data)
        filtered_template_alert_sla = list(set(template_alert_sla_names) - set(alert_sla_names_in_mms_yaml))
        skipp_data.get("skip_rules").extend(filtered_template_alert_sla)

    def extract_fields_from_alert_yaml_files(self, alert_path, alert_file, mandatory_required_keys, key_mapper,
                                             template_alert_sla_names, user_provided_params_map=None):
        entire_rule_commented = False
        yml_alert_file_content = ExtractFileFields.read_data(alert_path, alert_file)
        required_keys = {"slack_channel", "team_email", "xmatters_group"}
        processed_rules = list()
        rules_count = 0
        groups = None
        if len(yml_alert_file_content) > 0:
            groups = yml_alert_file_content.get('groups', [{}])
            for group in groups:
                rules = group.get('rules', [])
                rules_count = len(rules)
                for rule in rules:
                    if "record" in rule:
                        continue
                    status, parsed_rule_data = self.extraxt_rule(rule, key_mapper, required_keys,
                                                                 user_provided_params_map)
                    if not status:
                        entire_rule_commented = True
                        logger.error("Entire rule is commented, ignored.")
                        continue
                    processed_rules.append(parsed_rule_data)

        # Process and aggregate rules data
        all_data, thresholds, skipp_data, communication_dict, all_filter_strings = ExtractFileFields.aggregate_skipp_rules(processed_rules)

        # Clean up alert_id if it exists in all_data
        if 'alert_id' in all_data:
            alert_id_with_suffix = all_data['alert_id']
            
            # Extract the base alert_id by removing the suffix
            if '_' in alert_id_with_suffix:
                parts = alert_id_with_suffix.rsplit('_', 1)
                if len(parts) == 2 and parts[1].isdigit():
                    # Replace with base alert_id
                    all_data['alert_id'] = parts[0]

        if entire_rule_commented:
            ExtractFileFields.get_entire_disabled_rules_and_add_to_skip_rules(groups, template_alert_sla_names,
                                                                              skipp_data)
        data = ExtractFileFields.filter_and_validate_alert_rules_data(rules_count, all_data, skipp_data,
                                                                      mandatory_required_keys, required_keys,
                                                                      communication_dict)
        is_all_mandatory_keys_present, all_alerts_disabled, filtered_rule_dict = data

        if not is_all_mandatory_keys_present and not all_alerts_disabled:
            logger.error(f"Old alerts data present in {alert_path}/{alert_file}")
            logger.error(f"missing params are  {set(mandatory_required_keys) - set(all_data.keys())}")
            return False, all_data

        return True, {"data": {**filtered_rule_dict, "thresholds": thresholds, "filter_strings": all_filter_strings, **skipp_data}}

    def extract_fields_files(self, path, keys):
        service = os.path.basename(os.path.normpath(path))
        # get sre/app-owners folder
        alert_type_list = [file.name for file in os.scandir(path) if file.is_dir()]
        for alert_type in alert_type_list:
            # sre or app-owner
            _type = alert_type.rsplit("-", 1)
            if _type[0] in ALERT_TYPE:
                # go inside alert file dir and scan all files
                alerts_file_list = os.listdir(f"{path}/{alert_type}")

                for alert_file in alerts_file_list:
                    self.extract_fields(f"{path}/{alert_type}/{alert_file}", _type[0], service, keys)

            # convert to json and clear old results
            self.convert_to_json_file(_type[0])
            self.result = dict()

    def process(self):
        if self.is_sre_slas:
            location = os.path.join(self.root_location, )

        self.extract_fields_files(alert_files_path, filter_keys)

    def create_inventory_files(self, input_json, template_name):
        json_file = open(input_json)
        json_array = json.load(json_file)
        file_name = ""
        result = dict()

        for item in json_array:
            if "app_name" in item:
                val = item["app_name"]
                git_path = item["git_path"]

                if "sre" in os.path.basename(os.path.normpath(git_path)):
                    file_name = f"sre_{template_name}_alerts.yaml"
                else:
                    file_name = f"app_owner_{template_name}_alerts.yaml"

                result[val] = item

        # save
        f = open(os.getcwd() + "/" + file_name, 'w')
        ryaml.dump(result, f, default_flow_style=False, sort_keys=False, explicit_start=True)


if __name__ == "__main__":
    alert_files_path = "/Users/<USER>/git/mms-config/international-tech/intl-sre/golden-signals/rules/production/wcnp"
    filter_keys = {"severity", "alert_team", "mms_slack_channel", "mms_email", "mms_xmatters_group", "alert_id",
                   "namespace",
                   "app_name", "alert_sla_name", "alert_component", "tier", "market", "valunit", "region",
                   "pods_restarts_current_threshold_count"
                   }
    update = ExtractFileFields(template="wcnp_alerts.yaml")
    update.extract_fields_files(alert_files_path, filter_keys)
    # update.create_inventory_files("/Users/<USER>/Git/juno/common/data_store/sre_data.json", "wcnp")
