CONNECTOR = "teap"
PLATFORM = "ms-df-cache"


class MegCacheExecutor:
    def __init__(self, teap_data_handler, prometheus_handler, *args, **kwargs):
        """
        args: Always would be leaf nodes
        """
        self.teap_data_handler = teap_data_handler
        self.prometheus_handler = prometheus_handler
        self.kwargs = kwargs

    def execute(self, app_data, *args, **kwargs):
        market = app_data.get("metadata").get('market')
        cluster = app_data.get(PLATFORM).get('namespaceOrAssemblyOrDB')
        tier = app_data.get("metadata").get('tier')

        return {"market": market, "cluster": cluster, "tier": tier}


if __name__ == "__main__":
    from common.teap_handler.teap_data_handler import TeapDataHandler
    from libs.prometheus_client import Prometheus

    pro = Prometheus()
    #teap = TeapDataHandler()
    data = teap.teap_data.get_apps_by_platform_and_tier(platform="ms-df-cache", tier="one")
    t = MegCacheExecutor(teap, pro)
    t.execute(app_data=data[0])
