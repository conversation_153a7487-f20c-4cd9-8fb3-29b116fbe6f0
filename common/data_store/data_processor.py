import os, ruamel
import settings
import yaml as ryaml
import logging
from git_service.git import Git
from ruamel import yaml


logger = logging.getLogger(__name__)
TEMPLATE_REPO_NAME = "sre-alert-templates"
TEMPLATE_GIT_URL = "***************************:intl-ecomm-svcs/sre-alert-templates.git"
TEMPLATES_BASE = settings.TEMPLATE_BASE_DIR
TEMPLATE_NAME_POSTFIX = settings.TEMPLATE_POSTFIX
git = Git(TEMPLATE_GIT_URL, branch="main", cloned_to_location=settings.TEMPLATE_BASE_DIR)
THRESHOLDS_KEY = "thresholds"


class Data:
    def __init__(self, template, custom_inventory_file=None):
        self.template_file_name = template.rstrip().strip()
        self.template_file_name_without_extension = os.path.splitext(self.template_file_name)[0]
        self.templates_repo = "{}/{}".format(settings.TEMPLATE_BASE_DIR, git.working_folder_checkout_repo_name)
        self.data_location = os.path.join(self.templates_repo, "data")
        self.sre_sla_location = os.path.join(self.templates_repo, "data_store","sla", "sre")
        self.user_sla_location = os.path.join(self.templates_repo, "data_store","sla", "user")
        self.filter_pattern_location = os.path.join(self.templates_repo, "data_store", "filters")
        self.inventory_location = os.path.join(self.templates_repo, "data_store", "inventory",
                                               self.template_file_name_without_extension)

        # Add new custom inventory location
        self.custom_inventory_location = os.path.join(self.templates_repo, "data_store", "inventory", "custom",
                                               self.template_file_name_without_extension)
        
        # Create custom directory if it doesn't exist
        if custom_inventory_file:
            os.makedirs(self.custom_inventory_location, exist_ok=True)

        # data_file = os.path.join(self.data_location, self.template_file_name)
        # values = yaml.safe_load(open(data_file))
        # self.key_placeholder_string = values.get("file_name")

        self.ignore_placeholders = ['user', 'group']

        self.template_default_data = self.get_template_default_data(template)
        self.user_inventory_data = self.read_inventory_file(False, custom_inventory_file=custom_inventory_file)
        self.sre_inventory_data = self.read_inventory_file(True, custom_inventory_file=None)
        self.sre_sla_defaults = self.get_sre_default_data(template)
        self.user_sla_defaults = self.get_user_default_data(template)

        # Check if template_default_data is empty
        if not self.template_default_data:
            # raise ValueError("template_default_data is empty")
            logger.error("template_default_data is empty")
            return
            
        file_name = self.template_default_data.get("file_name")
        if file_name is None:
            # raise ValueError(f"'file_name' not found in template_default_data for {self.template_default_data}")
            logger.error(f"'file_name' not found in template_default_data for {self.template_default_data}")
            return
        
        placeholders_tokens = os.path.splitext(file_name)
        
        self.filters_data = self.get_filters_pattern_data(self.template_file_name)
        if len(placeholders_tokens) == 2:
            self.key_placeholder_string = placeholders_tokens[0]
        else:
            raise Exception(f"Unable to handle 'file_name' from template_default_data for {self.template_default_data}")

    def get_template_default_data(self, template):
        if not template:
            template = self.template_file_name
        return Data.read_data(self.data_location, template)

    def get_user_default_data(self, template):
        if not template:
            template = self.template_file_name
        return Data.read_data(self.user_sla_location, template)

    def get_sre_default_data(self, template):
        if not template:
            template = self.template_file_name
        return Data.read_data(self.sre_sla_location, template)

    def get_inventory_data(self, template):
        if not template:
            template = self.template_file_name
        return Data.read_data(self.inventory_location, template)

    def get_filters_pattern_data(self, template):
        if not template:
            template = self.template_file_name
        return Data.read_data(self.filter_pattern_location, template)

    @staticmethod
    def read_data(location, template_file_name):
        """
        Reads yml data as dict
        """
        values = dict()
        # if templates_default_data_yaml_file_name not in self.templates:
        #     logger.warning(f"Defaults file does not exits {templates_default_data_yaml_file_name}")
        #     return values
        try:
            _file = f"{location}/{template_file_name}"
            logger.info(f"Reading yaml file {_file}")
            file_exists = os.path.isfile(_file)
            if not file_exists:
                logger.warning(f"{_file} file not fount ")
                return values
            # values = yaml.safe_load(open(_file))
            values = ryaml.load(open(_file), Loader=ryaml.FullLoader)
        except yaml.YAMLError as exc:
            logger.exception(exc)
            return values
        if values is None:
            return dict()
        return values

    def _get_app_data_from_inventory_file(self, app_meta_data, inventory_data, ignore_inventory_data=True):
        """
        From template, get name. Name has variables. Use them as key to get the values
        """
        if not ignore_inventory_data:
            key_is = self._build_key_using_file_name_attr(app_meta_data)
            if key_is:
                return inventory_data.get(key_is, dict())
        return dict()

    @staticmethod
    def filter_required_field_data(key, filter_criteria):
        data_exact_filter = list(filter(lambda criteria: key == criteria, filter_criteria.get("include").get("exact")))
        data_like_filter = list(filter(lambda criteria: criteria in key, filter_criteria.get("include").get("like")))
        if len(data_exact_filter) > 0 or len(data_like_filter) > 0:
            return True
        return False

    @staticmethod
    def build_thresholds_map(key, filter_criteria):
        data_like_filter = list(
            filter(lambda criteria: criteria in key, filter_criteria.get("include").get("thresholds")))
        if len(data_like_filter) > 0:
            return True
        return False

    def comment_alert(self, alert_id):
        '''
        Given alert_id, serialize yaml file and get the name/message using alert id
        then set alert_XXX_status to false , so it comments slack and xmatters and email

        sometims, it's very hard to get alert_XXX_status. So serialize template.yml to get name if alert_XXX
        '''

    def read_inventory_file(self, is_sre_sla=True, custom_inventory_file=None):
        """Modified to handle custom inventory path"""
        try:
            if custom_inventory_file:
                # Fix the path construction - don't include filename in directory path
                custom_dir = os.path.join(self.templates_repo, "data_store", "inventory", "custom", 
                                        self.template_file_name_without_extension)
                # Create directory if it doesn't exist
                os.makedirs(custom_dir, exist_ok=True)
                # Construct the full file path correctly

                inventory_data = self.read_data(custom_dir, custom_inventory_file)
            else:
                # Original code for standard inventory
                template = self.get_sre_and_app_owner_templates(is_sre_sla)
                inventory_data = self.read_data(self.inventory_location, template)

            # Process thresholds
            for i_key, i_val in inventory_data.items():
                try:
                    thresholds = i_val.get(THRESHOLDS_KEY, dict())
                    if not thresholds:
                        i_val.pop(THRESHOLDS_KEY, None)
                        continue
                    i_val.update({**thresholds})
                    i_val.pop(THRESHOLDS_KEY, None)
                except Exception as e:
                    logger.exception(f"Exception occurred while processing read_inventory_file key {i_key} value {i_val}", e)
            return inventory_data
        except Exception as e:
            logger.exception(e)

    def get_sre_and_app_owner_templates(self, is_sre_sla, custom_inventory_file=None):
        if is_sre_sla:
            template = f"sre_{self.template_file_name}"
        elif custom_inventory_file:
            template = custom_inventory_file
        else:
            template = f"app_owner_{self.template_file_name}"
        return template

    def _build_key_using_file_name_attr(self, app_meta_data):
        try:
            return self.key_placeholder_string.format_map(app_meta_data)
        except Exception as e:
            logger.exception(e)
            return dict()

    def upsert_inventory_file(self, apps_meta_data, is_sre_sla):
        """
        Internal purpose only.
        It does three things
            1. filters thresholds and keeps in thresholds key bucket
            2. filters remain required fields other than thresholds
            3. persists data
        """
        current_data = self.read_inventory_file(is_sre_sla)
        for data in apps_meta_data:
            logger.info(f"processing {data}")
            _filter_data = dict()
            # _filter_data = OrderedDict()
            for key, val in data.items():
                # filters only thresholds
                is_key_contain_threshold = Data.build_thresholds_map(key, self.filters_data)
                if is_key_contain_threshold:
                    if THRESHOLDS_KEY not in _filter_data:
                        # _filter_data[THRESHOLDS_KEY] = OrderedDict()
                        _filter_data[THRESHOLDS_KEY] = dict()
                    _filter_data["thresholds"][key] = val
                    continue
                # filters remain required fields other than thresholds
                status = Data.filter_required_field_data(key, self.filters_data)
                if status:
                    _filter_data[key] = val
            if "status" not in data.keys():
                _filter_data["status"] = True
            key = self._build_key_using_file_name_attr(data)
            if not key:
                logger.error(f"Unable to insert data for {_filter_data}")
                continue
            current_data[key] = _filter_data
        _template = self.get_sre_and_app_owner_templates(is_sre_sla)
        f = open(os.path.join(self.inventory_location, _template), 'w')
        ryaml.dump(current_data, f, default_flow_style=False, sort_keys=False, explicit_start=True)
        # yaml.dump(current_data, f, default_flow_style=False, explicit_start=True)

    def merge_and_get_app_data(self, app_meta_data, is_sre_sla, sre_override_xmatters_and_slack=True):

        if is_sre_sla:
            sre_or_app_sla_defaults = self.sre_sla_defaults
            # For SRE alerts, xmatters_group and slack_channel are same, so overriding app details
            if sre_override_xmatters_and_slack:
                sre_or_app_inventory_data = Data.override_xmatters_and_slack(app_data=app_meta_data,
                                                                             sre_slas=sre_or_app_sla_defaults)
            else:
                sre_or_app_inventory_data = app_meta_data
        else:
            sre_or_app_sla_defaults = self.user_sla_defaults
            sre_or_app_inventory_data = app_meta_data
        # Todo: Looks below "slack_channel" code is absolute after moving to inventory file.
        # Check "slack" missing app. Only app_alerts not for SRE
        if not app_meta_data.get("slack_channel") or not sre_or_app_inventory_data.get("slack_channel"):
            # Get slack details from app_defaults
            if "slack_channel" in sre_or_app_sla_defaults:
                sre_or_app_inventory_data["slack_channel"] = sre_or_app_sla_defaults.get("slack_channel")

        # If slack_channel param does not have it in alert data
        if "slack_channel" not in sre_or_app_inventory_data:
            sre_or_app_inventory_data["slack_channel"] = self.template_default_data.get("slack_channel")

        # Todo: Before returning the data , check all mandatory required data hav valid data. Pands return 'None'
        # Todo: for null, so all the mandatory params should contain the data other than 'None
        return {**self.template_default_data, **sre_or_app_sla_defaults, **app_meta_data, **sre_or_app_inventory_data}

    @staticmethod
    def override_xmatters_and_slack(app_data, sre_slas):
        if "xmatters_group" in sre_slas:
            app_data["xmatters_group"] = sre_slas.get("xmatters_group")
        if "team_email" in sre_slas:
            app_data["team_email"] = sre_slas.get("team_email")
        if "slack_channel" in sre_slas:
            app_data["slack_channel"] = sre_slas.get("slack_channel")
        return app_data

    @staticmethod
    def _comment_communication_channels(alerts_yaml_dict, alert_id):
        for _alert in alerts_yaml_dict["groups"][0]["rules"]:
            matched_alert = False
            for tag in _alert.get("labels"):
                if tag == "alert_id":
                    if _alert["labels"][tag] == alert_id.strip().rstrip():
                        matched_alert = True
            if matched_alert:
                del _alert["labels"]['mms_xmatters_group']
                del _alert["labels"]['mms_slack_channel']

    def comment_communication_channels(self):
        pass


if __name__ == "__main__":
    import sys, os, datetime

    log_level = logging.DEBUG

    logger = logging.getLogger()
    logger.setLevel(log_level)
    # log_format = '%(asctime)s  %(lineno)4d %(levelname)10s: %(message)s'
    log_format = '%(asctime)s %(filename)25s %(lineno)3d %(levelname)10s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setLevel(level=logging.DEBUG)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)

    dara = {'tier': 'tier-2', 'market': 'mx', 'domain': 'Merchandising', 'app_name': 'badging',
            'slack_channel': 'int-core-sre-dummy-merch-merch', 'namespace': 'ca-badging',
            'namespace_or_assembly': 'ca-badging', 'app': 'laurentian',
            'team_email': '<EMAIL>',
            'alert_team_name': 'canada-catalog-ops-oncall-xmatters',
            'xmatters_group': 'canada-catalog-ops-oncall-xmatters',
            'base_repo': '***************************:Telemetry/mms-config.git',
            'fork_repo': '***************************:intl-ecomm-svcs/mms-config.git',
            'git_path': 'international-tech/intl-sre/golden-signals/rules/production/wcnp/sre-alerts',
            'file_name': '{app_name}-{namespace}.yaml', 'mms_source': 'wcnp', 'cluster_profile': 'prod',
            'job': 'kube-state-metrics', 'alert_team': 'intl_sre_golden_signals',
            'pods_restarts_current_threshold_count': 8,
            'cpu_usage_current_threshold_pct': 85, 'memory_usage_current_threshold_pct': 85,
            'fiveXX_trend_comparing_to_one_week_ago_threshold_pct': 50,
            'fiveXX_current_threshold_count_used_for_trend': 200,
            'fiveXX_current_threshold_pct': 50, 'traffic_spike_comparing_to_one_week_ago_threshold_pct': 75,
            'traffic_drop_comparing_to_one_week_ago_threshold_pct': 75,
            'latency_spike_comparing_to_one_week_ago_threshold_pct': 200,
            'scus_traffic_in_balance_current_threshold_pct': 10,
            'wus_traffic_in_balance_current_threshold_pct': 10, 'eus_traffic_in_balance_current_threshold_pct': 10,
            'eus_traffic_in_balance.current.threshold_pct': 10,
            'latency_spike.comparing_to_one_week_ago.threshold_pct': 200,
            'scus_traffic_in_balance.current.threshold_pct': 10,
            'traffic_drop.comparing_to_one_week_ago.threshold_pct': 75,
            'wus_traffic_in_balance.current.threshold_pct': 10}
    # d = Data("wcnp_alerts.yaml")
    force_pull = True
    sre_override_xmatters_and_slack = True
    import logging

    from common.alert.alertsPostDataBuilder import AlertsPostDataBuilder
    from template_engine.template_handler import get_latest_templates

    # get_latest_templates()

    get_latest_templates(force_pull_required=force_pull)
    # persist_data = False
    #
    # alert_data = AlertsPostDataBuilder("wcnp_alerts.yaml", is_sre_sla=True, tier="zero",
    #                                    custom_inventory_file=None)
    # data = alert_data.build_post_body(is_sre_sla=True, persist_data=persist_data,
    #                                   sre_override_xmatters_and_slack=sre_override_xmatters_and_slack)
    # d.upsert_inventory_file("wcnp_alerts.yaml", [dara, dara], is_sre_sla=False)
    # data = d.get_app_data_from_inventory_file(, {'namespace': 'ca-badging', 'app_name': 'badging'})
    # data1 = d.merge_and_get_app_data({"namespace": "mx-glass", "app_name": "journey"}, is_sre_sla=True,
    #                                  ignore_inventory_data=False)
    # alert_data = AlertsPostDataBuilder("wishwall_application.yaml", is_sre_sla=False,
    #                                    custom_inventory_file="mx_wishwall_application.yaml")
    template = "wishwall_application.yaml"
    custom_inventory_file = "mx_wishwall_application.yaml"
    # self.platform = tmp_mapper.get("platform")

    data_store = Data(template, custom_inventory_file=custom_inventory_file)
    print(data_store.user_inventory_data)
