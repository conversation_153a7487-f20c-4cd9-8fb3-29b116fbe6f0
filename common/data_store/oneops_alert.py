CONNECTOR = "teap"
PLATFORM = "oneops"


class OneOpsExecutor:
    def __init__(self, teap_data_handler, prometheus_handler, *args, **kwargs):
        """
        args: Always would be leaf nodes
        """
        self.teap_data_handler = teap_data_handler
        self.prometheus_handler = prometheus_handler
        self.kwargs = kwargs

    def execute(self, app_data, *args, **kwargs):
        xmatters = self.create_alert_data(app_data)
        return xmatters

    def create_alert_data(self, app_data):
        app_xmatters = dict()
        app_xmatters["platform"] = PLATFORM

        for _k, _value in app_data.items():
            if _k == "metadata":
                app_xmatters["assembly"] = _value.get('namespaceOrAssemblyOrDB').replace(" ", "")
                app_xmatters["namespace"] = _value.get('namespaceOrAssemblyOrDB').replace(" ", "")
                app_xmatters["app_name"] = _value.get("name").lower()

        for _k, _value in app_data.items():
            if _k == "app_inventory":
                app_inventory_val_is = None
                if len(_value) == 0:
                    raise Exception(f'Proper app_inventory data not found')
                elif len(_value) == 1:
                    app_inventory_val_is = _value[0]
                elif len(_value) > 1:
                    for inventory_data in _value:
                        if inventory_data["market"] == app_xmatters.get("market"):
                            app_inventory_val_is = inventory_data
                            break
                    if not app_inventory_val_is:
                        raise Exception(f'Proper app_inventory data not found')

                app_xmatters["slack_channel"] = app_inventory_val_is.get("slack_channel")
                app_xmatters["team_email"] = app_inventory_val_is.get("dl")
                app_xmatters["alert_team_name"] = app_inventory_val_is.get("xmatters")
                app_xmatters["xmatters_group"] = app_inventory_val_is.get("xmatters")

        return app_xmatters
