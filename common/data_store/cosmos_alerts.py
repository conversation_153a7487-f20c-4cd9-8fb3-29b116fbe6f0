CONNECTOR = "teap"
PLATFORM = "cosmosdb"


class Processor:
    def __init__(self, teap_data_handler, prometheus_handler, *args, **kwargs):
        """
        args: Always would be leaf nodes
        """
        self.teap_data_handler = teap_data_handler
        self.prometheus_handler = prometheus_handler
        self.kwargs = kwargs
        self.subscriptions = self.get_cosmos_subscription()

    def execute(self, app_data, *args, **kwargs):
        app_name = app_data.get("metadata").get("name")
        # Only take wcnp or oneOps data from leaf node
        leaf_node = self.teap_data_handler.get_leaf_node(app_name)
        sub, rc_group = self.get_subscription_and_rc_group_name(app_data.get(PLATFORM).get('namespaceOrAssemblyOrDB'))
        data = {"subscription_name": sub, "resource_group": rc_group}
        if "oneops" in leaf_node:
            app_data["oneops"] = leaf_node.get("oneops")
        elif "wcnp" in leaf_node:
            app_data["wcnp"] = leaf_node.get("wcnp")

        exmatters = self.create_alert_data(app_data)
        return {**data, **exmatters}

    def get_subscription_and_rc_group_name(self, name):
        for sub, rcs in self.subscriptions.items():
            for rc in rcs:
                if name in rc:
                    return sub, rc
        return None, None

    def get_cosmos_subscription(self):
        subscription_details = dict()
        end_time, start_time = self.prometheus_handler.get_last_n_minutes_epoch_times(3)
        query = 'sum({__name__=~"availablestorage_bytes_total"}) by(subscription_name)'
        res = self.prometheus_handler.get_query_range_data(query=query, start_time=start_time, end_time=end_time,
                                                           steps=3)
        for entry in res.get("data").get("result"):
            sub = entry.get("metric").get('subscription_name')
            subscription_details[sub] = self.get_resource_groups(sub)
        return subscription_details

    def get_resource_groups(self, subscription_name):
        resource_groups = list()
        query = f'sum(availablestorage_bytes_total{{subscription_name="{subscription_name}"}}) by(resource_group)'
        end_time, start_time = self.prometheus_handler.get_last_n_minutes_epoch_times(3)
        res = self.prometheus_handler.get_query_range_data(query=query, start_time=start_time, end_time=end_time,
                                                           steps=3)
        for entry in res.get("data").get("result"):
            resource_groups.append(entry.get("metric").get('resource_group'))
        return resource_groups

    def create_alert_data(self, app_data):
        app_xmatters = dict()
        for _k, _value in app_data.items():
            if _k == "metadata":
                app_xmatters["tier"] = _value.get("tier")
                app_xmatters["market"] = _value.get("market")
                app_xmatters["domain"] = _value.get("domain")
                app_xmatters["app_name"] = _value.get("name").lower()
                # app_xmatters["slack_channel"] = _value.get("slack_channel")
            elif _k == "wcnp":
                _namespace = _value.get('namespaceOrAssemblyOrDB').replace(" ", "").rstrip().strip()
                _app = _value.get('app').replace(" ", "").rstrip().strip()
                app_xmatters["namespace"] = _namespace
                app_xmatters["namespace_or_assembly"] = _namespace
                app_xmatters["app"] = _app
                app_xmatters["app_name"] = _app
                app_xmatters["app_name_or_platform"] = _app
                app_xmatters["is_wcnp_env"] = "true"
            elif _k == "oneops":
                app_xmatters["namespace_or_assembly"] = _value.get('namespaceOrAssemblyOrDB').replace(" ", "")
                app_xmatters["app_name_or_platform"] = _value.get('platform').replace(" ", "")
                app_xmatters["is_wcnp_env"] = _value.get('org').replace(" ", "")

        for _k, _value in app_data.items():
            if _k == "app_inventory":
                app_inventory_val_is = None
                if len(_value) == 0:
                    raise Exception(f'Proper app_inventory data not found')
                elif len(_value) == 1:
                    app_inventory_val_is = _value[0]
                elif len(_value) > 1:
                    for inventory_data in _value:
                        if inventory_data["market"] == app_xmatters.get("market"):
                            app_inventory_val_is = inventory_data
                            break
                    if not app_inventory_val_is:
                        raise Exception(f'Proper app_inventory data not found')

                app_xmatters["team_email"] = app_inventory_val_is.get("dl")
                app_xmatters["alert_team_name"] = app_inventory_val_is.get("xmatters")
                app_xmatters["xmatters_group"] = app_inventory_val_is.get("xmatters")

        return app_xmatters


if __name__ == "__main__":
    # from common.teap_handler.teap_data_handler import TeapDataHandler
    # from libs.oneOps import OneOps
    #
    # teap = TeapDataHandler().teap_data
    # data = teap.get_apps_by_platform_and_tier(platform="cosmosdb", tier="one")
    # _data = data[2]
    from libs.prometheus_client import Prometheus

    pro = Prometheus()
    # t = Processor(TeapDataHandler(), pro)
    # t.execute(app_data=_data)
    subscription_details = dict()
    end_time, start_time = pro.get_last_n_minutes_epoch_times(3)
    query = '''sum(kube_pod_status_phase{mms_source="wcnp", cluster_id=~"(eus2-prod-a11|scus-prod-a98)", namespace=~"sct-vulcan-ca",phase=~"Running"}
and on(pod) kube_pod_labels{mms_source="wcnp", cluster_id=~"(eus2-prod-a11|scus-prod-a98)", label_app=~"vulcan-tracking-updates-poller-ca-prod"}
and on (pod) (increase(kube_pod_container_status_restarts_total{mms_source="wcnp", cluster_id=~"(eus2-prod-a11|scus-prod-a98)"}[2m]) == 0)) by (phase,cluster_id)'''
    res = pro.get_query_range_data(query=query, start_time=start_time, end_time=end_time,
                                                       steps=3)
    for entry in res.get("data").get("result"):
        sub = entry.get("metric").get('subscription_name')


        
