import pandas as pd
import settings
import collections, logging, os
from settings import CONFIG_DIR, TEMPLATE_BASE_DIR
from git_service.git import Git
from template_engine.compile import get_latest_templates

logger = logging.getLogger(__name__)
TEMPLATE_REPO_NAME = "sre-alert-templates"
TEMPLATE_GIT_URL = "***************************:intl-ecomm-svcs/sre-alert-templates.git"
TEMPLATES_BASE = TEMPLATE_BASE_DIR
git = Git(TEMPLATE_GIT_URL, branch="main", cloned_to_location=settings.TEMPLATE_BASE_DIR)

templates_repo = "{}/{}".format(settings.TEMPLATE_BASE_DIR, git.working_folder_checkout_repo_name)
data_location = os.path.join(templates_repo, "data")
FILE = f"{data_location}/teap.xlsx"
# FILE = f"{CONFIG_DIR}/teap.xlsx"
SUPPORTED_TIERS = ["zero", "one", "two", "two+", "other"]
SUPPORTED_MARKETS = ["CA", "MX", "CL", "Chile"]

get_latest_templates()


class TeapFileProcessor():
    def __init__(self):
        self.apps = list()
        logger.info("Serializing teap sheet")
        self.teap_data_frame = pd.read_excel(FILE, sheet_name='CA MX TAEP', engine='openpyxl', )

        logger.info("Serializing App Inventory sheet")
        self.inventory_data_frame = pd.read_excel(FILE, sheet_name='App Inventory', engine='openpyxl', )

        logger.info("Renaming App Inventory and Teap sheets")
        self.rename_columns()

        logger.info("Serializing and combine App Inventory and Teap sheets")
        self.apps = self.process()

    def get_apps_by_tier(self, tier="zero", apps=None):
        if not apps:
            apps = self.apps
        if tier in SUPPORTED_TIERS:
            return list(filter(lambda x: str(x["metadata"]["tier"]) == tier, apps))

    def get_apps_by_market(self, market="MX", apps=None):
        if not apps:
            apps = self.apps
        if market in SUPPORTED_MARKETS:
            return list(filter(lambda x: market in str(x["metadata"]["market"]), apps))

    def get_market_and_tier(self, market="MX", tier="zero"):
        apps = self.get_apps_by_market(market)
        return self.get_apps_by_tier(tier=tier, apps=apps)

    def get_all_tenants(self):
        tenants = set()
        for app in self.apps:
            for key in app.keys():
                if key not in ("metadata","wcnp","app_inventory"):
                    tenants.add(key)
        return tenants

    def get_market_and_platform(self, market="MX", platform="wcnp"):
        apps = self.get_apps_by_market(market)
        return self.get_apps_by_platform(platform=platform, apps=apps)

    def get_apps_by_platform(self, platform="wcnp", apps=None):
        if not apps:
            apps = self.apps
        return list(filter(lambda x: platform in x.keys(), apps))

    def get_apps_by_market_platform(self, platform="wcnp", market="MX", apps=None):
        if not apps:
            apps = self.apps
        platform_apps = filter(lambda x: platform in x.keys(), apps)
        return self.get_apps_by_market(market=market, apps=platform_apps)

    def get_apps_by_domain(self, domain, apps=None):
        if not apps:
            apps = self.apps
        return list(filter(lambda x: domain in str(x["metadata"]["domain"]), apps))

    def get_apps_by_market_platform_domain(self, platform="wcnp", market="MX", domain="Digital Experience", apps=None):
        if not apps:
            apps = self.apps
        platform_apps = filter(lambda x: platform in x.keys(), apps)
        _apps = self.get_apps_by_market(market=market, apps=platform_apps)
        return self.get_apps_by_domain(domain=domain, apps=_apps)

    def get_apps_by_platform_and_tier(self, platform="wcnp", tier="zero", apps=None):
        if not apps:
            apps = self.apps
        apps = list(filter(lambda x: platform in x.keys(), apps))
        return self.get_apps_by_tier(tier=tier, apps=apps)

    def get_apps_by_platform_and_tier_and_market(self, platform="wcnp", tier="zero", market="MX"):
        apps_by_market = self.get_apps_by_market(market)
        return self.get_apps_by_platform_and_tier(platform=platform, tier=tier, apps=apps_by_market)

    def process(self, *args, **kwargs):
        apps = list()
        # df = self.teap_data_frame[(self.teap_data_frame["tier"] == tier)]
        df1 = self.teap_data_frame.where(pd.notnull(self.teap_data_frame), None)
        for row in df1.to_numpy():
            _app = str(row[3])
            app = collections.OrderedDict()
            app["metadata"] = collections.OrderedDict()
            app["metadata"]["domain"] = str(row[0])
            app["metadata"]["market"] = str(row[2])
            app["metadata"]["name"] = str(row[3])
            if row[20] is None:
                status = True
            else:
                status = TeapFileProcessor.bool_convertor(str(row[20]))
            app["metadata"]["enable_alerts"] = status
            tier = str(row[6])
            platform = str(row[7])
            if tier == "Tier 0":
                app["metadata"]["tier"] = "zero"
            elif tier == "Tier 1":
                app["metadata"]["tier"] = "one"
            elif tier in ["Tier 2", "Tier-2", "Tier2"]:
                app["metadata"]["tier"] = "two"
            elif "+" in tier:
                app["metadata"]["tier"] = "two+"
            else:
                app["metadata"]["tier"] = "other"
            if "wcnp" in platform.strip().rstrip().lower():
                app["wcnp"] = collections.OrderedDict()
                app["wcnp"]["namespaceOrAssemblyOrDB"] = str(row[8])
                app["wcnp"]["env"] = str(row[9])
                app["wcnp"]["app"] = str(row[10])
            else:
                app[row[7]] = collections.OrderedDict()
                app[row[7]]["namespaceOrAssemblyOrDB"] = str(row[8])
                app[row[7]]["env"] = str(row[9])
                app[row[7]]["platform"] = str(row[10])
            app["app_inventory"] = self.get_app_details(str(_app))
            apps.append(app)
        return apps

    @staticmethod
    def bool_convertor(val):
        if type(val) != bool:
            val = val.lower()
            if val in ('y', 'yes', 't', 'true', 'on', '1'):
                return True
            elif val in ('n', 'no', 'f', 'false', 'off', '0'):
                return False
            return False
        else:
            return val

    def get_app_details(self, app_name):
        apps = list()
        df = self.inventory_data_frame[(self.inventory_data_frame["name"] == app_name)]
        df1 = df.where(pd.notnull(df), None)
        for row in df1.to_numpy():
            app = collections.OrderedDict()
            app["dl"] = str(row[3])
            app["jira"] = str(row[4])
            app["xmatters"] = str(row[5])
            app["domain"] = str(row[9])
            app["market"] = str(row[10])
            app["tier"] = str(row[14])
            app["dependency"] = collections.OrderedDict()
            app["dependency"]["primary"] = str(row[25])
            app["dependency"]["secondary"] = str(row[26])
            app["dependency"]["platform"] = str(row[27])
            app["dependency"]["thirdParty"] = str(row[28])
            app["dependency"]["thirdParty"] = str(row[29])
            app["dependency"]["dcDistributionConfig"] = str(row[30])
            apps.append(app)
        return apps

    def search_name_field(self, search):
        apps = list()
        for app in self.apps:
            if str(search).lower() in str(app["metadata"]["name"]).lower():
                apps.append(app)
        return apps

    def get_one_matching_leaf_node_v1(self, query):
        """
        For a given search query, gets at least one matching leaf node details , either wcnp or oneOps.
        """
        _apps = list()
        apps = list()
        for app in self.apps:
            for key, value in app.items():
                if key not in ("metadata", "app_inventory"):
                    if str(query).lower().rstrip().strip() in str(
                            app[key]["namespaceOrAssemblyOrDB"]).lower().rstrip().strip():
                        apps.append(app)
        for app in apps:
            for key, value in app.items():
                if key not in ("metadata", "app_inventory"):
                    data = app[key]["namespaceOrAssemblyOrDB"]
                    _apps.append({data: self.get_app_name(app.get("metadata").get("name"))})

        return _apps

    def get_one_matching_leaf_node(self, name):
        """
        For a given search query, gets at least one matching leaf node details , either wcnp or oneOps.
        """
        # app_data.get("metadata").get("name") or resource_name
        apps = self.get_app_name(name)
        return apps

    def get_app_name(self, app_name):
        apps = list()
        for app in self.apps:
            if str(app_name).strip().rstrip() == str(app["metadata"]["name"]).strip().rstrip():
                apps.append(app)
        return apps

    def rename_columns(self):
        self.teap_data_frame = self.teap_data_frame.iloc[:, 0:21]
        self.teap_data_frame.columns = ['domain', 'banner', 'market', 'name',
                                        'inScope', 'justification', 'tier', 'wcnpOrOneops',
                                        'namespace', 'env', 'appId', 'cassandra', 'dcs',
                                        'gslb', 'custom_gslb', 'signoff', 'comment',
                                        'aa', 'activeDetails', 'dcSatate', 'is_alerts_required']
        self.inventory_data_frame = self.inventory_data_frame.iloc[:, 0:38]
        self.inventory_data_frame.columns = ["signoff", "signOffBy", "pendingSignOff", "dl", "jira", "xmatters",
                                             "emails", "apm", "name", "domain", "market", "function", "desc", "poc",
                                             "tier", "inScope", "outOfScopeReason", "drPlan", "drPlanStatus",
                                             "outOfScopeReason2", "BCICCoordinator", "BCICManager",
                                             "BCICDirector", "pendingApproval",
                                             "BCICManager", "primaryDependencies", "secondaryDependencies",
                                             "platformDependency", "thirdPartyDependency", "dbConfig",
                                             "dcDistributionConfig", "wcnpOrOneops", "comments", "isBCICPlanExits",
                                             "drLink", "isArchitectureExits", "comments", "vlookup"]


def read_teap_data(file_data):
    df = pd.read_excel(file_data, sheet_name='CA MX TAEP')
    # print(type(df.ravel()))
    print(list(df.columns))
    # columns =['Unnamed: 0','DR Tier ','Tenant', 'Assembly/Namespace/DB','Platform']
    # new_df = df[columns]
    new1 = df[(df["Unnamed: 0"] == "Digital Experience") & (df["DR Tier "] == "Tier 0") & (df["Tenant"] == "wcnp")]
    # for row in new1.to_numpy():
    #     print(row)
    n = new1.to_numpy()
    for row in n.tolist():
        print(row)
    # print(type(n.array))
    # for rown in new1.values:
    #
    # pd.set_option('display.max_rows', None)
    # pd.set_option('display.max_columns', None)
    # #for entry in new1:
    # print(new1.to_numpy())


if __name__ == "__main__":
    t = TeapFileProcessor()
    # data = t.get_apps_by_platform_and_tier_and_market(platform="wcnp", tier="zero", market="MX")
    # print(list(data))
    _ten = t.get_all_tenants()
    print(_ten)
    daa = t.get_apps_by_platform_and_tier(platform="cosmos", tier="zero")
    res = t.search_name_field("item")
    print(res)
    app_xmatters = {}

    # read_teap_data("/Users/<USER>/work/team.xlsx")
