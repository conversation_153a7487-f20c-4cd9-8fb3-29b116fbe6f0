from common.teap_handler.teap_file_processor import TeapFileProcessor, SUPPORTED_MARKETS, SUPPORTED_TIERS
from libs.oneOps import OneOps
import logging

# teap = TeapFileProcessor()
# oneops_orgs = OneOps.get_all_orgs()

logger = logging.getLogger(__name__)


def singleton(class_):
    instances = {}

    def get_instance(*args, **kwargs):
        if class_ not in instances:
            instances[class_] = class_(*args, **kwargs)
        return instances[class_]

    class_.get_instance = get_instance  # Attach the get_instance method to the class
    return class_


@singleton
class TeapDataHandler:
    def __init__(self):
        logger.info("Called TeapDataHandler")
        self.teap_data = TeapFileProcessor()
        oneops_orgs = OneOps.get_all_orgs()
        if oneops_orgs:
            self.oneops_orgs = oneops_orgs
        else:
            self.oneops_orgs = None
        self.ignored_tenants = ["Azuresql", "ms-df-solrcloud", "ms-df-es", "ms-df-cache",
                                "ms-df-cloudrdbms", "ms-df-cassandra", "On Prem", "azuresql", "cassandra", "kafka",
                                "Kafka", "wmt-gcp-mysql-gsflmp-db", "mssql", "Oracle", "oracle", "cosmosdb"]
        self.load_oneops_details()

    def load_oneops_details(self):
        if self.oneops_orgs:
            return
        oneops = OneOps()
        filtered_tenants = list()
        working_oneops_orgs = list()
        tenants = self.teap_data.get_all_tenants()
        for tenant in tenants:
            if tenant not in self.ignored_tenants:
                filtered_tenants.append(tenant)
        from concurrent.futures import ThreadPoolExecutor
        pool = ThreadPoolExecutor(10)
        futures = list()
        for tenant in filtered_tenants:
            futures.append(pool.submit(oneops.check_org_exits, tenant))
        for future in futures:
            try:
                status, tenant = future.result()
                if status:
                    working_oneops_orgs.append(tenant)
            except Exception as e:
                print(e)
        self.oneops_orgs = working_oneops_orgs

    def validate_oneops_org(self, tenant):
        pass

    def get_leaf_node(self, name):
        """
        Generally used to get leaf node  for managed services like cosmos,cassandra,meghacache,azureSQL ..
        Logic: For given name (metadata name not any namespace or ..), it gets matching apps from teap list and
        for each app in the list looks for wcnp or oneops platform app.

        The reason is , every managed service should be linked to atleast one leaf node which would be either wcnp or
        oneops
        """
        leaf_nodes = self.teap_data.get_one_matching_leaf_node(name)
        found_leaf_node = False
        _node_data = None
        for node_data in leaf_nodes:
            if found_leaf_node:
                break
            for k, v in node_data.items():
                if k.strip().rstrip() in ("wcnp", "WCNP"):
                    found_leaf_node = True
                    v.update({"is_wcnp_env": True})
                    _node_data = node_data
                    break
                elif k.strip().rstrip() in self.oneops_orgs:
                    found_leaf_node = True
                    node_data["oneops"] = v
                    node_data["oneops"].update({"is_wcnp_env": False})
                    node_data["oneops"].update({"org": k})
                    _node_data = node_data
                    break
                continue
        return _node_data


if __name__ == "__main__":
    # teap = TeapDataHandler()
    teap.load_oneops_details()
