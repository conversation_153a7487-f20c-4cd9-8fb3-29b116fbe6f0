__author__ = '<PERSON><PERSON><PERSON> Bharadwaj'

import json
import pyodbc
import pymssql
import sqlalchemy as sal
from sqlalchemy import create_engine, exc
from sqlalchemy.engine import URL
from datetime import datetime
import pytz
#
# dev_server = 'denali-db-nonprod-62df2e13.database.windows.net'
# prod_server = 'denali-db-prod-6446d44b-failover-group.database.windows.net'
# database = 'denali-db'
# username = '<EMAIL>'
# password = 'nF649fcjWYp5z7hCy'
# driver = 'ODBC Driver 17 for SQL Server'
#
# db_schema = 'juno'
#
# # conn_url = URL.create(
# #     "mssql+pyodbc",
# #     username=username,
# #     password=password,
# #     host=prod_server,
# #     port=1433,
# #     database=database,
# #     query={
# #         "driver": driver,
# #         "TrustServerCertificate": "yes",
# #         "authentication": "ActiveDirectoryPassword",
# #     },
# # )
# #
# # engine = create_engine(conn_url, pool_recycle=3600, echo=False, pool_pre_ping=True, pool_size=30, max_overflow=20)
# # engine.connect()
#
#
# def add_ad_group_to_db(api_key, ad_group, status="active"):
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     insert_query = ('INSERT INTO juno.api_access_group (id, api_key, ad_group, status) '
#                     'VALUES(newid(), ?, ?, ?);')
#     try:
#
#         params = (api_key, ad_group, status)
#         cursor.execute(insert_query, params)
#         con.commit()
#     except exc.DBAPIError as e:
#         if con:
#             con.rollback()
#             con.close()
#         return e
#     cursor.close()
#     con.close()
#     return 'True'
#
#
# def get_teams():
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     query = 'SELECT team_id, team_name, team_dl, slack_channel FROM denali_workload.team_details;'
#     try:
#         cursor.execute(query)
#         # row_headers = [x[0] for x in cursor.description]
#         row_headers = ['id', 'name', 'DL', 'slack']
#         res = cursor.fetchall()
#         json_data = []
#         for result in res:
#             row = [str(result[0]), result[1], result[2], result[3]]
#             json_data.append(dict(zip(row_headers, row)))
#         cursor.close()
#         con.close()
#         return json_data
#     except exc.DBAPIError as e:
#         cursor.close()
#         con.close()
#         return e
#
#
# def add_page_to_db(page_details):
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     insert_query = ('INSERT INTO denali_workload.page_details (page_name, test_type, page_team_id) '
#                     'VALUES(?, ?, ?);')
#     select_query = 'SELECT 1 from denali_workload.page_details where page_name = ? and test_type = ?;'
#     try:
#         cursor.execute(select_query, (page_details['page_name'], page_details['test_type']))
#         var = cursor.fetchall()
#         if not var:
#             params = (page_details['page_name'], page_details['test_type'], page_details['team_id'])
#             cursor.execute(insert_query, params)
#             con.commit()
#     except exc.DBAPIError as e:
#         if con:
#             con.rollback()
#             cursor.close()
#             con.close()
#         return e
#     cursor.close()
#     con.close()
#     return 'True'
#
#
# def get_page_details():
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     query = 'SELECT pd.page_id, pd.page_name, td.team_name, pd.test_type ' \
#             'FROM denali_workload.page_details pd join denali_workload.team_details td ' \
#             'ON pd.page_team_id =td.team_id;'
#     try:
#         cursor.execute(query)
#         row_headers = [x[0] for x in cursor.description]
#         res = cursor.fetchall()
#         json_data = []
#         for result in res:
#             row = [result[0], result[1], result[2], str(result[3])]
#             json_data.append(dict(zip(row_headers, row)))
#         cursor.close()
#         con.close()
#         return json_data
#     except exc.DBAPIError as e:
#         cursor.close()
#         con.close()
#         return e
#
#
# def add_api_to_db(api_details):
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     insert_query = ('INSERT INTO denali_workload.api_details (api_page_id, api_name, target_tps, notes, test_type, '
#                     'original_date, last_modified, revision_number, user_id, full_name) '
#                     'VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?);')
#
#     select_query = 'SELECT 1 from denali_workload.api_details where api_name = ? and test_type = ? and api_page_id = ?;'
#     try:
#         user_id = api_details['user_id']
#         full_name = api_details['full_name']
#
#         # time is in UTC
#         original_date = datetime.now(tz=pytz.utc).strftime('%Y-%m-%d %H:%M:%S')
#         last_modified_date = original_date
#         revision_number = 1
#         target_tps = float(api_details['tps'])
#
#         cursor.execute(select_query, (api_details['name'], api_details['test_type'], api_details['pageId']))
#         var = cursor.fetchall()
#         if var:
#             return 'Duplicate entry'
#         if not var:
#             params = (api_details['pageId'], api_details['name'], target_tps, api_details['note'],
#                       api_details['test_type'], original_date, last_modified_date, revision_number, user_id, full_name)
#             cursor.execute(insert_query, params)
#             con.commit()
#
#         '''
#         for api in api_details['apis']:
#             cursor.execute(select_query, (api['api_name'], api_details['test_type']))
#             var = cursor.fetchall()
#             if var:
#                 continue
#             else:
#                 params = (api['page_id'], api['api_name'], api['target_tps'], api['notes'], api_details['test_type'],
#                           original_date, last_modified_date, revision_number, user_id, full_name)
#                 cursor.execute(insert_query, params)
#                 con.commit()
#         '''
#     except exc.DBAPIError as e:
#         if con:
#             con.rollback()
#             cursor.close()
#             con.close()
#         return e
#     cursor.close()
#     con.close()
#     return 'True'
#
#
# def delete_api_to_db(api_id):
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     delete_query = 'DELETE FROM denali_workload.api_details WHERE api_id = ?;'
#     try:
#         cursor.execute(delete_query, api_id)
#         con.commit()
#     except exc.DBAPIError as e:
#         if con:
#             con.rollback()
#             cursor.close()
#             con.close()
#         return e
#     cursor.close()
#     con.close()
#     return 'True'
#
#
# def get_all_api_details():
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     team_query = 'SELECT team_id, team_name FROM denali_workload.team_details;'
#
#     page_query = 'SELECT pd.page_id, pd.page_name, td.team_name, td.team_id, pd.test_type ' \
#                  'FROM denali_workload.team_details td JOIN denali_workload.page_details pd ' \
#                  'ON td.team_id = pd.page_team_id;'
#
#     '''
#     api_query = 'select ad.api_id, pd.page_name, ad.api_name, ad.target_tps, ad.notes, ad.test_type, ad.last_modified,'\
#                 'ad.user_id, ad.full_name, ad.approval_date ' \
#                 'FROM denali_workload.team_details td JOIN denali_workload.page_details pd ' \
#                 'ON td.team_id = pd.page_team_id JOIN denali_workload.api_details ad ' \
#                 'ON pd.page_id = ad.api_page_id;'
#     '''
#     api_query = 'select td.team_id, ad.api_page_id, ad.api_id, td.team_name, pd.page_name, ad.api_name, ' \
#                 'ad.target_tps, ad.notes, ad.test_type, ad.last_modified, ad.user_id, ad.full_name, ad.approval_date ' \
#                 'FROM denali_workload.team_details td JOIN denali_workload.page_details pd ' \
#                 'ON td.team_id = pd.page_team_id JOIN denali_workload.api_details ad ON pd.page_id = ad.api_page_id;'
#     details_dict = {}
#
#     try:
#         '''
#         cursor.execute(team_query)
#         row_headers = [x[0] for x in cursor.description]
#         res = cursor.fetchall()
#         json_data = []
#         for result in res:
#             row = [result[0], result[1]]
#             json_data.append(dict(zip(row_headers, row)))
#         details_dict["team_details"] = json_data
#         cursor.execute(page_query)
#         row_headers = [x[0] for x in cursor.description]
#         res = cursor.fetchall()
#         json_data = []
#         for result in res:
#             row = [result[0], result[1], result[2], result[3], result[4]]
#             json_data.append(dict(zip(row_headers, row)))
#         details_dict["page_details"] = json_data
#         '''
#         cursor.execute(api_query)
#
#         row_headers = [x[0] for x in cursor.description]
#         res = cursor.fetchall()
#         json_data = []
#         for result in res:
#             row = [result[0], result[1], result[2], result[3], result[4], result[5], result[6], result[7],
#                    result[8], result[9], result[10], result[11], result[12]]
#             json_data.append(dict(zip(row_headers, row)))
#
#         details_dict["details"] = json_data
#         cursor.close()
#         con.close()
#         return details_dict
#     except exc.DBAPIError as e:
#         if con:
#             cursor.close()
#             con.close()
#         return e
#
#
# def get_all_details(test_type):
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     page_query = 'SELECT pd.page_id as "pageId", pd.page_name as "pageName", ' \
#                  'td.team_name as "teamName", td.team_id as "teamId" ' \
#                  'FROM denali_workload.page_details pd JOIN denali_workload.team_details td ' \
#                  'ON pd.page_team_id = td.team_id ' \
#                  'WHERE pd.test_type = ?; '
#
#     api_query = 'SELECT ad.api_id as "id", ad.api_name as "name", ad.notes as "note", ' \
#                 'ad.target_tps as "tps", ad.full_name as "user", ad.last_modified as "date" ' \
#                 'FROM denali_workload.api_details ad WHERE ad.api_page_id = ?;'
#
#     # All details list is to store it as a list
#     all_details = []
#
#     try:
#         cursor.execute(page_query, test_type)
#
#         row_headers = [x[0] for x in cursor.description]
#         res = cursor.fetchall()
#         team_details = []
#         for result in res:
#             row = [result[0], result[1], result[2], result[3]]
#             team_details.append(dict(zip(row_headers, row)))
#
#             # json_data.append(row)
#
#         pages_with_apis = []
#         for team in team_details:
#             page_id = team['pageId']
#             cursor.execute(api_query, page_id)
#             row_headers = ['id', 'name', 'note', 'tps']
#             res = cursor.fetchall()
#
#             api_details = []
#             for result in res:
#                 row = [result[0], result[1], result[2], result[3]]
#                 api_details.append(dict(zip(row_headers, row)))
#                 last_modified = {"user": str(result[4]), "date": str(result[5])}
#                 curr_index = len(api_details) - 1
#                 api_details[curr_index]["lastModified"] = last_modified
#             team["apis"] = api_details
#             # team.pop('page_id', None)
#             pages_with_apis.append(team)
#             '''
#             if res:
#                 api_details = []
#                 for result in res:
#                     row = [result[0], result[1], result[2], result[3]]
#                     api_details.append(dict(zip(row_headers, row)))
#                     last_modified = {"user": str(result[4]), "date": str(result[5])}
#                     curr_index = len(api_details) - 1
#                     api_details[curr_index]["lastModified"] = last_modified
#                 team["apis"] = api_details
#                 # team.pop('page_id', None)
#                 pages_with_apis.append(team)
#             else:
#                 continue
#             '''
#         cursor.close()
#         con.close()
#         return pages_with_apis
#     except exc.DBAPIError as e:
#         if con:
#             cursor.close()
#             con.close()
#         return e
#
#
# def edit_team(team_details):
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     # time is in UTC
#     current_time = datetime.now(tz=pytz.utc).strftime('%Y-%m-%d %H:%M:%S')
#
#     query = 'UPDATE denali_workload.team_details ' \
#             'SET team_name = ?, team_dl = ?, slack_channel = ? ' \
#             'WHERE team_id = ?;'
#     try:
#         for teams in team_details:
#             params = (teams['name'], teams['DL'], teams['slack'], teams['id'])
#             cursor.execute(query, params)
#             con.commit()
#     except exc.DBAPIError as e:
#         if con:
#             con.rollback()
#             cursor.close()
#             con.close()
#         return e
#     cursor.close()
#     con.close()
#     return 'True'
#
#
# def edit_api(api_details):
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     # time is in UTC
#     current_time = datetime.now(tz=pytz.utc).strftime('%Y-%m-%d %H:%M:%S')
#
#     get_current_api_query = 'SELECT pd.page_name, ad.api_name, ad.target_tps, ad.notes, ad.test_type, ' \
#                             'ad.last_modified, ad.revision_number, ad.user_id, ad.full_name, ad.approval_date ' \
#                             'FROM denali_workload.api_details ad JOIN denali_workload.page_details pd ' \
#                             'ON ad.api_page_id = pd.page_id ' \
#                             'WHERE ad.api_id = ?;'
#
#     insert_current_api_query = 'INSERT INTO denali_workload.api_details_history ' \
#                                '(page_name, api_name, target_tps, notes, test_type, last_modified, ' \
#                                'revision_number, ' \
#                                'user_id, full_name, approval_date) ' \
#                                'VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?);'
#
#     update_api_query = 'UPDATE denali_workload.api_details ' \
#                        'SET target_tps = ?, notes = ?, last_modified = ?, revision_number = revision_number + 1, ' \
#                        'user_id = ?, full_name = ? ' \
#                        'WHERE api_id = ?;'
#
#     try:
#         api = api_details['data']
#         cursor.execute(get_current_api_query, api['id'])
#         res = cursor.fetchone()
#         params = (res[0], res[1], res[2], res[3], res[4], res[5], res[6], res[7], res[8], res[9])
#         cursor.execute(insert_current_api_query, params)
#         con.commit()
#
#         params = (api['tps'], api['note'], current_time, api_details['user_id'], api_details['full_name'],
#                   api['id'])
#         cursor.execute(update_api_query, params)
#         con.commit()
#     except exc.DBAPIError as e:
#         if con:
#             con.rollback()
#             cursor.close()
#             con.close()
#         return e
#     cursor.close()
#     con.close()
#     return 'True'
#
#
# def delete_team(team_id):
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     query = 'DELETE FROM denali_workload.team_details ' \
#             'WHERE team_id = ?;'
#     try:
#         cursor.execute(query, team_id)
#         con.commit()
#     except exc.DBAPIError as e:
#         if con:
#             con.rollback()
#             cursor.close()
#             con.close()
#         return e
#     cursor.close()
#     con.close()
#     return 'True'
#
#
# def get_details_by_team_id(data):
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     team_id = data['team_id']
#     test_type = data['test_type']
#
#     page_query = 'SELECT pd.page_id as "pageId", pd.page_name as "pageName", ' \
#                  'td.team_name as "teamName", td.team_id as "teamId" ' \
#                  'FROM denali_workload.page_details pd JOIN denali_workload.team_details td ' \
#                  'ON pd.page_team_id = td.team_id ' \
#                  'WHERE td.team_id = ? and pd.test_type = ?;'
#
#     api_query = 'SELECT ad.api_id as "id", ad.api_name as "name", ad.notes as "note", ' \
#                 'ad.target_tps as "tps" ' \
#                 'FROM denali_workload.api_details ad WHERE ad.api_page_id = ?;'
#
#     all_details = []
#
#     try:
#         cursor.execute(page_query, (team_id, test_type))
#
#         row_headers = [x[0] for x in cursor.description]
#         res = cursor.fetchall()
#         team_details = []
#         for result in res:
#             row = [result[0], result[1], result[2], result[3]]
#             team_details.append(dict(zip(row_headers, row)))
#
#             # json_data.append(row)
#
#         pages_with_apis = []
#         for team in team_details:
#             page_id = team['pageId']
#             cursor.execute(api_query, page_id)
#             row_headers = ['id', 'name', 'note', 'tps']
#             res = cursor.fetchall()
#             if res:
#                 api_details = []
#                 for result in res:
#                     row = [result[0], result[1], result[2], result[3]]
#                     api_details.append(dict(zip(row_headers, row)))
#                     # last_modified = {"user": str(result[4]), "date": str(result[5])}
#                     # curr_index = len(api_details) - 1
#                     # api_details[curr_index]["lastModified"] = last_modified
#                 team["apis"] = api_details
#                 # team.pop('page_id', None)
#                 pages_with_apis.append(team)
#             else:
#                 continue
#         cursor.close()
#         con.close()
#         return pages_with_apis
#     except exc.DBAPIError as e:
#         if con:
#             cursor.close()
#             con.close()
#         return e
#
#
# def validate_by_teamid(data):
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     test_type = data[0]['test_type']
#     team_id = data[0]['team_id']
#     is_approved_without_change = data[0]['approved_without_change']
#
#     # time is in UTC
#     approval_date = datetime.now(tz=pytz.utc).strftime('%Y-%m-%d %H:%M:%S')
#
#     try:
#         if is_approved_without_change == 'True':
#             update_query = 'UPDATE denali_workload.api_details ' \
#                            'SET approval_date = ?, user_id = ?, full_name = ? ' \
#                            'WHERE api_id = ?; '
#
#             api_id_query = 'select ad.api_id ' \
#                            'FROM denali_workload.team_details td ' \
#                            'JOIN denali_workload.page_details pd ON td.team_id = pd.page_team_id ' \
#                            'JOIN denali_workload.api_details ad ON pd.page_id = ad.api_page_id ' \
#                            'WHERE td.team_id = ? and ad.test_type = ?; '
#
#             team_query = 'SELECT team_name FROM denali_workload.team_details ' \
#                          'WHERE team_id = ?; '
#
#             cursor.execute(api_id_query, (team_id, test_type))
#             res = cursor.fetchall()
#             for result in res:
#                 cursor.execute(update_query, (approval_date, data[0]['user_id'], data[0]['full_name'], result[0]))
#             con.commit()
#             cursor.execute(team_query, team_id)
#             res = cursor.fetchone()
#             # print(res[0])
#             slack_data = {
#                 "username": "OmniPerf",
#                 "icon_emoji": ":rocket:",
#                 "channel": '#workload_automations_alerts',
#                 "attachments": [
#                     {
#                         "color": "#f2c744",
#                         "blocks": [
#                             {
#                                 "type": "header",
#                                 "text": {
#                                     "type": "plain_text",
#                                     "text": "Targets approved for {} test".format(data[0]['test_type'])
#                                 }
#                             },
#                             {
#                                 "type": "divider"
#                             },
#                             {
#                                 "type": "section",
#                                 "text": {
#                                     "type": "mrkdwn",
#                                     "text": "{} from {} approved the targets without any changes.".format(
#                                         data[0]['full_name'],
#                                         res[0])
#                                 }
#                             }
#                         ]
#                     }
#                 ]
#             }
#             send_slack_notifications(slack_data)
#             con.close()
#             return "Successfully validated"
#         elif is_approved_without_change == 'False':
#             update_page_query = 'UPDATE denali_workload.page_details ' \
#                                 'SET overall_tps = ?' \
#                                 'WHERE page_id = ?;'
#
#             get_current_api_query = 'SELECT pd.page_name, ad.api_name, ad.target_tps, ad.notes, ad.test_type, ' \
#                                     'ad.last_modified, ad.revision_number, ad.user_id, ad.full_name, ad.approval_date ' \
#                                     'FROM denali_workload.api_details ad JOIN denali_workload.page_details pd ' \
#                                     'ON ad.api_page_id = pd.page_id ' \
#                                     'WHERE ad.api_id = ?;'
#
#             get_page_name_query = 'SELECT pd.page_name, ad.api_name ' \
#                                   'FROM denali_workload.api_details ad JOIN denali_workload.page_details pd ' \
#                                   'ON ad.api_page_id = pd.page_id ' \
#                                   'WHERE ad.api_id = ?;'
#
#             insert_current_api_query = 'INSERT INTO denali_workload.api_details_history ' \
#                                        '(page_name, api_name, target_tps, notes, test_type, last_modified, ' \
#                                        'revision_number, ' \
#                                        'user_id, full_name, approval_date) ' \
#                                        'VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?);'
#
#             update_api_query = 'UPDATE denali_workload.api_details ' \
#                                'SET target_tps = ?, notes = ?, last_modified = ?, ' \
#                                'revision_number = revision_number + 1, ' \
#                                'user_id = ?, full_name = ?, approval_date = ? ' \
#                                'WHERE api_id = ?;'
#
#             sanity_check_page_query = 'SELECT 1 from denali_workload.page_details where page_id = ? ' \
#                                       'and overall_tps = ?;'
#
#             sanity_check_api_query = 'SELECT 1 from denali_workload.api_details where api_id = ? ' \
#                                      'and target_tps = ?;'
#
#             update_api_only_approval_query = 'UPDATE denali_workload.api_details ' \
#                                              'SET approval_date = ?, user_id = ?, full_name = ? ' \
#                                              'WHERE api_id = ?; '
#
#             '''
#             # for page in data['data']:
#             for page in data['page_details']:
#                 cursor.execute(sanity_check_page_query, (page['page_id'], page['overall_tps']))
#                 var = cursor.fetchall()
#                 if var:
#                     continue
#                 else:
#                     cursor.execute(update_page_query, (page['overall_tps'], page['page_id']))
#                     con.commit()
#             '''
#             page_name = ''
#             # for api in data[0]['data']:
#             for team in data[0]['data']:
#                 for api in team['apis']:
#                     cursor.execute(get_current_api_query, api['id'])
#                     res = cursor.fetchone()
#                     params = (res[0], res[1], res[2], res[3], res[4], res[5], res[6], res[7], res[8], res[9])
#                     cursor.execute(insert_current_api_query, params)
#
#                     # Used in Slack notifications
#                     page_name = res[0]
#                     con.commit()
#
#                     params = (api['tps'], api['note'], approval_date, data[0]['user_id'], data[0]['full_name'],
#                               approval_date, api['id'])
#                     cursor.execute(update_api_query, params)
#                     con.commit()
#                     '''
#                     cursor.execute(sanity_check_api_query, (api['id'], api['tps']))
#                     var = cursor.fetchall()
#
#                     if var:
#                         cursor.execute(get_page_name_query, api['id'])
#                         res = cursor.fetchone()
#                         page_name = res[0]
#                         cursor.execute(update_api_only_approval_query,
#                                        (approval_date, data[0]['user_id'], data[0]['full_name'], api['id']))
#                     else:
#                         cursor.execute(get_current_api_query, api['id'])
#                         res = cursor.fetchone()
#                         params = (res[0], res[1], res[2], res[3], res[4], res[5], res[6], res[7], res[8], res[9])
#                         cursor.execute(insert_current_api_query, params)
#
#                         # Used in Slack notifications
#                         page_name = res[0]
#                         con.commit()
#
#                         params = (api['tps'], api['note'], approval_date, data[0]['user_id'], data[0]['full_name'],
#                                   approval_date, api['id'])
#                         cursor.execute(update_api_query, params)
#                         con.commit()
#                     '''
#
#             slack_data = {
#                 "username": "OmniPerf",
#                 "icon_emoji": ":rocket:",
#                 "channel": '#workload_automations_alerts',
#                 "attachments": [
#                     {
#                         "color": "#f2c744",
#                         "blocks": [
#                             {
#                                 "type": "header",
#                                 "text": {
#                                     "type": "plain_text",
#                                     "text": "Targets approved for {} test".format(data[0]['test_type'])
#                                 }
#                             },
#                             {
#                                 "type": "divider"
#                             },
#                             {
#                                 "type": "section",
#                                 "text": {
#                                     "type": "mrkdwn",
#                                     "text": "{} from {} approved the targets with changes."
#                                     .format(data[0]['full_name'], page_name)
#                                 }
#                             }
#                         ]
#                     }
#                 ]
#             }
#             send_slack_notifications(slack_data)
#             cursor.close()
#             con.close()
#             return "Successfully validated and updated"
#     except exc.DBAPIError as e:
#         if con:
#             con.rollback()
#             cursor.close()
#             con.close()
#         return e
#
#
# def get_team_data(data):
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     query = 'SELECT DISTINCT td.team_name, td.team_dl, td.team_id ' \
#             'FROM denali_workload.team_details td ' \
#             'JOIN denali_workload.page_details pd ON td.team_id = pd.page_team_id ' \
#             'RIGHT JOIN denali_workload.api_details ad ON pd.page_id = ad.api_page_id ' \
#             'WHERE ad.test_type = ?;'
#     test_type = data['testType']
#
#     try:
#         cursor.execute(query, test_type)
#         row_headers = ['team_name', 'dl', 'team_id']
#         res = cursor.fetchall()
#         json_data = []
#         for result in res:
#             row = [str(result[0]), result[1], result[2]]
#             json_data.append(dict(zip(row_headers, row)))
#         cursor.close()
#         con.close()
#         return json_data
#     except exc.DBAPIError as e:
#         if con:
#             cursor.close()
#             con.close()
#         return e
#
#
# def get_individual_team_data(test_type, page_id):
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     query = 'SELECT DISTINCT td.team_name, td.team_dl, td.team_id ' \
#             'FROM denali_workload.team_details td ' \
#             'JOIN denali_workload.page_details pd ON td.team_id = pd.page_team_id ' \
#             'RIGHT JOIN denali_workload.api_details ad ON pd.page_id = ad.api_page_id ' \
#             'WHERE ad.test_type = ? and pd.page_id = ?;'
#
#     try:
#         cursor.execute(query, (test_type, page_id))
#
#         row_headers = ['team_name', 'dl', 'team_id']
#         res = cursor.fetchall()
#         row = [str(res[0][0]), res[0][1], res[0][2]]
#         json_data = dict(zip(row_headers, row))
#         cursor.close()
#         con.close()
#         return json_data
#     except exc.DBAPIError as e:
#         if con:
#             cursor.close()
#             con.close()
#         return e
#
#
# def get_test_types():
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     query = 'SELECT DISTINCT pd.test_type ' \
#             'FROM denali_workload.page_details pd;'
#
#     try:
#         cursor.execute(query)
#         row_headers = ['test_type']
#         res = cursor.fetchall()
#         json_data = []
#         for result in res:
#             # print(str(result[0]))
#             row = str(result[0])
#             json_data.append(row)
#         cursor.close()
#         con.close()
#         return json_data
#     except exc.DBAPIError as e:
#         if con:
#             cursor.close()
#             con.close()
#         return e
#
#
# def update_team_page(data):
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     update_query = 'UPDATE denali_workload.page_details ' \
#                    'SET page_team_id = ? ' \
#                    'where page_id = ? ;'
#
#     select_query = 'select 1 ' \
#                    'from denali_workload.page_details pd ' \
#                    'where pd.page_id = ? and pd.page_team_id = ? and pd.test_type = ? ;'
#
#     old_team_id = data['old_team_id']
#     new_team_id = data['new_team_id']
#     test_type = data['test_type']
#     page_id = data['page_id']
#
#     try:
#         cursor.execute(select_query, (page_id, old_team_id, test_type))
#         var = cursor.fetchall()
#         if var:
#             params = (new_team_id, page_id)
#             cursor.execute(update_query, params)
#             con.commit()
#             cursor.close()
#             con.close()
#             return "Updated successfully"
#         else:
#             if con:
#                 cursor.close()
#                 con.close()
#             return "Incorrect details provided"
#     except exc.DBAPIError as e:
#         if con:
#             cursor.close()
#             con.close()
#         return e
#
#
# def has_fraction(value):
#     return value != int(value)
#
#
# def get_email_body(teams, test_type):
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     query = 'SELECT DISTINCT pd.page_id, pd.page_name ' \
#             'FROM denali_workload.team_details td ' \
#             'JOIN denali_workload.page_details pd ON td.team_id = pd.page_team_id ' \
#             'JOIN denali_workload.api_details ad ON pd.page_id = ad.api_page_id ' \
#             'WHERE td.team_id = ? and pd.test_type = ? ;'
#
#     # TODO: Setup an environment variable for validate_base_link
#     validate_base_link = 'https://omniperf.prod.walmart.com/#/workload-automation?team_id='
#     add_test_type_link = '&test_type='
#     table_class_begin = '<tr class="tableRow">'
#     metric_row = '<td style="border: 1px solid #bbb; padding: 8px 16px">'
#     th_end = '</th>'
#     td_end = '</td>'
#     table_class_end = '</tr>'
#     table_div_end = '</table></div>'
#     table_end = '</table>'
#     page_title_begin = '<h4>'
#     page_title_end = '</h4>'
#     separator = '<div style="margin: 0 auto; position: relative; top: -21px; width: calc(100% - 400px); margin-top: ' \
#                 '8px; height: 1px; margin-bottom: -20px; background-image: linear-gradient(to right, transparent, ' \
#                 'rgb(48,49,51), transparent);"></div>'
#
#     try:
#         email_body = template['start_body'] + str(teams['team_name']) + template['team_name'] + \
#                      validate_base_link + str(teams['team_id']) + add_test_type_link + test_type['testType'] + \
#                      template['validate_link']
#         cursor.execute(query, (str(teams['team_id']), test_type['testType']))
#         # row_headers = ['ap', 'tps']
#         row_headers = [x[0] for x in cursor.description]
#         res = cursor.fetchall()
#         json_data = []
#
#         page_count = 0
#         for result in res:
#             # row = [result[0], result[1]]
#             # json_data.append(dict(zip(row_headers, row)))
#             page_count += 1
#             page_name = result[1]
#
#             email_body = email_body + page_title_begin + page_name + page_title_end + template['page_name']
#
#             api_query = 'select ad.api_name, ad.target_tps, ad.notes ' \
#                         'from denali_workload.team_details td ' \
#                         'JOIN denali_workload.page_details pd ON td.team_id = pd.page_team_id ' \
#                         'JOIN denali_workload.api_details ad ON pd.page_id = ad.api_page_id ' \
#                         'WHERE ad.api_page_id = ?;'
#
#             cursor.execute(api_query, result[0])
#
#             api_results = cursor.fetchall()
#             for api_result in api_results:
#                 api_name = api_result[0]
#                 tps = api_result[1]
#                 notes = api_result[2]
#                 email_body = email_body + table_class_begin + metric_row + api_name + td_end
#                 if has_fraction(tps):
#                     email_body = email_body + metric_row + str(tps) + td_end
#                 else:
#                     email_body = email_body + metric_row + str(int(tps)) + td_end
#                 email_body = email_body + metric_row + notes + td_end + table_class_end
#             if page_count != len(res):
#                 email_body = email_body + table_div_end + '<br> </br>' + separator
#             else:
#                 email_body = email_body + table_div_end
#
#         email_body = email_body + template['end_html']
#
#         cursor.close()
#         con.close()
#
#         return email_body
#     except exc.DBAPIError as e:
#         if con:
#             cursor.close()
#             con.close()
#         return e
#
#
# def get_process_tree_data(page_id, page_tps):
#     con = engine.raw_connection()
#     cursor = con.cursor()
#
#     process_data_query = 'SELECT ptd.page_name, ptd.process_data ' \
#                          'FROM denali_workload.process_tree_details ptd ' \
#                          'WHERE ptd.process_page_id = ?;'
#
#     select_query = 'SELECT 1 ' \
#                    'FROM denali_workload.process_tree_details ptd ' \
#                    'WHERE ptd.process_page_id = ?;'
#
#     try:
#         team_details = []
#         cursor.execute(select_query, page_id)
#         var = cursor.fetchall()
#         if var:
#             cursor.execute(process_data_query, page_id)
#             row_headers = [x[0] for x in cursor.description]
#             res = cursor.fetchone()
#
#             row = [res[0], json.loads(res[1])]
#             team_details.append(dict(zip(row_headers, row)))
#             team_details[0]['process_data'][0]['value'] = page_tps
#             cursor.close()
#             con.close()
#             return team_details
#         else:
#             cursor.close()
#             con.close()
#             return 'NoneType'
#     except exc.DBAPIError as e:
#         if con:
#             cursor.close()
#             con.close()
#         return e


prod_server = 'denali-db-prod-6446d44b-failover-group.database.windows.net'
database = 'denali-db'
username = '<EMAIL>'
password = 'nF649fcjWYp5z7hCy'
driver = 'ODBC Driver 17 for SQL Server'

cnxn = pymssql.connect(server='denali-db-prod-6446d44b-failover-group.database.windows.net', port=1433, user='<EMAIL>', password='nF649fcjWYp5z7hCy',
                       database='denali-db', timeout=120, appname="juno")
import logging


logger = logging.getLogger(__name__)

def check_connection():
    global cnxn
    for retry in range(40):
        try:
            cursor = cnxn.cursor()
            cursor.execute('SELECT 1')
            break
        except Exception as e:
            logger.info(f"Unable le to execute query throwing {e}")
            try:
                cnxn = cnxn = pymssql.connect(server=prod_server, port=1433, user=username, password=password,
                       database=database, timeout=120, appname="juno")
            except Exception as e:
                pass


def insert_data_into_executions(api_key, ad_group, status="active"):
    cursor = cnxn.cursor()
    check_connection()

    sql = "INSERT INTO juno.api_access_group ( api_key, ad_group, status) " \
              "VALUES( %s, %s, %s);"
    cursor.execute(sql, (api_key, ad_group, status))

    return get_row_id(cnxn, cursor)



def get_row_id(cnxn, cursor):
    cnxn.commit()
    if cursor.rowcount == 1:
        logger.info("Row is inserted")
        logger.info("Row id is  {}".format(cursor.lastrowid))
        return cursor.lastrowid
    else:
        logger.exception("Failed to insert Row in agent_request")
    return None

if __name__ == '__main__':
    insert_data_into_executions(api_key="delete_account", ad_group="intl-sre", status="active")
