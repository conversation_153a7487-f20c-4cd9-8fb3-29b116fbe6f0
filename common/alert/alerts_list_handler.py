import urllib3
import logging, os, yaml
from libs.shell import bash
from logging import handlers
from git_service import exceptions
from collections import OrderedDict
from common.alert.alert_modifier import AlertModifier

logger = logging.getLogger(__name__)

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

IGNORE_APPS = ["wmt-vault-secrets-manager"]
IGNORE_PATTERN = ["preprod"]
ALERT_OWNER = "intl-ecomm-svcs"
FILTER_ONLY_RULES = "rules/production"
SRE_ALERTS = "sre-alerts"
APP_ALERTS = "app-owner-alerts"


def build_grep_command(filters: list, location=None):
    if not location:
        location = "."
    for _filter in filters:
        index = 0
        args_grep_command = None
        alert_sla_name = _filter.get("alert_sla_name")
        if alert_sla_name:
            args_grep_command = "grep -lr {} {}".format(alert_sla_name, location)
        for k, v in _filter.items():
            if k == "alert_sla_name":
                continue
            if k == "_include_filter":
                continue
            if k == "_exclude_filter":
                continue
            if k.startswith("_"):
                continue
            if not args_grep_command:
                args_grep_command = "grep -lr {} {}".format(v, location)
            else:
                args_grep_command = "{}|grep {}".format(args_grep_command, v)
            index = index + 1
        include_command = ""
        for include_filter in _filter.get("_include_filter", list()):
            include_command = f"{include_command} | grep '{include_filter}'"
        exclude_command = ""
        for exclude_filter in _filter.get("_exclude_filter", list()):
            exclude_command = f"{exclude_command}| grep -v '{exclude_filter}'"
        # _filter.update({"command": f"{args_grep_command}|grep 'rules/production'"})
        if len(include_command) > 4:
            args_grep_command = f"{args_grep_command}{include_command}"
        if len(exclude_command) > 4:
            args_grep_command = f"{args_grep_command}{exclude_command}"

        _filter.update({"command": args_grep_command})

    return filters


def group_by_alerts_with_file_name_matching(alerts):
    new_list = []  # empty list to hold unique elements from the list
    dup_list = []  # empty list to hold the duplicate elements from the list
    for alert in alerts:
        if alert.get("_file_name") not in new_list:
            new_list.append(alert.get("_file_name"))
        else:
            dup_list.append(alert.get("_file_name"))
    return new_list, dup_list


def aggregate_alerts_by_file(alerts):
    unique_files, duplicate_files = group_by_alerts_with_file_name_matching(alerts)
    dup_matching_data_files = dict()
    for dup_file in duplicate_files:
        for alert in alerts:
            if alert.get("_file_name") == dup_file:
                if len(dup_matching_data_files.get(alert.get("_file_name"), list())) == 0:
                    dup_matching_data_files[alert.get("_file_name")] = list()
                    alert.update({"_ignore_processing": False})
                    dup_matching_data_files[alert.get("_file_name")].append(alert)
                else:
                    dup_matching_data_files[alert.get("_file_name")].append(alert)
                    alert.update({"_ignore_processing": True})
    for dup_file in duplicate_files:
        for alert in alerts:
            if alert.get("_file_name") == dup_file:
                # False
                if not alert.get("_ignore_processing"):
                    alert["_alerts"] = merger_alerts(dup_matching_data_files.get(alert.get("_file_name")))
    print(alerts)


def merger_alerts(alerts_list):
    alerts = list()
    for data in alerts_list:
        alerts.extend(data.get("_alerts"))
    return list(set(alerts))


def add_filter_to_exclude_dashboards(filters):
    for _filter in filters:
        if FILTER_ONLY_RULES not in _filter.get("_include_filter", list()):
            _filter["_include_filter"] = list()
            _filter["_include_filter"].append(FILTER_ONLY_RULES)


def add_sre_alerts_filter(filters):
    for _filter in filters:
        if SRE_ALERTS not in _filter.get("_include_filter", list()):
            _filter["_include_filter"] = list()
            _filter["_include_filter"].append(SRE_ALERTS)
    for _filter in filters:
        if APP_ALERTS not in _filter.get("_exclude_filter", list()):
            _filter["_exclude_filter"] = list()
            _filter["_exclude_filter"].append(APP_ALERTS)


def add_app_alerts_filter(filters):
    for _filter in filters:
        if APP_ALERTS not in _filter.get("_include_filter", list()):
            _filter["_include_filter"] = list()
            _filter["_include_filter"].append(APP_ALERTS)
    for _filter in filters:
        if SRE_ALERTS not in _filter.get("_exclude_filter", list()):
            _filter["_exclude_filter"] = list()
            _filter["_exclude_filter"].append(SRE_ALERTS)


def process_filters_and_disable_and_enable_alerts(filters_data, intl_sre_only=True, disable_alerts=True,
                                                  comm_channel_list=None):
    not_empty_alerts = list()
    empty_alerts = list()
    alert_modifier = AlertModifier()
    url = None
    try:
        # clone and search
        if alert_modifier.fork_git.git.clone() is False:
            raise exceptions.GitBashCommandFailedToExecute("Clone for id: {} failed")
        location = alert_modifier.cloned_to_location
        if intl_sre_only:
            location = os.path.join(location, "mms-config/international-tech/intl-sre")

        results = get_all_alert_ids_and_file_name(filters_data, location)
        for alert_match_data in results:
            if len(alert_match_data.get("_alerts")) == 0:
                logger.info(f"Not found matching alerts, for {alert_match_data}, ignoring  ")
                empty_alerts.append(alert_match_data)
            else:
                not_empty_alerts.append(alert_match_data)

        aggregate_alerts_by_file(not_empty_alerts)
        for alert_match_data in not_empty_alerts:
            _name = alert_match_data.get('_file_name')
            _alerts = alert_match_data.get('_alerts')

            if alert_match_data.get("_ignore_processing"):
                msg = f"Ignored Deplicate alert file {_name} for {_alerts}"
                logger.info(msg)
                continue
            msg = f"Processing alert file {_name} for {_alerts}"
            logger.info(f"{msg}")
            if disable_alerts:
                alert_modifier.disable_rules(alert_match_data.get('_file_name'), alert_match_data.get('_alerts'),
                                             comm_channel_list)
            else:
                alert_modifier.enable_rules(alert_match_data.get('_file_name'), alert_match_data.get('_alerts'))

        # when it's done create a pr
        alert_modifier.fork_git.git.checkout(alert_modifier.local_repo_branch)
        for alert_match_data in not_empty_alerts:
            alert_modifier.git.add(alert_match_data.get('_file_name'))

        alert_modifier.fork_git.git.commit("Disabled alert with alerts list")
        alert_modifier.fork_git.git.push()
        fork_repo_with_branch = f"{ALERT_OWNER}:{alert_modifier.local_repo_branch}"
        title = "Disabled alert with alerts list"
        # url = create_pull_request('Telemetry', 'mms-config', fork_repo_with_branch, title, 'main')
        url = alert_modifier.fork_git.git.create_pull_request(fork_repo_with_branch, 'main', title)
    except Exception as e:
        logger.exception(e)
    finally:
        alert_modifier.fork_git.git.clean()
    results = OrderedDict()
    results["pull_request"] = url
    results["matching_alerts_data"] = not_empty_alerts
    results["not_matching_alerts_data"] = empty_alerts
    # return {"matching_alerts_data": not_empty_alerts, "pull_request": url,
    #         "not_matching_alerts_data": empty_alerts}
    return results


def serialize_yaml_file(file_with_loc):
    data = yaml.load(open(file_with_loc), Loader=yaml.FullLoader)
    return data


def get_all_alert_ids_and_file_name(payload, location):
    # location="/Users/<USER>/git/mms-config/international-tech/intl-sre"
    res = build_grep_command(payload, location=location)
    logger.info(f"Grep command details {res}")
    results = list()
    for entry in res:
        try:
            resp = bash(command=entry.get("command"), read_lines=True)
            for dat in resp.stdout:
                res_dict = serialize_yaml_file(dat)
                _alerts = list()
                for group in res_dict.get("groups"):
                    for rule in group.get("rules"):
                        try:
                            matched_filter_criterias = list()
                            count = 0
                            for k, v in entry.items():
                                try:
                                    logger.info(f"Actual val {v}")
                                    logger.info(f"Entry val {rule.get('labels').get(k)}")
                                    if k == "command":
                                        continue
                                    if k == "_include_filter":
                                        continue
                                    if k == "_exclude_filter":
                                        continue
                                    if k.startswith("_"):
                                        continue
                                    count = count + 1
                                    if rule.get("labels").get("tool") != "juno":
                                        break
                                    if rule.get("labels").get(k) == v:
                                        matched_filter_criterias.append(True)
                                    else:
                                        break
                                except Exception as e:
                                    logger.exception(e)

                            if len(matched_filter_criterias) > 0:
                                if matched_filter_criterias.count(True) == count:
                                    _alerts.append(rule.get("labels").get("alert_id"))
                        except Exception as e:
                            logger.exception(e)
                results.append({**entry, "_file_name": dat, "_alerts": _alerts})
        except Exception as e:
            logger.exception(e)
    return results


def search_qualified_files(search_items, git_service):
    search_params = git_service.encode_search_parameters(search_items)
    prefix = "international-tech/intl-sre/golden-signals/rules/production/"
    page = 1
    result = set()

    while True:
        search_params['page'] = page
        response = git_service.search_code(search_params).json()
        if len(response['items']) > 0:
            for item in response['items']:
                if prefix in item['path']:
                    result.add(item['path'])
        else:
            break
        page = page + 1
    return result


def get_all_files_for_threshold_update(item, git_service):
    result = set()

    if type(item) is dict:
        sub_items = [dict(list(item.items())[i:i + 3]) for i in range(0, len(item), 3)]
    elif type(item) is list:
        sub_items = [item[i:i + 3] for i in range(0, len(item), 3)]
    else:
        return result

    for sub_item in sub_items:
        tmp = search_qualified_files(sub_item, git_service)
        if len(result) == 0:
            result = tmp
        else:
            result = result.intersection(tmp)

    return result


def process_filters_and_update_threshold(filters_data):
    alert_modifier = AlertModifier()
    url = ""
    count = 0

    try:
        # clone and search
        if alert_modifier.fork_git.git.clone() is False:
            raise exceptions.GitBashCommandFailedToExecute("Clone for id: {} failed")
        for criteria_block in filters_data:
            # if it has criteria, search by criteria, otherwise,
            # search all files which contains threshold key name and update
            if 'criteria' in criteria_block.keys():
                files_list = get_all_files_for_threshold_update(criteria_block['criteria'], alert_modifier.fork_git.git)
            else:
                if 'threshold' in criteria_block.keys():
                    files_list = get_all_files_for_threshold_update(list(criteria_block['threshold'].keys()),
                                                                    alert_modifier.fork_git.git)
                else:
                    continue

            logger.info(f"All are {len(files_list)} to update alert threshold")
            # update files based on to threshold
            for file_path in files_list:
                # if only update sre files, otherwise, update all files
                if criteria_block.get('is_sre_alert') is True and 'sre-alerts' not in file_path.rsplit("/", 2)[1]:
                    continue
                complete_path = alert_modifier.cloned_to_location + "/mms-config/" + file_path
                count += alert_modifier.update_alert_threshold(complete_path, criteria_block)

        if count == 0:
            return {"pull_request": "No files changed, Please check your input"}

        # when it's done create a pr
        alert_modifier.fork_git.git.checkout(alert_modifier.local_repo_branch)
        alert_modifier.fork_git.git.add('.')
        alert_modifier.fork_git.git.commit("Update alert threshold")
        alert_modifier.fork_git.git.push()
        fork_repo_with_branch = f"{ALERT_OWNER}:{alert_modifier.local_repo_branch}"
        title = "Update alert threshold"
        # url = create_pull_request('Telemetry', 'mms-config', fork_repo_with_branch, title, 'main')
        url = alert_modifier.fork_git.git.create_pull_request(fork_repo_with_branch, 'main', title)
    except Exception as e:
        logger.exception(e)
        url = str(e)
    finally:
        alert_modifier.fork_git.git.clean()

    return {"pull_request": url}


def main():
    log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    log_format = '%(asctime)s %(filename)18s %(funcName)25s %(lineno)3d %(levelname)10s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(log_level)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)

    file_handler = handlers.RotatingFileHandler(filename=os.path.join(os.path.basename(__file__).replace('py', 'log')),
                                                maxBytes=1048576,
                                                backupCount=5)
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(fmt=formatter)
    logger.addHandler(file_handler)
    payload = [
        {"namespace": "ca-account", "app_name": "account", "_include_filter": ["rules/production", "sre"],
         "_exclude_filter": ["app-owner-alerts", "app"]}, \
        {"namespace": "ca-around-me", "app_name": "aroundme", "alert_sla_name": "cpu_usage_current_threshold_pct",
         "_include_filter": ["rules/production"]},
        {"alert_sla_name": "cpu_usage_current_threshold_pct",
         "_include_filter": ["rules/production"]}
    ]
    location = "/Users/<USER>/git/mms-config/international-tech/intl-sre"
    # import pprint
    # pprint.pprint(get_all_alert_ids_and_file_name(payload,location))
    process_filters_and_disable_and_enable_alerts(payload)


if __name__ == "__main__":
    # main()
    list1 = [
        {
            "is_sre_alert": True,
            "criteria": {
                "app_name": "content-layout-service-prod",
                "namespace": "ca-content-layout-service"
            },
            "threshold": {
                "cpu_usage_current_threshold_pct": 90
            }
        }
    ]
    process_filters_and_update_threshold(list1)
