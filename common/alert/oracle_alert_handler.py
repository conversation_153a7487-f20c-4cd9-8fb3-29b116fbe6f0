

from common.alert.alerts_improved import Alerts


class Oracle:

    def __init__(self,n_minutes=5):

        self.n_minutes_data = n_minutes



    def get_alerts_by_name(self, alert_name, tier=None, market=None):

        """
                                Gets all alerts for list of sla_names
                                Args:
                                    alert__name: list of strings consisting of sla names
                                    tier: app tier
                                    market: market (mx,ca...)

                                Returns:
                                return list of alerts(dict)
                                """
        return Alerts.get_alerts_by_criteria(alert_names=alert_name,n_minutes=self.n_minutes_data,tier=tier,market=market)



    def get_all_alerts(self, tier=None, market=None):
        """
                                        Gets all alerts triggered
                                        Args:
                                            tier: app tier
                                            market: market (mx,ca...)

                                        Returns:
                                        return list of alerts(dict)
                                        """

        alerts = Alerts.get_alerts_by_criteria(criteria={},component="OracleDB",n_minutes=self.n_minutes_data,tier=tier,market=market)


# Method for Database Lock and Deadlock metrics
    def get_db_lock_metrics(self, tier=None, market=None):
        db_lock_metrics = [
            "dead_lock_threshold_count",
            "library_cache_lock_threshold_pct"
        ]
        return self.get_alerts_by_name(db_lock_metrics, tier, market)
    # Method for Database CPU Usage metrics
    def get_db_cpu_usage(self, tier=None, market=None):
        return self.get_alerts_by_name(["db_cpu_threshold_pct"], tier, market)
    # Method for Database IO and Transaction metrics
    def get_db_io_transactions_metrics(self, tier=None, market=None):
        db_io_txn_metrics = [
            "wait_time_threshold_count",
            "high_transactions_threshold_count",
            "logical_io_threshold_count",
            "physical_reads_threshold_count",
            "physical_write_threshold_count"
        ]
        return self.get_alerts_by_name(db_io_txn_metrics, tier, market)
    

if __name__ == "__main__":
    oracle = Oracle()
    print(oracle.get_all_alerts(tier="app",market="mx"))

