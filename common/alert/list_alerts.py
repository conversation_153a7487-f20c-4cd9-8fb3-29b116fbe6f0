import logging
import os
import time
import uuid
from collections import OrderedDict

import yaml

import settings
from git_service import exceptions
from git_service.git import Git
from libs import shell
from libs.shell import bash

ALERT_CONFIG_URL = "***************************:intl-ecomm-svcs/mms-config.git"
ALERT_REPO_NAME = "mms-config"
ALERT_OWNER = "intl-ecomm-svcs"

MMS_SRE_DIR = "international-tech/intl-sre/golden-signals/rules/production"
# Thresholds keys are always returned
RETURN_KEYS = {
    "cassandra": ["alert_id", "namespace_or_assembly", "app_name_or_platform", "cluster", "market",
                  "tier", "alert_sla_name"],
    "cosmos": ["alert_id", "resource_group", "app_name_or_platform", "alert_team_name", "market", "tier",
               "namespace_or_assembly", "is_wcnp_env", "domain", "subscription_name", "alert_sla_name"],
    "kafka": ["alert_id", "alert_team_name", "app_name", "market", "namespace", "tier", "alert_sla_name"],
    "megacache": ["alert_id", "meghacache_assembly", "market", "tier", "alert_team_name", "alert_sla_name"],
    "oneops": ["alert_id", "assembly", "platform", "tenant", "market", "tier", "alert_team_name", "alert_sla_name"],
    "oracle": ["alert_id", "database", "alert_team_name", "market", "tier", "alert_sla_name"],
    "solr": ["alert_id", "assembly", "alert_team_name", "market", "tier", "alert_sla_name"],
    "sql": ["alert_id", "database", "alert_team_name", "market", "tier", "alert_sla_name"],
    "wcnp": ["alert_id", "alert_team_name", "app_name", "market", "namespace", "tier", "alert_sla_name"]
}
MANAGED_SERVICES = {"cosmos", "megacache", "cassandra", "sql", "kafka", "solr", "oracle"}
THRESHOLDS = "_threshold_"
THRESHOLDS_KEY = "thresholds"

logger = logging.getLogger(__name__)


# manipulate repo
class ListAlertInfo:
    def __init__(self):
        # clone to a random place
        bash(f"mkdir -p {settings.TEMPLATE_OUTPUT_DIR}")
        self.cloned_to_location = os.path.join(settings.TEMPLATE_OUTPUT_DIR, str(uuid.uuid4()))
        _time_stamp = int(time.time())
        self.local_repo_branch = f"mms_config_{_time_stamp}"
        self.working_folder = f"{self.cloned_to_location}/{ALERT_REPO_NAME}/{MMS_SRE_DIR}"
        # create temp location for clone
        response = shell.bash("mkdir -p {}".format(self.cloned_to_location))
        self.git = Git(ALERT_CONFIG_URL, ALERT_REPO_NAME, cloned_to_location=self.cloned_to_location,
                       project=ALERT_OWNER, branch=self.local_repo_branch)
        self.git()

        if response.return_code != 0:
            raise exceptions.GitBashCommandFailedToExecute("Failed to create random working dir: {} with reason: {}"
                                                           .format(self.cloned_to_location, response.stderr))

    def get_alert_info(self, file_list):
        alerts_non_ms = dict()
        alerts_managed_service = dict()
        alerts_managed_service.setdefault('managed_service', dict())

        for file_path in file_list:
            try:
                file_path_array = file_path.rsplit("/", 3)
                service = file_path_array[1]
                alert_type = file_path_array[2]
                file_name = file_path_array[3].split(".")[0]
                content = yaml.safe_load(open(file_path))
                alerts_info = self.extract_fields_from_file(content, service)
                if len(alerts_info) > 0:
                    # add to result
                    if alert_type == "sre":
                        alert_type = "sre-alerts"
                    else:
                        alert_type = "app-owner-alerts"

                    if service not in MANAGED_SERVICES:
                        result = alerts_non_ms
                    else:
                        result = alerts_managed_service['managed_service']

                    if service not in result.keys():
                        result[service] = dict()
                    if alert_type not in result[service].keys():
                        result[service][alert_type] = dict()
                    result[service][alert_type][file_name] = alerts_info

            except Exception as e:
                print(f"errors: {str(e)}")

        return {**alerts_non_ms, **alerts_managed_service}

    def extract_fields_from_file(self, file_content, service):
        result = list(list())
        filters = RETURN_KEYS.get(service)

        # to avoid some files have illegal chars
        if len(file_content) > 0:
            if 'groups' not in file_content.keys():
                return result

            for group in file_content['groups']:
                rules = group['rules']

                for rule in rules:
                    if rule is None:
                        continue
                    # Cassandra template is special, not always have labels
                    if 'labels' not in rule.keys():
                        continue

                    rule_attribute = list()
                    app = dict()
                    thresholds = dict()

                    # name always extract
                    app['alert_name'] = rule['alert']
                    for key, val in rule['labels'].items():
                        # don't check THRESHOLDS keys
                        if THRESHOLDS in key:
                            thresholds[key] = val
                        if filters is not None and key in filters:
                            app[key] = val

                    if len(thresholds) > 0:
                        app[THRESHOLDS_KEY] = OrderedDict(sorted(thresholds.items()))
                    rule_attribute = list(OrderedDict(sorted(app.items())).items())
                    result.append(rule_attribute)

        return result

    def list_alert(self):
        ret = dict()

        try:
            if self.git.clone() is False:
                raise exceptions.GitBashCommandFailedToExecute(f"Clone for {ALERT_REPO_NAME} failed")

            # find files with id with full path
            cmd_line = f"find {self.working_folder} -type f"
            alert_files = bash(cmd_line, True)
            alert_files = alert_files.stdout
            if len(alert_files) == 0:
                raise exceptions.GitBashCommandFailedToExecute(f"No files founded: {ALERT_REPO_NAME}")

            ret = self.get_alert_info(alert_files)
        except Exception as e:
            logger.exception(e)
            raise exceptions.GitBashCommandFailedToExecute(f"No files founded: {ALERT_REPO_NAME}")

        finally:
            self.git.clean()

        return ret


if __name__ == "__main__":
    list_alert = ListAlertInfo()
    list_alert.list_alert()
