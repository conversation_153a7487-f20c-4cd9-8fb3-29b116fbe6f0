from common.alert.alerts_improved import Alerts


class Kafka:

    def __init__(self,n_minutes=5):

        self.n_minutes_data = n_minutes



    def get_alerts_by_name(self, alert_name, tier=None, market=None):

        """
                                Gets all alerts for list of sla_names
                                Args:
                                    alert__name: list of strings consisting of sla names
                                    tier: app tier
                                    market: market (mx,ca...)

                                Returns:
                                return list of alerts(dict)
                                """
        return Alerts.get_alerts_by_criteria(alert_names=alert_name,tier=tier,market=market)



    def get_all_alerts(self, tier=None, market=None):
        """
                                        Gets all alerts triggered
                                        Args:
                                            tier: app tier
                                            market: market (mx,ca...)

                                        Returns:
                                        return list of alerts(dict)
                                        """

        alerts = Alerts.get_alerts_by_criteria(criteria={},component="kafka",tier=tier,market=market)

        return alerts



    def get_tcp_high(self,tier=None,market=None):
        return self.get_alerts_by_name(["tcp_wait_threshold_count"],tier,market)


    def get_authentication_fail(self,tier=None,market=None):

        return self.get_alerts_by_name(["failed_authentication_breaches_threshold_count"],tier,market)

    def get_crashloop_alerts(self,tier=None,market=None):
        return self.get_alerts_by_name(["pods_restarts_current_threshold_count"],tier,market)

    def get_cpu_usage(self,tier=None,market=None):
        return self.get_alerts_by_name(["cpu_usage_current_threshold_pct"],tier,market)

    def get_mem_usage(self,tier=None,market=None):

        return self.get_alerts_by_name(["memory_usage_current_threshold_pct"],tier,market)


    def get_brokers(self,tier=None,market=None):

        return self.get_alerts_by_name(["brokers_up_threshold_count"],tier,market)


    def get_partition(self,tier=None,market=None):


        return self.get_alerts_by_name(["low_number_of_offline_partitions_threshold_count","offline_partitions__threshold_count"],tier,market)
