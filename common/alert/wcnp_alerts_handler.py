
from common.alert.alerts_improved import Alerts


class Wcnp:

    def __init__(self,n_minutes=5):

        self.n_minutes_data = n_minutes



    def get_alerts_by_name(self, alert_name, tier=None, market=None):

        """
                                Gets all alerts for list of sla_names
                                Args:
                                    alert__name: list of strings consisting of sla names
                                    tier: app tier
                                    market: market (mx,ca...)

                                Returns:
                                return list of alerts(dict)
                                """
        return Alerts.get_alerts_by_criteria(alert_names=alert_name,n_minutes=self.n_minutes_data,tier=tier,market=market)



    def get_all_alerts(self, tier=None, market=None):
        """
                                        Gets all alerts triggered
                                        Args:
                                            tier: app tier
                                            market: market (mx,ca...)

                                        Returns:
                                        return list of alerts(dict)
                                        """

        alerts = Alerts.get_alerts_by_criteria(criteria={},component="wcnp",n_minutes=self.n_minutes_data,tier=tier,market=market)

        return alerts

    def get_alerts_by_namespace_app(self,namespace=None,app_name=None,market=None,tier=None):

        criteria = {}
        if namespace:
            criteria['namespace'] = namespace
        if app_name:
            criteria['app_name'] = app_name

        alerts = Alerts.get_alerts_by_criteria(criteria=criteria,component="wcnp",n_minutes=self.n_minutes_data,tier=tier,market=market)
      
        return alerts



    def get_crashloop_alerts(self,tier=None,market=None):


        return self.get_alerts_by_name(["pods_restarts_current_threshold_count"],tier,market)

    def get_5xx(self,tier=None,market=None):


        return self.get_alerts_by_name(["fiveXX_trend_comparing_to_one_week_ago_threshold_pct","fiveXX_current_threshold_pct"],tier,market)


    def get_cpu_utilization(self,tier=None,market=None):

        return self.get_alerts_by_name(['cpu_usage_current_threshold_pct'],tier,market)

    def get_mem_utilization(self,tier=None,market=None):

        return self.get_alerts_by_name(['memory_usage_current_threshold_pct'],tier,market)


    def get_traffic_spike(self,tier=None,market=None):

        return self.get_alerts_by_name(['traffic_spike_comparing_to_one_week_ago_threshold_pct'], tier, market)


    def get_traffic_drop(self,tier=None,market=None):

        return self.get_alerts_by_name(['traffic_drop_comparing_to_one_week_ago_threshold_pct'], tier, market)

    def get_latency_spike(self,tier=None,market=None):

        return self.get_alerts_by_name(['latency_spike_comparing_to_one_week_ago_threshold_pct'], tier, market)

    def get_traffic_imbalance(self,tier=None,market=None):

        return self.get_alerts_by_name(['scus_traffic_in_balance_current_threshold_pct','wus_traffic_in_balance_current_threshold_pct',
                                     'eus_traffic_in_balance_current_threshold_pct'], tier, market)

    def get_rate_limit(self,tier=None,market=None):

        return self.get_alerts_by_name(['rate_limit_threshold_count'], tier, market)

    def quota_limit(self,tier=None,market=None):
        return self.get_alerts_by_name(['quota_limit_threshold_pct'], tier, market)



def test():
    t = Wcnp(n_minutes=10)
    temp = t.get_all_alerts("one","CA")
    print(temp)
    #print(len(t.get_5xx("one","CA")))
    # print(t.get_traffic_spike())
    # print(t.get_crashloop_alerts())
    # print(t.get_cpu_utilization())
    #print(len(t.get_mem_utilization("one","CA")))
    print(t.get_alerts_by_namespace_app(namespace='sct-stride-zone-mgmt',app_name='stride-zonemgmt-cam-prod',
                                        tier="one",market="CA"))


if __name__ == "__main__":
    test()
