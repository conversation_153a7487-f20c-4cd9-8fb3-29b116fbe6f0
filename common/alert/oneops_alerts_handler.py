
from common.alert.alerts_improved import Alerts

class Oneops:

    def __init__(self,n_minutes=5):

        self.n_minutes_data = n_minutes



    def get_alerts_by_name(self, alert_name, tier=None, market=None):

        """
                                Gets all alerts for list of sla_names
                                Args:
                                    alert__name: list of strings consisting of sla names
                                    tier: app tier
                                    market: market (mx,ca...)

                                Returns:
                                return list of alerts(dict)
                                """
        return Alerts.get_alerts_by_criteria(alert_names=alert_name,n_minutes=self.n_minutes_data,tier=tier,market=market)



    def get_all_alerts(self, tier=None, market=None):
        """
                                        Gets all alerts triggered
                                        Args:
                                            tier: app tier
                                            market: market (mx,ca...)

                                        Returns:
                                        return list of alerts(dict)
                                        """

        alerts = Alerts.get_alerts_by_criteria(criteria={},component="oneops",n_minutes=self.n_minutes_data,tier=tier,market=market)

        return alerts

    def get_slb_health(self, tier=None, market=None):

        return self.get_alerts_by_name(["slb_health_current_threshold_pct"], tier, market)

    def get_ns_health(self, tier=None, market=None):
        return self.get_alerts_by_name(["ns_health_current_threshold_pct"], tier, market)

    def get_cpu_utilization(self, tier=None, market=None):

        return self.get_alerts_by_name(['cpu_current_threshold_pct'], tier, market)

    def get_5xx(self,tier=None,market=None):
        return self.get_alerts_by_name(["fiveXX_trend_comparing_to_one_week_ago_threshold_pct","fiveXX_current_threshold_pct"],tier,market)

    def get_mem_utilization(self, tier=None, market=None):

        return self.get_alerts_by_name(['memory_current_threshold_pct'], tier, market)


    def get_latency_spike(self, tier=None, market=None):

        return self.get_alerts_by_name(['latency_trend_comparing_to_one_week_ago_threshold_pct'], tier, market)

    def get_traffic_imbalance(self, tier=None, market=None):

        return self.get_alerts_by_name(
            ["traffic_in_balance_current_threshold_pct"], tier, market)

    def get_storage_space(self, tier=None, market=None):

        return self.get_alerts_by_name(['storage_current_threshold_pct'], tier, market)



if __name__ == "__main__":
    oneops = Oneops()
    print(oneops.get_all_alerts(tier="app",market="mx"))