import importlib
import logging, os, settings
from common.data_store.data_processor import Data
from common.teap_handler.teap_data_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>andler
from libs.prometheus_client import Prometheus
from importlib.machinery import SourceFileLoader

logger = logging.getLogger(__name__)
# teap_data_handler = TeapDataHandler()
# teap = teap_data_handler.teap_data

pro = Prometheus()


class AlertsPostDataBuilder:

    def __init__(self, template, is_sre_sla, tier, user_inventory_data=True, persist_data=False,
                 custom_inventory_file=None):
        self.template = template.strip().rstrip()
        self.is_sre_sla = is_sre_sla
        self.persist_data = persist_data
        self.tier = tier
        # If tier is all, no need filter. So setting tier to None
        if tier == "all":
            tier = None
        # self.platform = tmp_mapper.get("platform")

        self.data_store = Data(template, custom_inventory_file=custom_inventory_file)
        if is_sre_sla:
            self.teap_or_inventory_data_by_tier = self.data_store.sre_inventory_data
        else:
            self.teap_or_inventory_data_by_tier = self.data_store.user_inventory_data

        if tier:
            filter_data = dict()
            for k, v in self.teap_or_inventory_data_by_tier.items():
                if v.get("tier") == tier:
                    filter_data[k] = v
            self.teap_or_inventory_data_by_tier = filter_data

        self.is_required_additional_parms = False

        placeholders_tokens = os.path.splitext(self.template)
        if len(placeholders_tokens) == 2:
            self.template_without_postfix = placeholders_tokens[0]
        # Dynamic data processor , not required for request
        self.teap_data_processor = None

    def __call__(self, *args, **kwargs):
        pass

    def load_dynamic_executor_file(self):
        executor_file = f"{self.template_without_postfix}.py"
        exec_dir = os.path.join(settings.TEMPLATE_BASE_DIR, 'sre-alert-templates', 'executors')
        module = SourceFileLoader(self.template_without_postfix,
                                  f"{exec_dir}/{executor_file}").load_module()
        # self.teap_data_processor = module.Processor(teap_data_handler, pro)

    def build_post_body(self, is_sre_sla, persist_data=True, sre_override_xmatters_and_slack=True):
        apps = list()

        for app in self.teap_or_inventory_data_by_tier:
            _app_meta_data = None
            create_alerts = True
            exception_message = None
            try:
                app_created_data = self.add_additional_alert_parms_to_alert_data(app)
                # Get SLA override data
                _app_meta_data = self.data_store.merge_and_get_app_data(app_created_data, is_sre_sla,
                                                                        sre_override_xmatters_and_slack)
                alert_team_name = AlertsPostDataBuilder.get_team_name(_app_meta_data.get("git_path"))
                if alert_team_name:
                    _app_meta_data["alert_team_name"] = alert_team_name
                create_alerts = _app_meta_data.get("status", True)
                delete_alert_file_status = _app_meta_data.get("delete_alert_file_status", False)

                AlertsPostDataBuilder.append_create_alert_status(_app_meta_data, create_alerts, exception_message)
                AlertsPostDataBuilder.append_delete_alert_status(_app_meta_data, delete_alert_file_status,
                                                                 exception_message)
            except Exception as e:
                logger.exception(e)
                _app_meta_data = self.add_additional_alert_parms_to_alert_data(app)
                AlertsPostDataBuilder.append_create_alert_status(_app_meta_data, False, str(e))
            apps.append(_app_meta_data)

        data = AlertsPostDataBuilder.build_bulk_alerts_post_body(apps)
        # using apps data here, because  we don't need to send data to upsert_inventory_file. it has additional data
        if persist_data:
            # Todo: Only saves the data, but we need to write a code to push the data to sre-templates
            # Todo: this should be internal to SRE team. this code should go to admin part of it
            self.data_store.upsert_inventory_file(apps, is_sre_sla=self.is_sre_sla)
        return data

    @staticmethod
    def append_create_alert_status(dic_data, status, exception_message=None):
        """
        This will be used , while creating alert in templates
        """
        dic_data["alert_process_details"] = dic_data.get("alert_process_details", dict())
        if status:
            dic_data["alert_process_details"]["create_or_update_alert_status"] = True
        else:
            dic_data["alert_process_details"]["create_or_update_alert_status"] = False
            dic_data["alert_process_details"]["message"] = f"exception occurred while processing {exception_message}"

    @staticmethod
    def append_delete_alert_status(dic_data, status, exception_message=None):
        """
        This will be used , while deleting the alert in templates
        """
        dic_data["alert_process_details"] = dic_data.get("alert_process_details", dict())
        if status:
            dic_data["alert_process_details"]["delete_alert_file_status"] = True
        else:
            dic_data["alert_process_details"]["delete_alert_file_status"] = False
            dic_data["alert_process_details"]["message"] = f"exception occurred while processing {exception_message}"

    @staticmethod
    def get_team_name(git_path):
        # git_path: "international-tech/intl-sre/golden-signals/rules/production/wcnp/sre-alerts"
        git_path.split("/")
        tokens = git_path.split("/")
        if len(tokens) > 3:
            return f"{tokens[1]}-{tokens[2]}".replace("-", "_")
        return None

    def get_app_data_for_alert(self, app, skipp_alert_data=True):
        app_xmatters = dict()

        if skipp_alert_data:
            return self.teap_or_inventory_data_by_tier.get(app)
        # will remove below code , not required. Not supporting TEAP sheet anymore
        for _k, _value in app.items():
            if _k == "app_inventory":
                if len(_value) == 0:
                    raise Exception(f'Proper app_inventory data not found')
                app_xmatters["team_email"] = _value[0].get("dl")
                app_xmatters["alert_team_name"] = _value[0].get("xmatters")
                app_xmatters["xmatters_group"] = _value[0].get("xmatters")
            elif _k == "metadata":
                app_xmatters["tier"] = _value.get("tier")
                app_xmatters["market"] = _value.get("market")
                app_xmatters["domain"] = _value.get("domain")
                app_xmatters["app_name"] = _value.get("name").lower()
                # app_xmatters["slack_channel"] = _value.get("slack_channel")
            elif _k == "wcnp":
                _namespace = _value.get('namespaceOrAssemblyOrDB').replace(" ", "").rstrip().strip()
                _app = _value.get('app').replace(" ", "").rstrip().strip()
                app_xmatters["namespace"] = _namespace
                app_xmatters["namespace_or_assembly"] = _namespace
                app_xmatters["app"] = _app
                app_xmatters["app_name"] = _app
            else:
                app_xmatters["namespace_or_assembly"] = _value.get('namespaceOrAssemblyOrDB').replace(" ", "")
        return app_xmatters

    def add_additional_alert_parms_to_alert_data(self, app_teap_data):
        if self.is_required_additional_parms:
            if not self.teap_data_processor:
                self.load_dynamic_executor_file()
            _data = self.teap_data_processor.execute(app_data=app_teap_data)
        else:
            _data = self.get_app_data_for_alert(app_teap_data)
        return _data

    @staticmethod
    def build_bulk_alerts_post_body(apps):
        user_provided_data = dict()
        user_provided_data["apps_meta_data"] = apps
        user_provided_data["create_pull_request"] = True
        user_provided_data["check_sla"] = False
        user_provided_data["global_data_vars"] = {

        }
        return user_provided_data

    def _process_necessary_data(self):
        pass


if __name__ == "__main__":
    template = "wcnp_alerts.yaml"
    alert_data = AlertsPostDataBuilder(template, is_sre_sla=True, tier=None,custom_inventory_file="app_hr_wcnp_alerts.yaml")
    data = alert_data.build_post_body(is_sre_sla=True, persist_data=True)
    # alertMagr = AlertsPostDataBuilder("cosmos_alerts.yaml", is_sre_sla=False, tier="zero")
    # alertMagr.build_post_body(False, False)
