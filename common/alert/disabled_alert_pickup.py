# pickup alert name with disabled alert
import logging
import json

from git_service.git import Git
import settings
from libs import shell
import uuid
from git_service import exceptions
from libs.shell import bash
import concurrent.futures

ALERT_CONFIG_URL = "***************************:intl-ecomm-svcs/mms-config"
ALERT_REPO_NAME = "mms-config"
ALERT_OWNER = "intl-ecomm-svcs"
START_POS = "international-tech/intl-sre/golden-signals/rules/production"
DEFAULT_SIZE = 100
logger = logging.getLogger(__name__)


class DisabledAlertPickup:
    def __init__(self):
        # clone to a random place
        self.cloned_to_location = settings.BASE_DIR + "/" + str(uuid.uuid4().hex)
        self.root_location = self.cloned_to_location + "/" + ALERT_REPO_NAME + "/" + START_POS
        self.git = Git(ALERT_CONFIG_URL, ALERT_REPO_NAME, cloned_to_location=self.cloned_to_location,
                       project=ALERT_OWNER)
        # create temp location for clone
        response = shell.bash("mkdir -p {}".format(self.cloned_to_location))
        if response.return_code != 0:
            raise exceptions.GitBashCommandFailedToExecute("Failed to create random working dir: {} with reason: {}"
                                                           .format(self.cloned_to_location, response.stderr))

    def get_alert(self, file_path):
        alert_name = ""
        in_alert_block = False
        # compatible for old and new cases, remove space and compare
        commented_clue = '#mms_'
        result = list()
        pre_token = []

        with open(file_path, 'r') as alert_file:
            lines = alert_file.readlines()

        for line in lines:
            tmp = line.split(':')
            key = tmp[0].replace(" ", "").strip()

            # skip # line or newline
            if len(key) == 0 or key == "#":
                continue

            if len(pre_token) > 0:
                key = "".join(pre_token) + key
                pre_token.clear()

            # if already commented, save name and ignore rest until next one
            if key == '#-alert' or key == "-#alert":
                alert_name = tmp[1].strip()
                result.append(alert_name)
                in_alert_block = False
                continue
            elif key == '-':
                # if only -, need read next line to know what's the key
                pre_token.append('-')
                continue
            elif key == '-alert':
                alert_name = tmp[1].strip()
                in_alert_block = True
            else:
                if in_alert_block and key.startswith(commented_clue):
                    # generate result
                    result.append(alert_name)
                    # ignore rest commented lines until enter next alert block
                    in_alert_block = False
                    alert_name = ""
        return result

    def get_disable_alert(self, file_list):
        result = dict()

        for file_path in file_list:
            file_disabled_alerts = self.get_alert(file_path)
            if len(file_disabled_alerts) > 0:
                tmp = file_path.rsplit("/", 3)
                # service-alert type-file name
                short_path = f"{tmp[1]}/{tmp[2]}/{tmp[3]}"
                result[short_path] = file_disabled_alerts
        return result

    def search_disabled_alert(self):
        response = {"result": False, "msg": ""}
        result = dict()

        try:
            # clone and search
            if self.git.clone() is False:
                raise exceptions.GitBashCommandFailedToExecute("Clone is failed for 'search_disabled_alert'")

            # find files
            # grep_command = "grep -rl {} {}".format("#mms", self.root_location)
            grep_command = "find {} -type f -print".format(self.root_location)
            alert_files = bash(grep_command, True)
            alert_files = alert_files.stdout
            if len(alert_files) == 0:
                response['msg'] = "No disable alert found"
                return

            # split files
            print(f"All files: {len(alert_files)}")
            files_per_threads = [alert_files[i:i + DEFAULT_SIZE] for i in range(0, len(alert_files), DEFAULT_SIZE)]
            tasks = list()

            with concurrent.futures.ThreadPoolExecutor(max_workers=len(files_per_threads)) as thread_pool:
                for sub_list in files_per_threads:
                    sub_task = thread_pool.submit(self.get_disable_alert, sub_list)
                    tasks.append(sub_task)

            for sub_task in tasks:
                each_result = sub_task.result()
                result = {**result, **each_result}

            thread_pool.shutdown(wait=True)

            response['result'] = True
            response['msg'] = result
        except Exception as e:
            logger.exception("Errors when search disabled alerts with exception :{}".format(e))
            raise Exception("Errors when search disabled alerts with exception")

        finally:
            self.git.clean()

        return response


if __name__ == "__main__":
    ds = DisabledAlertPickup()
    ret = ds.search_disabled_alert()
    json_object = json.dumps(ret)
    print(json_object)

