# disable/enable alert by alert id
import logging
import settings
import time, uuid
import yaml, os
from libs import shell
from libs.shell import bash
from git_service.git import Git
from git_service import exceptions
from concurrent.futures import ThreadPoolExecutor
from git_service.git_fork import ForkGit

ALERT_CONFIG_URL = "***************************:intl-ecomm-svcs/mms-config.git"
UPSTREAM_GIT = '***************************:Telemetry/mms-config.git'
ALERT_REPO_NAME = "mms-config"
ALERT_OWNER = "intl-ecomm-svcs"
DEFAULT_REMOVED_SET = {'mms_slack_channel', 'mms_email', 'mms_xmatters_group'}

BASE_ORG = 'Telemetry'
BASE_REPO = 'mms-config'
BASE_BRANCH = "main"
logger = logging.getLogger(__name__)


class AlertModifier:
    def __init__(self):
        # clone to a random place
        bash(f"mkdir -p {settings.TEMPLATE_OUTPUT_DIR}")
        self.cloned_to_location = os.path.join(settings.TEMPLATE_OUTPUT_DIR, str(uuid.uuid4()))
        _time_stamp = int(time.time())
        self.local_repo_branch = f"{ALERT_REPO_NAME}_{_time_stamp}"
        # create temp location for clone
        response = shell.bash("mkdir -p {}".format(self.cloned_to_location))
        self.fork_git = ForkGit(cloned_to_location=self.cloned_to_location,
                                fork_git_url=ALERT_CONFIG_URL,
                                branch=self.local_repo_branch, upsteam_git=UPSTREAM_GIT,
                                upstream_repo_branch='main')
        self.fork_git.execute_init_git_tasks()
        # self.git = Git(ALERT_CONFIG_URL, cloned_to_location=self.cloned_to_location, branch=self.local_repo_branch)
        # self.git()
        # self.git.execute_init_git_tasks()

        if response.return_code != 0:
            raise exceptions.GitBashCommandFailedToExecute("Failed to create random working dir: {} with reason: {}"
                                                           .format(self.cloned_to_location, response.stderr))

    def comment_line(self, _str=""):
        space_str = (_str.split(':')[0]).rsplit(" ", 1)[0] + " "
        rest_str = _str.lstrip(' ')

        return space_str + "#" + rest_str

    # only un-comment those three data fields, ignore others
    def uncomment_line(self, _str=""):
        space_str = (_str.split('#'))[0]
        rest_str = (_str.split('#'))[1]
        data_field = rest_str.split(':')[0]

        if data_field.strip() in DEFAULT_REMOVED_SET:
            return space_str + rest_str.lstrip(' ')

        return _str

    def remove_fields(self, _lines_list=None):
        ret_list = list()
        if _lines_list is None:
            return ret_list

        for line in _lines_list:
            if line.split(':')[0].strip() not in DEFAULT_REMOVED_SET:
                ret_list.append(line)
            else:
                ret_list.append(self.comment_line(line))

        return ret_list

    def search_and_disable(self, _file_list=None):
        lines = list()
        for file in _file_list:
            try:
                lines.clear()
                with open(file, 'r') as alert_file:
                    lines = alert_file.readlines()
                lines = self.remove_fields(lines)
                with open(file, 'w') as output_file:
                    output_file.writelines(lines)

            except Exception as e:
                logger.exception(e)

    def disable_alert(self, _id=None):
        ret = {"result": False, "msg": ""}

        if id is None:
            return ret

        try:
            # clone and search
            # if self.git.clone() is False:
            #     ret['msg'] = "Clone for id: {} failed".format(_id)
            #     raise exceptions.GitBashCommandFailedToExecute("Clone for id: {} failed".format(_id))

            # find files with id with full path
            grep_command = "grep -rl {} {}".format(_id, self.cloned_to_location)
            alert_files = bash(grep_command, True)
            alert_files = alert_files.stdout
            if len(alert_files) == 0:
                ret['msg'] = "Alert id: {} not found".format(_id)
                raise exceptions.GitBashCommandFailedToExecute("Alert id: {} not found".format(_id))

            self.disable_rules(alert_files, list(_id))

            # when it's done create a pr
            # self.git.checkout()
            if self.fork_git.git.add("."):
                self.fork_git.git.commit("Disabled alert with alert id: {}".format(_id))
                self.fork_git.git.push()
                fork_repo_with_branch = f"{ALERT_OWNER}:{self.local_repo_branch}"
                ret['pull_request'] = self.create_pull_request("Disabled alerts")
                ret['local_branch'] = self.local_repo_branch
                ret['result'] = True
            else:
                raise exceptions.GitBashCommandFailedToExecute("Failed to add for id: {}".format(_id))

        except Exception as e:
            logger.exception(e)
            ret['msg'] = "Alert id: {} Git bash Command Failed.".format(_id)

        finally:
            self.fork_git.git.clean()

        return ret

    def create_pull_request(self,commit_message):
        return self.fork_git.git.create_pull_request(
            upstream_org=self.fork_git.upsteam_git_details.get("org"),
            upstream_repo=self.fork_git.upsteam_git_details.get("repo"),
            fork_org_name=self.fork_git.git.org,
            fork_branch_name=self.local_repo_branch, base="main",
            title=commit_message)

    def comment_current_line(self, line):
        output = []
        index = 0
        for char in line:
            if char == ' ' or char == '-':
                output.append(char)
            else:
                output.append('#')
                break
            index = index + 1
        output.append(line[index:])
        return "".join(output)

    # it replaces original way to get alert id due to python
    # added extra backslash if value has double quote which makes yaml parser return exception
    def read_alert_id(self, alert_block):
        alert_id = None

        for line in alert_block:
            tmp = line.split(':')
            key = tmp[0].replace(" ", "").strip()

            # some templates have double quote, in python it'll be '""'
            # remove all double, only keeps single
            if key == "alert_id":
                alert_id = tmp[1].replace(" ", "").replace('"', '').strip()
                break

        return alert_id

    def disable_comm_channels(self, line, comm_channel_list):
        tmp = line.split(':')
        key = tmp[0].replace(" ", "").strip()

        if key in comm_channel_list:
            return self.comment_current_line(line)
        return line

    # disable entire rule based on payload
    # convert alert block to yaml and check
    # if needed, comment them
    def disable_rules_checking(self, alert_block, alert_id_list, comm_channel_list):
        ret = list()
        comm_channels = list()
        alert_id = self.read_alert_id(alert_block)

        if len(comm_channel_list) != 0:
            comm_channels = list(map(lambda x: settings.ALERT_CHANNEL_MAPPER.get(x, ""), comm_channel_list))
        else:
            comm_channels = list(DEFAULT_REMOVED_SET)

        try:
            # checking current block need comment or not
            if alert_id is not None and alert_id in alert_id_list:
                # if comm_channel_list is empty, use original way
                # otherwise, disable fields in comm_channel_list
                for line in alert_block:
                    # if it's already comment, skip
                    if line.strip().startswith('#'):
                        ret.append(line)
                    elif len(line.strip()) == 0:
                        ret.append(line)
                    else:
                        # if len(comm_channels) == 0 or set(comm_channels) == DEFAULT_REMOVED_SET:
                        #     tmp_line = self.comment_current_line(line)
                        # else:
                        tmp_line = self.disable_comm_channels(line, comm_channels)
                        ret.append(tmp_line)


            else:
                ret = alert_block

        except Exception as e:
            logger.exception(e)
            return None

        return ret

    def enable_rules_checking(self, alert_block, alert_id_list):
        ret = list()
        lineno = 0
        has_alert_id = False
        uncommented = False

        try:
            for line in alert_block:
                tmp = line.split(":", 1)  # Split only on the first colon

                if len(tmp) > 1 and 'alert_id' in tmp[0]:
                    tmp[1] = tmp[1].strip().replace(" ", "")
                    cmp_id = tmp[1].replace('"', '').replace("'", "")
                    if cmp_id not in alert_id_list:
                        break
                    else:
                        has_alert_id = True
                        continue

                if '#' in line:
                    if '#' in tmp[0]:
                        # some cases have indent issues, use previous line to verify
                        tmp[0] = tmp[0].replace("#", "", 1)
                        if len(tmp) > 1:
                            ret.append(tmp[0] + ":" + tmp[1])
                        else:
                            ret.append(tmp[0])
                        uncommented = True
                    else:
                        ret.append(line)
                else:
                    ret.append(line)

                lineno += 1

        except Exception as e:
            logger.exception(e)
            return None

        if has_alert_id and uncommented:
            return ret
        return alert_block

    def disable_rules(self, alert_file, alert_id_list, comm_channel_list=None):
        result_block = list()
        alert_block = list()
        in_alert_block = False
        illegal_chars_found = False

        with open(alert_file, 'r') as input_file:
            lines = input_file.readlines()

        for line in lines:
            # if it's comment or new line skip to next one
            if line.split(':')[0].replace("-", "").strip() == 'alert':
                # alert block starts, if we have old one, parse it
                # otherwise, copy till meet next alert or ends
                if len(alert_block) > 0:
                    # to do disable rule
                    tmp = self.disable_rules_checking(alert_block, alert_id_list, comm_channel_list)
                    # if files containing illegal chars, we skip it
                    if tmp is not None:
                        result_block.extend(tmp)
                        alert_block.clear()
                    else:
                        illegal_chars_found = True
                        break

                alert_block.append(line)
                in_alert_block = True
            else:
                if in_alert_block:
                    alert_block.append(line)
                else:
                    result_block.append(line)

        # if illegal chars found, write back original and skip
        if illegal_chars_found:
            with open(alert_file, 'w') as output_file:
                output_file.writelines(lines)
            return
        else:
            # if ends normally and if there's a final block
            if len(alert_block) > 0:
                tmp = self.disable_rules_checking(alert_block, alert_id_list, comm_channel_list)
                result_block.extend(tmp)
                alert_block.clear()

        with open(alert_file, 'w') as output_file:
            output_file.writelines(result_block)

        return

    def enable_rules(self, alert_file, alert_id_list):
        result_block = list()
        alert_block = list()
        in_alert_block = False
        illegal_chars_found = False

        with open(alert_file, 'r') as input_file:
            lines = input_file.readlines()

        for line in lines:
            tmp = line.split(':')
            key = tmp[0].replace(" ", "").strip()

            # skip # line or newline
            if len(key) == 0 or key == "#":
                continue

            if '-alert' in key:
                if len(alert_block) > 0:
                    # to do disable rule
                    tmp = self.enable_rules_checking(alert_block, alert_id_list)
                    # if files containing illegal chars, we skip it
                    if tmp is not None:
                        result_block.extend(tmp)
                        alert_block.clear()
                    else:
                        illegal_chars_found = True
                        break

                alert_block.append(line)
                in_alert_block = True
                continue
            else:
                if in_alert_block:
                    alert_block.append(line)
                else:
                    result_block.append(line)

        # if illegal chars found, write back original and skip
        if illegal_chars_found:
            with open(alert_file, 'w') as output_file:
                output_file.writelines(lines)
            return
        else:
            # if ends normally and if there's a final block
            if len(alert_block) > 0:
                tmp = self.enable_rules_checking(alert_block, alert_id_list)
                result_block.extend(tmp)
                alert_block.clear()

        # validate restored file has issues ro not
        try:
            obj = yaml.safe_load("".join(result_block))
            with open(alert_file, 'w') as output_file:
                output_file.writelines(result_block)
        except Exception as e:
            print("Current file formatting is illegal")

        return

    def separate_alert_list(self, alert_list):
        ret = dict()

        for alert_id in alert_list:
            tmp_key = alert_id.split("_")[0]

            if tmp_key not in ret.keys():
                ret[tmp_key] = list()
            ret[tmp_key].append(alert_id)

        return ret

    def find_and_get_file_name(self, alert_id):
        grep_command = "grep -rl {} {}".format(alert_id, self.cloned_to_location)
        alert_files = bash(grep_command, True)
        alert_files = alert_files.stdout
        if len(alert_files) > 1:
            msg = f"Found multiple matching files for a given alert {alert_id},which should be single file"
            logger.info(msg)
            return False, None, alert_id
        if len(alert_files) == 0:
            msg = "Alert id: {} not found".format(alert_id)
            logger.info(msg)
            return False, None, alert_id
        return True, alert_files[0], alert_id

    def get_alert_file_by_alert(self, alert_list, response_message=None):
        response_message = {"result": False, "msg": ""}
        if len(alert_list) == 0:
            return response_message

        # alert_id: {alert_id_1, alert_id_2}
        separated = self.separate_alert_list(alert_list)
        alerts_and_alerts_files = dict()
        try:
            # clone and search
            # if self.git.clone() is False:
            #     response_message['msg'] = "Clone for disable failed"
            #     raise exceptions.GitBashCommandFailedToExecute("Clone for id: {} failed")

            futures = list()
            pool = ThreadPoolExecutor(len(alert_list))
            for item in separated.keys():
                futures.append(pool.submit(self.find_and_get_file_name, item))

            for future in futures:
                try:
                    status, file_name, alert_id = future.result()
                    if status:
                        alerts_and_alerts_files[file_name] = separated[alert_id]
                except Exception as e:
                    logger.exception(e)
        except Exception as e:
            logger.exception(e)
            response_message['msg'] = "Disable by Alert list Git bash Command Failed."
        return alerts_and_alerts_files

    def disable_or_enable_alerts(self, alert_list, is_disable=True, comm_channel_list=None):
        ret = {"result": False, "msg": ""}
        try:
            alerts_with_file = self.get_alert_file_by_alert(alert_list, ret)
            for k, v in alerts_with_file.items():
                if is_disable:
                    self.disable_rules(k, v, comm_channel_list)
                else:
                    self.enable_rules(k, v)

            # when it's done create a pr
            # self.git.checkout()
            for k, v in alerts_with_file.items():
                self.fork_git.git.add(k)

            self.fork_git.git.commit("Disabled alert with alerts list")
            self.fork_git.git.push()
            fork_repo_with_branch = f"{ALERT_OWNER}:{self.local_repo_branch}"
            title = "Disabled alert with alerts list"
            # ret['pull_request'] = create_pull_request('Telemetry', 'mms-config', fork_repo_with_branch, title,
            #                                           'main')
            ret['pull_request'] = self.create_pull_request(title)
            ret['local_branch'] = self.local_repo_branch
            ret['result'] = True

        except Exception as e:
            logger.exception(e)
            ret['msg'] = "Disable by Alert list Git bash Command Failed."

        finally:
            self.fork_git.git.clean()

        return ret

    def enable_fields(self, _lines_list=None):
        ret_list = list()
        if _lines_list is None:
            return ret_list

        for line in _lines_list:
            if line.lstrip(' ').startswith("#"):
                ret_list.append(self.uncomment_line(line))
            else:
                ret_list.append(line)

        return ret_list

    def search_and_enable(self, _file_list=None):

        lines = list()
        for file in _file_list:
            try:
                lines.clear()
                with open(file, 'r') as alert_file:
                    lines = alert_file.readlines()
                lines = self.enable_fields(lines)
                with open(file, 'w') as output_file:
                    output_file.writelines(lines)

            except Exception as e:
                logger.exception(e)

    def enable_alert(self, _id):
        ret = {"result": False, "msg": ""}

        if id is None:
            return ret

        try:
            # clone and search
            # if self.git.clone() is False:
            #     ret['msg'] = "Clone for id: {} failed".format(_id)
            #     raise exceptions.GitBashCommandFailedToExecute("Clone for id: {} failed".format(_id))

            # find files with id with full path
            grep_command = "grep -rl {} {}".format(_id, self.cloned_to_location)
            alert_files = bash(grep_command, True)
            alert_files = alert_files.stdout
            if len(alert_files) == 0:
                ret['msg'] = "Alert id: {} not found".format(_id)
                raise exceptions.GitBashCommandFailedToExecute("Alert id: {} not found".format(_id))

            self.search_and_enable(alert_files)

            # when it's done create a pr
            # self.git.checkout()
            if self.fork_git.git.add("."):
                self.fork_git.git.commit("Enable alert with alert id: {}".format(_id))
                self.fork_git.git.push()
                fork_repo_with_branch = f"{ALERT_OWNER}:{self.local_repo_branch}"
                title = "Enable alert with alert id: {}".format(_id)

                ret['pull_request'] = self.create_pull_request(title)
                ret['local_branch'] = self.local_repo_branch
                ret['result'] = True
            else:
                raise exceptions.GitBashCommandFailedToExecute("Failed to add for id: {}".format(_id))

        except Exception as e:
            logger.exception(e)
            ret['msg'] = "Alert id: {} Git bash Command Failed.".format(_id)

        finally:
            self.fork_git.git.clean()

        return ret

    def update_threshold(self, rule, criteria_block):
        changed = False
        try:
            for threshold in criteria_block['threshold']:
                if threshold in rule['labels'].keys():
                    rule['labels'][threshold] = str(criteria_block['threshold'][threshold])
                    changed = True
        except Exception as e:
            print(str(e))

        return changed

    def update_alert_threshold(self, file_path, criteria_block):
        content = yaml.safe_load(open(file_path))
        changed = False
        count = 0

        # to avoid some files have illegal chars
        if len(content) == 0 or 'groups' not in content.keys():
            return
        try:
            for group in content['groups']:
                rules = group['rules']
                for rule in rules:
                    # Cassandra template is special, not always have labels
                    if rule is None or 'labels' not in rule.keys():
                        continue

                    # if it has criteria, update alert when criteria meets, if it has no
                    # criteria, scan all alert block to find threshold to update, otherwise,
                    # skip
                    if 'criteria' in criteria_block.keys():
                        if set(criteria_block['criteria'].items()).issubset(set(rule['labels'].items())):
                            changed = changed | self.update_threshold(rule, criteria_block)
                    else:
                        changed = changed | self.update_threshold(rule, criteria_block)

            # write back after update
            if changed:
                count = count + 1
                print(f"Changed file name: {file_path}")
                f = open(file_path, 'w')
                yaml.dump(content, f, default_flow_style=False, explicit_start=True)

        except Exception as e:
            print(str(e))

        return count


if __name__ == "__main__":
    ds = AlertModifier()
    alerts_list = [
        "925725436e8c4f43be22934a20586792_1",
        "dcdfe3ca77c947c981f6f90fbe4b510a_2"
    ]
    comm_channel = [
        # "slack",
        # "email",
        # "xmatters"
    ]
    print(ds.disable_or_enable_alerts(alerts_list, False, comm_channel))

    # ret1 = ds.separate_alert_list(alerts_list)
    # separated_list = ret1["921ba304177f4b3d898057d37ccb2f6d"]
    # ds.disable_rules("/Users/<USER>/Git/mms_config/mms-config/international-tech/intl-sre/golden-signals/rules/production/wcnp/app-owner-alerts/account-ca-account.yaml", separated_list)
    # ds.disable_alerts(alerts_list)
    # ret1 = ds.disable_alert("4e66450ec7024d58b6736a65ab75da1a")
    # ret2 = ds.enable_alert("4e66450ec7024d58b6736a65ab75da1a")
    # print("result is :{}".format(ret1['result']))
    # print("url is :{}".format(ret1['msg']))
    # print(ret1)
    # print(ret2)
