import logging, os, time
from libs import shell
import uuid, yaml, settings
import concurrent.futures
from libs.shell import bash
from git_service.git import Git
from git_service import exceptions
from git_service.git_fork import ForkGit
from collections import defaultdict, OrderedDict
from settings import MMS_REPO_URL, MMS_REPO_NAME, MMS_ORG_NAME, SRE_REPO_NAME, SRE_REPO_URL, SRE_ORG_NAME

# ALERT_CONFIG_URL = "***************************:intl-ecomm-svcs/mms-config.git"
# ALERT_REPO_NAME = "mms-config"

ALERT_WRITE_CONFIG_URL = MMS_REPO_URL
ALERT_READ_CONFIG_URL = "***************************:Telemetry/mms-config.git"
ALERT_REPO_NAME = MMS_REPO_NAME
ALERT_OWNER = "m0c00jt"
DEFAULT_SIZE = 80
EXCLUDE_TYPE = {"performance", "asda", "mm"}
SCANNED_FILE_TYPE = {"app_owner", "sre"}
ALLOWED_SERVICES = {"wcnp", "cosmos", "meghacache", "cassandra", "sql", "kafka", "solr", "oneops", "oracle"}
MMS_SRE_DIR = "international-tech/intl-sre/golden-signals/rules/production"
SRE_INVENTORY_DIR = "data_store/inventory"
THRESHOLDS = "_threshold_"
THRESHOLDS_KEY = "thresholds"

logger = logging.getLogger(__name__)


# manipulate repo
class RepoProcessor:
    # working folder is the place where we search files
    def __init__(self, repo_url, repo_name, repo_owner, working_folder, test_uuid=None):
        self.repo_url = repo_url
        self.repo_name = repo_name
        self.repo_owner = repo_owner
        bash(f"mkdir -p {settings.TEMPLATE_OUTPUT_DIR}")
        # self.cloned_to_location = os.path.join(settings.TEMPLATE_OUTPUT_DIR, str(test_uuid))
        if test_uuid:
            self.cloned_to_location = os.path.join(settings.TEMPLATE_OUTPUT_DIR, str(test_uuid))
        else:
            self.cloned_to_location = os.path.join(settings.TEMPLATE_OUTPUT_DIR, str(uuid.uuid4()))
        self.working_folder = f"{self.cloned_to_location}/{self.repo_name}/{working_folder}"
        self.local_repo_branch = f"local_repo_{int(time.time())}"
        self.branch = self.local_repo_branch
        # create temp location for clone
        shell.bash("mkdir -p {}".format(self.cloned_to_location))
        self.git = Git(repo_url, cloned_to_location=self.cloned_to_location,
                       project=repo_owner, branch=self.local_repo_branch)
        self.git()
        self.git.clone()

    def get_keys_from_file(self, file_path):
        with open(file_path, 'r') as file:
            data = yaml.safe_load(file)
            keys = set()
            for group in data.get('groups', []):
                for rule in group.get('rules', []):
                    if rule is None:
                        continue
                    if rule is None:
                        continue
                    if rule.get('labels', []) is None:
                        continue
                    else:
                        keys.update(rule.get('labels', {}).keys())
            return keys

    def get_value_from_file(self, file_path, key):
        with open(file_path, 'r') as file:
            data = yaml.safe_load(file)
            if key == "alert_type":
                for group in data.get('groups', []):
                    for rule in group.get('rules', []):
                        if rule is None:
                            continue

                        value = rule.get('labels', {}).get(key)
                        if value is not None:
                            return value
            else:
                if key in data:
                    return data[key]
                else:
                    return None

    def write_dict_to_yaml_file(self, path, key, value):
        with open(path, 'r') as yaml_file:
            data = yaml.safe_load(yaml_file)
        if data is None:
            data = {}
        data[key] = value

        with open(path, 'w') as yaml_file:
            yaml.safe_dump(data, yaml_file)
        return self.submit_to_create_pr()

    def list_all_files(self):

        cmd_line = f"find {self.working_folder} -type f"
        alert_files = bash(cmd_line, True)
        alert_files = alert_files.stdout
        if len(alert_files) > 0:
            return alert_files
        else:
            raise exceptions.GitBashCommandFailedToExecute(f"No files founded: {self.repo_name}")

    def clone_and_list_all_files(self):
        try:
            if self.git.clone() is False:
                raise exceptions.GitBashCommandFailedToExecute(f"Clone for id: {self.repo_name} failed")

            # find files with id with full path
            cmd_line = f"find {self.working_folder} -type f"
            alert_files = bash(cmd_line, True)
            alert_files = alert_files.stdout
            if len(alert_files) > 0:
                return alert_files
            else:
                raise exceptions.GitBashCommandFailedToExecute(f"No files founded: {self.repo_name}")

        except Exception as e:
            logger.exception(e)
            raise exceptions.GitBashCommandFailedToExecute(f"Clone for id: {self.repo_name} failed")

    def submit_to_create_pr(self, upstream_org, upstream_repo, commit_message="Update config", title="Update config"):
        ret = {"result": False, "msg": ""}

        try:
            self.git.checkout()
            if self.git.add("."):
                self.git.commit(commit_message=commit_message)
                self.git.push()

                fork_repo_with_branch = f"{self.repo_owner}:{self.local_repo_branch}"
                ret['pull_request'] = self.git.create_pull_request('intl-ecomm-svcs', 'sre-templates', self.repo_owner,
                                                                   self.local_repo_branch, base='main', title=title)
                ret['local_branch'] = self.local_repo_branch
                ret['result'] = True
            else:
                raise exceptions.GitBashCommandFailedToExecute(f"Unable to add all the files for the branch")

        except Exception as e:
            logger.exception(e)
            ret['msg'] = f"Errors when create PR: {str(e)}"

        finally:
            self.clean_up()

        return ret

    def clean_up(self):
        self.git.clean()


class UpdateThreshold:
    def __init__(self):
        # mms_test_uuid = "a0fa0c6a-1fbb-4623-a5c3-0af97117a72d"
        # sre_templates_uuid = "d7321b12-f95e-4d07-a240-1567819d201a"
        self.sre_repo = RepoProcessor(SRE_REPO_URL, SRE_REPO_NAME, SRE_ORG_NAME, SRE_INVENTORY_DIR,
                                      test_uuid=None)
        self.mms_repo = RepoProcessor(ALERT_READ_CONFIG_URL, ALERT_REPO_NAME, ALERT_OWNER, MMS_SRE_DIR,
                                      test_uuid=None)

    def is_allowed_path(self, filter_type, file_path):
        ret = [element for element in filter_type if (element in file_path)]
        return ret

    def clean_up(self):
        # self.mms_repo.clean_up()
        # self.sre_repo.clean_up()
        pass

    def get_inventory(self):
        result = defaultdict(lambda: defaultdict(dict))
        path_list = self.sre_repo.list_all_files()

        for file_path in path_list:
            file_name = file_path.rsplit("/", 1)[-1].split(".")[0]
            service = self.is_allowed_path(ALLOWED_SERVICES, file_name)
            alert_type = self.is_allowed_path(SCANNED_FILE_TYPE, file_name)

            if len(service) == 1 and len(alert_type) == 1:
                target = f"{alert_type[0]}_{service[0]}_alerts"
                if file_name != target:
                    continue
            else:
                continue

            try:
                service = service[0]
                alert_type = "app" if alert_type[0] != "sre" else "sre"
                result[service][alert_type] = {'file_path': file_path}
            except Exception as e:
                logger.error(f"Error processing file {file_path}: {str(e)}")

        return dict(result)

    @staticmethod
    def read_yaml_content(file_path):
        try:
            with open(file_path, 'r') as inventory_file:
                inventory_file_content = yaml.safe_load(inventory_file)
                return inventory_file_content
        except Exception as e:
            logger.exception(e)

    @staticmethod
    def write_yaml_content(file_path, content):
        try:
            with open(file_path, 'w') as file:
                yaml.dump(content, file, sort_keys=False)
        except Exception as e:
            logger.exception(e)

    @staticmethod
    def print_communication_diff(inventory_data, yaml_data, key):
        try:
            _inventory_data = inventory_data.get(key)
            _yaml_data = yaml_data.get(key)
            logger.info(f"********  {key} inventory {_inventory_data} mms yml {_yaml_data}")
        except Exception as e:
            logger.error(f"Error occurred while processing {key} inventory_data {inventory_data} yaml_data {yaml_data}")

    @staticmethod
    def print_communication_log(inventory_file_content, _key, app):
        logger.info(f"Comaring Comminication channels for {_key}")
        UpdateThreshold.print_communication_diff(inventory_file_content, app['data'], "xmatters_group")
        UpdateThreshold.print_communication_diff(inventory_file_content, app['data'], "team_email")
        UpdateThreshold.print_communication_diff(inventory_file_content, app['data'], "slack_channel")

    @staticmethod
    def add_missing_communication_details_from_inventory_file(inventory_file_content_of_app, app_data):
        def add_if_required(key):
            if not app_data.get(key):
                app_data[key] = inventory_file_content_of_app.get(key)

        add_if_required("xmatters_group")
        add_if_required("team_email")
        add_if_required("slack_channel")

    @staticmethod
    # If the nested dictionaries also need to be ordered
    def order_nested_dict(data):
        if isinstance(data, dict):
            return OrderedDict(
                (k, UpdateThreshold.order_nested_dict(v)) for k, v in sorted(data.items())
            )
        return data

    @staticmethod
    def mms_sync(file_path, apps, key_string, consider_mms_data_only=True):
        """Update the inventory file with app data."""
        app = None
        try:
            inventory_file_content = UpdateThreshold.read_yaml_content(file_path)
            new_inventory_file_content = dict()
            # filtered_apps = [app for app in apps if not app["data"].get("name_space") or
            # not app["data"].get("namespace", "default")]
            apps.sort(key=lambda app: str(app["data"].get("name_space", app["data"].get("namespace", "default"))))
            for app in apps:
                try:
                    app_data = dict(OrderedDict(sorted(app['data'].items())))
                    _key = key_string.format(**app_data)
                    # inv_key = "{tenant}-{platform}-{assembly}".format(**app['data'])
                    if _key not in inventory_file_content:
                        logger.warning(
                            f"Data({_key}) not found in inventory_file (f{file_path}), added new entry to inventory_file")
                        new_inventory_file_content[_key] = app_data
                    else:
                        UpdateThreshold.print_communication_log(inventory_file_content[_key], _key, app)
                        UpdateThreshold.add_missing_communication_details_from_inventory_file(
                            inventory_file_content[_key], app['data'])

                    if consider_mms_data_only:
                        new_inventory_file_content[_key] = app_data
                    else:
                        inventory_file_content[_key] = app_data
                except Exception as e:
                    logger.error(e)
                    logger.error(f"app details {app}")
                    logger.exception(e)

            UpdateThreshold.write_yaml_content(file_path, new_inventory_file_content)

        except Exception as e:
            logger.error(f"Error updating inventory: {e} yaml data {app}")

    def group_by_yaml_alerts_data_owners(self, yaml_files_content):
        if len(yaml_files_content) == 0:
            return
        
        file_map = self.get_inventory()
        yaml_alerts_data_group_by_owners = defaultdict(list)
        
        for alert_yaml_file_data in yaml_files_content:
            if 'alert_owner_category' in alert_yaml_file_data['data']:
                try:
                    _alert_type = alert_yaml_file_data['data']['alert_type']
                    _alert_owner_category = alert_yaml_file_data['data']['alert_owner_category']
                    
                    # Check if custom output name is provided
                    custom_output_name = alert_yaml_file_data['data'].get('custom_output_name')
                    
                    if custom_output_name:
                        # Use custom filename if provided
                        file_path = f"{self.sre_repo.working_folder}/{_alert_type}_alerts/{_alert_owner_category}_{custom_output_name}"
                    else:
                        # Try to get existing file path or create new one
                        try:
                            file_path = file_map[_alert_type][_alert_owner_category]['file_path']
                        except KeyError:
                            # Create new file path if doesn't exist
                            # Special case for WCP app alerts to include "owner" in the filename
                            if _alert_type == "wcp" and _alert_owner_category == "app":
                                new_filename = f"{_alert_owner_category}_owner_{_alert_type}_alerts.yaml"
                            else:
                                new_filename = f"{_alert_owner_category}_{_alert_type}_alerts.yaml"
                            file_path = f"{self.sre_repo.working_folder}/{_alert_type}_alerts/{new_filename}"
                    
                    # Ensure directory exists
                    os.makedirs(os.path.dirname(file_path), exist_ok=True)
                    # Create empty file if it doesn't exist
                    if not os.path.exists(file_path):
                        with open(file_path, 'w') as f:
                            yaml.dump({}, f)
                        logger.info(f"Created new inventory file: {file_path}")
                    
                    yaml_alerts_data_group_by_owners[file_path].append(alert_yaml_file_data)
                    
                except Exception as e:
                    logger.error(f"Error processing alert data: {e}")
            else:
                logger.error("The key 'alert_owner_category' is missing in app['data']")
            
        return yaml_alerts_data_group_by_owners

    def sync_inventory_file(self, yaml_files, key_string, custom_output_name=None):
        """Sync inventory files based on the provided files."""
        logger.info("========================== Processing inventory update ============")
        
        # Add custom_output_name to each yaml file's data if provided
        if custom_output_name:
            for yaml_file in yaml_files:
                yaml_file['data']['custom_output_name'] = custom_output_name
        
        yaml_alerts_data_group_by_owners = self.group_by_yaml_alerts_data_owners(yaml_files)
        for _file, yaml_files_content in yaml_alerts_data_group_by_owners.items():
            logger.info(f"******** Updating following file {_file}")
            self.mms_sync(_file, yaml_files_content, key_string)
        return self.submit_to_create_pr()

    def read_sre_inventory(self):
        result = dict()
        path_list = self.sre_repo.clone_and_list_all_files()

        for file_path in path_list:
            file_name = file_path.rsplit("/", 1)[1].split(".")[0]
            service = self.is_allowed_path(ALLOWED_SERVICES, file_name)
            alert_type = self.is_allowed_path(SCANNED_FILE_TYPE, file_name)
            exclude = self.is_allowed_path(EXCLUDE_TYPE, file_name)

            if len(service) != 1 or len(alert_type) != 1 or len(exclude) > 0:
                continue

            try:
                yaml_obj = yaml.safe_load(open(file_path))
                service = service[0]
                alert_type = alert_type[0]
                alert_type_inventory = {
                    'file_path': file_path,
                    'changed': False,
                    'data': yaml_obj
                }
                # convert alert type
                if alert_type == "sre":
                    alert_type = "sre-alerts"
                else:
                    alert_type = "app-owner-alerts"

                if service not in result.keys():
                    result[service] = dict()
                result[service][alert_type] = alert_type_inventory
            except Exception as e:
                logger.error(str(e))

        return result

    # return threshold from alert files
    def read_mms_alert_threshold(self):
        path_list = self.mms_repo.clone_and_list_all_files()
        sub_path_list = [path_list[i:i + DEFAULT_SIZE] for i in range(0, len(path_list), DEFAULT_SIZE)]

        tasks = list()
        result = list()
        with concurrent.futures.ThreadPoolExecutor(max_workers=len(sub_path_list)) as thread_pool:
            for sub_list in sub_path_list:
                sub_task = thread_pool.submit(self.read_alert_files_to_get_threshold, sub_list)
                tasks.append(sub_task)

        for sub_task in tasks:
            each_result = sub_task.result()
            result.extend(each_result)

        thread_pool.shutdown(wait=True)

        return result

    def read_alert_files_to_get_threshold(self, sub_path_list):
        result = list()

        for file_path in sub_path_list:
            try:
                yaml_obj = yaml.safe_load(open(file_path))
                ret = self.extract_fields_from_file(yaml_obj, file_path)
                if len(ret[THRESHOLDS_KEY]) > 0:
                    result.append(ret)
            except Exception as e:
                logger.error(f"errors: {str(e)}")

        return result

    def extract_fields_from_file(self, file_content, file_path):
        tmp = file_path.rsplit("/", 3)
        ret = {
            "service": tmp[1],  # wcnp, oneops, kafka etc
            "type": tmp[2],  # sre/app
            "file_name": tmp[3].split(".")[0],  # file name, used as key in inventory
            THRESHOLDS_KEY: ""  # threshold
        }

        # to avoid some files have illegal chars
        if len(file_content) > 0:
            app = dict()
            thresholds = dict()

            if 'groups' not in file_content.keys():
                return None

            for group in file_content['groups']:
                rules = group['rules']

                for rule in rules:
                    if rule is None:
                        continue
                    # Cassandra template is special, not always have labels
                    if 'labels' not in rule.keys():
                        continue

                    for key, val in rule['labels'].items():
                        # we need only threshold
                        if THRESHOLDS in key:
                            thresholds[key] = val

            if len(thresholds) > 0:
                ret[THRESHOLDS_KEY] = thresholds

        return ret

    def compare_and_update_inventories(self, previous, current):
        try:
            service = current['service']
            alert_type = current['type']
            key = current['file_name']
            threshold = current[THRESHOLDS_KEY]
            old_obj = previous[service][alert_type]['data']

            if key in old_obj.keys():
                old_app_val = old_obj[key]
                # some have no threshold
                if THRESHOLDS_KEY in old_app_val.keys() and old_app_val[THRESHOLDS_KEY] is not None:
                    old_app_val[THRESHOLDS_KEY].update(threshold)
                    previous[service][alert_type]['changed'] = True
        except Exception as e:
            logger.error("compare_and_update_inventories" + str(e))

    def submit_to_create_pr(self):
        logger.info("Creating PR")
        result = self.sre_repo.submit_to_create_pr('intl-ecomm-svcs', 'sre-templates',
                                                   "mms sync", 'MMS update')

        # self.clean_up()
        return result

    def save_changed_file(self, sre_inventory):
        try:
            for service in sre_inventory.keys():
                for alert_type in sre_inventory[service].keys():
                    if sre_inventory[service][alert_type]['changed'] is True:
                        # write file
                        f = open(sre_inventory[service][alert_type]['file_path'], 'w')
                        yaml.dump(sre_inventory[service][alert_type]['data'], f, default_flow_style=False,
                                  sort_keys=False, explicit_start=True)
        except Exception as e:
            logger.error("save_changed_file" + str(e))

    def update_inventory_file(self, sre_inventory, alert_inventory):
        for alert_inventory_data in alert_inventory:
            self.compare_and_update_inventories(sre_inventory, alert_inventory_data)

        # check if changed, then create files and upload
        self.save_changed_file(sre_inventory)
        return self.submit_to_create_pr()

    def update(self):
        sre_inventory = self.read_sre_inventory()
        alert_inventory = self.read_mms_alert_threshold()
        result = self.update_inventory_file(sre_inventory, alert_inventory)

        return result

    def get_value_from_file(self, file_path, key):
        with open(file_path, 'r') as file:
            data = yaml.safe_load(file)
            if key in data:
                return data[key]
            else:
                return None

    def write_dict_to_yaml_file(self, path, key, value):
        with open(path, 'r') as yaml_file:
            data = yaml.safe_load(yaml_file)
        if data is None:
            data = {}
        data[key] = value
        with open(path, 'w') as yaml_file:
            yaml.safe_dump(data, yaml_file)
        return self.submit_to_create_pr()


if __name__ == "__main__":
    update = UpdateThreshold()

    print(update.get_inventory())

    # files = [{'data': {'alert_type': 'wcnp', 'alert_owner_category': 'sre', 'mms_slack_channel': 'temp',
    #                    'mms_email': '<EMAIL>',
    #                    'mms_xmatters_group': 'intl-sre-oncall1', 'alert_sla_name': 'gslb_health_current_threshold_pct',
    #                    'namespace': 'aurora-ca-analytics', 'app_name': 'assortment-growth-service-ca-prod',
    #                    'tier': 'one', 'market': 'MX',
    #                    'thresholds': {'pods_restarts_current_threshold_count': 4, 'cpu_usage_current_threshold_pct': 85,
    #                                   'memory_usage_current_threshold_pct': 85,
    #                                   'fiveXX_trend_comparing_to_one_week_ago_threshold_pct': 50,
    #                                   'fiveXX_current_threshold_count_used_for_trend': 10,
    #                                   'fiveXX_current_threshold_pct': 20,
    #                                   'traffic_spike_comparing_to_one_week_ago_threshold_pct': 75,
    #                                   'traffic_drop_comparing_to_one_week_ago_threshold_pct': 75,
    #                                   'latency_spike_comparing_to_one_week_ago_threshold_pct': 100,
    #                                   'scus_traffic_in_balance_current_threshold_pct': 10,
    #                                   'wus_traffic_in_balance_current_threshold_pct': 10,
    #                                   'eus_traffic_in_balance_current_threshold_pct': 10,
    #                                   'rate_limit_threshold_count': 0, 'quota_limit_threshold_pct': 95,
    #                                   'gslb_health_current_threshold_pct': 75},
    #                    'skip_rules': ['gslb_health_current_threshold_pct'], 'skip_xmatters': [], 'skip_slack': [],
    #                    'skip_email': []}}]

    # print(update.sync_inventory_file(files))
