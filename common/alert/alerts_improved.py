from libs.prometheus_client import Prometheus
import datetime
import concurrent.futures

class Alerts:
    """
    A class to interact with Prometheus and retrieve alert data.
    Attributes:
        n_minutes (int): The time frame in minutes for fetching alerts.
        component (str): The specific component to filter the alerts.
    """
    def __init__(self, n_minutes=5, component=None):
        """
        Initializes the Alerts class with Prometheus handler, time frame, and component.
        Args:
            n_minutes (int): The time frame in minutes for fetching alerts (default is 5).
            component (str, optional): The specific component to filter the alerts (default is None).
        """
        self._prometheus_handler = None
        self.n_minutes = n_minutes
        self.component = component
    @property
    def prometheus_handler(self):
        """
        Lazy loads and returns the Prometheus client handler.
        """
        if self._prometheus_handler is None:
            self._prometheus_handler = Prometheus()
        return self._prometheus_handler
    def _fetch_alerts(self, tier=None, market=None):
        """
        Fetches alert data from Prometheus based on the specified tier and market.
        Args:
            tier (str, optional): The application tier.
            market (str, optional): The market (e.g., mx, ca).
        Returns:
            list: A list of alert data.
        """
        try:
            alerts_data = self.prometheus_handler.get_all_alerts(tier=tier, n_minutes_data=self.n_minutes,
                                                                 market=market, tool="juno")
            return alerts_data["data"]["result"] if alerts_data else []
        except Exception as e:
            # Log the exception e or handle it as needed
            return []
    
    
    @staticmethod
    def add_timestamp(alerts):
        def convert_timestamp( timestamp):
            return datetime.datetime.fromtimestamp(timestamp).isoformat()
    
        for alert in alerts:
            if 'values' in alert and alert['values']:
                alert['metric']['timestamp'] = convert_timestamp(alert['values'][-1][0])
                alert['metric']['epoch_timestamp'] = alert['values'][-1][0]
    @staticmethod
    def _filter_alerts(alerts, alert_component=None, alert_names=None,tier=None,market=None):
        """
        Filters the given alerts based on the component and SLA names.
        Args:
            alerts (list): A list of alerts to be filtered.
            alert_component (str, optional): The component to filter alerts.
            alert_names (list of str, optional): The SLA names to filter alerts.
            tier (str, optional): The application tier.
            market (str, optional): The market (e.g., mx, ca).
        Returns:
            list: A filtered list of alerts.
        """
        
        filtered_alerts = [alert['metric'] for alert in alerts if alert['metric'].get('alertstate') == 'firing']
        Alerts.add_timestamp(alerts=alerts)
        
        
        if alert_component:
            filtered_alerts = [alert for alert in filtered_alerts if 'alert_type' in alert and alert_component.lower() in alert['alert_type'].lower()]
        if alert_names:
            filtered_alerts = [alert for alert in filtered_alerts if alert.get('alert_sla_name') in set(alert_names)]
        if tier:
            filtered_alerts = [alert for alert in filtered_alerts if tier.lower() in alert.get('tier', '').lower()]
        if market:
            filtered_alerts = [alert for alert in filtered_alerts if market.lower() in alert.get('market', '').lower()]
        filtered_alerts = Alerts._deduplicate_alerts(filtered_alerts)
        return filtered_alerts

    
    def _filter_alerts_by_criteria(self, alerts, criteria):
        """
        Filters the given alerts based on the specified criteria.
        Args:
            alerts (list): A list of alerts to be filtered.
            criteria (dict): A dictionary of criteria to filter alerts.
        Returns:
            list: A filtered list of alerts.
        """
        def matches_criteria(alert, criteria):
            for key, value in criteria.items():
                if alert.get(key) != value:
                    return False
            return True

        filtered_alerts = [alert for alert in alerts if matches_criteria(alert['metric'], criteria)]
        return filtered_alerts
    
    @staticmethod
    def _deduplicate_alerts(alerts):
        """
        Removes duplicate alerts based on the 'alert_id'.
        Args:
            alerts (list): A list of alerts.
        Returns:
            list: A deduplicated list of alerts.
        """
        seen = set()
        deduplicated_alerts = []
        for alert in alerts:
            alert_id = alert.get('alert_id')
            if alert_id and alert_id not in seen:
                seen.add(alert_id)
                deduplicated_alerts.append(alert)

        deduplicated_alerts.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
        return deduplicated_alerts


    def get_alerts_by_sla_names(self, alert_names, tier=None, market=None):
        """
        Fetches and returns alerts for the given SLA names.
        Args:
            alert_names (list of str): A list of SLA names to filter alerts.
            tier (str, optional): The application tier.
            market (str, optional): The market (e.g., mx, ca).
        Returns:
            list: A list of alerts matching the SLA names.
        """
        all_alerts = self._fetch_alerts(tier=tier, market=market)
        filtered_alerts = self._filter_alerts(all_alerts, alert_component=self.component, alert_names=alert_names)
        return Alerts._deduplicate_alerts(filtered_alerts)
    def get_all_alerts(self, tier=None, market=None):
        """
        Fetches and returns all alerts for the specified component.
        Args:
            tier (str, optional): The application tier.
            market (str, optional): The market (e.g., mx, ca).
        Returns:
            list: A list of all alerts for the specified component.
        """
        all_alerts = self._fetch_alerts(tier=tier, market=market)
        filtered_alerts = self._filter_alerts(all_alerts, alert_component=self.component)
        return Alerts._deduplicate_alerts(filtered_alerts)
    
    @staticmethod
    def get_alerts_by_criteria(criteria={}, n_minutes=60,start_epoch=None,end_epoch=None, component="",alert_names=None ,tier=None,market=None,origin_timestamp=False):
        """
        Fetches and returns alerts based on the specified criteria.
        Args:
            prometheus_handler: The prometheus handler instance.
            n_minutes (int): The number of minutes of data to fetch.
            component (str): The alert component.
            criteria (dict): A dictionary of criteria to filter alerts.
            start_epoch (int, optional): The start epoch time.
            end_epoch (int, optional): The end epoch time.
        Returns:
            list: A list of alerts matching the criteria.
        """
        prometheus_handler = Prometheus()
        all_alerts = prometheus_handler.get_all_alerts_improved(n_minutes_data=n_minutes, tool="juno", **criteria,start_epoch=start_epoch,end_epoch=end_epoch)
        all_alerts = all_alerts["data"]["result"] if all_alerts else []
        filtered_alerts = Alerts._filter_alerts(all_alerts, alert_component=component,alert_names=alert_names,tier=tier,market=market)
        if origin_timestamp:
            Alerts._add_latest_origin_timestamp(filtered_alerts)
        
        # filtered_alerts = Alerts._filter_alerts_by_criteria(all_alerts, criteria)
        return filtered_alerts
    
    @staticmethod
    def get_alert_by_id( alert_id,n_minutes=24*60,start_epoch=None,end_epoch=None):
        """
        Fetches and returns an alert by its ID.
        Args:
            alert_id (str): The ID of the alert to fetch.
            n_minutes (int): The number of minutes of data to fetch.
            start_epoch (int, optional): The start epoch time.
            end_epoch (int, optional): The end epoch time.
        Returns:
            dict: The alert matching the ID.
        """
        alerts = Alerts.get_alerts_by_criteria(criteria={"alert_id":alert_id}, n_minutes=n_minutes,start_epoch=start_epoch,end_epoch=end_epoch)
        return alerts
    
    @staticmethod
    def _add_latest_origin_timestamp(alerts):
        """
        Adds the latest origin timestamp to each alert where there is a gap > 60 seconds
        between consecutive timestamps, indicating a new alert occurrence.
        
        Args:
            alerts (list): A list of alerts to process.
            
        Note:
            - Uses ThreadPoolExecutor for concurrent processing
            - Searches for gaps > 60 seconds between timestamps to identify alert origins
            - Falls back to first timestamp if no gaps found
        """
        if not alerts:
            return

        prometheus_handler = Prometheus()
        
        def fetch_and_update(alert_index, alert_id):
            try:
                # Query last 7 days of data for the alert
                response = prometheus_handler.get_all_alerts_improved(
                    n_minutes_data=24*60*7,
                    tool="juno",
                    alert_id=alert_id
                )
                
                if not response or "data" not in response or "result" not in response["data"] or not response["data"]["result"]:
                    return
                    
                alert_data = response["data"]["result"][0]
                if not alert_data.get('values'):
                    return
                    
                values = alert_data['values']
                
                # Find the most recent gap > 60 seconds between timestamps
                for i in range(len(values) - 1, 0, -1):
                    current_timestamp = values[i][0]
                    prev_timestamp = values[i-1][0]
                    
                    if (current_timestamp - prev_timestamp) > 60:
                        alerts[alert_index]['latest_origin_timestamp'] = prev_timestamp
                        return
                        
                # If no gap found, use the first timestamp
                alerts[alert_index]['latest_origin_timestamp'] = values[0][0]
                
            except Exception as e:
                print(f"Error processing alert ID {alert_id}: {str(e)}")
                # Optionally, you could add logging here
                # logging.error(f"Error processing alert ID {alert_id}: {str(e)}")

        # Process alerts in parallel with bounded concurrency
        max_workers = max(1, min(32, len(alerts)))
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [
                executor.submit(fetch_and_update, i, alert.get('alert_id'))
                for i, alert in enumerate(alerts)
                if alert.get('alert_id')  # Only process alerts with valid IDs
            ]
            
            # Wait for all tasks to complete
            concurrent.futures.wait(futures, return_when=concurrent.futures.ALL_COMPLETED)




if __name__ == "__main__":
    # alerts = Alerts(component="",n_minutes=360)
    # alerts = Alerts.get_alerts_by_criteria(criteria={"mms_xmatters_group": "intl-sre-oncall",
    #                                                  "alert_id":"dcdfe3ca77c947c981f6f90fbe4b510a_8"})
    alerts = Alerts.get_alerts_by_criteria(criteria={'namespace': 'intl-fms-scheduler-mx','mms_xmatters_group': 'intl-sre-oncall'
                                                  },n_minutes=240,origin_timestamp=True)
    # alerts = Alerts.get_alert_by_id(alert_id="dcdfe3ca77c947c981f6f90fbe4b510a_8",n_minutes=3600)
    for alert in alerts:
        print(alert)

    print(len(alerts))

