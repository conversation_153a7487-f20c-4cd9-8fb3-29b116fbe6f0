# from rest_framework.response import Response
from common.alert.alertsPostDataBuilder import AlertsPostDataBuilder
from template_engine.manager import TemplateManager
from template_engine.compile import get_latest_templates
import logging
logger = logging.getLogger(__name__)


def execute_custom_inventory_data_file(template, custom_inventory_file, tier=None, is_sre_sla=False,force_pull=False,sre_override_xmatters_and_slack=True):
    get_latest_templates(force_pull_required=force_pull)
    persist_data = False
    alert_data = AlertsPostDataBuilder(template, is_sre_sla=is_sre_sla, tier=tier,custom_inventory_file=custom_inventory_file)
    data = alert_data.build_post_body(is_sre_sla=is_sre_sla, persist_data=persist_data,sre_override_xmatters_and_slack=sre_override_xmatters_and_slack)
    return create_alerts(template,data)

def create_alerts(template, data, required_pre_process=False):
    try:
        tm = TemplateManager(template, data, execute_child_templates=False)
        status, res = tm.execute()
        if status== 200:
            return True,{"ok": True, "body": res, "status": status}
            # return Response({"ok": True, "body": res}, status=200)
        return True,{"ok": True, "body": res, "status": status}
        # return Response({"ok": True, "body": res}, status=400)

    except Exception as e:
        logger.exception(e)
        # return Response({"ok": False, "body": str(e)})
        return False,{"ok": False, "body": str(e)}
