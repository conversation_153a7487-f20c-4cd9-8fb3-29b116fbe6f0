from common.alert.alerts_improved import Alerts


class Megacache:

    def __init__(self, n_minutes=5):
        self.alert_handler = Alerts(n_minutes=n_minutes, component='MeghaCache')
        self.n_minutes_data = n_minutes

    def get_alerts_by_name(self, alert_name, tier=None, market=None):
        """
                                Gets all alerts for list of sla_names
                                Args:
                                    alert__name: list of strings consisting of sla names
                                    tier: app tier
                                    market: market (mx,ca...)

                                Returns:
                                return list of alerts(dict)
                                """
        return Alerts.get_alerts_by_criteria(alert_names=alert_name,tier=tier,market=market)

    def get_all_alerts(self, tier=None, market=None):
        """
                                        Gets all alerts triggered
                                        Args:
                                            tier: app tier
                                            market: market (mx,ca...)

                                        Returns:
                                        return list of alerts(dict)
                                        """

        alerts = Alerts.get_alerts_by_criteria(criteria={},component="MeghaCache",tier=tier,market=market)

        # Method for Node Availability metrics
    def get_node_availability(self, tier=None, market=None):
        return self.get_alerts_by_name(["node_availability_threshold_pct"], tier, market)

        # Method for Latency metrics
    def get_latency_metrics(self, tier=None, market=None):
        latency_metrics = ["ping_latency_threshold_count"]
        return self.get_alerts_by_name(latency_metrics, tier, market)

        # Method for Queue Depth metrics
    def get_queue_depth_metrics(self, tier=None, market=None):
        queue_depth_metrics = [
                "inflight_queue_depth_threshold_count",
                "pending_queue_depth_threshold_count"
            ]
        return self.get_alerts_by_name(queue_depth_metrics, tier, market)

        # Method for Traffic Spike metrics
    def get_traffic_spike_metrics(self, tier=None, market=None):
        traffic_spike_metrics = [
                "ingress_get_traffic_spike_threshold_pct",
                "ingress_set_traffic_spike_threshold_pct"
            ]
        return self.get_alerts_by_name(traffic_spike_metrics, tier, market)

        # Method for TKO (Total Knock Out) metrics
    def get_tko_metrics(self, tier=None, market=None):
        tko_metrics = ["hard_tko_threshold_count", "soft_tko_threshold_count"]
        return self.get_alerts_by_name(tko_metrics, tier, market)

        # Method for Miscellaneous metrics
    def get_miscellaneous_metrics(self, tier=None, market=None):
        miscellaneous_metrics = [
                "cache_timeout_threshold_count",
                "cpu_threshold_sla_pct",
                "connection_yield_threshold_pct"
            ]
        return self.get_alerts_by_name(miscellaneous_metrics, tier, market)

