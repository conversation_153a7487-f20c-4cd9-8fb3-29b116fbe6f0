from common.alert.alerts_improved import Alerts


class Solr:

    def __init__(self, n_minutes=5):
        self.n_minutes_data = n_minutes

    def get_alerts_by_name(self, alert_name, tier=None, market=None):
        """
                                Gets all alerts for list of sla_names
                                Args:
                                    alert__name: list of strings consisting of sla names
                                    tier: app tier
                                    market: market (mx,ca...)

                                Returns:
                                return list of alerts(dict)
                                """
        return Alerts.get_alerts_by_criteria(alert_names=alert_name,n_minutes=self.n_minutes_data,tier=tier,market=market)

    def get_all_alerts(self, tier=None, market=None):
        """
                                        Gets all alerts triggered
                                        Args:
                                            tier: app tier
                                            market: market (mx,ca...)

                                        Returns:
                                        return list of alerts(dict)
                                        """

        alerts = Alerts.get_alerts_by_criteria(criteria={},component="solr",n_minutes=self.n_minutes_data,tier=tier,market=market)

    def get_core_metrics(self, tier=None, market=None):
        core_metrics = [
            "core_down_threshold_count",
            "core_recovery_threshold_count",
            "cores_recovery_failed_threshold_pct",
            "shards_down_threshold_pct",
            "cores_maxmin_threshold_pct",  # Assuming this is correctly listed twice and is not an error
        ]
        return self.get_alerts_by_name(core_metrics, tier, market)

    # Method for CPU-related metrics
    def get_cpu_metrics(self, tier=None, market=None):
        cpu_metrics = [
            "cpu_user_threshold_pct",
            "cpu_system_threshold_pct",
            "cpu_steal_threshold_pct",
            "cpu_iowait_threshold_pct",
        ]
        return self.get_alerts_by_name(cpu_metrics, tier, market)

    # Method for Storage and Document metrics
    def get_storage_doc_metrics(self, tier=None, market=None):
        storage_doc_metrics = [
            "max_doc_threshold_count",
            "root_storage_threshold_pct",
            "app_storage_threshold_pct",
            "tlog_size_threshold_count",
        ]
        return self.get_alerts_by_name(storage_doc_metrics, tier, market)

    # Method for Miscellaneous metrics
    def get_miscellaneous_metrics(self, tier=None, market=None):
        miscellaneous_metrics = [
            "high_field_cache_threshold_count",
            "oom_threshold_sla_pct",
        ]
        return self.get_alerts_by_name(miscellaneous_metrics, tier, market)

