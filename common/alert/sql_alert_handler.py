from common.alert.alerts_improved import Alerts


class Sql:

    def __init__(self,n_minutes=5):
        self.n_minutes_data = n_minutes



    def get_alerts_by_name(self, alert_name, tier=None, market=None):

        """
                                Gets all alerts for list of sla_names
                                Args:
                                    alert__name: list of strings consisting of sla names
                                    tier: app tier
                                    market: market (mx,ca...)

                                Returns:
                                return list of alerts(dict)
                                """
        return Alerts.get_alerts_by_criteria(alert_names=alert_name,n_minutes=self.n_minutes_data,tier=tier,market=market)



    def get_all_alerts(self, tier=None, market=None):
        """
                                        Gets all alerts triggered
                                        Args:
                                            tier: app tier
                                            market: market (mx,ca...)

                                        Returns:
                                        return list of alerts(dict)
                                        """

        alerts = Alerts.get_alerts_by_criteria(criteria={},component="azure_sql_server",n_minutes=self.n_minutes_data,tier=tier,market=market)

        return alerts


    def get_db_offline(self,tier=None,market=None):

        return self.get_alerts_by_name(['sqldb_offline_current_threshold_count'], tier, market)



    def get_cpu_utilization(self,tier=None,market=None):

        return self.get_alerts_by_name(['cpu_threshold_sla_pct'], tier, market)


    def get_storage_utilization(self,tier=None,market=None):

        return self.get_alerts_by_name(['sqldb_storage_usage_threshold_pct'], tier, market)

    def get_deadlocks(self,tier=None,market=None):

        return self.get_alerts_by_name(['cpu_threshold_sla_pct'], tier, market)


    def get_latency(self,tier=None,market=None):

        return self.get_alerts_by_name(['sqldb_dataio_latency_threshold_pct'], tier, market)


    def get_logwrite(self, tier=None, market=None):
        return self.get_alerts_by_name(['sqldb_logwrite_threshold_pct'], tier, market)


    def get_replication_lag(self, tier=None, market=None):
        return self.get_alerts_by_name(['sqldb_replication_lag_threshold_count'], tier, market)

    def get_tempo_lag(self, tier=None, market=None):
        return self.get_alerts_by_name(['sqldb_templog_usage_threshold_pct'], tier, market)

    def get_sqldbworkers_high(self, tier=None, market=None):
        return self.get_alerts_by_name(['sqldb_workers_threshold_pct'], tier, market)








