#!/bin/bash

echo "Creating .ssh folder "
mkdir -p /app/.ssh

echo "Copying Keys to .ssh folder "
cp  /app/juno/config/config /app/.ssh/config
cp  /app/juno/config/gieo_rsa /app/.ssh/gieo_rsa
cp  /app/juno/config/id_Salish_rsa /app/.ssh/id_rsa
cp  /app/juno/config/jenkins-id_rsa /app/.ssh/jenkins-id_rsa
cp  /app/juno/config/id_rsa_2024 /app/.ssh/id_rsa_2024


echo "Changing keys permissions "
chmod 700 /app/.ssh
chmod 600 /app/.ssh/jenkins-id_rsa
chmod 600 /app/.ssh/id_rsa
chmod 600 /app/.ssh/gieo_rsa
chmod 600 /app/.ssh/id_rsa_2024

#ARG1=${1:-api}
#echo "Running entrypoint script"
#echo "Argument 1 passed (or defaulted): $ARG1"
echo "Running entrypoint script"
echo "Environment variable MODE: ${MODE:-api}"

if [ "$MODE" == "api" ]; then
    export PATH=$PATH:/app/.local/bin
    cd /app/juno && gunicorn --workers=8 --threads=2 --worker-class=gthread juno.wsgi -b 0.0.0.0:8080 --timeout 900
elif [ "$MODE" == "bot" ]; then
    python3.6 -m slack_bot.bot
elif [ "$MODE" == "hardsync" ]; then
    python3.6 /app/juno/job/mms_to_sre.py hardsync
else
    echo "User not provided valid options. Exiting."
    exit 1
fi
