from django.shortcuts import render
import time, os, logging
from rest_framework.response import Response
from rest_framework.decorators import api_view
from libs.wcnp_handler import provide_wcnp_metrics
from teap.views import process_search_namespace_assembly_db_field, \
    normalize_get_apps_by_platform_and_tier_market, normalize_get_apps_by_platform_and_market, \
    normalize_get_apps_by_platform_and_market_and_domain
from concurrent.futures import ThreadPoolExecutor
from libs.file_handler import create_hpa_data
from slack_bot.slack import send_message
logger = logging.getLogger(__name__)


@api_view(['GET'])
def wcnp_info(request, namespace):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        logger.info(f"Getting data for {namespace}")
        pr = provide_wcnp_metrics(namespace)
        return Response({"ok": True, "body": pr}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def download_app_details(request):
    try:
        from libs.file_handler import download_alert_content
        # file_path = '/Users/<USER>/git/juno/output/1681331099029755000/cart-page-ca-cart-page.yaml'
        # FilePointer = open(file_path, "r")
        # response = HttpResponse(FilePointer, content_type='application/msword')
        # response['Content-Disposition'] = 'attachment; filename=NameOfFile'

        return download_alert_content({"name": "hello"})
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def get_all_wcnp_apps_hpa_values_by_tier_by_mx_market(request, tier):
    send_message("juno_logs", f"User called , {request.get_full_path()}")
    results = normalized_get_all_wcnp_apps_hpa_values(platform="wcnp", tier=tier, market="MX")
    data = normalize_wcnp_hpa_data(results)
    args = request.query_params
    if args.get("download"):
        return create_hpa_data(data)
    return Response({"ok": False, "body": {"message": data}}, status=200)


@api_view(['GET'])
def get_all_wcnp_apps_hpa_values_by_tier_0_by_mx_market(request):
    send_message("juno_logs", f"User called , {request.get_full_path()}")
    results = normalized_get_all_wcnp_apps_hpa_values(platform="wcnp", tier="zero", market="MX")
    data = normalize_wcnp_hpa_data(results)
    args = request.query_params
    if args.get("download"):
        return create_hpa_data(data)
    return Response({"ok": False, "body": {"message": data}}, status=200)


@api_view(['GET'])
def get_all_wcnp_apps_hpa_values_by_tier_1_by_mx_market(request):
    send_message("juno_logs", f"User called , {request.get_full_path()}")
    results = normalized_get_all_wcnp_apps_hpa_values(platform="wcnp", tier="one", market="MX")
    data = normalize_wcnp_hpa_data(results)
    args = request.query_params
    if args.get("download"):
        return create_hpa_data(data)
    return Response({"ok": False, "body": {"message": data}}, status=200)


@api_view(['GET'])
def get_all_wcnp_apps_hpa_values_by_tier_all_by_mx_market(request):
    send_message("juno_logs", f"User called , {request.get_full_path()}")
    results = normalized_get_all_wcnp_by_market_all_tier(platform="wcnp", market="MX")
    data = normalize_wcnp_hpa_data(results)
    args = request.query_params
    if args.get("download"):
        return create_hpa_data(data)
    return Response({"ok": False, "body": {"message": data}}, status=200)


@api_view(['GET'])
def get_all_wcnp_apps_hpa_values_by_tier_all_by_mx_market_by_de_domain(request, market):
    send_message("juno_logs", f"User called , {request.get_full_path()}")
    results = normalize_get_apps_by_platform_and_market_and_domain(platform="wcnp", market=market,
                                                                   domain="Digital Experience")
    args = request.query_params
    return send_wcnp_hpa_domain_data(args,results)


@api_view(['GET'])
def get_all_wcnp_apps_hpa_values_by_tier_all_by_mx_market_by_mrch_domain(request, market):
    send_message("juno_logs", f"User called , {request.get_full_path()}")
    results = normalize_get_apps_by_platform_and_market_and_domain(platform="wcnp", market=market,
                                                                   domain="Merchandising")
    args = request.query_params
    return send_wcnp_hpa_domain_data(args,results)


def send_wcnp_hpa_domain_data(args,results):
    _apps_results = process_hpa_data_parallel(results)
    data = normalize_wcnp_hpa_data(_apps_results)
    logger.info(f"args {args}")
    data = filter_data(args, data)
    if args.get("download"):
        return create_hpa_data(data)
    return Response({"ok": False, "body": {"message": data}}, status=200)

@api_view(['GET'])
def get_all_wcnp_apps_hpa_values_by_tier_all_by_mx_market_by_if_domain(request, market):
    send_message("juno_logs", f"User called , {request.get_full_path()}")
    results = normalize_get_apps_by_platform_and_market_and_domain(platform="wcnp", market=market,
                                                                   domain="Inventory & Fulfillment")
    args = request.query_params
    return send_wcnp_hpa_domain_data(args,results)


@api_view(['GET'])
def get_all_wcnp_apps_hpa_values_by_tier_all_by_mx_market_by_if_domain(request, market):
    send_message("juno_logs", f"User called , {request.get_full_path()}")
    results = normalize_get_apps_by_platform_and_market_and_domain(platform="wcnp", market=market,
                                                                   domain="Customer Engagement")
    args = request.query_params
    return send_wcnp_hpa_domain_data(args,results)


@api_view(['GET'])
def get_all_wcnp_apps_hpa_values_by_tier_all_by_ca_market(request):
    send_message("juno_logs", f"User called , {request.get_full_path()}")
    results = normalized_get_all_wcnp_by_market_all_tier(platform="wcnp", market="CA")
    data = normalize_wcnp_hpa_data(results)
    args = request.query_params
    if args.get("download"):
        return create_hpa_data(data)
    return Response({"ok": False, "body": {"message": data}}, status=200)


def normalized_get_all_wcnp_apps_hpa_values(platform="wcnp", tier="zero", market="MX"):
    apps = normalize_get_apps_by_platform_and_tier_market(platform=platform, tier=tier, market=market)
    return process_hpa_data_parallel(apps)


def normalized_get_all_wcnp_by_market_all_tier(platform="wcnp", market="MX"):
    apps = normalize_get_apps_by_platform_and_market(platform=platform, market=market)
    return process_hpa_data_parallel(apps)


def process_hpa_data_parallel(apps):
    futures = []
    results = list()
    for app in apps:
        try:
            logger.info(f"Processing {app['wcnp'].get('namespaceOrAssemblyOrDB')}")
            pool = ThreadPoolExecutor(25)
            futures.append(pool.submit(provide_wcnp_metrics, app["wcnp"].get('namespaceOrAssemblyOrDB')))
        except Exception as e:
            logger.exception(e)
    for future in futures:
        try:
            _results = future.result()
            results.append(_results)
        except Exception as e:
            logger.exception(e)

    return results


def fileter_normalize_wcnp_hpa_data(data, filter_key, filter_value):
    res = list()
    for x in data:
        if x.get(filter_key):
            logger.info(f" x.get(filter_key) {x.get(filter_key)}")
            if x.get(filter_key, 0) >= int(filter_value):
                res.append(x)

    return res


def filter_data(args, data):
    logger.info(f"inside filter {args}")
    # for k,v in args.items():
    #     print(k)
    #     print(v)
    if "prob_live_probe_interval" in args:
        data = fileter_normalize_wcnp_hpa_data(data, "prob_live_probe_interval",
                                               float(args.get("prob_live_probe_interval", 0)))
    if "prob_live_wait" in args:
        data = fileter_normalize_wcnp_hpa_data(data, "prob_live_wait", float(args.get("prob_live_wait", 0)))
    if "prob_live_time_out" in args:
        data = fileter_normalize_wcnp_hpa_data(data, "prob_live_time_out", float(args.get("prob_live_time_out", 0)))
    if "prob_ready_probe_interval" in args:
        data = fileter_normalize_wcnp_hpa_data(data, "prob_ready_probe_interval",
                                               float(args.get("prob_ready_probe_interval", 0)))
    if "prob_ready_wait" in args:
        data = fileter_normalize_wcnp_hpa_data(data, "prob_ready_wait", float(args.get("prob_ready_wait", 0)))
    if "prob_ready_time_out" in args:
        data = fileter_normalize_wcnp_hpa_data(data, "prob_ready_time_out", float(args.get("prob_ready_time_out", 0)))
    return data


# nornalize_get_apps_by_platform_and_market
def normalize_wcnp_hpa_data(results):
    data = list()
    for app in results:
        try:
            for _k, _v in app.items():
                for cluster in _v.get("clusters"):
                    current_pods = cluster.get("pods")
                    current_cluster = cluster.get("mm_name")
                    namespace = cluster.get("namespace")
                    if cluster.get("scaling", dict()).get("horizontal", dict()):
                        hap_min = cluster.get("scaling").get("horizontal").get("scaling").get("min")
                        hap_max = cluster.get("scaling").get("horizontal").get("scaling").get("max")
                        hap_enabled = cluster.get("scaling").get("horizontal").get("is_enabled")
                    else:
                        hap_min = None
                        hap_max = None
                        hap_enabled = None
                    if cluster.get("scaling", dict()).get("horizontal", dict()).get("hpa", dict()):
                        hap_criteria = cluster.get("scaling").get("horizontal").get("hpa").get("criteria")
                        hap_target = cluster.get("scaling").get("horizontal").get("hpa").get("target")
                    else:
                        hap_criteria = None
                        hap_target = None
                    if cluster.get("prob", dict()).get("liveness_probs", dict()):
                        prob_live_probe_interval = cluster.get("prob").get("liveness_probs").get("probe_interval")
                        prob_live_wait = cluster.get("prob").get("liveness_probs").get("wait")
                        prob_live_time_out = cluster.get("prob").get("liveness_probs").get("time_out")
                        prob_ready_probe_interval = cluster.get("prob").get("ready_probs").get("probe_interval")
                        prob_ready_wait = cluster.get("prob").get("ready_probs").get("wait")
                        prob_ready_time_out = cluster.get("prob").get("ready_probs").get("time_out")
                    else:
                        prob_live_probe_interval = None
                        prob_live_wait = None
                        prob_live_time_out = None
                        prob_ready_probe_interval = None
                        prob_ready_wait = None
                        prob_ready_time_out = None

                    data.append({"current_pods": current_pods, "current_cluster": current_cluster,
                                 "namespace": namespace, "hap_min": hap_min, "hap_max": hap_max,
                                 "hap_enabled": hap_enabled,
                                 "hap_criteria": hap_criteria, "hap_target": hap_target,
                                 "app": _k, "prob_live_probe_interval": prob_live_probe_interval,
                                 "prob_live_wait": prob_live_wait, "prob_live_time_out": prob_live_time_out,
                                 "prob_ready_probe_interval": prob_ready_probe_interval,
                                 "prob_ready_wait": prob_ready_wait,
                                 "prob_ready_time_out": prob_ready_time_out
                                 })
        except Exception as e:
            logger.exception(e)

    return data
