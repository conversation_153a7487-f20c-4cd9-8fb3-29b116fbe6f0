# #!/usr/bin/env python3
# # yamls/haps_modifier.py
# """
# Tool for updating HPA configurations in Kitt YAML files.
# """
# import argparse
# from kitt_handler.core.config_updater import process_yaml_files, update_hpa_config
#
#
# def main():
#     parser = argparse.ArgumentParser(description="Update HPA configuration in Kitt YAML files")
#     parser.add_argument("yaml_file", help="Path to the Kitt YAML file or directory")
#     parser.add_argument("--min-pods", type=int, required=True, help="New minimum pod count")
#     parser.add_argument("--min-cpu", help="New target CPU utilization percentage or value")
#     parser.add_argument("--min-memory", help="New memory value (e.g., '512Mi', '1Gi')")
#     parser.add_argument("--dry-run", action="store_true", help="Print changes without modifying files")
#     parser.add_argument("--env-name", help="Only update configurations for this environment name")
#     parser.add_argument("--file", help="Process a specific file referenced in the main YAML")
#     args = parser.parse_args()
#
#     # If a specific file is provided, process that directly
#     if args.file:
#         update_hpa_config(args.file, args.min_pods, args.min_cpu, args.min_memory, args.dry_run, args.env_name,
#                           args.deployment)
#         return
#
#     # Otherwise process the target file or directory
#     process_yaml_files(
#         args.yaml_file,
#         args.min_pods,
#         args.min_cpu,
#         args.min_memory,
#         args.dry_run,
#         args.env_name
#     )
# if __name__ == "__main__":
#     main()