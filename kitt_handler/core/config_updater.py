# kitt/config_updater.py
"""
Configuration update utilities for Kitt YAML files.
"""
import re, logging
from pathlib import Path
from kitt_handler.core.file_utils import load_yaml, read_file_lines, write_file_lines, find_kitt_common_file
from kitt_handler.core.output import print_update, print_summary, print_error
from kitt_handler.core.file_utils import find_yaml_files

logger = logging.getLogger(__name__)


def process_yaml_files(target, min_pods, min_cpu=None, min_memory=None, dry_run=False, env_name=None,
                       artifact_name=None, return_files=False):
    """Process a directory of YAML files or a single file."""
    processed_files = set()  # Track processed files to avoid duplicates
    files_used = []
    files_modified = set()

    if Path(target).is_dir():
        yaml_files = find_yaml_files(target)
        total_files = len(yaml_files)

        logger.info(f"Found {total_files} YAML files to process")
        if env_name:
            logger.info(f"Only updating configurations for environment: {env_name}")
        if artifact_name:
            logger.info(f"Only processing files with artifact: {artifact_name}")

        for yaml_file in yaml_files:
            file_path = Path(yaml_file)
            file_name = file_path.name
            # Skip non-Kitt files that don't contain "kitt" in their name
            if "kitt" not in str(file_name).lower():
                logger.info(f"Skipping non-Kitt file: {yaml_file}")
                continue
            if str(yaml_file) in files_modified:
                logger.info(f"Skipping already processed file: {yaml_file}")
                continue
            # Skip already processed files
            if str(yaml_file) in processed_files:
                continue

            files_used.append(str(yaml_file))

            # Process this file and count if it was updated
            is_updated =update_hpa_config(str(yaml_file), min_pods, min_cpu, min_memory, dry_run, env_name,
                                          artifact_name, is_primary=True, processed_files=processed_files,
                                          files_modified=files_modified)
            if is_updated:
                files_modified.add(str(target))
            processed_files.add(str(yaml_file))
        return {"files_used": files_used, "files_modified": files_modified}
    else:
        # Process single file
        files_used.append(str(target))
        is_updated = update_hpa_config(target, min_pods, min_cpu, min_memory, dry_run, env_name,
                                       artifact_name, is_primary=True)
        if is_updated:
            files_modified.add(str(target))
        return {"files_used": files_used, "files_modified": files_modified}


def update_hpa_config(yaml_file, min_pods, min_cpu=None, min_memory=None, dry_run=False, env_name=None,
                      artifact_name=None, is_primary=False, processed_files=None,files_modified=None):

    try:
        file_path = Path(yaml_file)
        file_name = file_path.name
        logger.info(f"Processing {file_name}...")

        content = load_yaml(yaml_file)
        if "kitt" not in yaml_file:
            logger.info(f"Skipping non-Kitt file: {yaml_file}")
            return False

        if not content:
            logger.info(f"Empty or invalid YAML file: {yaml_file}")
            return False

        if not is_primary and artifact_name and not contains_artifact(content, artifact_name):
            logger.info(f"Skipping {file_name} - does not contain artifact: {artifact_name}")
            return False

        # Recursively process references and collect modified files
        process_kitt_common_references(
            yaml_file, content, min_pods, min_cpu, min_memory,
            dry_run, env_name, artifact_name, processed_files,files_modified
        )

        file_updated = update_config_in_place(yaml_file, min_pods, min_cpu, min_memory, dry_run, env_name)
        return file_updated

    except Exception as e:
        print_error(f"Error processing {yaml_file}: {str(e)}")
        return False


def check_for_references(content):
    """Check if the YAML content has any references to other files."""
    # Check profiles section
    if 'profiles' in content and isinstance(content['profiles'], list):
        for profile in content['profiles']:
            if isinstance(profile, str) and (profile.startswith('rel://') or profile.startswith('git://')):
                return True

    # Check for build.postBuild tasks with kittFilePath references
    if 'build' in content and 'postBuild' in content['build']:
        post_build = content['build']['postBuild']
        if isinstance(post_build, list):
            for build_step in post_build:
                if isinstance(build_step, dict) and 'task' in build_step:
                    task = build_step['task']
                    if isinstance(task, dict) and 'kittFilePath' in task:
                        return True

    return False


def process_kitt_common_references(yaml_file, content, min_pods, min_cpu, min_memory, dry_run, env_name,
                                   artifact_name=None, processed_files=None,files_modified=None):
    """Process kitt.common references in the YAML content."""
    files_updated = 0
    referenced_files = []  # Collect all references before processing

    if processed_files is None:
        processed_files = set()

    logger.info(f"\n🔍 Checking for references in {Path(yaml_file).name}...")

    # Case 1: Check profiles section
    if 'profiles' in content and isinstance(content['profiles'], list):
        for profile in content['profiles']:
            if not isinstance(profile, str):
                continue

            # Handle relative references - rel://kitt/kitt.common
            if profile.startswith('rel://'):
                logger.info(f"  Found profile reference: {profile}")
                kitt_common_path = find_kitt_common_file(yaml_file, profile)
                if kitt_common_path:
                    referenced_files.append(kitt_common_path)
                else:
                    logger.info(f"  ⚠️ Referenced file not found: {profile}")

    # Case 2: Check for build.postBuild tasks with kittFilePath references
    if 'build' in content and 'postBuild' in content['build']:
        post_build = content['build']['postBuild']
        if isinstance(post_build, list):
            for build_step in post_build:
                if isinstance(build_step, dict) and 'task' in build_step:
                    task = build_step['task']
                    if isinstance(task, dict) and 'kittFilePath' in task:
                        kitt_file_path = task['kittFilePath']
                        logger.info(f"  Found kittFilePath reference: {kitt_file_path}")

                        # Determine the full path to the referenced file
                        yaml_dir = Path(yaml_file).parent
                        referenced_file = yaml_dir / kitt_file_path

                        if referenced_file.exists():
                            referenced_files.append(str(referenced_file))
                        else:
                            logger.info(f"  ⚠️ Referenced file not found: {kitt_file_path}")

    # Process unique referenced files
    if referenced_files:
        logger.info(f"  📄 Found {len(referenced_files)} referenced files")
        for file_path in referenced_files:
            if file_path in processed_files:
                logger.info(f"  Skipping already processed file: {Path(file_path).name}")
                continue  # Skip already processed files

            processed_files.add(file_path)

            logger.info(f"\n🔄 Processing referenced file: {Path(file_path).name}")
            # Check if this referenced file contains the artifact before processing
            if artifact_name:
                ref_content = load_yaml(file_path)
                has_artifact = contains_artifact(ref_content, artifact_name)
                logger.info(
                    f"  Checking for artifact '{artifact_name}': {'✅ Found' if has_artifact else '❌ Not found'}")

                if not has_artifact:
                    logger.info(f"  Skipping {Path(file_path).name} - does not contain artifact: {artifact_name}")
                    continue

            # Process referenced file
            updated = update_hpa_config(file_path, min_pods, min_cpu, min_memory, dry_run, env_name,
                                                  artifact_name, is_primary=False, processed_files=processed_files,
                                                  files_modified=files_modified)
            if updated:
                files_updated += 1
                files_modified.add(file_path)
                logger.info(f"  ✅ Updated configurations in {Path(file_path).name}")
            else:
                logger.info(f"  ℹ️ No matching configurations updated in {Path(file_path).name}")

    # If no referenced files were found, list available files in the directory
    else:
        logger.info("  No references found in this file")

    logger.info(f"  Total files updated from references: {files_updated}")
    return files_updated


def contains_artifact(content, artifact_name):
    """
    Recursively check if YAML content contains the specified artifact name.
    Searches through all nested dictionaries and lists at any depth.
    """
    # Base case: content is None or not a container type
    if content is None or not (isinstance(content, dict) or isinstance(content, list)):
        return False

    # Dictionary case
    if isinstance(content, dict):
        # Direct check for artifact field
        if 'artifact' in content and content['artifact'] == artifact_name:
            return True

        # Recursively check all values
        for value in content.values():
            if contains_artifact(value, artifact_name):
                return True

    # List case - recurse into each element
    elif isinstance(content, list):
        for item in content:
            if contains_artifact(item, artifact_name):
                return True

    # If we've checked everything and not found a match
    return False


def update_config_in_place(yaml_file, min_pods, min_cpu, min_memory, dry_run, env_name):
    """Make in-place updates to configuration values in the file."""
    file_name = Path(yaml_file).name
    lines = read_file_lines(yaml_file)
    if not lines:
        return False

    logger.info(f"\n🔍 Analyzing {file_name} for scaling configurations...")

    # Debug: Scan for scaling patterns in the file
    scaling_lines = []
    for i, line in enumerate(lines):
        stripped = line.lstrip()
        if 'scaling:' in line or 'min:' in line or 'max:' in line:
            scaling_lines.append(f"  Line {i + 1}: {line.rstrip()}")

    if scaling_lines:
        logger.info("  Found potential scaling configurations:")
        for line in scaling_lines[:10]:  # Show first 10 matches
            logger.info(line)
        if len(scaling_lines) > 10:
            logger.info(f"  ...and {len(scaling_lines) - 10} more lines")
    else:
        logger.info("  No scaling patterns found in this file")

    # Rest of the function remains the same
    context = {
        'in_scaling_section': False,
        'in_min_section': False,
        'scaling_indent': 0,
        'section_stack': [],
        'current_stage_name': "root",
        'last_stage_name_line': None,
        'in_target_env': env_name is None,  # If no env specified, target all environments
        'printed_stage_name': False,
        'modified': False
    }
    new_lines = []

    for line in lines:
        # Apply transformations based on context
        new_line = process_line(line, context, min_pods, min_cpu, min_memory, env_name)
        new_lines.append(new_line)

    # Save changes if modifications were made and not in dry-run mode
    file_name = Path(yaml_file).name
    if context['modified'] and not dry_run:
        if write_file_lines(yaml_file, new_lines):
            logger.info(f"✅ Saved changes to {file_name}")
    elif context['modified'] and dry_run:
        logger.info(f"ℹ️ Dry run - changes not saved to {file_name}")
    elif not context['modified']:
        logger.info(f"ℹ️ No matching scaling configurations found in {file_name}" +
                    (f" for environment '{env_name}'" if env_name else ""))

    return context['modified']


def process_line(line, context, min_pods, min_cpu, min_memory, env_name):
    """Process a single line of the YAML file based on context."""
    # Track stage names
    stage_match = re.match(r'\s*-\s*name:\s*(.+)', line)
    if stage_match:
        context['current_stage_name'] = stage_match.group(1).strip()
        context['last_stage_name_line'] = line.rstrip()
        # Check if this is our target environment
        context['in_target_env'] = (env_name is None) or (context['current_stage_name'] == env_name)
        context['printed_stage_name'] = False

    # Track section context using indentation
    stripped = line.lstrip()
    if stripped and not stripped.startswith('#'):
        current_indent = len(line) - len(stripped)

        # Update section stack based on indentation
        while context['section_stack'] and context['section_stack'][-1][1] >= current_indent:
            context['section_stack'].pop()

        # Detect which section we're in based on the line content
        if stripped.startswith('scaling:'):
            context['in_scaling_section'] = True
            context['scaling_indent'] = current_indent
            context['section_stack'].append(('scaling', current_indent))
        elif stripped.startswith('min:') and not stripped.startswith('min_'):
            context['in_min_section'] = True
            context['section_stack'].append(('min', current_indent))
        elif stripped.startswith('autoscaling:'):
            context['section_stack'].append(('autoscaling', current_indent))
        elif current_indent <= context['scaling_indent'] and context['in_scaling_section']:
            context['in_scaling_section'] = False

        # Check sections context from stack
        current_section = context['section_stack'][-1][0] if context['section_stack'] else ""

        # Only make changes if targeting all environments or we're in the target environment
        if context['in_target_env']:
            new_line = apply_updates(line, context, current_section, min_pods, min_cpu, min_memory)
            if new_line != line:
                line = new_line
                context['modified'] = True

    return line


def apply_updates(line, context, current_section, min_pods, min_cpu, min_memory):
    """Apply configuration updates to a line if it matches update patterns."""
    original_line = line

    # Handle different patterns for min pods
    if context['in_scaling_section'] and re.match(r'\s*min:\s*\d+\s*$', line):
        old_value = re.search(r'min:\s*(\d+)', line).group(1)
        new_line = re.sub(r'min:\s*\d+', f'min: {min_pods}', line)
        if new_line != line:
            print_stage_name_if_needed(context)
            print_update("scaling.min", old_value, min_pods)
            return new_line

    # Handle CPU percent in scaling section
    if min_cpu is not None and context['in_scaling_section'] and re.match(r'\s*cpuPercent:\s*\d+\s*$', line):
        old_value = re.search(r'cpuPercent:\s*(\d+)', line).group(1)
        new_line = re.sub(r'cpuPercent:\s*\d+', f'cpuPercent: {min_cpu}', line)
        if new_line != line:
            print_stage_name_if_needed(context)
            print_update("scaling.cpuPercent", old_value, min_cpu)
            return new_line

    # Handle min.cpu resources
    if min_cpu is not None and context['in_min_section'] and re.match(r'\s*cpu:\s*.*\s*$', line):
        old_value = re.search(r'cpu:\s*(.*?)(?:\s*$|\s*#)', line).group(1).strip()
        new_cpu = min_cpu
        # Preserve format like "800m"
        if re.search(r'\d+m', old_value):
            new_cpu = f"{min_cpu}m"
        new_line = re.sub(r'cpu:\s*.*?(?=\s*$|\s*#)', f'cpu: {new_cpu}', line)
        if new_line != line:
            print_stage_name_if_needed(context)
            print_update("min.cpu", old_value, new_cpu)
            return new_line

    # Handle min.memory resources
    if min_memory is not None and context['in_min_section'] and re.match(r'\s*memory:\s*.*\s*$', line):
        old_value = re.search(r'memory:\s*(.*?)(?:\s*$|\s*#)', line).group(1).strip()
        new_line = re.sub(r'memory:\s*.*?(?=\s*$|\s*#)', f'memory: {min_memory}', line)
        if new_line != line:
            print_stage_name_if_needed(context)
            print_update("min.memory", old_value, min_memory)
            return new_line

    # Handle targetCPUUtilizationPercentage
    if min_cpu is not None and current_section == 'autoscaling' and re.match(
            r'\s*targetCPUUtilizationPercentage:\s*\d+\s*$', line):
        old_value = re.search(r'targetCPUUtilizationPercentage:\s*(\d+)', line).group(1)
        new_line = re.sub(r'targetCPUUtilizationPercentage:\s*\d+',
                          f'targetCPUUtilizationPercentage: {min_cpu}', line)
        if new_line != line:
            print_stage_name_if_needed(context)
            print_update("targetCPUUtilizationPercentage", old_value, min_cpu)
            return new_line

    return line


def print_stage_name_if_needed(context):
    """Print the stage name if it hasn't been printed yet for the current changes."""
    if not context['printed_stage_name'] and context['last_stage_name_line']:
        logger.info(f"{context['last_stage_name_line']}")
        context['printed_stage_name'] = True
