# kitt/file_utils.py
"""
File handling utilities for working with Kitt YAML files.
"""
import os,logging
from pathlib import Path
import yaml
logger = logging.getLogger(__name__)


def find_kitt_common_file(yaml_file, profile_path):
    """Find kitt.common.yml file referenced in profile"""
    if 'kitt/kitt.common' in profile_path:
        kitt_dir = os.path.dirname(os.path.dirname(yaml_file))
        kitt_common_path = os.path.join(kitt_dir, 'kitt', 'kitt.common.yml')
        alt_path = os.path.join(kitt_dir, 'kitt', 'kitt.common.yaml')
    else:
        kitt_dir = os.path.dirname(yaml_file)
        kitt_common_path = os.path.join(kitt_dir, 'kitt.common.yml')
        alt_path = os.path.join(kitt_dir, 'kitt.common.yaml')

    if os.path.exists(kitt_common_path):
        return kitt_common_path
    elif os.path.exists(alt_path):
        return alt_path
    return None


def load_yaml(file_path):
    """Load a YAML file safely."""
    try:
        with open(file_path, 'r') as f:
            content = yaml.safe_load(f)
        return content
    except Exception as e:
        print(f"Error loading {file_path}: {str(e)}")
        return None


def read_file_lines(file_path):
    """Read a file and return its lines."""
    try:
        with open(file_path, 'r') as f:
            return f.readlines()
    except Exception as e:
        print(f"Error reading {file_path}: {str(e)}")
        return []


def write_file_lines(file_path, lines):
    """Write lines to a file."""
    try:
        with open(file_path, 'w') as f:
            f.writelines(lines)
        return True
    except Exception as e:
        print(f"Error writing to {file_path}: {str(e)}")
        return False


def find_yaml_files(directory):
    """Find all YAML files in a directory recursively."""
    yaml_files = []
    directory_path = Path(directory)
    for extension in ['.yml', '.yaml']:
        yaml_files.extend(list(directory_path.glob(f"**/*{extension}")))
    return yaml_files