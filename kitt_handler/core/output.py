# kitt/output.py
"""
Output formatting utilities.
"""
import logging
logger = logging.getLogger(__name__)
def print_update(field, old_value, new_value):
    """Print a formatted update message."""
    logger.info(f"  Updated {field}: {old_value} → {new_value}")


def print_summary(updated, total_files, dry_run):
    """Print a summary of changes made."""
    logger.info(f"\nSummary: Updated {updated} of {total_files} YAML files")
    if dry_run:
        print("Dry run - no changes were saved")


def print_error(message):
    """Print an error message."""
    logger.info(f"❌ {message}")