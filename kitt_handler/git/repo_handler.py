# kitt_handler/git/repo_handler.py
import os
import requests,logging
import tempfile
import time
from pathlib import Path
from git import Repo
from kitt_handler.core.config_updater import process_yaml_files
from kitt_handler.core.file_utils import find_yaml_files

logger = logging.getLogger(__name__)

class GitRepoHandler:
    def __init__(self, repo_url, token=None, location=None):
        """Initialize git repository handler."""
        self.feature_branch = None
        self.repo_url = repo_url
        self.provided_location = location
        self.token = token or os.environ.get('GITHUB_TOKEN')
        self.repo = None  # Initialize repo attribute to None

        if not self.token:
            raise ValueError("GitHub token required. Set GITHUB_TOKEN env variable or pass token parameter.")

        # Parse organization and repository name from URL
        parts = repo_url.rstrip('/').split('/')
        self.org_name = parts[-2]
        self.repo_name = parts[-1]

        # API URL base for Walmart GitHub Enterprise
        self.api_base = "https://gecgithub01.walmart.com/api/v3"
        self.auth_headers = {
            "Authorization": f"token {self.token}",
            "Accept": "application/vnd.github.v3+json"
        }

        self.local_path = None
        self.forked_repo_url = None

    def use_existing_repository(self):
        """Check if the provided location is a valid git repository and fetch remote changes."""
        if not os.path.exists(self.provided_location):
            logger.info(f"Provided location {self.provided_location} does not exist")
            return False

        try:
            # Check if this is a valid git repository
            repo = Repo(self.provided_location)
            if repo.bare:
                logger.info(f"Repository at {self.provided_location} is a bare repository")
                return False

            # Valid repository
            self.repo = repo
            self.local_path = self.provided_location
            logger.info(f"Found valid git repository at {self.local_path}")

            # Try to fetch remote changes
            logger.info("Fetching latest changes from remote...")
            try:
                origin = repo.remote('origin')
                origin.fetch()
                current_branch = repo.active_branch.name
                repo.git.pull('origin', current_branch)
                logger.info(f"Successfully updated local repository with remote changes")

                # Set up upstream if needed
                if hasattr(self, 'needs_manual_sync') and self.needs_manual_sync:
                    try:
                        upstream = repo.remote('upstream')
                    except ValueError:
                        upstream_url = f"https://{self.token}@gecgithub01.walmart.com/{self.org_name}/{self.repo_name}.git"
                        upstream = repo.create_remote('upstream', upstream_url)
                    upstream.fetch()
                    logger.info("Synced with upstream repository")

                return True
            except Exception as e:
                logger.info(f"Error fetching remote changes: {str(e)}")
                return False

        except Exception as e:
            logger.info(f"Not a valid git repository at {self.provided_location}: {str(e)}")
            return False

    def fork_repository(self):
        """Fork the repository to the user's personal account."""
        # First, get the authenticated user's username
        user_url = f"{self.api_base}/user"
        user_response = requests.get(user_url, headers=self.auth_headers)

        if user_response.status_code != 200:
            raise Exception(f"Failed to get user info: {user_response.status_code} {user_response.text}")

        username = user_response.json()['login']
        logger.info(f"Authenticated as: {username}")

        # Create the fork
        fork_url = f"{self.api_base}/repos/{self.org_name}/{self.repo_name}/forks"

        # Explicitly specify personal account (not organization)
        fork_data = {"organization": None}

        logger.info(f"Forking repository {self.org_name}/{self.repo_name} to {username}...")
        response = requests.post(fork_url, headers=self.auth_headers, json=fork_data)

        if response.status_code not in (200, 202):
            if response.status_code == 403:
                logger.info("Permission denied. Make sure your token has 'repo' and 'fork' permissions.")
            raise Exception(f"Failed to fork repository: {response.status_code} {response.text}")

        fork_data = response.json()
        self.forked_repo_url = fork_data['clone_url']

        logger.info(f"Fork created at {self.forked_repo_url}")
        logger.info("Waiting for fork to be ready...")
        time.sleep(5)  # Give GitHub time to set up the fork

        return self.forked_repo_url

    def clone_repository(self):
        """Clone the repository (either forked or original) locally."""
        self.local_path = tempfile.mkdtemp(prefix="kitt-hpa-update-")
        logger.info(f"Cloning repository to {self.local_path}...")

        if self.use_fork and self.forked_repo_url:
            # Clone the fork if we're using the fork approach
            clone_url = self.forked_repo_url
        else:
            # Clone the original repository if using feature branch approach
            clone_url = self.repo_url

        auth_url = clone_url.replace("https://", f"https://{self.token}@")
        self.repo = Repo.clone_from(auth_url, self.local_path)

        if not self.use_fork:
            # Create a new feature branch for changes if not using fork
            timestamp = int(time.time())
            feature_branch = f"hpa-update-{timestamp}"
            logger.info(f"Creating new feature branch: {feature_branch}")
            self.repo.git.checkout('-b', feature_branch)
            self.feature_branch = feature_branch

        # Handle manual sync if needed for forks
        if self.use_fork and hasattr(self, 'needs_manual_sync') and self.needs_manual_sync:
            logger.info("Performing manual sync with upstream repository...")
            try:
                # Add upstream as remote
                upstream_url = f"https://{self.token}@gecgithub01.walmart.com/{self.org_name}/{self.repo_name}.git"
                upstream = self.repo.create_remote('upstream', upstream_url)
                upstream.fetch()

                # Create new branch based on upstream/main
                sync_branch = f"sync-{int(time.time())}"
                self.repo.git.checkout('-b', sync_branch, 'upstream/main')

                logger.info(f"Created new branch '{sync_branch}' based on upstream/main")
            except Exception as e:
                logger.info(f"Manual sync failed: {str(e)}. Will continue with current state.")

        logger.info(f"Repository cloned successfully to {self.local_path}")
        return self.local_path

    def update_hpa_configurations(self, min_pods, min_cpu=None, min_memory=None, env_name=None, dry_run=False,
                                  artifact_name=None, specific_file=None, create_pr=True):
        """Update HPA configurations in all YAML files in the repository."""
        # If repo doesn't exist yet, try to clone it
        if self.repo is None:
            if self.provided_location and os.path.exists(self.provided_location):
                self.use_existing_repository()
            else:
                self.clone_repository()

        logger.info(f"Updating HPA configurations in repository...")
        pr_url = None

        try:
            # If specific file is provided, process just that file
            if specific_file:
                # Construct full path
                file_path = Path(self.local_path) / specific_file
                if not file_path.exists():
                    return {
                        "files_used": [],
                        "files_modified": [],
                        "pr_url": None,
                        "commit_hash": None,
                        "num_updated": 0,
                        "message": f"Specified file {specific_file} not found in repository."
                    }

                logger.info(f"Processing specific file: {specific_file}")
                file_details = process_yaml_files(
                    str(file_path),
                    min_pods,
                    min_cpu,
                    min_memory,
                    dry_run,
                    env_name,
                    None,  # Ignore artifact name when specific file is provided
                    return_files=True
                )
                files_used = file_details.get('files_used', [])
                files_modified = file_details.get('files_modified', [])
            else:
                # Process all matching files
                file_details = process_yaml_files(
                    self.local_path,
                    min_pods,
                    min_cpu,
                    min_memory,
                    dry_run,
                    env_name,
                    artifact_name,
                    return_files=True
                )
                files_used = file_details.get('files_used', [])
                files_modified = file_details.get('files_modified', [])

            commit_hash = None
            if len(files_modified) > 0 and not dry_run:
                logger.info(f"Committing and pushing changes for {len(files_modified)} files...")
                commit_message = f"Update HPA configurations: min_pods={min_pods}"
                if min_cpu:
                    commit_message += f", min_cpu={min_cpu}"
                if min_memory:
                    commit_message += f", min_memory={min_memory}"
                if env_name:
                    commit_message += f" for environment {env_name}"

                commit_result = self.commit_and_push_changes(commit_message)
                if commit_result:
                    commit_hash = str(self.repo.head.commit)
                    logger.info(f"Changes committed: {commit_hash}")

                    # Only create PR if requested
                    if create_pr:
                        pr_url = self.create_pull_request(
                            title=commit_message,
                            body=f"Automated update of HPA configurations\n\nFiles modified:\n" +
                                 "\n".join([f"- {Path(f).name}" for f in files_modified])
                        )
                        logger.info(f"Created pull request: {pr_url}")
                    else:
                        logger.info("Skipping PR creation as requested")
                else:
                    logger.info("No changes were committed")
            elif len(files_modified) == 0:
                logger.info("No matching configurations found to update.")

        finally:
            if not self.provided_location:  # Only cleanup if we created a temp repository
                self.cleanup()

        # Convert file paths to include git URLs
        files_used_with_urls = []
        files_modified_with_urls = []

        # Base URL for the repository (using original repo, not fork)
        base_repo_url = f"https://gecgithub01.walmart.com/{self.org_name}/{self.repo_name}"

        # Process files_used
        for file_path in files_used:
            # Get relative path within repository
            rel_path = os.path.relpath(file_path, self.local_path)
            file_name = Path(file_path).name
            # Create URL to file in repository
            file_url = f"{base_repo_url}/blob/main/{rel_path}"
            files_used_with_urls.append({
                "name": file_name,
                "path": rel_path,
                "url": file_url
            })

        # Process files_modified
        for file_path in files_modified:
            # Get relative path within repository
            rel_path = os.path.relpath(file_path, self.local_path)
            file_name = Path(file_path).name
            # Create URL to file in repository
            file_url = f"{base_repo_url}/blob/main/{rel_path}"
            files_modified_with_urls.append({
                "name": file_name,
                "path": rel_path,
                "url": file_url
            })

        result = {
            "files_used": files_used_with_urls,
            "files_modified": files_modified_with_urls,
            "pr_url": pr_url,
            "commit_hash": commit_hash,
            "num_updated": len(files_modified)
        }

        if len(files_modified) == 0:
            result["message"] = "No matching configurations found to update."
            if specific_file:
                yaml_files = find_yaml_files(self.local_path)
                available_files = []
                for f in yaml_files[:20]:
                    rel_path = os.path.relpath(str(f), self.local_path)
                    file_url = f"{base_repo_url}/blob/main/{rel_path}"
                    available_files.append({
                        "name": f.name,
                        "path": rel_path,
                        "url": file_url
                    })
                result["available_files"] = available_files

        return result

    def commit_and_push_changes(self, commit_message="Update HPA configurations"):
        """Commit and push changes to the repository."""
        try:
            if self.repo is None:
                logger.info("Repository not initialized. Cannot commit changes.")
                return False

            if not self.repo.is_dirty(untracked_files=True):
                logger.info("No changes to commit")
                return False

            logger.info("Committing changes...")
            self.repo.git.add(A=True)
            self.repo.index.commit(commit_message)

            logger.info("Pushing changes...")
            origin = self.repo.remote('origin')

            if self.use_fork:
                # Push to fork's main branch
                origin.push(progress=None)
            else:
                # Push the feature branch to original repo - FIX IS HERE
                # Pass None as progress parameter to avoid string being interpreted as progress handler
                origin.push(self.feature_branch, progress=None, u=True)

            logger.info("Changes pushed successfully")
            return True
        except Exception as e:
            logger.exception(e)

    def create_pull_request(self, title="Update HPA configurations", body="Automated update of HPA configurations"):
        """Create a pull request from either fork or feature branch to the original repository."""
        pr_url = f"{self.api_base}/repos/{self.org_name}/{self.repo_name}/pulls"

        if self.use_fork:
            # For fork approach
            fork_owner = self.username
            head = f"{fork_owner}:main"  # From fork's main branch
        else:
            # For feature branch approach
            head = self.feature_branch  # From feature branch in original repo

        pr_data = {
            "title": title,
            "body": body,
            "head": head,
            "base": "main"
        }

        logger.info(f"Creating pull request to {self.org_name}/{self.repo_name}...")
        response = requests.post(pr_url, headers=self.auth_headers, json=pr_data)

        if response.status_code != 201:
            raise Exception(f"Failed to create pull request: {response.status_code} {response.text}")

        pr_data = response.json()
        return pr_data['html_url']

    def cleanup(self):
        """Clean up local repository."""
        if self.local_path and os.path.exists(self.local_path):
            try:
                import shutil
                logger.info(f"Cleaning up local repository at {self.local_path}")
                shutil.rmtree(self.local_path)
                logger.info(f"Cleaned up local repository at {self.local_path}")
            except Exception as e:
                logger.warning(f"Error during repository cleanup: {str(e)}")
                logger.debug("Cleanup exception details:", exc_info=True)
    @staticmethod
    def get_scm_details(namespace, app, env="prod"):
        """Gets git repository link with retry mechanism."""
        url = f"https://console.dx.walmart.com/proxy/wcnp-apps/apps?namespace={namespace}&profile={env}"
        max_retries = 3
        retry_delay = 3  # seconds

        for attempt in range(1, max_retries + 1):
            try:
                logger.info(f"Attempt {attempt}/{max_retries}: Fetching SCM details for {app} in {namespace}")
                response = requests.get(url,verify=False)

                if response.ok:
                    json_response = response.json()
                    for item in json_response:
                        if item["app_name"] == app:
                            return item["links"]["Github"]
                    logger.info(f"App '{app}' not found in response data")
                    return None
                else:
                    logger.info(f"Request failed with status code: {response.status_code}")

            except requests.exceptions.RequestException as e:
                logger.info(f"Request error (attempt {attempt}/{max_retries}): {e}")
            except Exception as e:
                logger.info(f"Unexpected error (attempt {attempt}/{max_retries}): {e}")

            # Don't sleep after the last attempt
            if attempt < max_retries:
                logger.info(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)

        logger.info(f"Failed to fetch SCM details after {max_retries} attempts")
        return None

    def process_repository(self):
        """Process repository: check permissions first, then try fork or feature branch approach."""
        try:
            # Get the authenticated user's username
            user_url = f"{self.api_base}/user"
            user_response = requests.get(user_url, headers=self.auth_headers)

            if user_response.status_code != 200:
                raise Exception(f"Failed to get user info: {user_response.status_code} {user_response.text}")

            username = user_response.json()['login']
            self.username = username

            # Check if user has push access to the original repository
            access_check_url = f"{self.api_base}/repos/{self.org_name}/{self.repo_name}"
            repo_response = requests.get(access_check_url, headers=self.auth_headers)

            if repo_response.status_code != 200:
                raise Exception(f"Repository not accessible: {repo_response.status_code}")

            repo_data = repo_response.json()
            has_push_access = repo_data.get('permissions', {}).get('push', False)
            fork_allowed = not repo_data.get('fork', True) is False  # Check if forking is explicitly disabled

            if not has_push_access and not fork_allowed:
                raise Exception(
                    f"You don't have push access to this repository and forking is not allowed. Cannot proceed.")

            # Set our strategy based on permissions
            self.use_fork = fork_allowed and not has_push_access

            # First check if fork already exists if we're using fork strategy
            if self.use_fork:
                fork_check_url = f"{self.api_base}/repos/{username}/{self.repo_name}"
                fork_response = requests.get(fork_check_url, headers=self.auth_headers)

                if fork_response.status_code == 200:
                    # Fork exists, use it
                    logger.info(f"Fork already exists at {username}/{self.repo_name}")
                    self.forked_repo_url = fork_response.json()['clone_url']

                    # Update fork with latest changes from upstream
                    logger.info("Attempting to sync fork with upstream repository...")
                    sync_url = f"{self.api_base}/repos/{username}/{self.repo_name}/merge-upstream"
                    sync_data = {"branch": "main"}
                    sync_response = requests.post(sync_url, headers=self.auth_headers, json=sync_data)

                    if sync_response.status_code not in (200, 201, 204):
                        logger.info(f"Warning: Could not sync fork with upstream: {sync_response.status_code}")
                        logger.info("Will perform manual sync after cloning...")
                        self.needs_manual_sync = True
                    else:
                        self.needs_manual_sync = False
                else:
                    # Create new fork
                    try:
                        self.fork_repository()
                    except Exception as e:
                        if "forking is disabled" in str(e):
                            if has_push_access:
                                logger.info("Forking is disabled. Falling back to feature branch approach.")
                                self.use_fork = False
                            else:
                                raise Exception("Forking is disabled and you don't have push access to the repository.")
                        else:
                            raise

            # Check if we can use the provided local repository
            if self.provided_location and self.use_existing_repository():
                logger.info(f"Using existing repository at {self.local_path}")
            else:
                # Clone repository - either the fork or the original
                if not self.clone_repository():
                    raise Exception("Failed to clone repository")

            return True
        except Exception as e:
            logger.error(f"Error in process_repository: {str(e)}")
            return False


def main():
    log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    log_format = '%(asctime)s %(filename)18s %(funcName)25s %(lineno)3d %(levelname)10s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(log_level)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)

    import argparse
    parser = argparse.ArgumentParser(description="Update HPA configurations in a Git repository")
    parser.add_argument("repo_url", help="URL of the Git repository")
    parser.add_argument("--token", help="GitHub personal access token (or set GITHUB_TOKEN env variable)")
    parser.add_argument("--min-pods", type=int, required=True, help="New minimum pod count")
    parser.add_argument("--min-cpu", help="New target CPU utilization percentage or value")
    parser.add_argument("--min-memory", help="New memory value (e.g., '512Mi', '1Gi')")
    parser.add_argument("--env-name", help="Only update configurations for this environment name")
    parser.add_argument("--artifact", help="Only process files with this artifact name")
    parser.add_argument("--file", help="Process a specific file in the repository")
    parser.add_argument("--dry-run", action="store_true", help="Print changes without modifying files")

    # args = parser.parse_args()

    #git = GitRepoHandler.get_scm_details("polaris-gm", "polaris-mx-prod")
    handler = GitRepoHandler("https://gecgithub01.walmart.com/LabsSearch/polaris-usgm",
                             "****************************************", location=None)

    try:
        status = handler.process_repository()
        if not status:
            print("Failed to process repository.")
            return 1
        result = handler.update_hpa_configurations(
            1
        )

        # Show summary of results
        if result["num_updated"] > 0:
            print(f"\nSuccessfully updated {result['num_updated']} files")
            if result["pr_url"]:
                print(f"Pull request: {result.get('pr_url')}")
        else:
            print(f"\n{result.get('message', 'No files were updated')}")

        print(f"\nFiles processed: {len(result['files_used'])}")
        for f in result['files_used']:
            print(f"  - {Path(f).name}")

        return 0
    except Exception as e:
        print(f"Error: {str(e)}")
        return 1


if __name__ == "__main__":
    import sys

    sys.exit(main())
