"""juno URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, re_path
from template import views
from mms import views as mms_views
from wcnp import views as sre_view
from utils import views as utils_view
from teap import views as teap_view
from bizops import views as bizops_view
from sre import views as sre_view_operations
from rest_framework_swagger.views import get_swagger_view
from job import views as sync_view
from django.contrib.staticfiles.urls import staticfiles_urlpatterns

schema_view = get_swagger_view(title='Pastebin API')

admin.site.site_header = "Juno Admin"
admin.site.site_title = "Juno Portal"
admin.site.index_title = "Juno, international SRE tooling"

urlpatterns = [
    # path('admin/', admin.site.urls),
    re_path(r'^$', views.ecv),
    path('ecv/', views.ecv),
    # Old way templates
    path('templates/', views.get_all_templates),
    path('templates/<str:template>/', views.template_metadata),
    path('templates/bulk/<str:template>/', views.bulk_template_metadata),
    # new way templates
    path('api/templates/', views.get_all_templates),
    path('api/templates/<str:template>/', views.template_metadata),
    path('api/templates/create/<str:template>/', views.bulk_template_metadata),
    path('api/templates/pre/processor/<str:template>/', views.preprocessor_template_results),

    # MMS
    path('api/mms/namespace/<str:namespace>/', mms_views.mms_dashboards),
    path('api/mms/namespace/<str:namespace>/<str:app_id>/', mms_views.mms_dashboards_with_app_id),
    # path('api/mms/service/search/', test_view.mms_search_service_data),
    path('api/mms/namespace/<str:namespace>/<str:app_id>/<int:minutes_ago>/', mms_views.mms_app_data),
    path('api/mms/oneops/<str:org>/<str:assembly>/', mms_views.mms_oneops_dashboards),
    path('api/mms/pod/<str:namespace>/<str:app_id>/<int:minutes_ago>/', mms_views.mms_app_data),
    path('api/mms/gslb/mx/data/', mms_views.monitor_mx_gslbs),
    path('api/mms/gslb/canada/data/', mms_views.monitor_canada_gslbs),
    path('api/mms/gslb/mx/', mms_views.dashboards_for_mx_gslbs),
    path('api/mms/gslb/canada/', mms_views.dashboards_canada_gslbs),
    path('api/mms/pod/<str:namespace>/<str:app_id>/<int:minutes_ago>/', mms_views.mms_app_data),

    path('api/oneops/assemblies/<str:org>/', mms_views.get_all_oneops_assemblies),
    # WCNP
    path('api/wcnp/namespace/<str:namespace>/', sre_view.wcnp_info),
    path('api/wcnp/hpa/download', sre_view.download_app_details),
    path('api/wcnp/hpa/mx/<str:tier>/', sre_view.get_all_wcnp_apps_hpa_values_by_tier_by_mx_market),
    path('api/wcnp/hpa/mx/all/', sre_view.get_all_wcnp_apps_hpa_values_by_tier_all_by_mx_market),
    path('api/wcnp/hpa/ca/all/', sre_view.get_all_wcnp_apps_hpa_values_by_tier_all_by_ca_market),
    path('api/wcnp/hpa/mx/tier/zero/', sre_view.get_all_wcnp_apps_hpa_values_by_tier_0_by_mx_market),
    path('api/wcnp/hpa/mx/tier/one/', sre_view.get_all_wcnp_apps_hpa_values_by_tier_1_by_mx_market),
    path('api/wcnp/hpa/domain/de/<str:market>/',
         sre_view.get_all_wcnp_apps_hpa_values_by_tier_all_by_mx_market_by_de_domain),
    path('api/wcnp/hpa/domain/mc/<str:market>/',
         sre_view.get_all_wcnp_apps_hpa_values_by_tier_all_by_mx_market_by_mrch_domain),
    path('api/wcnp/hpa/domain/if/<str:market>/',
         sre_view.get_all_wcnp_apps_hpa_values_by_tier_all_by_mx_market_by_if_domain),
    path('api/wcnp/hpa/domain/ce/<str:market>/',
         sre_view.get_all_wcnp_apps_hpa_values_by_tier_all_by_mx_market_by_if_domain),
    # Utils
    path('api/utils/slack/message/', utils_view.send_slack_message),
    path('api/utils/xmatters/message/', utils_view.invoke_xmatters_call),

    # create_cosmos_alerts
    path('api/alerts/cosmos/app/tier/zero/', utils_view.create_cosmos_alerts_user_tier_0),
    path('api/alerts/cosmos/app/tier/one/', utils_view.create_cosmos_alerts_user_tier_1),
    path('api/alerts/cosmos/app/tier/all/', utils_view.create_cosmos_alerts_user_all),
    path('api/alerts/cosmos/sre/tier/zero/', utils_view.create_cosmos_alerts_sre_tier_0),
    path('api/alerts/cosmos/sre/tier/one/', utils_view.create_cosmos_alerts_sre_tier_1),
    path('api/alerts/cosmos/sre/tier/all/', utils_view.create_cosmos_alerts_sre_tier_all),
    path('api/alerts/cosmos/sre/tier/<str:tier>/', utils_view.create_cosmos_alerts_sre_tier_any),
    path('api/alerts/cosmos/app/tier/<str:tier>/', utils_view.create_cosmos_alerts_user_tier_any),
    path('api/alerts/cosmos/<str:resource_group>/', utils_view.create_user_cosmos_alerts),
    # Create WCNP alerts
    # create_wcnp_alerts_sre_tier_1
    path('api/alerts/wcnp/sre/tier/zero/', utils_view.create_wcnp_alerts_sre_tier_0),
    path('api/alerts/wcnp/sre/tier/one/', utils_view.create_wcnp_alerts_sre_tier_1),
    path('api/alerts/wcnp/sre/tier/two/', utils_view.create_wcnp_alerts_sre_tier_2),
    path('api/alerts/wcnp/sre/tier/all/', utils_view.create_wcnp_alerts_sre_tier_all),
    path('api/alerts/wcnp/sre/tier/<str:tier>/', utils_view.create_wcnp_alerts_sre_tier_any),
    # Create user
    path('api/alerts/wcnp/app/tier/zero/', utils_view.create_wcnp_alerts_user_tier_0),
    path('api/alerts/wcnp/app/tier/one/', utils_view.create_wcnp_alerts_user_tier_1),
    path('api/alerts/wcnp/app/tier/two/', utils_view.create_wcnp_alerts_user_tier_2),
    path('api/alerts/wcnp/app/tier/all/', utils_view.create_wcnp_alerts_user_tier_all),
    path('api/alerts/wcnp/app/tier/<str:tier>/', utils_view.create_wcnp_alerts_user_tier_any),
    # Async WCNP alerts creation
    path('api/alerts/wcnp/async/', utils_view.create_wcnp_alerts_async),
    path('api/alerts/wcnp/async/status/<str:job_id>/', utils_view.get_wcnp_alerts_job_status),
    path('api/alerts/wcnp/async/list/', utils_view.list_wcnp_alerts_jobs),
    path('api/spotlight/webhook/', sre_view_operations.spotlight_hook),

    # Creating oneops alerts
    # SRE alerts
    path('api/alerts/oneops/sre/tier/zero/', utils_view.create_oneops_alerts_sre_tier_zero),
    path('api/alerts/oneops/sre/tier/one/', utils_view.create_oneops_alerts_sre_tier_one),
    path('api/alerts/oneops/sre/tier/two/', utils_view.create_oneops_alerts_sre_tier_two),
    path('api/alerts/oneops/sre/tier/all/', utils_view.create_oneops_alerts_sre_tier_all),
    path('api/alerts/oneops/sre/tier/<str:tier>/', utils_view.create_oneops_alerts_sre_tier_any),
    # User alerts
    path('api/alerts/oneops/app/tier/zero/', utils_view.create_oneops_alerts_user_tier_zero),
    path('api/alerts/oneops/app/tier/one/', utils_view.create_oneops_alerts_user_tier_one),
    path('api/alerts/oneops/app/tier/two/', utils_view.create_oneops_alerts_user_tier_two),
    path('api/alerts/oneops/app/tier/all/', utils_view.create_oneops_alerts_user_tier_all),
    path('api/alerts/oneops/app/tier/<str:tier>/', utils_view.create_oneops_alerts_user_tier_any),

    # Creating Megha cache alerts
    # SRE alerts
    path('api/alerts/meghacache/sre/tier/zero/', utils_view.create_megha_cache_alerts_sre_tier_zero),
    path('api/alerts/meghacache/sre/tier/one/', utils_view.create_megha_cache_alerts_sre_tier_one),
    path('api/alerts/meghacache/sre/tier/two/', utils_view.create_megha_cache_alerts_sre_tier_two),
    path('api/alerts/meghacache/sre/tier/all/', utils_view.create_megha_cache_alerts_sre_tier_all),
    path('api/alerts/meghacache/sre/tier/<str:tier>/', utils_view.create_megha_cache_alerts_sre_tier_any),
    # User alerts
    path('api/alerts/meghacache/app/tier/zero/', utils_view.create_megha_cache_alerts_user_tier_zero),
    path('api/alerts/meghacache/app/tier/one/', utils_view.create_megha_cache_alerts_user_tier_one),
    path('api/alerts/meghacache/app/tier/two/', utils_view.create_megha_cache_alerts_user_tier_two),
    path('api/alerts/meghacache/app/tier/all/', utils_view.create_megha_cache_alerts_user_tier_all),
    path('api/alerts/meghacache/app/tier/<str:tier>/', utils_view.create_megha_cache_alerts_user_tier_any),

    # Creating cassandra alerts
    # SRE alerts
    path('api/alerts/cassandra/sre/tier/zero/', utils_view.create_cassandra_alerts_sre_tier_zero),
    path('api/alerts/cassandra/sre/tier/one/', utils_view.create_cassandra_alerts_sre_tier_one),
    path('api/alerts/cassandra/sre/tier/two/', utils_view.create_cassandra_alerts_sre_tier_two),
    path('api/alerts/cassandra/sre/tier/all/', utils_view.create_cassandra_alerts_sre_tier_all),
    path('api/alerts/cassandra/sre/tier/<str:tier>/', utils_view.create_cassandra_alerts_sre_tier_any),
    # User alerts
    path('api/alerts/cassandra/app/tier/zero/', utils_view.create_cassandra_alerts_user_tier_zero),
    path('api/alerts/cassandra/app/tier/one/', utils_view.create_cassandra_alerts_user_tier_one),
    path('api/alerts/cassandra/app/tier/two/', utils_view.create_cassandra_alerts_user_tier_two),
    path('api/alerts/cassandra/app/tier/two/', utils_view.create_cassandra_alerts_user_tier_all),
    path('api/alerts/cassandra/app/tier/<str:tier>/', utils_view.create_cassandra_alerts_user_tier_any),

    # Creating SQL Server alerts
    # SRE alerts
    path('api/alerts/sqlserver/sre/tier/zero/', utils_view.create_sql_server_alerts_sre_tier_zero),
    path('api/alerts/sqlserver/sre/tier/one/', utils_view.create_sql_server_alerts_sre_tier_one),
    path('api/alerts/sqlserver/sre/tier/two/', utils_view.create_sql_server_alerts_sre_tier_two),
    path('api/alerts/sqlserver/sre/tier/all/', utils_view.create_sql_server_alerts_sre_tier_all),
    path('api/alerts/sqlserver/sre/tier/<str:tier>/', utils_view.create_sql_server_alerts_sre_tier_any),
    # User alerts
    path('api/alerts/sqlserver/app/tier/zero/', utils_view.create_sql_server_alerts_user_tier_zero),
    path('api/alerts/sqlserver/app/tier/one/', utils_view.create_sql_server_alerts_user_tier_one),
    path('api/alerts/sqlserver/app/tier/two/', utils_view.create_sql_server_alerts_user_tier_two),
    path('api/alerts/sqlserver/app/tier/all/', utils_view.create_sql_server_alerts_user_tier_all),
    path('api/alerts/sqlserver/app/tier/<str:tier>/', utils_view.create_sql_server_alerts_user_tier_any),

    # Creating Oracle Server alerts
    # SRE alerts
    path('api/alerts/oracle/sre/tier/zero/', utils_view.create_oracle_server_alerts_sre_tier_zero),
    path('api/alerts/oracle/sre/tier/one/', utils_view.create_oracle_server_alerts_sre_tier_one),
    path('api/alerts/oracle/sre/tier/two/', utils_view.create_oracle_server_alerts_sre_tier_two),
    path('api/alerts/oracle/sre/tier/all/', utils_view.create_oracle_server_alerts_sre_tier_all),
    path('api/alerts/oracle/sre/tier/<str:tier>/', utils_view.create_oracle_server_alerts_sre_tier_any),
    # User alerts
    path('api/alerts/oracle/app/tier/zero/', utils_view.create_oracle_server_alerts_user_tier_zero),
    path('api/alerts/oracle/app/tier/one/', utils_view.create_oracle_server_alerts_user_tier_one),
    path('api/alerts/oracle/app/tier/two/', utils_view.create_oracle_server_alerts_user_tier_two),
    path('api/alerts/oracle/app/tier/all/', utils_view.create_oracle_server_alerts_user_tier_all),
    path('api/alerts/oracle/app/tier/<str:tier>/', utils_view.create_oracle_server_alerts_user_tier_any),

    # Creating Solr Server alerts
    # SRE alerts
    path('api/alerts/solr/sre/tier/zero/', utils_view.create_solr_server_alerts_sre_tier_zero),
    path('api/alerts/solr/sre/tier/one/', utils_view.create_solr_server_alerts_sre_tier_one),
    path('api/alerts/solr/sre/tier/two/', utils_view.create_solr_server_alerts_sre_tier_two),
    path('api/alerts/solr/sre/tier/all/', utils_view.create_solr_server_alerts_sre_tier_all),
    path('api/alerts/solr/sre/tier/<str:tier>/', utils_view.create_solr_server_alerts_sre_tier_any),
    # User alerts
    path('api/alerts/solr/app/tier/zero/', utils_view.create_solr_server_alerts_user_tier_zero),
    path('api/alerts/solr/app/tier/one/', utils_view.create_solr_server_alerts_user_tier_one),
    path('api/alerts/solr/app/tier/two/', utils_view.create_solr_server_alerts_user_tier_two),
    path('api/alerts/solr/app/tier/all/', utils_view.create_solr_server_alerts_user_tier_any),
    path('api/alerts/solr/app/tier/<str:tier>/', utils_view.create_solr_server_alerts_user_tier_any),

    # Creating Kafka alerts
    # SRE alerts
    path('api/alerts/kafka/sre/tier/zero/', utils_view.create_kafka_alerts_sre_tier_zero),
    path('api/alerts/kafka/sre/tier/one/', utils_view.create_kafka_alerts_sre_tier_one),
    path('api/alerts/kafka/sre/tier/two/', utils_view.create_kafka_alerts_sre_tier_two),
    path('api/alerts/kafka/sre/tier/all/', utils_view.create_kafka_alerts_sre_tier_all),

    # User alerts
    path('api/alerts/kafka/app/tier/zero/', utils_view.create_kafka_alerts_app_tier_zero),
    path('api/alerts/kafka/app/tier/one/', utils_view.create_kafka_alerts_app_tier_one),
    path('api/alerts/kafka/app/tier/two/', utils_view.create_kafka_alerts_app_tier_two),
    path('api/alerts/kafka/app/tier/all/', utils_view.create_kafka_alerts_app_tier_all),

    # update inventory threshold by reading from alert files
    path('api/alerts/threshold/update', utils_view.update_threshold_by_reading_alerts),
    # Create Custom inventory file alerts api
    path('api/alerts/template/<str:template>/custom_inventory_file/<str:custom_inventory_file>/tier/<str:tier>/',
         utils_view.create_custom_inventory_file_any),
    path('api/alerts/template/<str:template>/custom_inventory_file/<str:custom_inventory_file>/',
         utils_view.create_custom_inventory_file_without_tier),

    # Wishwall data
    path('api/wishwall/mx/application/', utils_view.process_wishwall_application),
    path('api/wishwall/mx/system/', utils_view.process_wishwall_system),
    path('api/wishwall/ca/application/', utils_view.process_canada_wishwall_application),
    path('api/wishwall/ca/system/', utils_view.process_canada_wishwall_system),


    # Enable and disable api
    path('api/alerts/alert/enable/<str:alert_id>/', utils_view.enable_alert),
    path('api/alerts/alert/disable/<str:alert_id>/', utils_view.disable_alert),
    path('api/alerts/list/disable/', utils_view.disable_alerts_by_ids),
    path('api/alerts/list/enable/', utils_view.enable_alerts_by_ids),
    path('api/alerts/traffic/spike/disable/', utils_view.disable_alerts_by_criteria_spike_in_traffic),
    path('api/alerts/traffic/spike/enable/', utils_view.enable_alerts_by_criteria_spike_in_traffic),
    path('api/alerts/filter/all/disable/', utils_view.disable_alerts_by_criteria),
    path('api/alerts/filter/all/enable/', utils_view.enable_alerts_by_criteria),
    path('api/alerts/filter/sre/disable/', utils_view.disable_alerts_by_criteria_sre),
    path('api/alerts/filter/sre/enable/', utils_view.enable_alerts_by_criteria_sre),
    path('api/alerts/filter/app/disable/', utils_view.disable_alerts_by_criteria_app),
    path('api/alerts/filter/app/enable/', utils_view.enable_alerts_by_criteria_app),
    path('api/alerts/fetch/all/disabled/alerts/', utils_view.get_disable_alerts),
    path('api/alerts/list/all/enabled/alerts/', utils_view.get_enable_alerts),
    # get_disable_alerts
    # create_cosmos_alerts
    # update threshold
    path('api/alerts/threshold/update/', utils_view.update_alerts_threshold_by_criteria),
    # TEAP
    path('api/teap/search/name/<str:query>/', teap_view.search),
    # Search namespace or assembly or db
    path('api/teap/search/nad/<str:query>/', teap_view.get_apps_by_searching_namespace_Assembly_db_field),
    path('api/teap/app/<str:app_name>/', teap_view.get_app_by_name),

    # analysis wcnp
    # get hpa or canary or verical scaling breached apps
    path('api/analyze/wcnp/raw/data/all/tier/zero/', teap_view.wcnp_pull_all_tier_zero),
    path('api/analyze/wcnp/raw/data/all/tier/one/', teap_view.wcnp_pull_all_tier_one),
    # get hpa or canary or verical scaling breached apps
    path('api/analyze/wcnp/all/tier/zero/', teap_view.analyze_wcnp_namespaces_standards_tier_zero),
    path('api/analyze/wcnp/all/tier/one/', teap_view.analyze_wcnp_namespaces_standards_tier_one),

    # get hpa  breached apps
    path('api/analyze/wcnp/hpa/tier/zero/', teap_view.analyze_hpa_ratio_tier_zero),
    path('api/analyze/wcnp/hpa/tier/one/', teap_view.analyze_hpa_ratio_tier_one),

    # get vertical scaling breached apps
    path('api/analyze/wcnp/vertical/tier/zero/', teap_view.analyze_vertical_scaling_tier_zero),
    path('api/analyze/wcnp/vertical/tier/one/', teap_view.analyze_vertical_scaling_tier_one),

    # get probe urls scaling breached apps
    path('api/analyze/wcnp/probes/tier/zero/', teap_view.analyze_probes_tier_zero),
    path('api/analyze/wcnp/probes/tier/one/', teap_view.analyze_probes_tier_one),

    # get canary scaling breached apps
    path('api/analyze/wcnp/canary/tier/zero/', teap_view.analyze_canary_tier_zero),
    path('api/analyze/wcnp/canary/tier/one/', teap_view.analyze_canary_tier_one),

    # Analyze oneClick scalar
    path('api/oneclick/scalar/ca/post/stresstest/', teap_view.analyze_canada_one_click_scalar_post_stress_test),
    path('api/oneclick/scalar/ca/pre/stresstest/', teap_view.analyze_canada_one_click_scalar_pre_stress_test),
    path('api/oneclick/scalar/mx/post/stresstest/', teap_view.analyze_mexico_one_click_scalar_post_stress_test),
    path('api/oneclick/scalar/mx/pre/stresstest/', teap_view.analyze_mexico_one_click_scalar_pre_stress_test),
    path('api/oneclick/scalar/processor/', teap_view.analyze_click_scalar_processor),

    # TEAP
    path('api/teap/market/list/', teap_view.get_list_of_markets),
    path('api/teap/tier/<str:tier>/', teap_view.get_apps_by_tier),
    path('api/teap/tier/list/', teap_view.get_list_of_tier),
    path('api/teap/platform/<str:platform>/', teap_view.get_apps_by_platform),
    path('api/teap/platform/<str:platform>/tier/<str:tier>/', teap_view.get_platform_tier),
    path('api/teap/market/<str:market>/', teap_view.get_apps_by_market),
    path('api/teap/market/<str:market>/tier/<str:tier>/', teap_view.get_apps_by_market_tier),
    path('api/teap/market/<str:market>/platform/<str:platform>/', teap_view.get_apps_by_market_platform),
    path('api/teap/market/<str:market>/platform/<str:platform>/tier/<str:tier>/', teap_view.get_market_tier_platform),
    path('api/teap/wcnp/', teap_view.get_platform),
    path('api/teap/wcnp/tier/zero/', teap_view.get_platform_tier_default),
    path('api/teap/wcnp/tier/zero/mx/', teap_view.get_wcnp_tier_zero_mx_apps),
    path('api/teap/wcnp/tier/zero/ca/', teap_view.get_wcnp_tier_zero_ca_apps),
    path('api/teap/wcnp/tier/one/', teap_view.get_platform_tier_1),
    path('api/teap/wcnp/tier/one/mx/', teap_view.get_wcnp_tier_one_mx_apps),
    path('api/teap/wcnp/tier/one/ca/', teap_view.get_wcnp_tier_one_ca_apps),
    # bizops_view
    path('api/bizops/accounts/delete/', bizops_view.delete_user_accounts),
    path('api/bizops/accounts/delete/parallel/', bizops_view.delete_user_accounts_in_parallel),

    # sre_view_operations
    path('api/sre/sams/bcdc/restapp/enable/', sre_view_operations.sams_bcdc_switch_enable_rest_app),
    path('api/sre/sams/bcdc/restapp/disable/', sre_view_operations.sams_bcdc_switch_disable_rest_app),
    path('api/sre/sams/bcdc/restapp/status/', sre_view_operations.sams_bcdc_switch_status_rest_app),
    path('api/sre/sams/bcdc/mobiapp/enable/', sre_view_operations.sams_bcdc_switch_enable_mobi_app),
    path('api/sre/sams/bcdc/mobiapp/disable/', sre_view_operations.sams_bcdc_switch_disable_mobi_app),
    path('api/sre/sams/bcdc/mobiapp/status/', sre_view_operations.sams_bcdc_switch_status_mobi_app),

    # I5 cache flush
    path('api/sre/flush/v1/upcs/', sre_view_operations.flush_i5_cache_and_optimize_image_using_upcs),
    path('api/sre/flush/v2/akamai/urls/', sre_view_operations.flush_akamai_urls),
    path('api/sre/flush/v1/pipeline/', sre_view_operations.flush_i5_cache_and_optimize_image),
    path('api/sre/flush/akamai/cp_codes/', sre_view_operations.flush_akamai_cp_codes),
    path('api/sre/flush/fastly/', sre_view_operations.flush_fastly_cache),
    # flush_akamai_cp_codes
    # IRO item cache flush
    path('api/sre/item/flush/', sre_view_operations.iro_item_cache_delete_by_upcs),
    path('api/sre/item/data/', sre_view_operations.iro_item_cache_get_by_upc),
    path('api/sre/item/details/', sre_view_operations.get_iro_details),
    # itemdetails
    
    # Carrier methods API
    path('api/sre/carrier/methods/', sre_view_operations.get_carrier_methods),
    path('api/sre/carrier/methods/cross-validation/', sre_view_operations.get_carrier_methods_cross_validation),

    # PNO cache flush
    path('api/sre/pno/flush/', sre_view_operations.pno_cache_delete_by_item),
    path('api/sre/pno/data/', sre_view_operations.pno_cache_get_by_item),
    path('api/sre/containers/', sre_view_operations.get_containers),

    path('api/sre/upload/', sre_view_operations.upload),
    path('api/sre/job/<str:job_id>/status/', sre_view_operations.check_batch_job_status_by_item),
    path('api/sre/job/<str:job_id>/', sre_view_operations.download_app_details),
    path('api/sre/item/anomalies/', sre_view_operations.get_item_anomaly_alerts),
    path('api/sre/itemOffer/anomalies/', sre_view_operations.get_item_anomaly_alerts_batch),
    path('api/sre/item/visibility/', sre_view_operations.get_item_visibility_batch),

    path('api/sre/sync', sync_view.sre_to_mms_sync),
    path('api/mms/sync', sync_view.mms_to_sre_sync),
    path('api/sync/wcnp', sync_view.wcnp_sync),
    path('api/sync/wcp', sync_view.wcp_sync),
    path('api/sync/sql', sync_view.sql_sync),
    path('api/sync/megacache', sync_view.megacache_sync),
    path('api/sync/oracle', sync_view.oracle_sync),
    path('api/sync/kafka', sync_view.kafka_sync),
    path('api/sync/cosmos', sync_view.cosmos_sync),
    path('api/sync/cassandra', sync_view.cassandra_sync),
    path('api/sync/oneops', sync_view.oneops_sync),
    path('api/sync/custom', sync_view.custom_sync),
    path('api/jobs/<str:job_id>', sync_view.job_status),
    
    
    path('api/alerts/search', sync_view.search_alerts_view),
    path('api/alerts/match',sync_view.matched_alerts_view),
    path('api/alerts/searchall',sync_view.search_match_view),
    path('api/bulk/alerts/edit',sync_view.bulk_edit_alerts_view),
    path('api/alerts/edit',sync_view.edit_alerts_view),
    path('api/alerts/files',sync_view.get_files_by_service),
    # path('api/alerts/bulk/disable',sync_view.bulk_disable_alerts_view),
    # alert modifier
    path('api/xmatters/event/slackreport/', utils_view.generate_slack_report),
    path("api/xmatters/event/emailreport/", utils_view.generate_email_report),
    # xmatters apis
    path('api/inventory/validator/', sync_view.get_validator_results_view),
    # inventory validator

    path('api/alerts/id/<str:alert_id>/', sre_view_operations.get_alert_by_id),
    path('api/alerts/criteria/', sre_view_operations.get_alerts_by_criteria),
    # alerts by criteria and alert id

    path('api/xmatters/alerts/', utils_view.get_xmatters_events),
    path('api/zscore/wcnp',utils_view.trigger_anomaly_detection),


    path('api/anomaly',utils_view.trigger_anomaly_detection_all),
    path('api/anomaly/inventory',utils_view.trigger_grouped_anomaly_detection),

    path('api/analyze/namespace/details/', sre_view_operations.analyze_metrics_by_namespace_app),
    path('api/kitt/update/', sre_view_operations.update_kitt_file),
    path('api/analyze/apps/', sre_view_operations.analyze_metrics_by_apps),
    path('api/prometheus/pods/', sre_view_operations.get_pods_metrics),

    # inventory validator
]

urlpatterns += staticfiles_urlpatterns()
