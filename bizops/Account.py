import logging
import requests, time, json
from Crypto.Hash import SHA256
from Crypto.PublicKey import RSA
from Crypto.Signature import PKCS1_v1_5
from base64 import b64encode, b64decode
import time
from base64 import b64encode, b64decode

logger = logging.getLogger(__name__)

# BASE_URL = "https://single-profile-qa3.ca.glb.us.walmart.net"
BASE_URL = "https://single-profile-prod.ca.glb.us.walmart.net"


# Create your tests here.
class AccountDeletion:
    SEARCH_URL = f"{BASE_URL}/profile/api/v2/search"
    DELETE_URL = f"{BASE_URL}/profile/api/v1/profile/delete"

    CONSUMER_ID = "2bd2dd71-6359-4167-af21-66a1f0a34a36"
    PRIVATE_KEY = 'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDTFrLeYIK3w+kYWS24Tn3IeuNNKe4AT2CMZnBF7e1UOUbiCYi6vdN+bJ7mHywnoR3E7qqvdqlq2XlMHhXOgvclBm58rSBL6cx1v/G+Q3U+NTM6wBe3ANpcq75MohS9ImE+a2EjARkC+Ox2NJl5DUW6KPc1RnRcFq1+kQ0dM2IrJT3cotBktsERtE8Z5G2Zai/NW2Jv2VmzhnSvQdZBNYcW2VVy3VcskmQyGB/xJgGsS2gFpS/cizmqQUMk50ajL7i2jbgfAo7A7uJux+y99jNhsae4ac9qwiUJXO1Z9c5Bg93T+wDTinNQBgjFP2zPrnVIZEKCtry9Ptng1k2q6m9pAgMBAAECggEAKCBGfDNIZfpd0v0QzdpN2tBRaRexYGm2ZroKWaCy3rHMoadCUI1JKN0pD4GZeScNa6Qa2iPqbJ9OiYOmjY0jwewJELYwT33wGeoU4CAep2ahiGd/mpAU8kzLbfHZk2J3rc/ntSm+QWxkNswk6Hf3az3iUERGVkMRU9/q6EQeQykGxRbA6E3fe3KrLZHkVllMSA+vLGJ8MUpGmusPwL5gX718g/fDKWJ4gLBvLRdRd4U7+jKHl9lEqPCQVBjvgOEFQKnvrrLuOE4PUgSdf6NAc9+9KtQEfzfkHMeoV+lWtlnNMEzL4/TB0MxEIp19/GYEjr5sQ72q7vYuRxyseOGLqQKBgQDtglkcQKWrvJbN9iAkzrond5DDIzMDnvk2lW1DLLr46yzQBB1ZdISlx9zx2HBM3lOSXPyk97FqVYPoD81XTtVVMCAOHXkFhI7wiXUmrnIEgLs6ZKt7QHI+5IWZRs8D6hzclhuPXSKGAdq5xwf1H9WdKtw+6XjVVp0Q8Jvqdu6U6wKBgQDjhceLzUrpqL8r5bpMpmfPbWxw8HlH5/NfnMaWXzFBJn+Yg2ql83Ku2yZDjzn9U1EmrMH8kEDVFJn8QWuQWwqS1eM9FUBx3eJwZOSkBrS31ec9jzqqE3Qew2b7+CgXvEmhEfB0M1yWrgIb7lxHcKNbHtOIGHEZKGL2KSukT8QH+wKBgQDf9XfdokAWbuCtGhc5Xg4/RQmmWDy0f2V0SPcczsMIEYrSI+8g+TRTtuDq1DMg6MxZSYGadU5AfJ7DZpM0bsvPZm/zn2sM/vBidNuZPAmboV/rMh5ZPG3+TDKGVDTE9i98+bbHOwVg48LCR6GDTR968n14fWNJmEDHRntfXgKfPQKBgGNxQToVTDy8CHJDQh3mTVEX3mGueRO4iWKsvw9SI9D55qLeLWrC8MkD98FnhEz4cWuZ7l9ZHT0EeBkM7tp1MDXlTiCQfU5HUIUXU+dtFueSkjMRuu5C+mnHtedDGyLkpLnFgXcIS9t/ymQy1hpTXNKisom1RRld0YMNk4mJY4CpAoGBAIGW8ym2su+hKDZP1gjw83Np9LWai6Ntjs/8fE5OlJrDS5liysUzrm+qHHWfY9IqJaWyxQveEDIaC+JQk5+T/YlHgRuUrD3NHgLzRQweA1va0LNACl8xma49d+n7ZivUPx7+ofjmUP4WwylOxku73iKA0x9Ss9eEfXHk2vxJ4ouk'
    # CONSUMER_ID = "0e2c8c23-43e3-448e-a8e9-29698ed4b7b9"
    # PRIVATE_KEY = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCy6MgLO13b0YMLdo2Jjqaxhsf0Bqp0RtNmW9OXyasfTtz7gB2b6yvThNlz2yykMHciuDrbT+4LYkf5uKQeYDzhasABe4lgWFUNL/cb7z+Vi1Gw6NBDeB8wblmOzD9XMOw5dGI/jscAuXzKw5SgzD0Zw3/ADJC/3dLr9dyn07C+xuUPKhsQdldkjr5UnkKUcahP8KO0yYd52uOZjvBOXIKbhg2rk1mVmE4bgoWEjlQrDX4ReUmS6Np1Um8KkAYr0f89o7r7ugClv7yaJzTgEKHepBdx9ZGwiokw73KFIuDnjxj2szi0u1d7JrTD7pVI9iLuYQ+hW9MyWiMqR3muTMOjAgMBAAECggEBAJ18LUM90eQCrAD2LabtjCxvASn8iCTsh3/tyvctDVX03K8CWo36WEzL/4JtZwFsrCJC24Rb+qXIsKTQdJ9MharDhhqH/Shy+QMvuqy7UFQ6HAHHHjSV05aVPH5nqkX9CaOVoDpkWyZ3ILN3sChPQGWiYpzSMsTB5b3RYZfVzxpOjbV9xMYZonUkejOwz8DJWvlP+jqdoMm41weE38QbvdK/UytyS2FsgPREXr+hSKQ1yvmajUz6MBaVSA6U776uqLMkzUITl/+SZLH8SpUYkt/3467pZq/eXYXZj9iQ+EHG9hALr7435iLuWy6vEhRd/P2I9+N5mXd2yFPw7XBHD0ECgYEA6zK16MKiHUHIIX/hCH7p3k89A7xmRF1yu8J1sHMrGXTgdtRZPAbHFvO10O375+e+hI3kLGcsLg4lrbjgefTttXhUyM+8GA7/WyzCUFidd9Shp2WOYs7mXMk1eKJpfpmGEh2t65GTffxv6Z8KFkUYjIuk3xT0bBby6BZR2qbzfcMCgYEAwruYiymxIdYrh9zXlrCA+nr9DR51XHzr7k9LqeIynRc8A9MP/8Ph5054xddtWsJGWyXl8e27nNG5Xt8uxHiuf5vBzD+um8hJWrq0sPnWPOjltLLlg5/0taHXOuQr78oggtxL+Ij+1fjnv5118GuO8oaC2YVdHd+NaabURECj5KECgYAQRYPcRIrep0xnNOempNEdNjIsbf5NQIHNh5iIyo7yxek5j1tVEcr4RYgQmkZRNvExTne4srKVxRjKOf4dsdo4Mcj705NZvr/f2OrgkCDkx2sfn6EIKPQ6+xid7e6KeaiS3EbKA1pG3w4HBZ+3BrS6FHclj9eRGs1XTSQAkohJ7QKBgQC668/VZUO+MxK962KWJv7ncL8OpLoK+W6O5la+z32+BNt64FJvM9vYaB7N6afygqF+RLagr45zTW0egCUoOp9lpmE0abx1lJ+1E5r2dhsLhJNaDI769cOzxZIP2Y3DMsZaVpQmEXpscewipkbbbKNecXeQzM9BKaFYVOdUTKgFgQKBgQCsPRxZnJ5HqLoit7hNUHgDwGm23A9wqN/CywtubCecs8RpvOxFsAvacM4wrHOMtxSj+qlEcGIjF4YmRXnmIAAQBHnEpvZJ1JPtY1WhGHWxJwbZDI2KDN9uIgx9rwAUDEFPO67nCh5tZp3LocjQevNl99u+2GkCXzPxe7bdmyFTHg=="
    ENV = "Prod"

    @staticmethod
    def headers(signature, timestamp):
        headers = {'Content-type': 'application/json',
                   'WM_CONSUMER.ID': AccountDeletion.CONSUMER_ID,
                   'WM_SVC.NAME': 'CA-PROFILESRVCS',
                   'WM_SVC.ENV': AccountDeletion.ENV,
                   'WM_SEC.AUTH_SIGNATURE': signature,
                   'WM_CONSUMER.INTIMESTAMP': timestamp,
                   'WM_SEC.KEY_VERSION': '1'

                   }
        return headers

    @staticmethod
    def get_account_id(email):
        data = {"query": {
            "account": [{"email": {"operator": "eq", "value": email, "ignoreCase": False},
                         "lastLoginAt": {"sort": "DESC"}}]
        }
        }
        time_stamp, sig = AccountDeletion.generate_auth_signature()
        headers = AccountDeletion.headers(sig, time_stamp)
        r = requests.post(AccountDeletion.SEARCH_URL, data=json.dumps(data), headers=headers, verify=False,
                          timeout=100)
        profile_id = None
        status = False
        if r.ok:
            res_as_json = r.json()
            if res_as_json.get("size") == 0:
                return status, profile_id, time_stamp, sig
            profiles = res_as_json.get('profileRespList')
            if len(profiles) == 1:
                profile_id = profiles[0].get('profile').get('profileId')
        if profile_id:
            status = True
        return status, profile_id, time_stamp, sig

    @staticmethod
    def delete_account(email):
        logger.info(f"Deleting account for {email}")
        status = False
        is_email_not_found = False
        find_status, profile_id, time_stamp, sig = AccountDeletion.get_account_id(email)
        if not find_status:
            status = True
            is_email_not_found = True
            return email, status, is_email_not_found
        data = {"username": email,
                "profileId": profile_id
                }
        headers = AccountDeletion.headers(sig, time_stamp)
        headers.update({"WM_CONSUMER.USER_TYPE": "enterprise"})
        r1 = requests.post(AccountDeletion.DELETE_URL, data=json.dumps(data), headers=headers, verify=False,
                           timeout=100)
        if r1.ok:
            # Todo: Have Validations, after validation set status = True
            res_as_json = r1.json()
        # Todo: Have Validations, after validation set status = True
        find_status, profile_id, time_stamp, sig = AccountDeletion.get_account_id(email)
        if not find_status:
            status = True
        return email, status, is_email_not_found

    @staticmethod
    def generate_auth_signature():
        """
        Generate auth signature for service mesh headers
        """
        key_version = '1'
        private_key = RSA.importKey(
            b64decode(AccountDeletion.PRIVATE_KEY)
        )
        consumer_id = AccountDeletion.CONSUMER_ID
        intimestamp = int(time.time()) * 1000
        data = consumer_id + '\n' + str(intimestamp) + '\n' + key_version + '\n'
        signer = PKCS1_v1_5.new(private_key)
        digest = SHA256.new()
        digest.update(data.encode('utf-8'))
        sign = signer.sign(digest)
        return str(intimestamp), b64encode(sign).decode('utf-8')


if __name__ == "__main__":
    AccountDeletion.delete_account("<EMAIL>")
