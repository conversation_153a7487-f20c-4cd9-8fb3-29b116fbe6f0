from django.shortcuts import render
import logging
import requests, time, json
from drf_yasg import openapi
from rest_framework.response import Response
from rest_framework.decorators import api_view
from drf_yasg.utils import swagger_auto_schema
from concurrent.futures import ThreadPoolExecutor
from bizops.Account import AccountDeletion
from libs.ad_group_validation import does_user_belongs_to_ad_group
from slack_bot.slack import send_message
logger = logging.getLogger(__name__)


#
@swagger_auto_schema(method='post')  # get_template_variables_and_default_vars
@api_view(['POST'])
def delete_user_accounts(request):
    access_ad_group = ["ca_fe_support","GEC-at-Support-RapidResponseTeam","iecatsupport-rapidre","intl-sre-auto-us"]
    results = list()
    message = None
    try:
        by_pass = bool_convertor(request.query_params.get("by_pass", False))
        user_id = request.data.get("user_id")
        user_email = request.data.get("email")
        display_name = request.data.get("display_name")
        is_user_has_ad_group_have_access = does_user_belongs_to_ad_group(user_id, access_ad_group)
        send_message("juno_logs", f"User called , {request.get_full_path()} user_id {user_id}")
        if not by_pass:
            if not is_user_has_ad_group_have_access:
                return Response({"ok": True, "body": {"message": "User does not have access, "
                                                                 "should be part of {}".format(access_ad_group)}},
                                status=401)

        for email in request.data.get("emails"):
            logger.info(f"Deleting account of {email} by {display_name}, user_id is {user_id}, email is {user_email}")
            email, status, status_404 = AccountDeletion.delete_account(email=email)
            if status_404:
                message = "Email not found"
            res = {
                "email": email,
                "status": status,
                "message": message
            }
            results.append(res)

        return Response({"ok": True, "body": {"results": results}}, status=200)
        # results = list()
        # futures = list()
        # post_data = request.data
        # pool = ThreadPoolExecutor(5)
        # for email in post_data.get("emails"):
        #     futures.append(pool.submit(AccountDeletion.delete_account, email=email))
        # for future in futures:
        #     try:
        #         res = future.result()
        #         results.append(res)
        #         future.done()
        #     except Exception as e:
        #         logger.exception(e)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


#
@swagger_auto_schema(method='post')  # get_template_variables_and_default_vars
@api_view(['POST'])
def delete_user_accounts_in_parallel(request):
    access_ad_group = ["ca_fe_support", "GEC-at-Support-RapidResponseTeam","iecatsupport-rapidre"]
    results = list()
    message = None
    try:
        by_pass = bool_convertor(request.query_params.get("by_pass", False))
        user_id = request.data.get("user_id")
        user_email = request.data.get("email")
        display_name = request.data.get("display_name")
        send_message("juno_logs", f"User called , {request.get_full_path()} user_id {user_id}")
        is_user_has_ad_group_have_access = does_user_belongs_to_ad_group(user_id, access_ad_group)
        if not by_pass:
            if not is_user_has_ad_group_have_access:
                return Response({"ok": True, "body": {"message": "User does not have access, "
                                                                 "should be part of {}".format(access_ad_group)}},
                                status=401)

        # for email in request.data.get("emails"):
        #     logger.info(f"Deleting account of {email} by {display_name}, user_id is {user_id}, email is {user_email}")
        #     email, status, status_404 = AccountDeletion.delete_account(email=email)
        #     if status_404:
        #         message = "Email not found"
        #     res = {
        #         "email": email,
        #         "status": status,
        #         "message": message
        #     }
        #     results.append(res)
        # return Response({"ok": True, "body": {"results": results}}, status=200)

        futures = list()
        post_data = request.data
        pool = ThreadPoolExecutor(len(post_data.get("emails")))
        for email in set(post_data.get("emails")):
            logger.info(f"Parallel task:: Deleting account of {email} by {display_name}, user_id is {user_id}, "
                        f"email is {user_email}")
            futures.append(pool.submit(AccountDeletion.delete_account, email=email))
        for future in futures:
            try:
                email, status, status_404 = future.result()
                if status_404:
                    message = "Email not found"
                res = {
                    "email": email,
                    "status": status,
                    "message": message
                }
                results.append(res)
                future.done()
            except Exception as e:
                logger.exception(e)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)

    return Response({"ok": True, "body": {"results": results}}, status=200)


def bool_convertor(val):
    if type(val) != bool:
        val = val.lower()
        if val in ('y', 'yes', 't', 'true', 'on', '1'):
            return True
        elif val in ('n', 'no', 'f', 'false', 'off', '0'):
            return False
        return False
    else:
        return val
