import os

# CORE LOCATIONS
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
CONFIG_FILE = "sha.txt"
TEMPLATE_BASE_DIR = os.path.join(BASE_DIR, 'templates')
CONFIG_DIR = os.path.join(BASE_DIR, 'config')
TEMPLATE_OUTPUT_DIR = os.path.join(BASE_DIR, 'output')
ASYNC = {
    "UPLOAD_FILE_NAME": "file_{job_id}",
    "SSH_SERVER": "heavy-forwarder-591532908-1-711019584.prod.mx-splunk.mexicoecomm.prod-az-westus-4.prod.us.walmart.net",
    "SSH_SERVER_BASE_FOLDER": "/app/juno",
}

# SRE to MMS sync Template Details
SRE_ORG_NAME = "intl-ecomm-svcs"
SRE_REPO_NAME = "sre-templates"
SRE_REPO_URL = "***************************:intl-ecomm-svcs/sre-templates.git"
SRE_FOLDER_ACCESS = "data_store/inventory"
BASE_URL = "https://gecgithub01.walmart.com/api/v3/repos"
API_TOKEN = "****************************************"  # Your personal access token with 'repo' scope
JUNO_BASE_URL = "https://juno.api.stg.walmart.com"
JUNO_HEADERS = {"Content-Type": "application/json",  # Assuming the API expects JSON
                "Accept": "application/json",  # Optional: Specifies that you expect JSON in response
                }
MMS_ORG_NAME = "intl-ecomm-svcs"
MMS_REPO_NAME = "mms-config"
MMS_REPO_URL = "***************************:intl-ecomm-svcs/mms-config.git"
MMS_FOLDER_ACCESS = "international-tech/intl-sre/golden-signals/rules/production/wcnp/sre-alerts"
ANALYSIS_BASE_REPO = "data_store/analysis"

TEMPLATE_POSTFIX = {
    "alerts": "alerts",
    "dashboard": "dashboard"
}
GIT = {
    'ssh_config': '~/.ssh/config',
    'ssh_key': '~/.ssh/id_rsa_2024',
    'raas_repo_name': 'production-playbooks',
    # threshold_disk_free in MB
    'threshold_disk_free': 100,
    'playbooks': {"wcnp": "yaml-playbooks/chaosmart/wcnp", "oneops": "yaml-playbooks/chaosmart/oneops"},
    'branch_format': '{user_id}_{time_stamp}',
    'org': 'CE-PERFREL'
}
PROMETHEUS_SERVER = "http://prometheus-query.prod.mms.walmart.net"
PROMETHEUS_NON_PROD_SERVER = "https://prometheus.query.non-prod.mms.walmart.net"

# TEMPLATES = {
#     "data": f"{TEMPLATE_BASE_DIR}/data",
#     "templates": f"{TEMPLATE_BASE_DIR}/templates"
# }
# GIT = {
#     'ssh_config': '~/.ssh/config',
#     'ssh_key': '~/.ssh/gieo_rsa',
#     'raas_repo_name': 'production-playbooks',
#     # threshold_disk_free in MB
#     'threshold_disk_free': 100,
#     'playbooks': {"wcnp": "yaml-playbooks/chaosmart/wcnp", "oneops": "yaml-playbooks/chaosmart/oneops"},
#     'branch_format': '{user_id}_{time_stamp}'
# }

ALERT_CHANNEL_MAPPER = {
    "slack": "mms_slack_channel",
    "email": "mms_email",
    "xmatters": "mms_xmatters_group"
}

CACHE_FLUSH_META_DATA = {
    "od": {
        "tenant_id": 7,
        "banner_id": 3,
        "vertical": "OD",
        "version": "v1"
    },
    "ea": {
        "tenant_id": 7,
        "banner_id": 2,
        "vertical": "EA",
        "version": "v2"
    },

}
CACHE_FLUSH_URL = {
    "delete": "https://catalog-service.walmart.com/automation/cache/cleanup",
    "get": "https://catalog-service.walmart.com/automation/cache",
    "consumer_id": ""
}
ITEM_VISIBLE_HEADER_DATA = {
    "mx-od": {
        "x-o-bu": "walmart-mx",
        "x-o-vertical": "OD",
        "WM_TENANT": "MX"
    },
    "mx-ea": {
        "x-o-bu": "walmart-mx",
        "x-o-vertical": "EA",
        "WM_TENANT": "MX"
    },
    "bodega-ea": {
        "x-o-bu": "bodega-mx",
        "x-o-vertical": "EA",
        "WM_TENANT": "MX"
    },
    "bodega-od": {
        "x-o-bu": "bodega-mx",
        "x-o-vertical": "OD",
        "WM_TENANT": "MX"
    },
}

odnBg_and_odnUpScale = dict()
odnBg = {"odnBg": "FFFFFF"}
odnUpScale = {"odnUpScale": 1}
odnBg_and_odnUpScale.update(**odnBg, **odnUpScale)
URI_EXTRA_PARAMS = {"ca":
    {"optimize_queue": {
        "add": odnBg,
        "rm": dict()
    },
        "torbit": {
            "add": odnBg,
            "rm": dict()
        },
        "akamai": {
            "add": odnBg,
            "rm": dict()
        }
    },
    "od": {"optimize_queue": {
        "add": odnBg_and_odnUpScale,
        "rm": dict()
    },
        "torbit": {
            "add": odnBg,
            "rm": dict()
        },
        "akamai": {
            "add": odnBg,
            "rm": dict()
        }
    },
    "sams": {"optimize_queue": {
        "add": odnBg_and_odnUpScale,
        "rm": dict()
    },
        "torbit": {
            "add": odnBg,
            "rm": dict()
        },
        "akamai": {
            "add": odnBg,
            "rm": dict()
        }
    },
    "ea": {"optimize_queue": {
        "add": odnBg_and_odnUpScale,
        "rm": dict()
    },
        "torbit": {
            "add": odnBg,
            "rm": dict()
        },
        "akamai": {
            "add": odnBg,
            "rm": dict()
        }
    },

    "us": {"optimize_queue": {
        "add": odnBg_and_odnUpScale,
        "rm": dict()
    },
        "torbit": {
            "add": odnBg,
            "rm": dict()
        },
        "akamai": {
            "add": odnBg,
            "rm": dict()
        }
    }
}

ITEM_VISIBILITY = {
    "url": "https://iro.walmart.com/v3/items/summary",
    "header": ITEM_VISIBLE_HEADER_DATA,
    "include_headers": {
        "WM_CONSUMER.ID": "8888ba88-d8cc-88bb-ab84-6888e88888a8_cs",
        "Content-Type": "application/json",
        "x-o-mart": "b2c"
    }

}

ITEM_DETAILS = {
    "url": "https://iro.walmart.com/v3/items/detail?locale=es_MX"
}

MX_ITEM_SERVICE = {
    "url": "http://mx-item-services-prod.walmart.com",
    "fullItems": "v2/items/fullItems/",
    "header": {
        "wm_svc.env": "1",
        "wm_get_errors": "true",
        "wm_mart": "walmart_mexico",
        "wm_market": "mexico",
        "wm_bu": "walmart_mexico",
        "wm_tenant.id": "mx",
        "Content-Type": "application/json",
        "wm_consumer.id": "3fbb252f-e308-4b9f-8630-d8ce984b2d34"
    }
}

XMATTERS_API_KEY = "x-api-key-9b8e35bd-3cea-43af-9ae9-9210fbe04837"
XMATTERS_API_SECRET = "062f39da-2c3b-4aa7-b466-4589f5870771"

FASTLY = {
    "key": "********************************",
    "service_ids": {
        "ca": "qKSuhG6BWAvRIsWDwLLNK4",
        "za": "fcuB0CGv71q69jxc1tcaU0"
    },
    "end_point": "https://api.fastly.com/service/{service_id}/purge",
    "headers": {
        "Fastly-Key": "********************************",
        "Accept": "application/json",
        "Content-Type": "application/json"
    }
}
