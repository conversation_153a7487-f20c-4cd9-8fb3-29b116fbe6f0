profiles:
  - gateway-disable
owner:
  group: perf-engg-us
notify:
  slack:
    channelName: juno-wcnp-deployments

setup:
  releaseRefs: ["main"]
  featureFlagMap:
    enableIstioSubChart: true
    useArtifactory: true
    imageValidation: false
    validatePreProdStageExists: false
build:
  skip: false
  version: "0.4.7"
  buildType: docker
  docker:
    dockerFile: Dockerfile

deploy:
  namespace: intl-sre
  releaseType:
    deployTimeout: 900
  helm:
    values:
      env:
        dbEnv: stopaProd
        cb_env: new_prod
        steller_api_v2_port: 8080
        pullImageOnRestart: yes
        logs_location: /app/logs
        artifactId: steller
        app_type: api
      global:
        networking:
          httpsEnabled: true
          gateway:
            enableIstioSubChart: true
          externalPort: "8080"
          internalPort: "8080"
      readinessProbe:
        path: "/ecv/"
        wait: 100
        timeout: 20
        port: 8080
      livenessProbe:
        path: "/ecv/"
        wait: 90
        timeout: 20
        port: 8080
  gslb:
    strategy: stage
    lbRoutings:
      prod:
        cnames: [ juno.api.walmart.com ]
      stage:
        cnames: [ juno.api.stg.walmart.com,juno.api.prod.walmart.com  ]
      dev:
        cnames: [ juno.api.dev.walmart.com ]
  stages:
    - name: prod
      target:
        cluster_id: [ "scus-prod-a74","uswest-prod-az-045" ]
      helm:
        values:
          scaling:
            cpuPercent: 80
            min: 15
            max: 40
          context:
            environment: prod
            environmentProfile: PROD
          min:
            cpu: 2
            memory: 8G
            ephemeralStorage: 15G
          max:
            cpu: 2
            memory: 8G
            ephemeralStorage: 15G
      refs: [ "develop" ]
    - name: stage
      target:
        cluster_id: [ "uswest-stage-az-006","uscentral-stage-az-300" ]
      helm:
        values:
          scaling:
            cpuPercent: 60
            min: 5
            max: 40
          context:
            environment: staging
            environmentProfile: STAGING
          min:
            cpu: 2
            memory: 8G
            ephemeralStorage: 15G
          max:
            cpu: 2
            memory: 8G
            ephemeralStorage: 15G
      refs: [ "main" ]
    - name: dev
      target:
        cluster_id: [ "uscentral-stage-az-300" ]
      helm:
        values:
          context:
            environment: staging
            environmentProfile: STAGING
          min:
            cpu: 2
            memory: 8G
            ephemeralStorage: 15G
          max:
            cpu: 2
            memory: 8G
            ephemeralStorage: 15G
      refs: [ "dev" ]
  postDeploy:
    - task:
        name: messageSlack
        text: "App deployed!"
        
        

