profiles:
  - gateway-disable
owner:
  group: perf-engg-us
notify:
  slack:
    channelName: juno-wcnp-deployments

setup:
  releaseRefs: ["main"]
  featureFlagMap:
    useArtifactory: true
    imageValidation: false

build:
  skip: false
  version: "0.3.3"
  buildType: docker
  docker:
    dockerFile: Dockerfile

deploy:
  namespace: omniperf-steller
  helm:
    values:
      env:
        dbEnv: stopaProd
        cb_env: new_prod
        steller_api_v2_port: 8080
        pullImageOnRestart: yes
        logs_location: /app/logs
        artifactId: steller
        app_type: bot
      global:
        networking:
          httpsEnabled: true
          gateway:
            enableIstioSubChart: true
          externalPort: "8080"
          internalPort: "8080"
      readinessProbe:
        path: "/ecv/"
        wait: 240
      livenessProbe:
        path: "/ecv/"
        wait: 360
  gslb:
    lbRoutings:
      stage:
        cnames: [juno.prod.walmart.com]
  stages:
    - name: stage
      target:
        cluster_id: ["uswest-stage-az-003"]
      helm:
        values:
          context:
            environment: staging
            environmentProfile: STAGING
          min:
            cpu: 4
            memory: 4G
          max:
            cpu: 4
            memory: 6G
      refs: ["main"]
    - name: dev
      target:
        cluster_id: [ "uswest-stage-az-003" ]
      helm:
        values:
          context:
            environment: staging
            environmentProfile: STAGING
          min:
            cpu: 2
            memory: 2G
          max:
            cpu: 2
            memory: 2G
      refs: [ "develop" ]
  postDeploy:
    - task:
        name: messageSlack
        text: "App deployed!"