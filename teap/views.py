import logging
from rest_framework.response import Response
from rest_framework.decorators import api_view
from common.teap_handler.teap_file_processor import SUPPORTED_MARKETS, SUPPORTED_TIERS
from common.teap_handler.teap_data_handler import TeapDataHandler
from common.alert.alertsPostDataBuilder import AlertsPostDataBuilder
from libs.oneClick import analyze_scalar_data

logger = logging.getLogger(__name__)
teap = None
# teap = TeapDataHandler().teap_data
from libs import wcnp_handler


# Market
@api_view(['GET'])
def get_apps_by_market(request, market):
    try:
        global teap
        if not teap:
            teap = TeapDataHandler().teap_data
        data = teap.get_apps_by_market(market=market)
        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# list market
@api_view(['GET'])
def get_list_of_markets(request):
    try:
        return Response({"ok": True, "body": SUPPORTED_MARKETS}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# Tier
@api_view(['GET'])
def get_apps_by_tier(request, tier):
    try:
        global teap
        if not teap:
            teap = TeapDataHandler().teap_data
        data = teap.get_apps_by_tier(tier=tier)
        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# List tier
@api_view(['GET'])
def get_list_of_tier(request):
    try:

        return Response({"ok": True, "body": SUPPORTED_TIERS}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# Platform
@api_view(['GET'])
def get_apps_by_platform(request, platform):
    try:
        global teap
        if not teap:
            teap = TeapDataHandler().teap_data
        data = teap.get_apps_by_platform(platform=platform)
        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# Market,Tier
@api_view(['GET'])
def get_apps_by_market_tier(request, market, tier):
    try:
        global teap
        if not teap:
            teap = TeapDataHandler().teap_data
        data = teap.get_market_and_tier(market=market, tier=tier)
        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# Market,Platform
@api_view(['GET'])
def get_apps_by_market_platform(request, market, platform):
    try:
        global teap
        if not teap:
            teap = TeapDataHandler().teap_data
        data = teap.get_market_and_platform(market=market, platform=platform)
        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# Market,Platform, Tier,
@api_view(['GET'])
def get_market_tier_platform(request, market, platform, tier):
    try:
        global teap
        if not teap:
            teap = TeapDataHandler().teap_data
        data = teap.get_apps_by_platform_and_tier_and_market(platform=platform, tier=tier, market=market)
        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# Platform,Tier
@api_view(['GET'])
def get_platform_tier(request, platform, tier):
    try:
        global teap
        if not teap:
            teap = TeapDataHandler().teap_data
        data = normalize_get_apps_by_platform_and_tier(platform=platform, tier=tier)
        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# Platform, Market
@api_view(['GET'])
def get_platform_market(request, platform, market):
    try:
        global teap
        if not teap:
            teap = TeapDataHandler().teap_data
        data = teap.get_apps_by_market_platform(platform=platform, market=market)
        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# WCNP
@api_view(['GET'])
def get_platform(request):
    try:
        global teap
        if not teap:
            teap = TeapDataHandler().teap_data
        data = teap.get_apps_by_platform()
        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# WCNP,Tier-0
@api_view(['GET'])
def get_platform_tier_default(request):
    try:
        global teap
        if not teap:
            teap = TeapDataHandler().teap_data
        data = teap.get_apps_by_platform_and_tier()
        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def get_platform_tier_1(request):
    try:
        global teap
        if not teap:
            teap = TeapDataHandler().teap_data
        data = teap.get_apps_by_platform_and_tier(tier="one")
        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# WCNP,MX,Tier-0
@api_view(['GET'])
def get_wcnp_tier_zero_mx_apps(request):
    try:
        global teap
        if not teap:
            teap = TeapDataHandler().teap_data
        data = teap.get_apps_by_platform_and_tier_and_market(platform="wcnp", tier="zero", market="MX")
        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# WCNP,CA,Tier-0
@api_view(['GET'])
def get_wcnp_tier_zero_ca_apps(request):
    try:
        global teap
        if not teap:
            teap = TeapDataHandler().teap_data
        data = teap.get_apps_by_platform_and_tier_and_market(platform="wcnp", tier="zero", market="CA")
        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# WCNP,MX,Tier-1
@api_view(['GET'])
def get_wcnp_tier_one_mx_apps(request):
    try:
        global teap
        if not teap:
            teap = TeapDataHandler().teap_data
        data = teap.get_apps_by_platform_and_tier_and_market(platform="wcnp", tier="one", market="MX")
        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# WCNP,CA,Tier-1
@api_view(['GET'])
def get_wcnp_tier_one_ca_apps(request):
    try:
        global teap
        if not teap:
            teap = TeapDataHandler().teap_data
        data = teap.get_apps_by_platform_and_tier_and_market(platform="wcnp", tier="one", market="CA")
        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# Search by name filed
@api_view(['GET'])
def search(request, query):
    try:
        global teap
        if not teap:
            teap = TeapDataHandler().teap_data
        data = query_teap_sheet(query)
        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# Search by namespace_Assembly_db filed
@api_view(['GET'])
def get_apps_by_searching_namespace_Assembly_db_field(request, query):
    try:
        global teap
        if not teap:
            teap = TeapDataHandler().teap_data
        data = process_search_namespace_assembly_db_field(query)
        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# Get app by name
@api_view(['GET'])
def get_app_by_name(request, app_name):
    try:
        global teap
        if not teap:
            teap = TeapDataHandler().teap_data
        data = teap.get_app_name(app_name)
        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# WCNP,MX,Tier-0
@api_view(['GET'])
def analyze_wcnp_namespaces_standards_tier_zero(request):
    try:
        data = analyze_wcnp(request, "zero")
        return Response({"ok": True, "body": data, "url": request.get_full_path()}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def wcnp_pull_all_tier_zero(request):
    try:
        data = wcnp_handler.get_wcnp_helm_data_from_config_store("zero")
        return Response({"ok": True, "body": data, "url": request.get_full_path()}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def wcnp_pull_all_tier_one(request):
    try:
        data = wcnp_handler.get_wcnp_helm_data_from_config_store("one")
        return Response({"ok": True, "body": data, "url": request.get_full_path()}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def analyze_wcnp_namespaces_standards_tier_one(request):
    try:
        data = analyze_wcnp(request, "one")
        return Response({"ok": True, "body": data, "url": request.get_full_path()}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def analyze_hpa_ratio_tier_zero(request):
    try:

        return Response({"ok": True, "body": hpa_ratio_breached(request, "zero"),
                         "url": request.get_full_path()}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def analyze_hpa_ratio_tier_one(request):
    try:

        return Response({"ok": True, "body": hpa_ratio_breached(request, "one"),
                         "url": request.get_full_path()}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def analyze_canary_tier_zero(request):
    try:

        return Response({"ok": True, "body": canary_breached("zero"), "url": request.get_full_path()}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def analyze_canary_tier_one(request):
    try:
        return Response({"ok": True, "body": canary_breached("one"), "url": request.get_full_path()}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def analyze_vertical_scaling_tier_zero(request):
    try:

        return Response({"ok": True, "body": vertical_scaling_breached("zero"), "url": request.get_full_path()},
                        status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def analyze_vertical_scaling_tier_one(request):
    try:
        return Response({"ok": True, "body": vertical_scaling_breached("one"), "url": request.get_full_path()},
                        status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def analyze_canada_one_click_scalar_post_stress_test(request):
    try:

        url = "https://gecgithub01.walmart.com/raw/SRE/production-playbooks/main/yaml-playbooks/international-tech/" \
              "intl-canada/ca-wcnp-scaler-holiday-2023/" \
              "ca-normal-mode-scale.yaml?token=GHSAT0AAAAAAAADOAUDWYQKSOWWXSSRP3W2ZKSW5NA"
        res = analyze_scalar_data(url)
        return Response({"ok": True, "body": res, "url": request.get_full_path()}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def analyze_canada_one_click_scalar_pre_stress_test(request):
    try:
        url = "https://gecgithub01.walmart.com/raw/SRE/production-playbooks/main/yaml-playbooks/international-tech/" \
              "intl-canada/ca-wcnp-scaler-holiday-2023/ca-stress-test-scale-100pct.yaml?" \
              "token=GHSAT0AAAAAAAADOAUDOUNJKCPSPTGKUY5SZKSW4GA"
        res = analyze_scalar_data(url)
        return Response({"ok": True, "body": res, "url": request.get_full_path()}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def analyze_mexico_one_click_scalar_pre_stress_test(request):
    try:
        url = "https://gecgithub01.walmart.com/raw/SRE/production-playbooks/main/yaml-playbooks/international-tech/" \
              "intl-mexico/mx-wcnp-scaler-holiday-2023/" \
              "mx-scale-event-mode-100pct.yaml?token=GHSAT0AAAAAAAADOAUCDTP3YZDY5JLCPLJIZKSW7KA"
        res = analyze_scalar_data(url)
        return Response({"ok": True, "body": res, "url": request.get_full_path()}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def analyze_click_scalar_processor(request):
    try:
        url = request.data.get("url")
        if not url:
            return Response({"ok": False, "body": {"message": "provide url in the post body"}}, status=400)

        res = analyze_scalar_data(url)
        return Response({"ok": True, "body": res, "url": request.get_full_path()}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def analyze_mexico_one_click_scalar_post_stress_test(request):
    try:
        url = "https://gecgithub01.walmart.com/raw/SRE/production-playbooks/main/yaml-playbooks/international-tech/" \
              "intl-mexico/mx-wcnp-scaler-holiday-2023/" \
              "mx-scale-normal-mode.yaml?token=GHSAT0AAAAAAAADOAUD3BY4QIYOM3QMHYEIZKSXBTA"
        res = analyze_scalar_data(url)
        return Response({"ok": True, "body": res, "url": request.get_full_path()}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


def analyze_wcnp(request, tier):
    res = dict()
    hpa_threshold_rate = float(request.query_params.get("hpa_threshold_rate", 2.5))
    validate_slas = bool_convertor(request.query_params.get("validate_slas", True))
    data = wcnp_handler.get_wcnp_not_followed_standards_apps(tier, hpa_threshold_rate=hpa_threshold_rate,
                                                             validate_slas=validate_slas)
    res["breached_apps"] = wcnp_handler.club_all_clusters_into_one_namespace_app(data.get("breached_apps"))
    res["not_breached"] = wcnp_handler.club_all_clusters_into_one_namespace_app(data.get("not_breached"))
    return res


def hpa_ratio_breached(request, tier):
    res = dict()
    hpa_threshold_rate = float(request.query_params.get("hpa_threshold_rate", 2.5))
    data = wcnp_handler.hpa_breached(tier, hpa_threshold_rate)
    res["breached_apps"] = wcnp_handler.club_all_clusters_into_one_namespace_app(data.get("breached_apps"))
    res["not_breached"] = wcnp_handler.club_all_clusters_into_one_namespace_app(data.get("not_breached"))
    return res


def canary_breached(tier):
    res = dict()
    data = wcnp_handler.canary_breached(tier)
    res["breached_apps"] = wcnp_handler.club_all_clusters_into_one_namespace_app(data.get("breached_apps"))
    res["not_breached"] = wcnp_handler.club_all_clusters_into_one_namespace_app(data.get("not_breached"))
    return res


def vertical_scaling_breached(tier):
    res = dict()
    data = wcnp_handler.vertical_scaling_breached(tier)
    res["breached_apps"] = wcnp_handler.club_all_clusters_into_one_namespace_app(data.get("breached_apps"))
    res["not_breached"] = wcnp_handler.club_all_clusters_into_one_namespace_app(data.get("not_breached"))
    return res


@api_view(['GET'])
def analyze_probes_tier_one(request):
    try:
        return Response({"ok": True, "body": probs_uri("one"), "url": request.get_full_path()},
                        status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def analyze_probes_tier_zero(request):
    try:
        return Response({"ok": True, "body": probs_uri("zero"), "url": request.get_full_path()},
                        status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


def probs_uri(tier):
    res = dict()
    data = wcnp_handler.prob_urls(tier)
    return data


def bool_convertor(val):
    if type(val) != bool:
        val = val.lower()
        if val in ('y', 'yes', 't', 'true', 'on', '1'):
            return True
        elif val in ('n', 'no', 'f', 'false', 'off', '0'):
            return False
        return False
    else:
        return val


# def _process_wcnp_standards(tier, hpa_threshold_rate=2.5, validate_slas=False):
#     global teap
#     if not teap:
#         teap = TeapDataHandler().teap_data
#     data = teap.get_apps_by_platform_and_tier_and_market(platform="wcnp", tier=tier)
#     results = get_wcnp_hpa_data(data, tier)
#
#     if not validate_slas:
#         return results
#
#     breached_apps = list()
#     for _data in results:
#         if len(_data) == 0:
#             continue
#
#         _sla_breached = False
#         for cluster in _data:
#             if cluster.get("hpa_ratio") < hpa_threshold_rate:
#                 _sla_breached = True
#             if not cluster.get("is_canary_enabled"):
#                 _sla_breached = True
#             if cluster.get("is_vertical_scaling_enabled"):
#                 _sla_breached = True
#
#             if _sla_breached:
#                 breached_apps.append(cluster)
#     return breached_apps


def query_teap_sheet(query):
    return teap.search_name_field(query)


def process_search_namespace_assembly_db_field(query):
    return teap.get_one_matching_leaf_node_v1(query)


def normalize_get_apps_by_platform_and_tier(platform, tier):
    return teap.get_apps_by_platform_and_tier(platform=platform, tier=tier)


def normalize_get_apps_by_platform_and_tier_market(platform="wcnp", tier="zero", market="MX"):
    return teap.get_apps_by_platform_and_tier_and_market(platform=platform, tier=tier, market=market)


def normalize_get_apps_by_platform_and_market(platform="wcnp", market="MX"):
    return teap.get_apps_by_market_platform(platform=platform, market=market)


def normalize_get_apps_by_platform_and_market_and_domain(platform="wcnp", market="MX", domain="Digital Experience"):
    return teap.get_apps_by_market_platform_domain(platform=platform, market=market, domain=domain)

# get_apps_by_market_domain
# get_apps_by_market_platform
# search_namespace_Assembly_db_field
# @api_view(['GET'])
# def get_apps_by_tier(request, tier):
#     try:
#         data = teap.get_apps_by_tier(tier=tier)
#         return Response({"ok": True, "body": list(data)}, status=200)
#     except Exception as e:
#         return Response({"ok": False, "body": {"message": str(e)}}, status=400)
#
#
# @api_view(['GET'])
# def get_apps_by_tier_market_platform(request, tier, market, platform):
#     try:
#         data = teap.get_apps_by_market_platform(platform=platform, market=market)
#         return Response({"ok": True, "body": list(data)}, status=200)
#     except Exception as e:
#         return Response({"ok": False, "body": {"message": str(e)}}, status=400)
