import logging, csv
import json  
import math
import os
import yaml
import uuid
import threading
from datetime import datetime
from common.alert.list_alerts import ListAlertInfo
from slack_bot.slack import WMTSlack
from template.views import generate_template_compiled_files
from libs.xmatters import invoke_xmatters
from libs.xmatters import generate_report as xmatters_generate
from libs.xmatters import get_xmatters_events
from libs.xmatters import email_report
from rest_framework.response import Response
from libs.prometheus_client import Prometheus
from rest_framework.decorators import api_view
from common.alert.alert_modifier import AlertModifier
from template_engine.compile import get_latest_templates
from common.alert.disabled_alert_pickup import DisabledAlertPickup
from common.alert.update_threshold import UpdateThreshold
from common.alert.alertsPostDataBuilder import AlertsPostDataBuilder
from common.alert.alerts_list_handler import process_filters_and_disable_and_enable_alerts, \
    add_filter_to_exclude_dashboards, add_sre_alerts_filter, add_app_alerts_filter
from common.data_store.data_processor import Data
from analysis.wcnp_metric_handler import WcnpMetrics
from analysis.anomaly_orchestrator import AnomalyDetectionOrchestrator

logger = logging.getLogger(__name__)

slack = WMTSlack()
pro = Prometheus()
# sub_details = pro.get_cosmos_subscription()

get_latest_templates()

# Dictionary to store async job status
async_jobs = {}

# Thread to process WCNP alerts asynchronously
def process_wcnp_alerts_async(job_id, template, tier, is_sre_sla, request_data, query_params):
    try:
        # Update job status to in-progress
        async_jobs[job_id]["status"] = "in-progress"
        async_jobs[job_id]["start_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Create alert data
        force_pull = bool_convertor(query_params.get("force_pull", False))
        sre_override_xmatters_and_slack = bool_convertor(query_params.get("override_xmatters_and_slack", True))
        get_latest_templates(force_pull_required=force_pull)
        
        slack.send_message("juno_logs", text=f"Processing async job {job_id} for template {template}")
        
        alert_data = AlertsPostDataBuilder(template, is_sre_sla=is_sre_sla, tier=tier)
        post_body = alert_data.build_post_body(is_sre_sla=is_sre_sla, persist_data=False,
                                          sre_override_xmatters_and_slack=sre_override_xmatters_and_slack)
        
        # Generate the template files
        tm = None
        try:
            from template.views import TemplateManager
            tm = TemplateManager(template, post_body, execute_child_templates=False)
            status, res = tm.execute()
            
            # Update job status based on result
            if status:
                async_jobs[job_id]["status"] = "completed"
                async_jobs[job_id]["result"] = res
            else:
                async_jobs[job_id]["status"] = "failed"
                async_jobs[job_id]["error"] = str(res)
                
        except Exception as e:
            logger.exception(e)
            async_jobs[job_id]["status"] = "failed"
            async_jobs[job_id]["error"] = str(e)
        finally:
            # Cleanup
            if tm:
                try:
                    tm.clean_up()
                except Exception as e:
                    logger.error(f"Unable to cleanup output location and cloned location: {e}")
                    
        async_jobs[job_id]["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
    except Exception as e:
        logger.exception(e)
        async_jobs[job_id]["status"] = "failed"
        async_jobs[job_id]["error"] = str(e)
        async_jobs[job_id]["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")


@api_view(['POST'])
def create_wcnp_alerts_async(request):
    """
    Start an asynchronous job to create WCNP alerts.
    This endpoint returns immediately with a job ID that can be used to check status.
    """
    try:
        # Validate password
        password = request.data.get("password", False)
        if not password or password != "intl-sre-automation":
            return Response({"ok": False, "body": "Please provide valid password"}, status=400)
            
        # Get parameters
        tier = request.data.get("tier", None)
        is_sre_sla = request.data.get("is_sre_sla", False)
        template = "wcnp_alerts.yaml"
        
        # Create a unique job ID
        job_id = str(uuid.uuid4())
        
        # Initialize job in the tracking dictionary
        async_jobs[job_id] = {
            "id": job_id,
            "template": template,
            "tier": tier,
            "is_sre_sla": is_sre_sla,
            "status": "queued",
            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "result": None,
            "error": None
        }
        
        # Start background processing thread
        thread = threading.Thread(
            target=process_wcnp_alerts_async,
            args=(job_id, template, tier, is_sre_sla, request.data, request.query_params)
        )
        thread.daemon = True
        thread.start()
        
        slack.send_message("juno_logs", text=f"Started async WCNP alerts job {job_id}")
        
        # Return job ID immediately
        return Response({
            "ok": True,
            "body": {
                "job_id": job_id,
                "message": "WCNP alerts generation started in the background",
                "status_url": f"/api/wcnp_alerts_job_status/{job_id}/"
            }
        }, status=202)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def get_wcnp_alerts_job_status(request, job_id):
    """
    Get the status of an asynchronous WCNP alerts generation job.
    """
    try:
        if job_id not in async_jobs:
            return Response({
                "ok": False,
                "body": {"message": f"Job ID {job_id} not found"}
            }, status=404)
            
        job = async_jobs[job_id]
        
        response_data = {
            "job_id": job_id,
            "status": job["status"],
            "template": job["template"],
            "tier": job["tier"],
            "is_sre_sla": job["is_sre_sla"],
            "created_at": job["created_at"]
        }
        
        if job["status"] == "completed":
            response_data["result"] = job["result"]
            response_data["end_time"] = job.get("end_time")
        elif job["status"] == "failed":
            response_data["error"] = job["error"]
            response_data["end_time"] = job.get("end_time")
        elif job["status"] == "in-progress":
            response_data["start_time"] = job.get("start_time")
            
        return Response({"ok": True, "body": response_data}, status=200)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET']) 
def list_wcnp_alerts_jobs(request):
    """
    List all recent WCNP alerts generation jobs.
    """
    try:
        # Filter just the basic info to avoid large responses
        jobs_list = []
        for job_id, job in async_jobs.items():
            jobs_list.append({
                "job_id": job_id,
                "status": job["status"],
                "template": job["template"],
                "tier": job["tier"],
                "is_sre_sla": job["is_sre_sla"],
                "created_at": job["created_at"],
                "end_time": job.get("end_time")
            })
            
        # Sort by creation time descending (newest first)
        jobs_list.sort(key=lambda x: x["created_at"], reverse=True)
        
        return Response({"ok": True, "body": {"jobs": jobs_list}}, status=200)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def send_slack_message(request):
    try:
        post_data = request.data
        channel = post_data.get("channel")
        text = post_data.get("text")
        # user = post_data.get("user")
        thread_ts = post_data.get("thread_ts")
        blocks = post_data.get("blocks")
        user = post_data.get("user")
        res = slack.send_message(channel=channel, text=text, blocks=blocks, user=user, thread_ts=thread_ts)
        if res.status_code in (200, 201, 201):
            return Response({"ok": True, "body": {"message": res.data.get("ts")}}, status=200)
        else:
            return Response({"ok": False, "body": {"message": "Error occurred"}}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def upload_attachment_to_slack(request):
    try:
        from libs.file_handler import download_alert_content
        # file_path = '/Users/<USER>/git/juno/output/1681331099029755000/cart-page-ca-cart-page.yaml'
        # FilePointer = open(file_path, "r")
        # response = HttpResponse(FilePointer, content_type='application/msword')
        # response['Content-Disposition'] = 'attachment; filename=NameOfFile'

        return download_alert_content({"name": "hello"})
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def enable_alert(request, alert_id):
    try:
        ds = AlertModifier()
        slack.send_message("juno_logs", text=f"User called , {request.get_full_path()} for {alert_id}")
        ret2 = ds.enable_alert(alert_id.strip().rstrip())
        return Response({"ok": True, "body": ret2}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# fixed

@api_view(['GET'])
def disable_alert(request, alert_id):
    try:
        ds = AlertModifier()
        slack.send_message("juno_logs", text=f"User called , {request.get_full_path()} for {alert_id}")
        ret1 = ds.disable_alert(alert_id.strip().rstrip())
        return Response({"ok": True, "body": ret1}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def disable_alerts_by_ids(request):
    try:
        ds = AlertModifier()
        payload = request.data
        slack.send_message("juno_logs", text=f"User called , {request.get_full_path()} for disable alerts")
        alerts = payload.get("alerts")
        comm_channels = payload.get("comm_channels", list())
        ret1 = ds.disable_or_enable_alerts(alerts, is_disable=True, comm_channel_list=comm_channels)
        return Response({"ok": True, "body": ret1}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def enable_alerts_by_ids(request):
    try:
        ds = AlertModifier()
        payload = request.data
        slack.send_message("juno_logs", text=f"User called , {request.get_full_path()} for disable alerts")
        alerts = payload.get("alerts")
        ret1 = ds.disable_or_enable_alerts(alerts, is_disable=False)
        return Response({"ok": True, "body": ret1}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def disable_alerts_by_criteria(request):
    try:
        slack.send_message("juno_logs", text=f"User called , {request.get_full_path()} for disable alerts")
        filters = request.data.get("filters", list())
        # only filter rules not dashboards .
        if len(filters) == 0:
            return Response(
                {"ok": False, "body": {"message": "Alert filter criteria is null, should not be empty"}}, status=400)

        add_filter_to_exclude_dashboards(filters)
        comm_channels = request.data.get("comm_channels", list())
        results = process_filters_and_disable_and_enable_alerts(filters, comm_channel_list=comm_channels)
        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def enable_alerts_by_criteria(request):
    try:
        slack.send_message("juno_logs", text=f"User called , {request.get_full_path()} for disable alerts")
        filters = request.data.get("filters", list())
        # only filter rules not dashboards .
        if len(filters) == 0:
            return Response(
                {"ok": False, "body": {"message": "Alert filter criteria is null, should not be empty"}}, status=400)
        add_filter_to_exclude_dashboards(filters)
        results = process_filters_and_disable_and_enable_alerts(filters, disable_alerts=False)
        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def disable_alerts_by_criteria_spike_in_traffic(request):
    try:
        slack.send_message("juno_logs", text=f"User called , {request.get_full_path()} for disable alerts")
        # filters = request.data.get("filters",dict())
        filters = list()
        filters.append({"alert_sla_name": "traffic_spike_comparing_to_one_week_ago_threshold_pct"})
        # only filter rules not dashboards .
        add_filter_to_exclude_dashboards(filters)
        comm_channels = request.data.get("comm_channels", list())
        results = process_filters_and_disable_and_enable_alerts(filters, comm_channel_list=comm_channels)
        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def enable_alerts_by_criteria_spike_in_traffic(request):
    try:
        slack.send_message("juno_logs", text=f"User called , {request.get_full_path()} for disable alerts")
        filters = list()
        filters.append({"alert_sla_name": "traffic_spike_comparing_to_one_week_ago_threshold_pct"})
        # only filter rules not dashboards .
        add_filter_to_exclude_dashboards(filters)
        results = process_filters_and_disable_and_enable_alerts(filters, disable_alerts=False)
        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def disable_alerts_by_criteria_sre(request):
    try:
        slack.send_message("juno_logs", text=f"User called , {request.get_full_path()} for disable alerts")
        filters = request.data.get("filters", list())
        # only filter rules not dashboards .
        if len(filters) == 0:
            return Response(
                {"ok": False, "body": {"message": "Alert filter criteria is null, should not be empty"}}, status=400)
        add_filter_to_exclude_dashboards(filters)
        add_sre_alerts_filter(filters)
        comm_channels = request.data.get("comm_channels", list())
        results = process_filters_and_disable_and_enable_alerts(filters, comm_channel_list=comm_channels)
        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def enable_alerts_by_criteria_sre(request):
    try:
        slack.send_message("juno_logs", text=f"User called , {request.get_full_path()} for disable alerts")
        filters = request.data.get("filters", list())
        # only filter rules not dashboards .
        if len(filters) == 0:
            return Response(
                {"ok": False, "body": {"message": "Alert filter criteria is null, should not be empty"}}, status=400)
        add_filter_to_exclude_dashboards(filters)
        add_sre_alerts_filter(filters)
        results = process_filters_and_disable_and_enable_alerts(filters, disable_alerts=False)
        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def disable_alerts_by_criteria_app(request):
    try:
        slack.send_message("juno_logs", text=f"User called , {request.get_full_path()} for disable alerts")
        filters = request.data.get("filters", list())
        # only filter rules not dashboards .
        if len(filters) == 0:
            return Response(
                {"ok": False, "body": {"message": "Alert filter criteria is null, should not be empty"}}, status=400)
        add_filter_to_exclude_dashboards(filters)
        add_app_alerts_filter(filters)
        comm_channels = request.data.get("comm_channels", list())
        results = process_filters_and_disable_and_enable_alerts(filters, comm_channel_list=comm_channels)
        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def enable_alerts_by_criteria_app(request):
    try:
        slack.send_message("juno_logs", text=f"User called , {request.get_full_path()} for disable alerts")
        filters = request.data.get("filters", list())
        # only filter rules not dashboards .
        if len(filters) == 0:
            return Response(
                {"ok": False, "body": {"message": "Alert filter criteria is null, should not be empty"}}, status=400)
        add_filter_to_exclude_dashboards(filters)
        add_app_alerts_filter(filters)
        results = process_filters_and_disable_and_enable_alerts(filters, disable_alerts=False)
        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def get_disable_alerts(request):
    try:
        slack.send_message("juno_logs", text=f"User called , {request.get_full_path()} for disable alerts")
        ds = DisabledAlertPickup()
        ret = ds.search_disabled_alert()
        return Response({"ok": True, "body": ret}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def update_alerts_threshold_by_criteria(request):
    try:
        slack.send_message("juno_logs", text=f"User called , {request.get_full_path()} for update threshold")
        filters = request.data.get("filters", list())
        # only filter rules not dashboards .
        if len(filters) == 0:
            return Response(
                {"ok": False, "body": {"message": "Alert filter criteria is null, should not be empty"}}, status=400)
        results = process_filters_and_update_threshold(filters)
        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def get_enable_alerts(request):
    try:
        slack.send_message("juno_logs", text=f"User called , {request.get_full_path()} for listing "
                                             f"all enable alerts")
        ds = ListAlertInfo()
        ret = ds.list_alert()
        return Response({"ok": True, "body": ret}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


def write_data_to_csv_file(data: list, file_name_with_path: str):
    """
     data: is list of list data, Ex: [["SN", "Movie", "Protagonist"],[1, "Lord of the Rings", "Frodo Baggins"]]
    """

    with open(file_name_with_path, 'w', newline='') as file:
        writer = csv.writer(file)
        writer.writerows(data)
        # for list_data in data:
        #     writer.writerow(list_data)


def build_bulk_alerts_post_body(apps):
    user_provided_data = dict()
    user_provided_data["apps_meta_data"] = apps
    user_provided_data["create_pull_request"] = True
    user_provided_data["check_sla"] = False
    return user_provided_data


def override_teap_xmatters_details_with_global_xmatters(app_created_data, global_data_vars):
    for entry in app_created_data:
        if "xmatters_group" in global_data_vars:
            entry["xmatters_group"] = global_data_vars.get("xmatters_group")
        if "team_email" in global_data_vars:
            entry["team_email"] = global_data_vars.get("team_email")
        if "alert_team_name" in global_data_vars:
            entry["alert_team_name"] = global_data_vars.get("alert_team_name")
        if "slack_channel" in global_data_vars:
            entry["slack_channel"] = global_data_vars.get("slack_channel")


@api_view(['POST'])
def create_user_cosmos_alerts(request, resource_group):
    try:
        pass
        # app_created_data = [build_cosmos_metadata(resource_group)]
        # post_data = request.data
        # data = build_bulk_alerts_post_body(app_created_data, post_data)
        # return create_alerts(template="cosmos_alerts.yaml", data=data)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


@api_view(['POST'])
def create_cosmos_alerts_user_tier_0(request):
    template = "cosmos_alerts.yaml"
    status, data = create_alert_data(request, template, "zero", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def process_wishwall_application(request):
    template = "wishwall_application.yaml"
    custom_inventory_file = "mx_wishwall_application.yaml"
    post_data = request.data
    base_repo_branch = post_data.get("base_repo_branch", "dev")
    force_pull = bool_convertor(request.query_params.get("force_pull", False))
    common_data = handle_wishwall_inventory_files(template, custom_inventory_file, base_repo_branch, force_pull)
    return generate_template_compiled_files(template=template, data=common_data)


@api_view(['POST'])
def process_wishwall_system(request):
    template = "wishwall_system.yaml"
    custom_inventory_file = "mx_wishwall_system.yaml"
    post_data = request.data
    base_repo_branch = post_data.get("base_repo_branch", "dev")
    force_pull = bool_convertor(request.query_params.get("force_pull", False))
    common_data = handle_wishwall_inventory_files(template, custom_inventory_file, base_repo_branch, force_pull)
    return generate_template_compiled_files(template=template, data=common_data)


def handle_wishwall_inventory_files(template, custom_inventory_file, base_repo_branch, force_pull=False,
                                    **global_params):
    get_latest_templates(force_pull_required=force_pull)
    global_data_vars = {
        "base_repo_branch": base_repo_branch,
    }
    common_data = {
        "create_pull_request": True,
        "apps_meta_data": []
    }
    if global_params:
        global_data_vars = {**global_params, **global_data_vars}
    apps_meta_data = []
    data_store = Data(template, custom_inventory_file=custom_inventory_file)
    for k, v in data_store.user_inventory_data.items():
        for service in v.get("services"):
            if "namespace" in service.keys():
                service.update({"infra_platform": "wcnp"})
        apps_meta_data.append(v)

    common_data.update({"apps_meta_data": apps_meta_data})
    common_data.update({"global_data_vars": global_data_vars})
    return common_data


@api_view(['POST'])
def process_canada_wishwall_system(request):
    template = "wishwall_system.yaml"
    custom_inventory_file = "ca_wishwall_system.yaml"
    post_data = request.data
    base_repo_branch = post_data.get("base_repo_branch", "dev")
    force_pull = bool_convertor(request.query_params.get("force_pull", False))
    global_params = {"base_repo": "***************************:Omnitech-SRE/wishwall-manifest-canada.git",
                     "fork_repo": "***************************:intl-ecomm-svcs/wishwall-manifest-canada.git"}
    common_data = handle_wishwall_inventory_files(template, custom_inventory_file, base_repo_branch,
                                                  force_pull, **global_params)
    return generate_template_compiled_files(template=template, data=common_data)


@api_view(['POST'])
def process_canada_wishwall_application(request):
    template = "wishwall_application.yaml"
    custom_inventory_file = "ca_wishwall_application.yaml"
    post_data = request.data
    base_repo_branch = post_data.get("base_repo_branch", "dev")
    force_pull = bool_convertor(request.query_params.get("force_pull", False))
    global_params = {"base_repo": "***************************:Omnitech-SRE/wishwall-manifest-canada.git",
                     "fork_repo": "***************************:intl-ecomm-svcs/wishwall-manifest-canada.git"}
    common_data = handle_wishwall_inventory_files(template, custom_inventory_file, base_repo_branch,
                                                  force_pull, **global_params)
    return generate_template_compiled_files(template=template, data=common_data)


@api_view(['POST'])
def create_cosmos_alerts_user_tier_1(request):
    template = "cosmos_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_cosmos_alerts_user_all(request):
    template = "cosmos_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_cosmos_alerts_sre_tier_0(request):
    template = "cosmos_alerts.yaml"
    status, data = create_alert_data(request, template, "zero", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_cosmos_alerts_sre_tier_1(request):
    template = "cosmos_alerts.yaml"
    status, data = create_alert_data(request, template, "one", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_cosmos_alerts_sre_tier_all(request):
    template = "cosmos_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_cosmos_alerts_sre_tier_any(request, tier):
    template = "cosmos_alerts.yaml"
    status, data = create_alert_data(request, template, tier, is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_cosmos_alerts_user_tier_any(request, tier):
    template = "cosmos_alerts.yaml"
    status, data = create_alert_data(request, template, tier, is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_wcnp_alerts_sre_tier_0(request):
    template = "wcnp_alerts.yaml"
    status, data = create_alert_data(request, template, "zero", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_wcnp_alerts_sre_tier_1(request):
    template = "wcnp_alerts.yaml"
    status, data = create_alert_data(request, template, "one", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_wcnp_alerts_sre_tier_2(request):
    template = "wcnp_alerts.yaml"
    status, data = create_alert_data(request, template, "two", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_wcnp_alerts_sre_tier_all(request):
    template = "wcnp_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_wcnp_alerts_sre_tier_any(request, tier):
    template = "wcnp_alerts.yaml"
    status, data = create_alert_data(request, template, tier, is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_wcnp_alerts_user_tier_0(request):
    template = "wcnp_alerts.yaml"
    status, data = create_alert_data(request, template, "zero", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_wcnp_alerts_user_tier_1(request):
    template = "wcnp_alerts.yaml"
    status, data = create_alert_data(request, template, "one", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_wcnp_alerts_user_tier_2(request):
    template = "wcnp_alerts.yaml"
    status, data = create_alert_data(request, template, "two", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_wcnp_alerts_user_tier_all(request):
    template = "wcnp_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_wcnp_alerts_user_tier_any(request, tier):
    template = "wcnp_alerts.yaml"
    status, data = create_alert_data(request, template, tier, is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_custom_inventory_file_any(request, template, tier, custom_inventory_file):
    status, data = create_alert_data(request, template, tier, is_sre_sla=False,
                                     custom_inventory_file=custom_inventory_file)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_custom_inventory_file_without_tier(request, template, custom_inventory_file):
    status, data = create_alert_data(request, template, None, is_sre_sla=False,
                                     custom_inventory_file=custom_inventory_file)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['GET'])
def analyze_wcnp_namespaces_standards(request, tier):
    pass


def normalize_alert_data_by_tier():
    pass


def create_alert_data(request, template, tier, is_sre_sla, custom_inventory_file=None):
    password = request.data.get("password", False)
    if not password or password != "intl-sre-automation":
        return False, Response({"ok": True, "body": "Please provide valid password"}, status=200)

    force_pull = bool_convertor(request.query_params.get("force_pull", False))
    sre_override_xmatters_and_slack = bool_convertor(request.query_params.get("override_xmatters_and_slack", True))
    get_latest_templates(force_pull_required=force_pull)
    persist_data = False
    slack.send_message("juno_logs", text=f"User called , {request.get_full_path()} for template {template}")
    alert_data = AlertsPostDataBuilder(template, is_sre_sla=is_sre_sla, tier=tier,
                                       custom_inventory_file=custom_inventory_file)
    return True, alert_data.build_post_body(is_sre_sla=is_sre_sla, persist_data=persist_data,
                                            sre_override_xmatters_and_slack=sre_override_xmatters_and_slack)


def bool_convertor(val):
    if type(val) != bool:
        val = val.lower()
        if val in ('y', 'yes', 't', 'true', 'on', '1'):
            return True
        elif val in ('n', 'no', 'f', 'false', 'off', '0'):
            return False
        return False
    else:
        return val


@api_view(['POST'])
def create_oneops_alerts_user_tier_any(request, tier):
    template = "oneops_alerts.yaml"
    status, data = create_alert_data(request, template, tier, is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_oneops_alerts_sre_tier_any(request, tier):
    template = "oneops_alerts.yaml"
    status, data = create_alert_data(request, template, tier, is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_oneops_alerts_sre_tier_zero(request):
    template = "oneops_alerts.yaml"
    status, data = create_alert_data(request, template, "zero", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_oneops_alerts_sre_tier_one(request):
    template = "oneops_alerts.yaml"
    status, data = create_alert_data(request, template, "one", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_oneops_alerts_sre_tier_two(request):
    template = "oneops_alerts.yaml"
    status, data = create_alert_data(request, template, "two", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_oneops_alerts_sre_tier_all(request):
    template = "oneops_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_oneops_alerts_user_tier_zero(request):
    template = "oneops_alerts.yaml"
    status, data = create_alert_data(request, template, "zero", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_oneops_alerts_user_tier_one(request):
    template = "oneops_alerts.yaml"
    status, data = create_alert_data(request, template, "one", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_oneops_alerts_user_tier_two(request):
    template = "oneops_alerts.yaml"
    status, data = create_alert_data(request, template, "two", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_oneops_alerts_user_tier_all(request):
    template = "oneops_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


# Megha cache alerts


@api_view(['POST'])
def create_megha_cache_alerts_user_tier_any(request, tier):
    template = "meghacache_alerts.yaml"
    status, data = create_alert_data(request, template, tier, is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_megha_cache_alerts_sre_tier_any(request, tier):
    template = "meghacache_alerts.yaml"
    status, data = create_alert_data(request, template, tier, is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_megha_cache_alerts_sre_tier_zero(request):
    template = "meghacache_alerts.yaml"
    status, data = create_alert_data(request, template, "zero", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_megha_cache_alerts_sre_tier_one(request):
    template = "meghacache_alerts.yaml"
    status, data = create_alert_data(request, template, "one", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_megha_cache_alerts_sre_tier_two(request):
    template = "meghacache_alerts.yaml"
    status, data = create_alert_data(request, template, "two", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_megha_cache_alerts_sre_tier_all(request):
    template = "meghacache_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_megha_cache_alerts_user_tier_zero(request):
    template = "meghacache_alerts.yaml"
    status, data = create_alert_data(request, template, "zero", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_megha_cache_alerts_user_tier_one(request):
    template = "meghacache_alerts.yaml"
    status, data = create_alert_data(request, template, "one", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_megha_cache_alerts_user_tier_two(request):
    template = "meghacache_alerts.yaml"
    status, data = create_alert_data(request, template, "two", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_megha_cache_alerts_user_tier_all(request):
    template = "meghacache_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


# Cassandra alerts

@api_view(['POST'])
def create_cassandra_alerts_user_tier_any(request, tier):
    template = "cassandra_alerts.yaml"
    status, data = create_alert_data(request, template, tier, is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_cassandra_alerts_sre_tier_any(request, tier):
    template = "cassandra_alerts.yaml"
    status, data = create_alert_data(request, template, tier, is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_cassandra_alerts_sre_tier_zero(request):
    template = "cassandra_alerts.yaml"
    status, data = create_alert_data(request, template, "zero", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_cassandra_alerts_sre_tier_one(request):
    template = "cassandra_alerts.yaml"
    status, data = create_alert_data(request, template, "one", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_cassandra_alerts_sre_tier_two(request):
    template = "cassandra_alerts.yaml"
    status, data = create_alert_data(request, template, "two", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_cassandra_alerts_sre_tier_all(request):
    template = "cassandra_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_cassandra_alerts_user_tier_zero(request):
    template = "cassandra_alerts.yaml"
    status, data = create_alert_data(request, template, "zero", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_cassandra_alerts_user_tier_one(request):
    template = "cassandra_alerts.yaml"
    status, data = create_alert_data(request, template, "one", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_cassandra_alerts_user_tier_two(request):
    template = "cassandra_alerts.yaml"
    status, data = create_alert_data(request, template, "two", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_cassandra_alerts_user_tier_all(request):
    template = "cassandra_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


# SQL Server

@api_view(['POST'])
def create_sql_server_alerts_user_tier_any(request, tier):
    template = "sql_alerts.yaml"
    status, data = create_alert_data(request, template, tier, is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_sql_server_alerts_sre_tier_any(request, tier):
    template = "sql_alerts.yaml"
    status, data = create_alert_data(request, template, tier, is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_sql_server_alerts_sre_tier_zero(request):
    template = "sql_alerts.yaml"
    status, data = create_alert_data(request, template, "zero", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_sql_server_alerts_sre_tier_one(request):
    template = "sql_alerts.yaml"
    status, data = create_alert_data(request, template, "one", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_sql_server_alerts_sre_tier_two(request):
    template = "sql_alerts.yaml"
    status, data = create_alert_data(request, template, "two", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_sql_server_alerts_sre_tier_all(request):
    template = "sql_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_sql_server_alerts_user_tier_zero(request):
    template = "sql_alerts.yaml"
    status, data = create_alert_data(request, template, "zero", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_sql_server_alerts_user_tier_one(request):
    template = "sql_alerts.yaml"
    status, data = create_alert_data(request, template, "one", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_sql_server_alerts_user_tier_two(request):
    template = "sql_alerts.yaml"
    status, data = create_alert_data(request, template, "two", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_sql_server_alerts_user_tier_all(request):
    template = "sql_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


# Oracle Server

@api_view(['POST'])
def create_oracle_server_alerts_user_tier_any(request, tier):
    template = "oracle_alerts.yaml"
    status, data = create_alert_data(request, template, tier, is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_oracle_server_alerts_sre_tier_any(request, tier):
    template = "oracle_alerts.yaml"
    status, data = create_alert_data(request, template, tier, is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_oracle_server_alerts_sre_tier_zero(request):
    template = "oracle_alerts.yaml"
    status, data = create_alert_data(request, template, "zero", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_oracle_server_alerts_sre_tier_one(request):
    template = "oracle_alerts.yaml"
    status, data = create_alert_data(request, template, "one", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_oracle_server_alerts_sre_tier_two(request):
    template = "oracle_alerts.yaml"
    status, data = create_alert_data(request, template, "two", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_oracle_server_alerts_sre_tier_all(request):
    template = "oracle_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_oracle_server_alerts_user_tier_zero(request):
    template = "oracle_alerts.yaml"
    status, data = create_alert_data(request, template, "zero", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_oracle_server_alerts_user_tier_one(request):
    template = "oracle_alerts.yaml"
    status, data = create_alert_data(request, template, "one", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_oracle_server_alerts_user_tier_two(request):
    template = "oracle_alerts.yaml"
    status, data = create_alert_data(request, template, "two", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_oracle_server_alerts_user_tier_all(request):
    template = "oracle_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


# Solr Server

@api_view(['POST'])
def create_solr_server_alerts_user_tier_any(request, tier):
    template = "solr_alerts.yaml"
    status, data = create_alert_data(request, template, tier, is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_solr_server_alerts_sre_tier_any(request, tier):
    template = "solr_alerts.yaml"
    status, data = create_alert_data(request, template, tier, is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_solr_server_alerts_sre_tier_zero(request):
    template = "solr_alerts.yaml"
    status, data = create_alert_data(request, template, "zero", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_solr_server_alerts_sre_tier_one(request):
    template = "solr_alerts.yaml"
    status, data = create_alert_data(request, template, "one", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_solr_server_alerts_sre_tier_two(request):
    template = "solr_alerts.yaml"
    status, data = create_alert_data(request, template, "two", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_solr_server_alerts_sre_tier_all(request):
    template = "solr_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_solr_server_alerts_user_tier_zero(request):
    template = "solr_alerts.yaml"
    status, data = create_alert_data(request, template, "zero", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_solr_server_alerts_user_tier_one(request):
    template = "solr_alerts.yaml"
    status, data = create_alert_data(request, template, "one", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_solr_server_alerts_user_tier_two(request):
    template = "solr_alerts.yaml"
    status, data = create_alert_data(request, template, "two", is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_solr_server_alerts_user_tier_all(request):
    template = "solr_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=False)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def invoke_xmatters_call(request):
    try:
        subject = request.data.get("subject")
        message = request.data.get("message")

        results = invoke_xmatters(subject, message)
        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


@api_view(['POST'])
def create_kafka_alerts_sre_tier_zero(request):
    template = "kafka_alerts.yaml"
    status, data = create_alert_data(request, template, "zero", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_kafka_alerts_sre_tier_one(request):
    template = "kafka_alerts.yaml"
    status, data = create_alert_data(request, template, "one", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_kafka_alerts_sre_tier_two(request):
    template = "kafka_alerts.yaml"
    status, data = create_alert_data(request, template, "two", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_kafka_alerts_sre_tier_all(request):
    template = "kafka_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_kafka_alerts_app_tier_zero(request):
    template = "kafka_alerts.yaml"
    status, data = create_alert_data(request, template, "zero", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_kafka_alerts_app_tier_one(request):
    template = "kafka_alerts.yaml"
    status, data = create_alert_data(request, template, "one", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_kafka_alerts_app_tier_two(request):
    template = "kafka_alerts.yaml"
    status, data = create_alert_data(request, template, "two", is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def create_kafka_alerts_app_tier_all(request):
    template = "kafka_alerts.yaml"
    status, data = create_alert_data(request, template, None, is_sre_sla=True)
    if not status:
        return data
    return generate_template_compiled_files(template=template, data=data)


@api_view(['POST'])
def update_threshold_by_reading_alerts(request):
    update = UpdateThreshold()
    return update.update()


@api_view(['POST'])
def generate_slack_report(request):
    try:
        slack.send_message("juno_logs", text=f"User called , {request.get_full_path()} for event report")
        post_data = request.data
        alert_list = xmatters_generate(post_data.get('days', 7), post_data.get('group', 'intl-sre-oncall'),
                                       post_data.get('num', 7))
        channel = post_data.get('channel', 'juno_xmatters_test')
        slack.send_message(post_data.get('channel', channel),
                           text=f"Weekly report of top {post_data.get('num', 7)} Alerts \n {alert_list} ")
        return Response({"ok": True, "body": alert_list}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def generate_email_report(request):
    try:
        slack.send_message("juno_logs", text=f"User called , {request.get_full_path()} for event report")

        post_data = request.data

        email_report(post_data.get('recipients'), post_data.get('days', 7), post_data.get('group', 'intl-sre-oncall'),
                     post_data.get('num', 10))

        return Response({"ok": True, "body": "Email sent"}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def get_xmatters_events(request):
    try:
        start_epoch = request.query_params.get('start_epoch', None)
        end_epoch = request.query_params.get('end_epoch', None)
        group = request.query_params.get('group', None)
        n_hours = request.query_params.get('n_hours', None)
        events = get_xmatters_events(start_epoch=start_epoch, end_epoch=end_epoch, group=group, n_hours=n_hours)
        return Response({"ok": True, "body": events}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def trigger_anomaly_detection(request):
    try:
        metrics = WcnpMetrics()
        data = request.data
        namespace = data.get("namespace")
        app_id = data.get("app")
        utc_time_string = data.get("utc_time_string", None)
        n_minutes = data.get("n_minutes", 5)
        duration = data.get("duration", 10)  # Default to 10 if not provided
        threshold = data.get("threshold", 3)  # Default threshold
        metric = data.get("metric")

        results = []

        if metric == "latency":
            result = metrics.anamoly_latency(namespace, app_id, utc_time_string, threshold=threshold,
                                             n_minutes=n_minutes, duration=duration)
        elif "spike_in_traffic" in data:
            result = metrics.anamoly_traffic(namespace, app_id, utc_time_string, threshold=threshold,
                                             n_minutes=n_minutes, duration=duration)
        elif "spike_in_5xx" in data:
            # Assuming a method for 2XX exists
            result = metrics.anamoly_5xx(namespace, app_id, utc_time_string, threshold=threshold, n_minutes=n_minutes,
                                         duration=duration)
        elif "spike_in_cpu" in data:
            result = metrics.anamoly_cpu(namespace, app_id, utc_time_string, threshold=threshold, n_minutes=n_minutes,
                                         duration=duration)
        elif "spike_in_memory" in data:
            result = metrics.anamoly_mem(namespace, app_id, utc_time_string, threshold=threshold, n_minutes=n_minutes,
                                         duration=duration)
        # elif "crashLoop" in data:
        #     # Assuming a method for crashLoop exists
        #     result = metrics.anamoly_crash_loop(namespace, app_id, utc_time_string, threshold=threshold, n_minutes=n_minutes,duration=duration)
        else:
            result = {"error": "Unknown metric type"}

        results.append(result)

        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)




@api_view(['POST'])
def trigger_anomaly_detection_all(request):
    try:
        data = request.data
        force_pull = data.get('force_pull', False)

        # # Update templates if required
        get_latest_templates(force_pull_required=force_pull)

        # Use the orchestrator to handle anomaly detection
        orchestrator = AnomalyDetectionOrchestrator(data)
        grouped_data = orchestrator.group_by_type(data.get('services'))

        # Process the grouped data
        results_json = orchestrator.process_grouped_data(grouped_data)

        if grouped_data.get('wcnp'):
            results_json['wcnp'] = orchestrator.parse_wcnp_results(results_json.get('wcnp'))

        if not grouped_data:
            return Response({"ok": False, "body": {"message": "No valid service data found for anomaly detection."}}, status=400)

       

        return Response(results_json, status=200)
    except ValueError as ve:
        return Response({"ok": False, "body": {"message": str(ve)}}, status=400)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)
    

@api_view(['POST'])
def trigger_grouped_anomaly_detection(request):
    try:
        data = request.data
        force_pull = data.get('force_pull', False)

        # Check for required parameters
        file_name = data.get('file_name','sre.yaml')
        group = data.get('group','t0')
        if not file_name or not group:
            raise ValueError("Both 'file_name' and 'group' parameters are required.")

        # Update templates if required
        # get_latest_templates(force_pull_required=force_pull)

        # Use the orchestrator to handle grouped anomaly detection
        orchestrator = AnomalyDetectionOrchestrator(data)
        
        # Load and group the data
        grouped_data = orchestrator.load_analysis_inv(file_name, group)

        # Process the grouped data
        results_json = orchestrator.process_grouped_data(grouped_data)

        return Response(results_json, status=200)
    except ValueError as ve:
        return Response({"ok": False, "body": {"message": str(ve)}}, status=400)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


# def sanitize_floats(data):
#     if isinstance(data, dict):
#         return {k: sanitize_floats(v) for k, v in data.items()}
#     elif isinstance(data, list):
#         return [sanitize_floats(item) for item in data]
#     elif isinstance(data, float):
#         if math.isnan(data) or math.isinf(data):
#             return None
#     return data

