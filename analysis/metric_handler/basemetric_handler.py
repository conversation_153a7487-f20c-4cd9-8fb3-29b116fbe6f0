import yaml
import os
from settings import TEMPLATE_BASE_DIR,ANALYSIS_BASE_REPO
TEMPLATE_REPO_NAME = "sre-alert-templates"

class BaseMetricHandler:
    def __init__(self, yaml_file_name):
        """
        Initializes the BaseMetricHandler class and loads queries from a specified YAML file.
        """
        file_path = os.path.join(TEMPLATE_BASE_DIR, TEMPLATE_REPO_NAME, ANALYSIS_BASE_REPO, 'config', yaml_file_name)
        self.queries = self._load_queries(file_path)

    def _load_queries(self, file_path):
        with open(file_path, 'r') as file:
            return yaml.safe_load(file)['queries']

    def _construct_query(self, metric_type, **kwargs):
        query_template = self.queries.get(metric_type)
        if not query_template:
            raise ValueError(f"Query for metric type '{metric_type}' not found.")

        return query_template.format(**kwargs)