import json
import math
import os
import yaml
import urllib.parse
from analysis.wcnp_metric_new import WcnpMetrics
from analysis.oneops_metric_handler import OneopsMetrics
from analysis.gql_metric_handler import GQLMetrics
from analysis.megacache_metric_handler import MegaCacheMetrics
from analysis.cosmos_metric_handler import CosmosMetrics
from analysis.torbit_metric_handler import TorbitMetrics
from template_engine.compile import get_latest_templates
from settings import TEMPLATE_BASE_DIR, ANALYSIS_BASE_REPO
TEMPLATE_REPO_NAME = "sre-alert-templates"

SERVICE_CONFIG = {
            "wcnp": {
                "class": WcnpMetrics,
                "params": ["namespace", "app"]
            },
            "oneops": {
                "class": OneopsMetrics,
                "params": ["tenant", "assembly", "env", "platform"]
            },
            "gql": {
                "class": GQLMetrics,
                "params": ["wm_app"]
            },
            "megacache": {
                "class": MegaCacheMetrics,
                "params": ["meghacache_assembly"]
            },
            "cosmos": {
                "class": CosmosMetrics,
                "params": ["subscription_name", "resource_group"]
            },
            "torbit": {
                "class": TorbitMetrics,
                "params": ["origreq_host"]
            }
        }
class AnomalyDetectionOrchestrator:
    def __init__(self, data):
        self.data = data
        self.time_dict ={
                "utc_time_string": self.data.get("utc_time_string", None),
                "n_minutes": self.data.get("n_minutes", 5),
                "duration": self.data.get("duration", 10)
            }
        self.thresholds = self.data.get("thresholds", None)

        self.debug = self.data.get("debug", False)

    def trigger_anomaly_detection_all(self):
        try:
           

            service_type = self.identify_service_type()

            if not service_type:
                return {"ok": False, "body": {"message": "Invalid or missing parameters for any known service type"}}, 400

            service_info = SERVICE_CONFIG[service_type]
            metrics_class = service_info["class"]
            required_params = service_info["params"]

            metrics = metrics_class()

            time_dict = {
                "utc_time_string": self.data.get("utc_time_string", None),
                "n_minutes": self.data.get("n_minutes", 5),
                "duration": self.data.get("duration", 10)
            }
            thresholds = self.data.get("thresholds", None)

            results = metrics.anomaly_all(
                *[self.data[param] for param in required_params],
                time_dict,
                thresholds=thresholds
            )

            sanitized_results = self.sanitize_floats(results)

            response_data = json.dumps({"ok": True, "body": sanitized_results})
            return json.loads(response_data), 200
        except Exception as e:
            return {"ok": False, "body": {"message": str(e)}}, 400

    def sanitize_floats(self, data):
        if isinstance(data, dict):
            return {k: self.sanitize_floats(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self.sanitize_floats(item) for item in data]
        elif isinstance(data, float):
            if math.isnan(data) or math.isinf(data):
                return None
        return data

    def load_analysis_inv(self, file_name, group):
        file_path = os.path.join(TEMPLATE_BASE_DIR, TEMPLATE_REPO_NAME, ANALYSIS_BASE_REPO, 'inventory', file_name)
        try:
            with open(file_path, 'r') as file:
                data = yaml.safe_load(file)
            grouped_data = self.group_by_type(data.get(group, []))
            return grouped_data
        except FileNotFoundError:
            print(f"File {file_name} not found in the specified directory.")
            return {}
        except yaml.YAMLError as e:
            print(f"Error parsing YAML file: {e}")
            return {}
    
    def group_by_type(self, data):
        grouped = {}
        for item in data:
            for service_type, config in SERVICE_CONFIG.items():
                if all(param in item for param in config["params"]):
                    if service_type not in grouped:
                        grouped[service_type] = []
                    grouped[service_type].append(item)
                    break
        return grouped
    
    def process_grouped_data(self, grouped_data):
        results_by_service_type = {}

        for service_type, items in grouped_data.items():
            metrics_class = SERVICE_CONFIG[service_type]["class"]
            metrics = metrics_class()

            if service_type == "wcnp":
                namespaces = [item["namespace"].strip() for item in items]
                apps = [item["app"].strip() for item in items]
                res = metrics.anomaly_by_identifier(namespaces, apps, self.time_dict,self.thresholds,self.debug)
                # print(res)
            else:
                res = []
                for item in items:
                    params = [item[param].strip() for param in SERVICE_CONFIG[service_type]["params"]]
                    
                    result = metrics.anomaly_all(*params, time_dict=self.time_dict, thresholds=self.thresholds)
                    #add params to result
                    final = {}
                    for param in SERVICE_CONFIG[service_type]["params"]:
                        final[param] = item[param]
                    
                    for key in result:
                        promql_query = metrics._construct_query(key, **{param: item[param] for param in SERVICE_CONFIG[service_type]["params"]}).replace('\n', '')
                        encoded_query = urllib.parse.quote(promql_query)    
                        result[key]['promql_query_link'] = f"https://prometheus.query.prod.mms.walmart.net/graph?g0.expr={encoded_query}&g0.tab=1&g0.stacked=0&g0.range_input=10m"
                    final["anomalies"] = result
                    res.append(final)

            # Sanitize the results for this service type
            sanitized_res = self.sanitize_floats(res)
            results_by_service_type[service_type] = sanitized_res

        # Convert the results to JSON
        return results_by_service_type

    def identify_service_type(self):
       
        for service_type, config in SERVICE_CONFIG.items():
            if all(param in self.data for param in config["params"]):
                return service_type
        return None

    def parse_wcnp_results(self, service_data):
        """
        Parses the grouped results for the WCNp service, grouping by namespace and app.

        Args:
            service_data (dict): The grouped data for WCNp.

        Returns:
            list: Parsed and grouped anomaly detection results.
        """
        grouped_results = {}
        wcnp_metrics = WcnpMetrics()
        for key, metrics in service_data.items():
            app_info = self.extract_app_info(key)
            namespace = app_info.get("namespace")
            app = app_info.get("app")

            if not namespace or not app:
                continue  # Skip if essential information is missing

            # Initialize the group if not already present
            group_key = (namespace, app)
            if group_key not in grouped_results:
                grouped_results[group_key] = {
                    "namespace": namespace,
                    "app": app,
                    "anomalies": []
                }

            anomaly_detail = {
                "cluster_id": app_info.get("cluster_id")
            }

            for metric_name, metric_data in metrics.items():
                promql_query = wcnp_metrics._construct_query(metric_name, [namespace], [app]).replace('\n', '')
                encoded_query = urllib.parse.quote(promql_query)
                anomaly_detail[metric_name] = {
                    "anomaly": metric_data.get("anomaly", []),
                    "trend_analysis": metric_data.get("trend_analysis", {}),
                    "promql_query_link": f"https://prometheus.query.prod.mms.walmart.net/graph?g0.expr={encoded_query}&g0.tab=1&g0.stacked=0&g0.range_input=10m"
                }

                if self.debug:
                    anomaly_detail["error_stats"] = metric_data.get("error_stats", None)

            grouped_results[group_key]["anomalies"].append(anomaly_detail)

        # Convert the grouped_results dictionary to a list
        parsed_results = list(grouped_results.values())
        return parsed_results

    def extract_app_info(self, key):
        """
        Extracts application information from the key string.

        Args:
            key (str): The key string containing app, cluster_id, and namespace.

        Returns:
            dict: Extracted application information.
        """
        info = {}
        parts = key.split(", ")
        for part in parts:
            if ": " in part:
                k, v = part.split(": ", 1)
                info[k.strip()] = v.strip()
        return info





def main():

    orchestrator = AnomalyDetectionOrchestrator(data={'n_minutes':10,})
    grouped_dat = orchestrator.load_analysis_inv(file_name="test.yaml",group="t0")
    print(grouped_dat)

    res = orchestrator.process_grouped_data(grouped_dat)
    print(res)


    


