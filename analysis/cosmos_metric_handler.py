
from analysis.Anomaly_detector import PrometheusAnomalyDetector
from libs.prometheus_client import Prometheus

class CosmosMetrics:

    def __init__(self):
        """
        Initializes the CosmosMetrics class with Prometheus handler.
        """
        self._prometheus_handler = None

    @property
    def prometheus_handler(self):
        """
        Lazy loads and returns the Prometheus client handler.
        """
        if self._prometheus_handler is None:
            self._prometheus_handler = Prometheus()
        return self._prometheus_handler

    def _construct_query(self, metric_type, subscription_name, resource_group):
        template = {
            'latency': ((
                'avg by(connectionmode,databasename,operationtype,publicapitype,resource_name,region,subscription_name,resource_group)'
                '(avg_over_time(serversidelatencydirect_milliseconds_percentile99th{{subscription_name="{subscription_name}",'
                'resource_group="{resource_group}"}}))').format_map({"subscription_name": subscription_name, "resource_group": resource_group}
            )),
            'availability': ((
                'max(serviceavailability_percent_maximum{{subscription_name="{subscription_name}", resource_group="{resource_group}"}})'
            ).format_map({"subscription_name": subscription_name, "resource_group": resource_group}
            )),
            'throttled_request_pct': ((
                'sum by (resource_name, region, databasename, collectionname, subscription_name, resource_group) '
                '(rate(totalrequests_count_count{{subscription_name="{subscription_name}", resource_group="{resource_group}", statuscode="429"}}[15m])) '
                '/ sum by (resource_name, region, databasename, collectionname, subscription_name, resource_group) '
                '(rate(totalrequests_count_count{{subscription_name="{subscription_name}", resource_group="{resource_group}", statuscode=~"2.*"}}[15m])) * 100'
            ).format_map({"subscription_name": subscription_name, "resource_group": resource_group}
            ))
        }
        return template[metric_type]

    def anomaly_latency(self, time_dict, subscription_name, resource_group, threshold=3):
        query = self._construct_query("latency", subscription_name, resource_group)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_availability(self, time_dict, subscription_name, resource_group, threshold=3):
        query = self._construct_query("availability", subscription_name, resource_group)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_throttled_request_pct(self, time_dict, subscription_name, resource_group, threshold=3):
        query = self._construct_query("throttled_request_pct", subscription_name, resource_group)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_all(self, subscription_name, resource_group, time_dict, thresholds=None):
        """
        Calls all anomaly detection methods and returns their results.
        
        :param time_dict: Dictionary containing time-related parameters.
        :param subscription_name: Subscription name.
        :param resource_group: Resource group name.
        :param thresholds: Optional dictionary to specify thresholds for each metric type.
        :return: Dictionary containing results of all anomaly detections.
        """
        if thresholds is None:
            thresholds = {
                "latency": 3,
                "availability": 3,
                "throttled_request_pct": 3
            }

        results = {}
        for metric in ["latency", "availability", "throttled_request_pct"]:
            anomaly_result = getattr(self, f"anomaly_{metric}")(time_dict, subscription_name, resource_group, thresholds.get(metric))
            results[metric] = {
                "anomaly": anomaly_result["anomaly"],
                "baseline_stats": anomaly_result["baseline_stats"],
                "error_stats": anomaly_result["error"]
            }

        return results

if __name__ == "__main__":
    metrics = CosmosMetrics()
    print(metrics.anomaly_all(time_dict={'n_minutes':10}, subscription_name='sp-pl-cosmos-prod-000', resource_group='custaccount-prod-azure-cosmosdb-rg'))