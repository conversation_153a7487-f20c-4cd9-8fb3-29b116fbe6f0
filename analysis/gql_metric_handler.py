from analysis.Anomaly_detector import PrometheusAnomalyDetector
from libs.prometheus_client import Prometheus

class GQLMetrics:

    def __init__(self):
        """
        Initializes the GQLMetrics class with Prometheus handler.
        """
        self._prometheus_handler = None

    @property
    def prometheus_handler(self):
        """
        Lazy loads and returns the Prometheus client handler.
        """
        if self._prometheus_handler is None:
            self._prometheus_handler = Prometheus()
        return self._prometheus_handler

    def _construct_query(self, metric_type, wm_app, deployment_environment, span_name):
        template = {
            '4xx': ((
                '(sum(avg_over_time(c4XX_total:rate2m{{wm_app="{wm_app}",deployment_environment="{deployment_environment}",span_name="{span_name}"}}[5m]))by (wm_app_version, cluster_id) '
                '/ sum(avg_over_time(processTime_seconds_count:rate2m{{wm_app="{wm_app}",deployment_environment="{deployment_environment}",span_name="{span_name}"}}[5m]))by (wm_app_version, cluster_id)) * 100'
            ).format_map({"wm_app": wm_app, "deployment_environment": deployment_environment, "span_name": span_name})),
            '5xx': ((
                '(sum(avg_over_time(c5XX_total:rate2m{{wm_app="{wm_app}",deployment_environment="{deployment_environment}",span_name="{span_name}"}}[5m]))by (wm_app_version, cluster_id) '
                '/ sum(avg_over_time(processTime_seconds_count:rate2m{{wm_app="{wm_app}",deployment_environment="{deployment_environment}",span_name="{span_name}"}}[5m]))by (wm_app_version, cluster_id)) * 100'
            ).format_map({"wm_app": wm_app, "deployment_environment": deployment_environment, "span_name": span_name}))
        }
        return template[metric_type]

    def anomaly_4xx(self, time_dict, wm_app, deployment_environment, span_name, threshold=3):
        query = self._construct_query("4xx", wm_app, deployment_environment, span_name)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }
        transformations = {
            "transform_type": ["log"]
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source, transformations)
        return result

    def anomaly_5xx(self, time_dict, wm_app, deployment_environment, span_name, threshold=3):
        query = self._construct_query("5xx", wm_app, deployment_environment, span_name)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }
        transformations = {
            "transform_type": ["log"]
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source,transformations)
        return result

    def anomaly_all(self, wm_app,time_dict, deployment_environment='prod', span_name='graphql-request', thresholds=None):
        """
        Calls all anomaly detection methods and returns their results.
        
        :param time_dict: Dictionary containing time-related parameters.
        :param wm_app: Application name.
        :param deployment_environment: Deployment environment.
        :param span_name: Span name.
        :param thresholds: Optional dictionary to specify thresholds for each metric type.
        :return: Dictionary containing results of all anomaly detections.
        """
        if thresholds is None:
            thresholds = {
                "4xx": 3,
                "5xx": 3
            }

        results = {}
        for metric in ["4xx", "5xx"]:
            anomaly_result = getattr(self, f"anomaly_{metric}")(time_dict, wm_app, deployment_environment, span_name, thresholds.get(metric))
            results[metric] = {
                "anomaly": anomaly_result["anomaly"],
                "baseline_stats": anomaly_result["baseline_stats"],
                "error_stats": anomaly_result["error"]
            }

        return results

if __name__ == "__main__":
    metrics = GQLMetrics()
    print(metrics.anomaly_all(wm_app='CE-AROUND-ME', deployment_environment='prod', span_name='graphql-request'
                              ,time_dict={'n_minutes':10}))