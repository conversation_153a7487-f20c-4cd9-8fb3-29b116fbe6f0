import pandas as pd
import numpy as np
from analysis.old_analysis.Analysis_handlers import Analyze
from libs.prometheus_client import Prometheus
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
class AnomalyDetector:
    def __init__(self, threshold):
        self.threshold = threshold

    def fetch_data(self, **keys):
        """
        Fetches the data using the provided fetch function.
        This method should be overridden by subclasses for different data sources.
        """
        raise NotImplementedError("The fetch_data method must be overridden by subclasses.")

    def parse_data(self, raw_data):
        """
        Parses the raw data into a standardized DataFrame.
        This method should be overridden by subclasses for different data sources.
        """
        raise NotImplementedError("The parse_data method must be overridden by subclasses.")
    
    @staticmethod
    def calculate_generic_baseline(historical_df, stat_column="value",agg_id = 'Metric'):
        
        
        if agg_id in historical_df.columns:
            # Group by 'Metric' and calculate stats
            baseline_stats = historical_df.groupby(agg_id)[stat_column].agg(['mean', 'std']).reset_index()
            baseline_stats = baseline_stats.round({'mean': 2, 'std': 2})
        else:
            raise ValueError(f"Column '{agg_id}' not found in DataFrame.")
        
        return baseline_stats
    
    @staticmethod
    def remove_outliers(df, column_name="value"):
        # Calculate Q1, Q3 and IQR
        Q1 = df[column_name].quantile(0.01)
        Q3 = df[column_name].quantile(0.99)
        IQR = Q3 - Q1
        # Calculate the outlier thresholds
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        # Filter out the outliers
        filtered_df = df[(df[column_name] >= lower_bound) & (df[column_name] <= upper_bound)]
        return filtered_df
    
    @staticmethod
    def log_transform(data):
        # Apply log transformation, adding a small constant to avoid log(0)
        return np.log(data + 1)
    
    @staticmethod
    def inverse_log_transform(data):
        # Apply inverse log transformation
        return np.exp(data) - 1
    
    def transform_data(self, df, column_name="value", transform_type="none"):
        """
        Transforms the data by removing outliers or applying a log transformation.
        
        Parameters:
        - df: DataFrame containing the data.
        - column_name: The name of the column to transform.
        - transform_type: The type of transformation to apply. Options are 'outliers', 'log', or 'none'.
        
        Returns:
        - Transformed DataFrame.
        """
        if "outliers" in transform_type:
            # Remove outliers
            df = self.remove_outliers(df, column_name)
        
        if "log" in transform_type:
            # Apply log transformation
            df.loc[:, column_name] = self.log_transform(df[column_name])
        
        return df

    def compute_z_scores(self, current_df, baseline_stats, agg_id="Metric"):
        """
        Computes z-scores of the 'value' column in the DataFrame, grouped by 'Metric'.
        """
        # Merge current data with baseline statistics
        if agg_id and agg_id in current_df.columns:
            merged_df = current_df.merge(baseline_stats, on=agg_id, how='left')
        else:
            # If agg_id not present, replicate baseline stats across all rows
            replicated_baseline = pd.concat([baseline_stats] * len(current_df), ignore_index=True)
            merged_df = pd.concat([current_df.reset_index(drop=True), replicated_baseline], axis=1)
        
        # Handle cases where 'std' is zero or NaN to avoid invalid z-scores
        merged_df['std'] = merged_df['std'].replace(0, np.nan)
        merged_df['z_score'] = (merged_df["value"] - merged_df['mean']) / merged_df['std']
        merged_df['z_score'].fillna(0, inplace=True)  # Assuming z-score of 0 for undefined cases
        
        return merged_df

    def detect_anomalies(self, merged_df, tail="right"):
        """
        Identifies anomalies based on the z-score and threshold.
        """
        if tail == 'right':
            anomalies = merged_df[merged_df['z_score'] > self.threshold]
        elif tail == 'left':
            anomalies = merged_df[merged_df['z_score'] < -self.threshold]
        else:  # 'both'
            anomalies = merged_df[merged_df['z_score'].abs() > self.threshold]
        
        # Create a copy to avoid SettingWithCopyWarning
        anomalies = anomalies.copy()
        anomalies['value'] = anomalies['value'].round(2)
        
        if "Metric" in merged_df.columns:
            return anomalies[['Timestamp', "value", "Metric"]]
        else:
            return anomalies[['Timestamp', "value"]]
        

    
    # def collect_historical_data(self, datasource, days, start_time=None, end_time=None):
    #     history = []
        
    #     if start_time is not None and end_time is not None:
    #         # Use the provided custom time range
    #         try:
    #             data = self.fetch_data(**datasource, start_time=start_time, end_time=end_time)
    #             parsed_data = self.parse_data(data)
    #             history.append(parsed_data)
    #         except Exception as e:
    #             # Log the exception or handle it as needed
    #             pass
    #     else:
    #         # Default to the current flow
    #         for i in range(1, days + 1):
    #             try:
    #                 data = self.fetch_data(**datasource, offset=i)
    #                 parsed_data = self.parse_data(data)
    #                 history.append(parsed_data)
    #             except Exception as e:
    #                 # Log the exception or handle it as needed
    #                 pass
        
    #     historical_df = pd.concat(history, ignore_index=True)
    #     return historical_df

    def collect_historical_data(self, datasource, days, start_time=None, end_time=None):
        history = []

        def fetch_and_parse(offset=None):
            try:

                if start_time is not None and end_time is not None:
                    data = self.fetch_data(**datasource, start_time=start_time, end_time=end_time)
                else:
                    data = self.fetch_data(**datasource, offset=offset)
                return self.parse_data(data)
            except Exception as e:
                # Log the exception or handle it as needed
                return None

        with ThreadPoolExecutor() as executor:
            futures = []
            if start_time is not None and end_time is not None:
                futures.append(executor.submit(fetch_and_parse))
            else:
                for i in range(1, days + 1):
                    futures.append(executor.submit(fetch_and_parse, offset=i))

            for future in as_completed(futures):
                result = future.result()
                if result is not None:
                    history.append(result)

        historical_df = pd.concat(history, ignore_index=True)
        return historical_df
            

    def process(self, data_source, transformations={"transform_type": []}):
        """
        Full processing pipeline: parse data, apply transformations, compute z-scores, and detect anomalies.
        Returns a dictionary with current data, baseline stats, anomalies, and any errors.
        """
        result = {"current_data": None, "baseline_stats": None, "anomaly": [], "error": None}
        
        try:
            # Extract time ranges from data_source
            current_start_time = data_source.get("current_start_time")
            current_end_time = data_source.get("current_end_time")
            historic_start_time = data_source.get("historic_start_time")
            historic_end_time = data_source.get("historic_end_time")
            
            # Fetch current data
            response = self.fetch_data(**data_source, start_time=current_start_time, end_time=current_end_time)
            # Parse the data
            df = self.parse_data(response)
            df = self.transform_data(df, column_name="value", **transformations)
            result["current_data"] = df.to_dict(orient='records')
            
            # Collect historical data and apply transformations
            historical_df = self.collect_historical_data(datasource=data_source, days=15, 
                                                        start_time=historic_start_time, end_time=historic_end_time)
            transformations["transform_type"].append("outliers")
            historical_df = self.transform_data(historical_df, column_name="value", **transformations)
            
            # Compute baseline statistics
            baseline_stats = self.calculate_generic_baseline(historical_df)
            result["baseline_stats"] = baseline_stats.to_dict(orient='records')
            
            # Compute z-scores
            df = self.compute_z_scores(df, baseline_stats)
            
            # Detect anomalies
            anomalies = self.detect_anomalies(df)
            result["anomaly"] = anomalies.to_dict(orient='records')
            
        except Exception as e:
            result["error"] = str(e)
        
        return result







class PrometheusAnomalyDetector(AnomalyDetector):

    def _fetch_metrics(self, metric_type, namespace, app_id, start_time=None, end_time=None, n_minutes=5,
                       steps=60, offset=0):

        if not start_time:
            end_time, start_time = self._offset_handler(n_minutes=n_minutes, offset=offset)


        query = self._construct_query(metric_type, namespace, app_id)

        return self.prometheus_handler.get_query_range_data(query=query, start_time=start_time, end_time=end_time,
                                                            steps=steps)
    
    @property
    def prometheus_handler(self):
        """
        Lazy loads and returns the Prometheus client handler.
        """
        
        self._prometheus_handler = Prometheus()
        return self._prometheus_handler
    
    def _offset_handler(self,  start_time=None, end_time=None, offset=0):

        
        offset_sec = offset * 24 * 60 * 60
        # print(start_time, end_time)
        # print(offset_sec)
        if offset:
            start_time -= offset_sec
            end_time -= offset_sec

        # print(start_time, end_time)

        return end_time, start_time

    def fetch_data(self, **keys):
        start_time = keys.get("start_time")
        end_time = keys.get("end_time")
        utc_time_string = keys.get("utc")
        duration = keys.get("duration", None)
        n_minutes = keys.get("n_minutes", 5)  # Default value if not provided
        query = keys.get("query", "")
        steps = keys.get("steps", 60)
        offset = keys.get("offset", 0)

        if start_time is None or end_time is None:
            # Calculate start and end time if not provided
            end, start = Analyze.utc_to_epoch(utc_time_string, duration=duration)
            if end is None:
                end, start = Prometheus.get_last_n_minutes_epoch_times(n_minutes)
        else:
            # Use provided start_time and end_time
            start, end = start_time, end_time

        end, start = self._offset_handler(start_time=start, end_time=end, offset=offset)

        for attempt in range(3):
            response = self.prometheus_handler.get_query_range_data(query=query, start_time=start, end_time=end, steps=steps)
            if response and response['data']['result']:
                return response
            else:
                if attempt < 2:  # Only wait if not the last attempt
                    time.sleep(2)

        raise ValueError("Empty response or Prometheus connection error after 3 attempts. Check namespace or app.")

        # response = self.prometheus_handler.get_query_range_data(query=query, start_time=start, end_time=end, steps=steps)

        # if not response or not response['data']['result']:
        #     raise ValueError("Empty response or Prometheus connection error. Check namespace or app.")
        
        # return response
    
    def parse_data(self, response):
        data = []

        # Iterate through the response data
        for result in response['data']['result']:
            metric_info = result['metric']
            # Create a user-friendly string from the metric dictionary
            metric_str = ', '.join(f"{key}: {value}" for key, value in metric_info.items())
            for timestamp, value in result['values']:
                # Append the data with the metric string
                data.append({
                    'Timestamp': int(float(timestamp)),
                    'Metric': metric_str if metric_str else 'No Metric',
                    'value': float(value)
                })

        # Create a DataFrame from the list of dictionaries
        df = pd.DataFrame(data)
        return df
