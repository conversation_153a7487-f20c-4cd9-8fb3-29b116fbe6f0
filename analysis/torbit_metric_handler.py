from libs.prometheus_client import Prometheus
from analysis.Anomaly_detector import PrometheusAnomalyDetector
from concurrent.futures import ThreadPoolExecutor
class TorbitMetrics:

    def __init__(self):
        """
        Initializes the TorbitMetrics class with Prometheus handler.
        """
        self._prometheus_handler = None

    @property
    def prometheus_handler(self):
        """
        Lazy loads and returns the Prometheus client handler.
        """
        if self._prometheus_handler is None:
            self._prometheus_handler = Prometheus()
        return self._prometheus_handler

    def _construct_query(self, metric_type, origreq_host):
        template = {
            'traffic': (
                'sum(backend:odnd_http_response_code_2xx:irate5m{{origreq_host="{origreq_host}",backend=~".*",'
                'dc=~"(cdc|cnecnn2|cyr1|da11|dc15|dc5|dfw|edc|eus2|geus4|gsus1|hil1|scus|se2|uks|ukw|wus|wus2)"}}) '
                'by (dc,backend)'
            ).format_map({"origreq_host": origreq_host}),

            '5xx': (
                'backend:odnd_http_response_code_5xx_per_2xx:irate5m{{origreq_host="{origreq_host}"}}'
            ).format_map({"origreq_host": origreq_host}),

            '4xx': (
                'backend:odnd_http_response_code_4xx_per_2xx:irate5m{{origreq_host="{origreq_host}"}}'
            ).format_map({"origreq_host": origreq_host}),

            'latency': (
                'max(backend:odnd_http_response_latency_seconds:99_rate5m{{origreq_host="{origreq_host}",backend=~".*",'
                'dc=~"(cdc|cne2|cnn2|cyr1|da11|dc15|dc5|dfw|edc|eus2|geus4|gsus1|hil1|scus|se2|uks|ukw|wus|wus2)"}}) '
                'by (backend,dc)*100'
            ).format_map({"origreq_host": origreq_host})
        }
        return template[metric_type]

    def anomaly_traffic(self, origreq_host, time_dict, threshold=3):
        query = self._construct_query("traffic", origreq_host)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_5xx(self, origreq_host, time_dict, threshold=2):
        query = self._construct_query("5xx", origreq_host)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_4xx(self, origreq_host, time_dict, threshold=2):
        query = self._construct_query("4xx", origreq_host)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_latency(self, origreq_host, time_dict, threshold=3):
        query = self._construct_query("latency", origreq_host)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_all(self, origreq_host, time_dict, thresholds=None):
        """
        Calls all anomaly detection methods and returns their results.

        :param origreq_host: Original request host.
        :param time_dict: Dictionary containing time-related parameters.
        :param thresholds: Optional dictionary to specify thresholds for each metric type.
        :return: Dictionary containing results of all anomaly detections.
        """
        if thresholds is None:
            thresholds = {
                "traffic": 3,
                "5xx": 2,
                "4xx": 2,
                "latency": 3
            }

        results = {}
        metrics = ["5xx", "4xx", "latency"]

        def detect_anomaly(metric):
            anomaly_result = getattr(self, f"anomaly_{metric}")(origreq_host, time_dict, thresholds.get(metric))
            return metric, {
                "anomaly": anomaly_result["anomaly"],
                "baseline_stats": anomaly_result["baseline_stats"]
            }

        with ThreadPoolExecutor() as executor:
            futures = {executor.submit(detect_anomaly, metric): metric for metric in metrics}
            for future in futures:
                metric, result = future.result()
                results[metric] = result

        return results

if __name__ == "__main__":
    metrics = TorbitMetrics()
    print(metrics.anomaly_all(origreq_host='super.walmart.com.mx' ,time_dict={'n_minutes':10}))