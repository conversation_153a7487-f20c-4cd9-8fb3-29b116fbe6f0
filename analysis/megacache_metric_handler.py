from analysis.Anomaly_detector import PrometheusAnomalyDetector
from libs.prometheus_client import Prometheus

class MegaCacheMetrics:

    def __init__(self):
        """
        Initializes the MegaCacheMetrics class with Prometheus handler.
        """
        self._prometheus_handler = None

    @property
    def prometheus_handler(self):
        """
        Lazy loads and returns the Prometheus client handler.
        """
        if self._prometheus_handler is None:
            self._prometheus_handler = Prometheus()
        return self._prometheus_handler

    def _construct_query(self, metric_type, meghacache_assembly):
        template = {
            'availability': ((
                'count by (dc) (n_cpus{{mms_source="medusa-ss",metricgroup="system",ooa=~"{meghacache_assembly}", ooe="prod", oop="meghacache", oot="ms-df-cache"}}) '
                '/ max by (dc) (n_vms{{mms_source="non-wcnp",name=~"{meghacache_assembly}", ooe="prod", oop="meghacache", oot="ms-df-cache"}}) * 100'
            ).format_map({"meghacache_assembly": meghacache_assembly})),
            'latency': ((
                'avg by (dc,level3,ooa,oop,ooe,oot,metricgroup) '
                '(avg_over_time(ping_ms{{mms_source="medusa-ss",metricgroup="meghacache_ping",ooa=~"{meghacache_assembly}",ooe="prod",oop="meghacache",oot="ms-df-cache"}}[20m]))'
            ).format_map({"meghacache_assembly": meghacache_assembly})),
            'cpu': ((
                '100 - min by (dc,ooa,oop,ooe,oot,metricgroup) '
                '(usage_idle{{mms_source="medusa-ss",metricgroup="cpu",ooa="{meghacache_assembly}",ooe="prod",oop="meghacache",oot="ms-df-cache"}})'
            ).format_map({"meghacache_assembly": meghacache_assembly}))
        }
        return template[metric_type]

    def anomaly_availability(self, time_dict, meghacache_assembly, threshold=3):
        query = self._construct_query("availability", meghacache_assembly)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_latency(self, time_dict, meghacache_assembly, threshold=3):
        query = self._construct_query("latency", meghacache_assembly)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_cpu(self, time_dict, meghacache_assembly, threshold=3):
        query = self._construct_query("cpu", meghacache_assembly)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_all(self, meghacache_assembly, time_dict, thresholds=None):
        """
        Calls all anomaly detection methods and returns their results.
        
        :param time_dict: Dictionary containing time-related parameters.
        :param meghacache_assembly: Meghacache assembly name.
        :param thresholds: Optional dictionary to specify thresholds for each metric type.
        :return: Dictionary containing results of all anomaly detections.
        """
        if thresholds is None:
            thresholds = {
                "availability": 3,
                "latency": 3,
                "cpu": 3
            }

        results = {}
        for metric in ["availability", "latency", "cpu"]:
            anomaly_result = getattr(self, f"anomaly_{metric}")(time_dict, meghacache_assembly, thresholds.get(metric))
            results[metric] = {
                "anomaly": anomaly_result["anomaly"],
                "baseline_stats": anomaly_result["baseline_stats"],
                "error_stats": anomaly_result["error"]
            }

        return results

if __name__ == "__main__":
    metrics = MegaCacheMetrics()
    # print(metrics.anomaly_all(meghacache_assembly='ca-gbas', time_dict={"current_start_time":1730138320,"current_end_time":1730138420,"historic_start_time":1730136420,"historic_end_time":1730137420}))
    print(metrics.anomaly_all(meghacache_assembly='mx-personalization-service-prod',time_dict={'n_minutes':10}))