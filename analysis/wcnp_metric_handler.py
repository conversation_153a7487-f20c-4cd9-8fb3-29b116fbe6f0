from libs.prometheus_client import Prometheus
import pandas as pd
from analysis.old_analysis.Analysis_handlers import Analyze
import numpy as np
from analysis.old_analysis.wcnp_parser import <PERSON><PERSON><PERSON><PERSON><PERSON> as parser
from analysis.Anomaly_detector import PrometheusAnomalyDetector
from concurrent.futures import Thread<PERSON>oolExecutor
from concurrent import futures
class WcnpMetrics:

    def __init__(self):
        """
        Initializes the wcnp_metrics class with Prometheus handler, time frame, and component.

        """
        self._prometheus_handler = None

    def log_transform(self,data):
        # Apply log transformation, adding a small constant to avoid log(0)
        return np.log(data + 1)

    @property
    def prometheus_handler(self):
        """
        Lazy loads and returns the Prometheus client handler.
        """
        if self._prometheus_handler is None:
            self._prometheus_handler = Prometheus()
        return self._prometheus_handler

    def _offset_handler(self, n_minutes, start_time=None, end_time=None, offset=0):

        if not start_time and not end_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(n_minutes)

        offset_sec = offset * 24 * 60 * 60
        # print(start_time, end_time)
        # print(offset_sec)
        if offset:
            start_time -= offset_sec
            end_time -= offset_sec

        # print(start_time, end_time)

        return end_time, start_time
    



    def _construct_query(self, metric_type, namespace, app_id):
        template = {

            'error_5xx': 'sum(envoy_cluster_upstream_rq:sum_rate2m{{cluster_name =~"outbound.*{app_name}.' \
                         '*{namespace}\\\\.svc\\\\.cluster\\\\.local", response_code_class = "5xx"}}) ' \
                         'by (response_code_class, cluster_id) * 60'.format_map({"app_name": app_id,
                                                                                 "namespace": namespace}),

            '5xx_pct': (
                'sum(avg_over_time(envoy_cluster_upstream_rq:sum_rate2m{{'
                'cluster_name=~"outbound.*{app_name}.*{namespace}.svc.cluster.local",'
                'cluster_name!~".*canry.*|.*cnry.*|.*canary.*",response_code_class="5xx"}}[5m])) '
                'by (cluster_id, app, namespace) / '
                'sum(avg_over_time(envoy_cluster_upstream_rq:sum_rate2m{{'
                'cluster_name=~"outbound.*{app_name}.*{namespace}.svc.cluster.local",'
                'cluster_name!~".*canry.*|.*cnry.*|.*canary.*"}}[5m])) '
                'by (cluster_id, app, namespace) * 100'
            ).format_map({"app_name": app_id, "namespace": namespace}),

            'non_2xx': 'sum(envoy_cluster_upstream_rq:sum_rate2m{{cluster_name =~"outbound.*{app_name}.' \
                       '*{namespace}\\\\.svc\\\\.cluster\\\\.local", response_code_class != "2xx"}}) ' \
                       'by (response_code_class, cluster_id) * 60'.format_map({"app_name": app_id,
                                                                               "namespace": namespace}),

            'traffic': 'sum(envoy_cluster_upstream_rq:sum_rate2m{{cluster_name=~"outbound.*{app_name}.*{namespace}' \
                       '\\\\.svc\\\\.cluster\\\\.local"}}) by (cluster_id) *60'.format_map({"app_name": app_id,
                                                                                            "namespace": namespace}),

            'latency': 'histogram_quantile(0.95, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{{cluster_name=~' \
                       '"outbound.*{app_name}.*{namespace}\\\\.svc\\\\.cluster\\\\.local", cluster_id=~".*"}}) by (le,cluster_id))' \
                       ''.format_map({"app_name": app_id, "namespace": namespace}),

            'cpu': '(max(rate(container_cpu_usage_seconds_total{{namespace="{namespace}",' \
                   'container=~".*{app_name}.*", container!="", container!="POD", ' \
                   'container!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|.*default-tester"}}[10m])) ' \
                   'by (namespace, pod, container,cluster_id) / min(kube_pod_container_resource_limits{{resource="cpu", ' \
                   ' namespace="{namespace}"}}) by (namespace, pod, container,cluster_id) * 100 )'.format_map(
                {"app_name": app_id, "namespace": namespace}),

            'mem': ('( max(container_memory_working_set_bytes{{ namespace=\"{namespace}\",'
                    'container=~".*{app_name}.*", container!="", '
                    'container!="POD",container!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|.*default-tester"}}) '
                    'by (namespace, pod, container,cluster_id) / min(kube_pod_container_resource_limits{{resource="memory",  '
                    'namespace="{namespace}"}}) by (namespace, pod, container,cluster_id) * 100 )').format_map(
                {"app_name": app_id, "namespace": namespace})
        }
        return template[metric_type]

    def _fetch_metrics(self, metric_type, namespace, app_id, start_time=None, end_time=None, n_minutes=5,
                       steps=60, offset=0):

        if not start_time:
            end_time, start_time = self._offset_handler(n_minutes=n_minutes, offset=offset)


        query = self._construct_query(metric_type, namespace, app_id)

        return self.prometheus_handler.get_query_range_data(query=query, start_time=start_time, end_time=end_time,
                                                            steps=steps)

 

    
   

    def anomaly_mem(self, namespace, app_id, time_dict, threshold=5):
        query = self._construct_query("mem", namespace, app_id)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_cpu(self, namespace, app_id, time_dict, threshold=5):
        query = self._construct_query("cpu", namespace, app_id)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_latency(self, namespace, app_id, time_dict, threshold=3):
        query = self._construct_query("latency", namespace, app_id)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_5xx(self, namespace, app_id, time_dict, threshold=2):
        query = self._construct_query("error_5xx", namespace, app_id)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        transformations = {
            "transform_type": ["log"]
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source, transformations=transformations)
        return result
    
    def anomaly_5xx_pct(self, namespace, app_id, time_dict, threshold=2):
        query = self._construct_query("5xx_pct", namespace, app_id)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_traffic(self, namespace, app_id, time_dict, threshold=3):
        query = self._construct_query("traffic", namespace, app_id)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result
    
    def anomaly_all(self, namespace, app_id, time_dict, thresholds=None):
        """
        Calls all anomaly detection methods and returns their results.

        :param namespace: Namespace name.
        :param app_id: Application ID.
        :param time_dict: Dictionary containing time-related parameters.
        :param thresholds: Optional dictionary to specify thresholds for each metric type.
        :return: Dictionary containing results of all anomaly detections.
        """
        if thresholds is None:
            thresholds = {
                "mem": 5,
                "cpu": 5,
                "latency": 3,
                "5xx": 2,
                "traffic": 3
            }
        # [ "latency", "5xx", "traffic"]

        # results = {}
        # for metric in [ "latency", "5xx", "traffic","5xx_pct"]:
        #     anomaly_result = getattr(self, f"anomaly_{metric}")(namespace, app_id, time_dict, thresholds.get(metric))
        #     results[metric] = {
        #         "anomaly": anomaly_result["anomaly"],
        #         "baseline_stats": anomaly_result["baseline_stats"],
        #         "error_stats": anomaly_result["error"]
        #     }

        results = {}
        metrics = ["latency", "5xx", "traffic", "5xx_pct"]

        with ThreadPoolExecutor() as executor:
            future_to_metric = {
                executor.submit(getattr(self, f"anomaly_{metric}"), namespace, app_id, time_dict, thresholds.get(metric)): metric
                for metric in metrics
            }

            for future in futures.as_completed(future_to_metric):
                metric = future_to_metric[future]
                anomaly_result = future.result()
                results[metric] = {
                    "anomaly": anomaly_result["anomaly"],
                    "baseline_stats": anomaly_result["baseline_stats"],
                    "error_stats": anomaly_result["error"]
                }

        return results
    



if __name__ == "__main__":
    metrics = WcnpMetrics()

    # res = metrics._fetch_metrics("mem", "mx-glass", "mexico-journey-prod")
    #
    # print(res)
    # # print(Analyze.parse_data_traffic(res))
    # res =metrics.anomaly_traffic("mx-glass", "mexico-journey-prod",{'n_minutes':10}, 3)
    # print(res)

    print({"status":"Event validated and processing started in the services module","payload":"{\"user\":\"<EMAIL>\",\"occurrenceTime\":1739545443622,\"eventType\":\"f62e8b06-50fb-45ac-8f24-b4b9a749ce0b\",\"shortDesc\":\"WCNP GSLB Health Status lower than 75% for Namespace intl-sct-ei-agg-inv and application ei-realtime-aggregator-sink-ca\",\"longDesc\":\"\",\"severity\":\"\",\"suggestedAction\":\"\",\"systemIdentity\":{\"ip\":\"127.0.0.1\"},\"properties\":{\"alertComponent\":\"juno__ca__tier-0__intl-sre-golden-signal-alerts\",\"alertName\":\"WCNP GSLB Health Status lower than 75% for Namespace intl-sct-ei-agg-inv and application ei-realtime-aggregator-sink-ca\",\"alertTeam\":\"intl_sre_golden_signals\",\"alerts\":\"Annotations:\\n - message=WCNP Alert for Galend Health Check for namespace \\nLabels:\\n - alert_component=juno__ca__tier-0__intl-sre-golden-signal-alerts\\n - alert_filters=galend_health_check__exclude__like_dc_ids_key_value\\n - alert_id=f984b64ae8db4270b93ce735a4400acd_14\\n - alert_owner_category=sre\\n - alert_sla_name=gslb_health_current_threshold_pct\\n - alert_team=intl_sre_golden_signals\\n - alert_type=wcnp\\n - alertname=WCNP GSLB Health Status lower than 75% for Namespace intl-sct-ei-agg-inv and application ei-realtime-aggregator-sink-ca\\n - app_name=ei-realtime-aggregator-sink-ca\\n - galend_health_check__exclude__like_dc_ids_key_value=['10_43_59_196']\\n - gslb_health_current_threshold_pct=75\\n - market=ca\\n - mms_email=<EMAIL>\\n - mms_slack_channel=intl-sre-golden-signal-alerts\\n - mms_xmatters_group=intl-sre-oncall\\n - namespace=intl-sct-ei-agg-inv\\n - severity=critical\\n - tier=tier-0\\n - tool=juno\\n - valunit=%\",\"clusterId\":\"\",\"clusterProfile\":\"\",\"clusterRegion\":\"\",\"clusterType\":\"\",\"commonAnnotations\":[\"message=WCNP Alert for Galend Health Check for namespace \"],\"commonLabels\":[\"alert_filters=galend_health_check__exclude__like_dc_ids_key_value\",\"alert_type=wcnp\",\"galend_health_check__exclude__like_dc_ids_key_value=['10_43_59_196']\",\"market=ca\",\"mms_slack_channel=intl-sre-golden-signal-alerts\",\"namespace=intl-sct-ei-agg-inv\",\"alert_sla_name=gslb_health_current_threshold_pct\",\"severity=critical\",\"tier=tier-0\",\"alert_component=juno__ca__tier-0__intl-sre-golden-signal-alerts\",\"alert_id=f984b64ae8db4270b93ce735a4400acd_14\",\"alert_owner_category=sre\",\"app_name=ei-realtime-aggregator-sink-ca\",\"gslb_health_current_threshold_pct=75\",\"mms_xmatters_group=intl-sre-oncall\",\"alert_team=intl_sre_golden_signals\",\"alertname=WCNP GSLB Health Status lower than 75% for Namespace intl-sct-ei-agg-inv and application ei-realtime-aggregator-sink-ca\",\"mms_email=<EMAIL>\",\"tool=juno\",\"valunit=%\"],\"endsAt\":\"2025-02-19T13:22:03Z\",\"generatorURL\":\"http://prometheus.query.prod.mms.walmart.net/graph?g0.expr=%28sum%28avg_over_time%28galend_health_check%7Bcheck%21~%22.%2A10_43_59_196.%2A%22%2Ccheck%21~%22.%2Apre-prod.%2A%7C.%2Aprod-cnry.%2A%7C.%2Aprod-e2e.%2A%7C.%2Ae2e.%2A%7C.%2Alaurentian.%2A%7C.%2Apreprod.%2A%22%2Ccheck%3D~%22.%2Aprod_.%2A%22%2Ccheck%3D~%22MTD%5C%5C%7Cei-realtime-aggregator-sink-ca.%2Aintl-sct-ei-agg-inv_.%2A%22%2Cstatus%3D%22up%22%7D%5B5m%5D%29%29+%2F+sum%28avg_over_time%28galend_health_check%7Bcheck%21~%22.%2A10_43_59_196.%2A%22%2Ccheck%21~%22.%2Apre-prod.%2A%7C.%2Aprod-cnry.%2A%7C.%2Aprod-e2e.%2A%7C.%2Ae2e.%2A%7C.%2Alaurentian.%2A%7C.%2Apreprod.%2A%22%2Ccheck%3D~%22.%2Aprod_.%2A%22%2Ccheck%3D~%22MTD%5C%5C%7Cei-realtime-aggregator-sink-ca.%2Aintl-sct-ei-agg-inv_.%2A%22%7D%5B5m%5D%29%29%29+%2A+100+%3C+75\\u0026g0.tab=1\",\"groupCount\":\"1\",\"groupKey\":\"953baec909fbf1abc05a655f38af3be134a677ee6ab958307b62f47f4ac85d83\",\"groupLabels\":[\"alertname=WCNP GSLB Health Status lower than 75% for Namespace intl-sct-ei-agg-inv and application ei-realtime-aggregator-sink-ca\",\"mms_email=<EMAIL>\",\"mms_slack_channel=intl-sre-golden-signal-alerts\",\"mms_xmatters_group=intl-sre-oncall\",\"namespace=intl-sct-ei-agg-inv\",\"severity=critical\",\"alert_component=juno__ca__tier-0__intl-sre-golden-signal-alerts\",\"alert_team=intl_sre_golden_signals\"],\"mmsEmail\":\"<EMAIL>\",\"mmsSlackChannel\":\"intl-sre-golden-signal-alerts\",\"mmsXmattersGroup\":\"intl-sre-oncall\",\"namespace\":\"intl-sct-ei-agg-inv\",\"receiver\":\"spotlight-alertmanager-bridge\",\"severity\":\"critical\",\"startsAt\":\"2025-02-14T15:04:03Z\",\"status\":\"resolved\",\"statusSpotlight\":\"terminate\",\"version\":\"4\"},\"correlationId\":\"\"}","metadata":{"eventTypeName":"PrometheusAlert","applicationName":"Telemetry","eventTypeId":"f62e8b06-50fb-45ac-8f24-b4b9a749ce0b","recordedTime":"1739971434333"}})

    # res = metrics.anomaly_5xx_pct("mx-glass", "mexico-journey-prod",{'n_minutes':10}, 3)
    # print(res)

    # res = metrics.anomaly_all("mx-glass", "mexico-journey-prod", {'n_minutes':10})
    # print(res)

    # res = metrics.anamoly_latency("mx-glass", "mexico-journey-prod", (1728012043,1728012543), 3, n_minutes=10)
    # {'current_data': [{'timestamp': 1728012043, 'latency': 733.4905660377359, 'cluster_id': 'eus2-prod-a36'},
    #                   {'timestamp': 1728012103, 'latency': 746.2962962962954, 'cluster_id': 'eus2-prod-a36'},
    #                   {'timestamp': 1728012163, 'latency': 503.124999999998, 'cluster_id': 'eus2-prod-a36'},
    #                   {'timestamp': 1728012223, 'latency': 488.02570093457933, 'cluster_id': 'eus2-prod-a36'},
    #                   {'timestamp': 1728012283, 'latency': 500.8928571428573, 'cluster_id': 'eus2-prod-a36'},
    #                   {'timestamp': 1728012343, 'latency': 512.9999999999989, 'cluster_id': 'eus2-prod-a36'},
    #                   {'timestamp': 1728012403, 'latency': 495.0407608695652, 'cluster_id': 'eus2-prod-a36'},
    #                   {'timestamp': 1728012463, 'latency': 491.23831775700927, 'cluster_id': 'eus2-prod-a36'},
    #                   {'timestamp': 1728012523, 'latency': 496.2184873949579, 'cluster_id': 'eus2-prod-a36'},
    #                   {'timestamp': 1728012043, 'latency': 2330.4691663896556, 'cluster_id': 'scus-prod-a77'},
    #                   {'timestamp': 1728012103, 'latency': 2409.3241551939923, 'cluster_id': 'scus-prod-a77'},
    #                   {'timestamp': 1728012163, 'latency': 2345.405138339921, 'cluster_id': 'scus-prod-a77'},
    #                   {'timestamp': 1728012223, 'latency': 2304.6538461538466, 'cluster_id': 'scus-prod-a77'},
    #                   {'timestamp': 1728012283, 'latency': 2329.6409807355512, 'cluster_id': 'scus-prod-a77'},
    #                   {'timestamp': 1728012343, 'latency': 2314.062499999999, 'cluster_id': 'scus-prod-a77'},
    #                   {'timestamp': 1728012403, 'latency': 2309.760551948051, 'cluster_id': 'scus-prod-a77'},
    #                   {'timestamp': 1728012463, 'latency': 2304.4403892944038, 'cluster_id': 'scus-prod-a77'},
    #                   {'timestamp': 1728012523, 'latency': 2276.28959276018, 'cluster_id': 'scus-prod-a77'},
    #                   {'timestamp': 1728012043, 'latency': 28269.84126984128, 'cluster_id': 'wus-prod-a66'},
    #                   {'timestamp': 1728012103, 'latency': 28723.666210670315, 'cluster_id': 'wus-prod-a66'},
    #                   {'timestamp': 1728012163, 'latency': 28393.122676579926, 'cluster_id': 'wus-prod-a66'},
    #                   {'timestamp': 1728012223, 'latency': 27408.443540183118, 'cluster_id': 'wus-prod-a66'},
    #                   {'timestamp': 1728012283, 'latency': 24610.800744878958, 'cluster_id': 'wus-prod-a66'},
    #                   {'timestamp': 1728012343, 'latency': 7123.603351955306, 'cluster_id': 'wus-prod-a66'},
    #                   {'timestamp': 1728012403, 'latency': 3449.459876543211, 'cluster_id': 'wus-prod-a66'},
    #                   {'timestamp': 1728012463, 'latency': 3349.402730375428, 'cluster_id': 'wus-prod-a66'},
    #                   {'timestamp': 1728012523, 'latency': 3425.7936507936515, 'cluster_id': 'wus-prod-a66'}],
    #  'baseline_stats': [{'cluster_id': 'eus2-prod-a36', 'mean': 1880.6942159428288, 'std': 1502.9940448331683},
    #                     {'cluster_id': 'scus-prod-a77', 'mean': 2484.0033074116845, 'std': 1048.7334701808197},
    #                     {'cluster_id': 'wus-prod-a66', 'mean': 4036.750884442264, 'std': 1424.7928034559786}],
    #  'anomaly': [{'cluster_id': 'wus-prod-a66', 'timestamp': 1728012163.0, 'latency': 27481.174888430724}],
    #  'error': None}
    # print(res)
    #
    # res = metrics.anomaly_non_2xx("mx-glass", "mexico-journey-prod", None, 3, n_minutes=10)
    # print(res)
    #
    # res = metrics.anomaly_mem("mx-glass", "mexico-journey-prod",{'n_minutes':10}, 3)
    # print(res)
    # #
    # res = metrics.anomaly_cpu("mx-glass", "mexico-journey-prod",{'n_minutes':10}, 3)
    # print(res)
    #
    # print(metrics.anamoly_latency("mx-glass", "mexico-journey-prod", utc_time_string=""))

