import re
from concurrent import futures

import pandas as pd
import yaml
import os
from analysis.old_analysis.Analysis_handlers import Analyze
from concurrent.futures import ThreadPoolExecutor
from functools import lru_cache
from analysis.Anomaly_detector import PrometheusAnomalyDetector
from libs.prometheus_client import Prometheus
from analysis.metric_handler.basemetric_handler import BaseMetricHandler
from settings import TEMPLATE_BASE_DIR,ANALYSIS_BASE_REPO
TEMPLATE_REPO_NAME = "sre-alert-templates"

class OneopsMetrics(BaseMetricHandler):
    def __init__(self):
        super().__init__('oneops.yaml')
        self._prometheus_handler = None


    # def __init__(self):
    #     """
    #     Initializes the wcnp_metrics class with Prometheus handler, time frame, and component.
    #
    #     """
    #     self._prometheus_handler = None

    @property
    def prometheus_handler(self):
        """
        Lazy loads and returns the Prometheus client handler.
        """
        if self._prometheus_handler is None:
            self._prometheus_handler = Prometheus()
        return self._prometheus_handler

    def _offset_handler(self,n_minutes,start_time=None,end_time=None,offset=0):

        if not start_time and not end_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(n_minutes)

        offset_sec = offset * 24 * 60 * 60
        if offset:
            start_time -= offset_sec
            end_time -= offset_sec



        return int(end_time),int(start_time)



    def anomaly_mem(self, time_dict, tenant, assembly, env, platform, threshold=5):
        # query = self._construct_query("mem", platform, env, assembly, tenant)
        query = self._construct_query(
            "mem",
            platform=platform,
            env=env,
            assembly=assembly,
            tenant=tenant
        )
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_cpu(self, time_dict, tenant, assembly, env, platform, threshold=5):
        # query = self._construct_query("cpu", platform, env, assembly, tenant)
        query = self._construct_query(
            "cpu",
            platform=platform,
            env=env,
            assembly=assembly,
            tenant=tenant
        )
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_latency(self, time_dict, tenant, assembly, env, platform, threshold=3):
        # query = self._construct_query("latency", platform, env, assembly, tenant)
        query = self._construct_query(
            "latency",
            platform=platform,
            env=env,
            assembly=assembly,
            tenant=tenant
        )
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_5xx(self, time_dict, tenant, assembly, env, platform, threshold=2):
        # query = self._construct_query("5xx", platform, env, assembly, tenant)
        query = self._construct_query(
            "5xx",
            platform=platform,
            env=env,
            assembly=assembly,
            tenant=tenant
        )
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        transformations = {
            "transform_type": ["log"]
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source, transformations=transformations)
        return result

    def anomaly_traffic(self, time_dict, tenant, assembly, env, platform, threshold=3):
        # query = self._construct_query("traffic", platform, env, assembly, tenant)
        query = self._construct_query(
            "traffic",
            platform=platform,
            env=env,
            assembly=assembly,
            tenant=tenant
        )
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result
    
    def anomaly_all(self, tenant, assembly, env, platform,time_dict, thresholds=None):
        """
        Calls all anomaly detection methods and returns their results.
        
        :param time_dict: Dictionary containing time-related parameters.
        :param tenant: Tenant name.
        :param assembly: Assembly name.
        :param env: Environment name.
        :param platform: Platform name.
        :param thresholds: Optional dictionary to specify thresholds for each metric type.
        :return: Dictionary containing results of all anomaly detections.
        """
        if thresholds is None:
            thresholds = {
                "mem": 5,
                "cpu": 5,
                "latency": 3,
                "5xx": 2,
                "traffic": 3
            }

        results = {}
        metrics = ["latency", "5xx", "traffic"]

        with ThreadPoolExecutor() as executor:
            futures = {
                executor.submit(
                    getattr(self, f"anomaly_{metric}"),
                    time_dict, tenant, assembly, env, platform, thresholds.get(metric)
                ): metric for metric in metrics
            }

            for future in futures:
                metric = futures[future]
                anomaly_result = future.result()
                results[metric] = {
                    "anomaly": anomaly_result["anomaly"],
                    "baseline_stats": anomaly_result["baseline_stats"],
                    "error_stats": anomaly_result["error"]
                }

        return results

    def anomaly_by_identifier(self, tenant, assembly, env, platform,time_dict, thresholds=None):
        """
        Processes each metric and organizes anomalies by identifier.

        :param namespaces: List of namespace names.
        :param app_ids: List of application IDs.
        :param time_dict: Dictionary containing time-related parameters.
        :param thresholds: Optional dictionary to specify thresholds for each metric type.
        :return: Dictionary containing anomalies organized by identifier.
        """
        if thresholds is None:
            thresholds = {
                "mem": 5,
                "cpu": 5,
                "latency": 3,
                "5xx": 2,
                "traffic": 3,
                "5xx_pct": 2,
            }

        results_by_identifier = {}

        with ThreadPoolExecutor() as executor:
            future_to_metric = {
                executor.submit(
                    getattr(self, f"anomaly_{metric}"),
                    time_dict, tenant, assembly, env, platform, thresholds.get(metric)
                ): metric
                for metric in ["latency", "5xx", "traffic"]
            }

            for future in futures.as_completed(future_to_metric):

                metric = future_to_metric[future]
                anomaly_result = future.result()
                print(anomaly_result["anomaly"])
                baseline_stats = anomaly_result.get("baseline_stats")
                if baseline_stats is not None:
                    for entry in baseline_stats:
                        identifier = entry["Metric"]
                        if identifier not in results_by_identifier:
                            results_by_identifier[identifier] = {}

                        if metric not in results_by_identifier[identifier]:
                            results_by_identifier[identifier][metric] = {
                                "anomaly": [],
                                "baseline_stats": entry,
                                "error_stats": anomaly_result.get("error"),
                            }

                        # Add anomalies to the respective identifier and metric
                        for anomaly in anomaly_result["anomaly"]:
                            if anomaly["Metric"] == identifier:
                                results_by_identifier[identifier][metric]["anomaly"].append({
                                    "Timestamp": anomaly['Timestamp'],
                                    "value": anomaly['value']
                                    # "Metric": anomaly['Metric']
                                })

        return results_by_identifier

if __name__ == "__main__":
    metrics = OneopsMetrics()
    # traffic = metrics._fetch_metrics(metric_type="traffic", tenant='mexicoecomm', assembly='samsoms',
    #                             env='prod', platform='imsapp')
    # #
    # print(traffic)


    print(metrics.anomaly_by_identifier(time_dict={}, tenant='mexicoecomm', assembly='samsoms', env='prod',
                        platform='imsapp'))


    # print(metrics.anomaly_all(time_dict={"current_start_time":1730138320,"current_end_time":1730138420,"historic_start_time":1730136420,"historic_end_time":1730137420}, tenant='mexicoecomm', assembly='samsoms', env='prod',
    #                     platform='imsapp'))

    # def _construct_query(self, metric_type, platform, env, assembly, tenant):
    #     template = {
    #         'latency': ((
    #                         'avg(origin:slb_response_latency_seconds:avg_rate5m{{mms_source="ef", '
    #               'origcfg_host=~"(?i:({platform}.{env}.{assembly}.{tenant}.prod.us.walmart.net))",role=~".*"}}) '
    #               'by(origreq_host)').format_map({"platform": platform, "env": env, "assembly": assembly,
    #                                            "tenant": tenant}
    #                                              )),
    #         '5xx': ((
    #                     'sum(irate(odnd_http_response_code_total{{mms_source="ef", origcfg_host=~"(?i:({platform}.{env}.{assembly}.'
    #          '{tenant}.prod.us.walmart.net))",job=~".*"}}[5m]))'
    #          ' by (statusCode)').format_map({"platform": platform, "env": env, "assembly": assembly,
    #                                            "tenant": tenant}
    #                                         )),
    #         'traffic_status': ((
    #                                'sum(irate(odnd_http_response_code_total{{mms_source="ef", origcfg_host=~"(?i:({platform}.{env}.{assembly}.'
    #          '{tenant}.prod.us.walmart.net))",job=~".*"}}[5m]))'
    #          ' by (statusCode)').format_map({"platform": platform, "env": env, "assembly": assembly,
    #                                            "tenant": tenant}
    #                                         )),
    #         'traffic': ((
    #                         'sum(rate(slb:dc_origcfg_host:odnd_origin_request_total{{mms_source="ef",'
    #      'origcfg_host=~"(?i:({platform}.*{env}.*.{assembly}.{tenant}.{env}.*.walmart.net))",'
    #      'role=~".*"}}[20m:1m])) by (dc)'
    #      ' / scalar(sum(rate(slb:dc_origcfg_host:odnd_origin_request_total{{mms_source="ef",'
    #      'origcfg_host=~"(?i:({platform}.*{env}.*.{assembly}.{tenant}.{env}.*.walmart.net))",'
    #      'role=~".*"}}[20m:1m]))) * 100 ').format_map({"platform": platform, "env": env, "assembly": assembly,
    #                                                             "tenant": tenant}
    #                                                   )),
    #         'cpu': ((
    #                     'avg(usage_idle{{oot="{tenant}", metricgroup="cpu",ooa="{assembly}",'
    #               'ooe=~"{env}.*", oop="{platform}"}} )  by (ooa,oop,ooe,dc) ').
    #              format_map({"tenant": tenant, "assembly": assembly, "env": env, "platform": platform}
    #                         )),
    #         'mem': ((
    #                     'avg(used_percent{{oot="{tenant}", metricgroup="mem",ooa="{assembly}",'
    #              'ooe=~"{env}.*", oop="{platform}"}}) by (' \
    #         'ooa, oop, ooe, dc) ').
    #              format_map({"tenant": tenant, "assembly": assembly, "env": env, "platform": platform}
    #                         ))
    #     }
    #     return template[metric_type]