from analysis.Anomaly_detector import PrometheusAnomalyDetector
from libs.prometheus_client import Prometheus

class AzureSQLMetrics:

    def __init__(self):
        """
        Initializes the AzureSQLMetrics class with Prometheus handler.
        """
        self._prometheus_handler = None

    @property
    def prometheus_handler(self):
        """
        Lazy loads and returns the Prometheus client handler.
        """
        if self._prometheus_handler is None:
            self._prometheus_handler = Prometheus()
        return self._prometheus_handler

    def _construct_query(self, metric_type, database):
        template = {
            'cpu': (
                'azuresql_cpu_percent{{database="{database}"}}'
            ).format_map({"database": database}),
            'storage': (
                'azuresql_database_used_space{{server!=".*-secondary",type="PctSpaceUsed",database="{database}"}}'
            ).format_map({"database": database}),
            'deadlocks': (
                'avg(irate(azuresql_deadlocks{{database="{database}"}}[5m])) by (cluster_id, server)'
            ).format_map({"database": database}),
            'latency_io': (
                'avg(avg_over_time(azuresql_data_io_percent{{database="{database}"}}[15m])) by (cluster_id, server)'
            ).format_map({"database": database})
        }
        return template[metric_type]

    def anomaly_cpu(self, time_dict, database, threshold=3):
        query = self._construct_query("cpu", database)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_storage(self, time_dict, database, threshold=3):
        query = self._construct_query("storage", database)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_deadlocks(self, time_dict, database, threshold=3):
        query = self._construct_query("deadlocks", database)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_latency_io(self, time_dict, database, threshold=3):
        query = self._construct_query("latency_io", database)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_all(self, database, time_dict, thresholds=None):
        """
        Calls all anomaly detection methods and returns their results.
        
        :param time_dict: Dictionary containing time-related parameters.
        :param database: Database name.
        :param thresholds: Optional dictionary to specify thresholds for each metric type.
        :return: Dictionary containing results of all anomaly detections.
        """
        if thresholds is None:
            thresholds = {
                "cpu": 3,
                "storage": 3,
                "deadlocks": 3,
                "latency_io": 3
            }

        results = {}
        for metric in ["cpu", "storage", "deadlocks", "latency_io"]:
            anomaly_result = getattr(self, f"anomaly_{metric}")(time_dict, database, thresholds.get(metric))
            results[metric] = {
                "anomaly": anomaly_result["anomaly"],
                "baseline_stats": anomaly_result["baseline_stats"]
            }

        return results

if __name__ == "__main__":
    metrics = AzureSQLMetrics()
    print(metrics.anomaly_all(database='example_database', time_dict={"current_start_time":1730138320,"current_end_time":1730138420,"historic_start_time":1730136420,"historic_end_time":1730137420}))