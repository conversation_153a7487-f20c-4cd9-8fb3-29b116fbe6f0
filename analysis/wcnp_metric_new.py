from libs.prometheus_client import Prometheus
import pandas as pd
from analysis.old_analysis.Analysis_handlers import Analyze
import numpy as np
from analysis.old_analysis.wcnp_parser import <PERSON><PERSON><PERSON><PERSON><PERSON> as parser
from analysis.Anomaly_detector import PrometheusAnomalyDetector
from concurrent.futures import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>xecutor,as_completed
import logging
from concurrent import futures
import re,os
import yaml
from settings import TEMPLATE_BASE_DIR,ANALYSIS_BASE_REPO
TEMPLATE_REPO_NAME = "sre-alert-templates"
from typing import List, Dict, Any, Optional
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class WcnpMetrics:

    def __init__(self):
        """
        Initializes the WcnpMetrics class with a Prometheus handler.
        """
        self._prometheus_handler = None
        file_path = os.path.join(TEMPLATE_BASE_DIR, TEMPLATE_REPO_NAME, ANALYSIS_BASE_REPO, 'config', "wcnp.yaml")
        self.queries = self._load_queries(file_path)

        # Load SLA file
        sla_file_path = os.path.join(TEMPLATE_BASE_DIR, TEMPLATE_REPO_NAME, ANALYSIS_BASE_REPO, 'sla', "wcnp.yaml")
        self.sla_dict = self._load_slas(sla_file_path)
        

    def _load_slas(self, file_path):
        try:
            with open(file_path, 'r') as sla_file:
                sla_dict = yaml.safe_load(sla_file)
                if sla_dict is None:
                    logger.warning(f"SLA file at {file_path} is empty or not properly formatted.")
                    return {}
                return sla_dict
        except FileNotFoundError:
            logger.error(f"SLA file not found at {file_path}.")
            return {}
        except yaml.YAMLError as e:
            logger.error(f"Error parsing SLA YAML file at {file_path}: {e}")
            return {}

    def _load_queries(self, file_path):
        with open(file_path, 'r') as file:
            return yaml.safe_load(file)['queries']

    def log_transform(self, data):
        # Apply log transformation, adding a small constant to avoid log(0)
        return np.log(data + 1)

    @property
    def prometheus_handler(self):
        """
        Lazy loads and returns the Prometheus client handler.
        """
        if self._prometheus_handler is None:
            self._prometheus_handler = Prometheus()
        return self._prometheus_handler

    def _offset_handler(self, n_minutes, start_time=None, end_time=None, offset=0):

        if not start_time and not end_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(n_minutes)

        offset_sec = offset * 24 * 60 * 60
        if offset:
            start_time -= offset_sec
            end_time -= offset_sec

        return end_time, start_time

    def _construct_regex(self, items):
        """
        Constructs a regex pattern from a list of items.
        """
        # escaped_items = [re.escape(item) for item in items]
        regex_pattern = '|'.join(items)
        return regex_pattern
    
    def _construct_query(self, metric_type, namespaces, app_ids):
        namespace_regex = self._construct_regex(namespaces)
        app_name_regex = self._construct_regex(app_ids)

        query_template = self.queries.get(metric_type)
        if not query_template:
            raise ValueError(f"Query for metric type '{metric_type}' not found.")

        return query_template.format(app_name=app_name_regex, namespace=namespace_regex)


    def _fetch_metrics(self, metric_type, namespaces, app_ids, start_time=None, end_time=None, n_minutes=5,
                       steps=60, offset=0):

        if not start_time:
            end_time, start_time = self._offset_handler(n_minutes=n_minutes, offset=offset)

        query = self._construct_query(metric_type, namespaces, app_ids)

        return self.prometheus_handler.get_query_range_data(
            query=query, start_time=start_time, end_time=end_time, steps=steps
        )

    def anomaly_mem(self, namespaces, app_ids, time_dict, threshold=5):
        query = self._construct_query("mem", namespaces, app_ids)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60,
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_cpu(self, namespaces, app_ids, time_dict, threshold=5):
        query = self._construct_query("cpu", namespaces, app_ids)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60,
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_latency(self, namespaces, app_ids, time_dict, threshold=3):
        query = self._construct_query("latency", namespaces, app_ids)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60,
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_5xx(self, namespaces, app_ids, time_dict, threshold=2):
        query = self._construct_query("error_5xx", namespaces, app_ids)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60,
        }

        transformations = {
            "transform_type": ["log"],
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source, transformations=transformations)
        return result

    def anomaly_5xx_pct(self, namespaces, app_ids, time_dict, threshold=2):
        query = self._construct_query("5xx_pct", namespaces, app_ids)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60,
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_traffic(self, namespaces, app_ids, time_dict, threshold=3):
        query = self._construct_query("traffic", namespaces, app_ids)
        data_source = {
            **time_dict,
            "query": query,
            "steps": 60,
        }

        anomaly_detector = PrometheusAnomalyDetector(threshold=threshold)
        result = anomaly_detector.process(data_source)
        return result

    def anomaly_all(self, namespaces, app_ids, time_dict, thresholds=None):
        """
        Calls all anomaly detection methods and returns their results.

        :param namespaces: List of namespace names.
        :param app_ids: List of application IDs.
        :param time_dict: Dictionary containing time-related parameters.
        :param thresholds: Optional dictionary to specify thresholds for each metric type.
        :return: Dictionary containing results of all anomaly detections.
        """
        if thresholds is None:
            thresholds = {
                "mem": 5,
                "cpu": 5,
                "latency": 3,
                "5xx": 2,
                "traffic": 3,
                "5xx_pct": 2,
            }

        results = {}
        metrics = ["latency", "5xx", "traffic", "5xx_pct"]

        with ThreadPoolExecutor() as executor:
            future_to_metric = {
                executor.submit(
                    getattr(self, f"anomaly_{metric}"),
                    namespaces,
                    app_ids,
                    time_dict,
                    thresholds.get(metric),
                ): metric
                for metric in metrics
            }

            for future in futures.as_completed(future_to_metric):
                metric = future_to_metric[future]
                anomaly_result = future.result()
                results[metric] = {
                    "anomaly": anomaly_result["anomaly"],
                    "baseline_stats": anomaly_result["baseline_stats"],
                    "error_stats": anomaly_result["error"],
                }

        return results
    

    def anomaly_by_identifier(
        self,
        namespaces: List[str],
        app_ids: List[str],
        time_dict: Dict[str, Any],
        thresholds: Optional[Dict[str, float]] = None,
        debug: bool = False
    ) -> Dict[str, Any]:
        """
        Processes each metric and organizes anomalies by identifier.

        :param namespaces: List of namespace names.
        :param app_ids: List of application IDs.
        :param time_dict: Dictionary containing time-related parameters.
        :param thresholds: Optional dictionary to specify thresholds for each metric type.
        :param debug: Boolean to output errors if True.
        :return: Dictionary containing anomalies organized by identifier.
        """
        thresholds = thresholds or self.default_thresholds()
        results_by_identifier: Dict[str, Any] = {}

        metrics = ["latency", "traffic", "5xx_pct"]
        logger.debug(f"Starting anomaly detection for metrics: {metrics}")

        with ThreadPoolExecutor() as executor:
            futures = {
                executor.submit(
                    getattr(self, f"anomaly_{metric}"),
                    namespaces,
                    app_ids,
                    time_dict,
                    thresholds.get(metric)
                ): metric
                for metric in metrics
            }

            for future in as_completed(futures):
                metric = futures[future]
                try:
                    anomaly_result = future.result()
                    logger.debug(f"Anomaly result for metric '{metric}': {anomaly_result}")

                    baseline_stats = anomaly_result.get("baseline_stats")
                    if baseline_stats:
                        self._process_baseline_stats(
                            baseline_stats, metric, results_by_identifier, anomaly_result, debug
                        )
                    else:
                        self._handle_missing_baseline_stats(metric, anomaly_result, results_by_identifier, debug)

                except Exception as e:
                    logger.error(f"Error processing metric '{metric}': {e}")
                    if debug:
                        results_by_identifier.setdefault("metric_errors", {})[metric] = str(e)

        return results_by_identifier

    def default_thresholds(self) -> Dict[str, float]:
        """
        Returns the default thresholds for metrics.

        :return: Dictionary of default thresholds.
        """
        return {
            "mem": 5,
            "cpu": 5,
            "latency": 3,
            "5xx": 2,
            "traffic": 3,
            "5xx_pct": 2,
        }

    def _process_baseline_stats(
        self,
        baseline_stats: List[Dict[str, Any]],
        metric: str,
        results: Dict[str, Any],
        anomaly_result: Dict[str, Any],
        debug: bool
    ) -> None:
        """
        Processes baseline statistics and populates the results dictionary.

        :param baseline_stats: List of baseline statistics entries.
        :param metric: The metric being processed.
        :param results: The results dictionary to populate.
        :param anomaly_result: The anomaly result containing anomalies and baseline stats.
        :param debug: Boolean indicating if debug information should be included.
        """
        for entry in baseline_stats:
            identifier = entry.pop("Metric")
            identifier_dict = results.setdefault(identifier, {})
            metric_dict = identifier_dict.setdefault(metric, {
                "anomaly": [],
                "trend_analysis": {
                    "expected_trend": entry.get("mean"),
                    "error_margin": entry.get("std"),
                }
            })

            if debug:
                metric_dict["error_stats"] = anomaly_result.get("error")

            self._add_anomalies(
                identifier,
                metric,
                anomaly_result.get("anomaly", []),
                metric_dict
            )

    def _add_anomalies(
        self,
        identifier: str,
        metric: str,
        anomalies: List[Dict[str, Any]],
        metric_dict: Dict[str, Any]
    ) -> None:
        """
        Adds anomalies to the metric dictionary based on SLA conditions.

        :param identifier: The identifier for which anomalies are being added.
        :param metric: The metric type.
        :param anomalies: List of anomaly entries.
        :param metric_dict: The dictionary to append anomalies to.
        """
        for anomaly in anomalies:
            if anomaly.get("Metric") != identifier:
                continue

            anomaly_value = anomaly.get("value")
            sla_conditions = self.sla_dict.get(metric, {})
            gt_value = sla_conditions.get('gt')
            lt_value = sla_conditions.get('lt')

            try:
                if self._is_anomaly_within_sla(anomaly_value, gt_value, lt_value):
                    metric_dict["anomaly"].append({
                        "Timestamp": anomaly.get("Timestamp"),
                        "value": anomaly_value
                    })
            except Exception as e:
                logger.warning(f"Exception while evaluating SLA conditions: {e}")
                metric_dict["anomaly"].append({
                    "Timestamp": anomaly.get("Timestamp"),
                    "value": anomaly_value
                })

    def _is_anomaly_within_sla(
        self,
        anomaly_value: float,
        gt_value: Optional[float],
        lt_value: Optional[float]
    ) -> bool:
        """
        Determines if an anomaly value meets the SLA conditions.

        :param anomaly_value: The value of the anomaly.
        :param gt_value: Greater-than SLA threshold.
        :param lt_value: Less-than SLA threshold.
        :return: True if anomaly meets SLA conditions, else False.
        """
        if gt_value is None and lt_value is None:
            return True
        return (gt_value is not None and anomaly_value > gt_value) or \
               (lt_value is not None and anomaly_value < lt_value)

    def _handle_missing_baseline_stats(
        self,
        metric: str,
        anomaly_result: Dict[str, Any],
        results: Dict[str, Any],
        debug: bool
    ) -> None:
        """
        Handles scenarios where baseline statistics are missing.

        :param metric: The metric being processed.
        :param anomaly_result: The anomaly result containing anomalies and baseline stats.
        :param results: The results dictionary to populate.
        :param debug: Boolean indicating if debug information should be included.
        """
        if debug:
            error_message = anomaly_result.get("error", "No baseline stats available.")
            results.setdefault("metric_errors", {})[metric] = error_message
            logger.debug(f"Missing baseline stats for metric '{metric}': {error_message}")
    
    # def anomaly_by_identifier(self, namespaces, app_ids, time_dict, thresholds=None, debug=False):
    #     """
    #     Processes each metric and organizes anomalies by identifier.
    #
    #     :param namespaces: List of namespace names.
    #     :param app_ids: List of application IDs.
    #     :param time_dict: Dictionary containing time-related parameters.
    #     :param thresholds: Optional dictionary to specify thresholds for each metric type.
    #     :param debug: Boolean to output errors if True.
    #     :return: Dictionary containing anomalies organized by identifier.
    #     """
    #     if thresholds is None:
    #         thresholds = {
    #             "mem": 5,
    #             "cpu": 5,
    #             "latency": 3,
    #             "5xx": 2,
    #             "traffic": 3,
    #             "5xx_pct": 2,
    #         }
    #
    #     results_by_identifier = {}
    #
    #     with ThreadPoolExecutor() as executor:
    #         future_to_metric = {
    #             executor.submit(
    #                 getattr(self, f"anomaly_{metric}"),
    #                 namespaces,
    #                 app_ids,
    #                 time_dict,
    #                 thresholds.get(metric)
    #             ): metric
    #             for metric in ["latency", "traffic", "5xx_pct"]
    #         }
    #
    #         for future in futures.as_completed(future_to_metric):
    #             metric = future_to_metric[future]
    #             anomaly_result = future.result()
    #             new_baseline_stats = anomaly_result.get("baseline_stats")
    #             if new_baseline_stats is not None:
    #                 for entry in new_baseline_stats:
    #                     identifier = entry.pop("Metric")
    #                     if identifier not in results_by_identifier:
    #                         results_by_identifier[identifier] = {}
    #
    #                     if metric not in results_by_identifier[identifier]:
    #                         results_by_identifier[identifier][metric] = {
    #                             "anomaly": [],
    #                             "trend analysis": {
    #                                 "expected_trend": entry["mean"],
    #                                 "error_margin": entry["std"],
    #                             }}
    #                         if debug:
    #                             results_by_identifier[identifier][metric]["error_stats"]=anomaly_result.get("error")
    #
    #                     # Add anomalies to the respective identifier and metric
    #                     for anomaly in anomaly_result["anomaly"]:
    #                         if anomaly["Metric"] == identifier:
    #                             # Retrieve SLA conditions for the current identifier
    #                             gt_value = self.sla_dict.get('gt', {}).get(metric, None)
    #                             lt_value = self.sla_dict.get('lt', {}).get(metric, None)
    #
    #                             anomaly_value = anomaly['value']
    #                             try:
    #                                 # Check if the anomaly value meets the SLA conditions
    #                                 if gt_value is None and lt_value is None:
    #                                     results_by_identifier[identifier][metric]["anomaly"].append({
    #                                         "Timestamp": anomaly['Timestamp'],
    #                                         "value": anomaly_value
    #                                     })
    #                                 # Check if the anomaly value meets the SLA conditions
    #                                 elif (gt_value is not None and anomaly_value > gt_value) or \
    #                                      (lt_value is not None and anomaly_value < lt_value):
    #                                     results_by_identifier[identifier][metric]["anomaly"].append({
    #                                         "Timestamp": anomaly['Timestamp'],
    #                                         "value": anomaly_value
    #                                     })
    #                             except Exception as e:
    #                                 # Append anomaly if any exception occurs
    #                                 results_by_identifier[identifier][metric]["anomaly"].append({
    #                                     "Timestamp": anomaly['Timestamp'],
    #                                     "value": anomaly_value
    #                                 })
    #
    #             else:
    #                 # Handle the case where new_baseline_stats is None
    #                 if debug:
    #                     if "metric errors" not in results_by_identifier:
    #                         results_by_identifier["metric errors"] = {}
    #
    #                     results_by_identifier["metric errors"].update({
    #                         metric: anomaly_result.get("error")
    #                     })
    #
    #     return results_by_identifier
    



    # def anomaly_by_identifier(self, namespaces, app_ids, time_dict, thresholds=None):
    #     """
    #     Processes each metric and organizes anomalies by identifier.

    #     :param namespaces: List of namespace names.
    #     :param app_ids: List of application IDs.
    #     :param time_dict: Dictionary containing time-related parameters.
    #     :param thresholds: Optional dictionary to specify thresholds for each metric type.
    #     :return: Dictionary containing anomalies organized by identifier.
    #     """
    #     if thresholds is None:
    #         thresholds = {
    #             "mem": 5,
    #             "cpu": 5,
    #             "latency": 3,
    #             "5xx": 2,
    #             "traffic": 3,
    #             "5xx_pct": 2,
    #         }

    #     results_by_identifier = {}

    #     with ThreadPoolExecutor() as executor:
    #         future_to_metric = {
    #             executor.submit(
    #                 getattr(self, f"anomaly_{metric}"),
    #                 namespaces,
    #                 app_ids,
    #                 time_dict,
    #                 thresholds.get(metric)
    #             ): metric
    #             for metric in ["latency", "traffic", "5xx_pct"]
    #         }

    #         for future in futures.as_completed(future_to_metric):
                
    #             metric = future_to_metric[future]
    #             anomaly_result = future.result()
    #             baseline_stats = anomaly_result.get("baseline_stats")
    #             if baseline_stats is not None:
    #                 for entry in baseline_stats:
    #                     identifier = entry["Metric"]
    #                     if identifier not in results_by_identifier:
    #                         results_by_identifier[identifier] = {}

    #                     if metric not in results_by_identifier[identifier]:
    #                         results_by_identifier[identifier][metric] = {
    #                             "anomaly": [],
    #                             "baseline_stats": entry,
    #                             "error_stats": anomaly_result.get("error"),
    #                         }

    #                     # Add anomalies to the respective identifier and metric
    #                     for anomaly in anomaly_result["anomaly"]:
    #                         if anomaly["Metric"] == identifier:
    #                             results_by_identifier[identifier][metric]["anomaly"].append({
    #                                 "Timestamp": anomaly['Timestamp'],
    #                                 "value": anomaly['value']
    #                                 # "Metric": anomaly['Metric']
    #                             })

    #     return results_by_identifier
    

    # def anomaly_by_identifier(self, namespaces, app_ids, time_dict, thresholds=None):
    #     """
    #     Processes each metric and organizes results by metric identifiers.

    #     :param namespaces: List of namespace names.
    #     :param app_ids: List of application IDs.
    #     :param time_dict: Dictionary containing time-related parameters.
    #     :param thresholds: Optional dictionary to specify thresholds for each metric type.
    #     :return: Dictionary containing results organized by metric identifiers.
    #     """
    #     if thresholds is None:
    #         thresholds = {
    #             "mem": 5,
    #             "cpu": 5,
    #             "latency": 3,
    #             "5xx": 2,
    #             "traffic": 3,
    #             "5xx_pct": 2,
    #         }

    #     results_by_identifier = {}
    #     metrics = ["latency",  "traffic", "5xx_pct"]

    #     with ThreadPoolExecutor() as executor:
    #         future_to_metric = {
    #             executor.submit(
    #                 getattr(self, f"anomaly_{metric}"),
    #                 namespaces,
    #                 app_ids,
    #                 time_dict,
    #                 thresholds.get(metric)
    #             ): metric
    #             for metric in metrics
    #         }

    #         for future in futures.as_completed(future_to_metric):
    #             metric = future_to_metric[future]
    #             anomaly_result = future.result()

    #             baseline_stats = anomaly_result.get("baseline_stats")
    #             if baseline_stats is not None:
    #                 for entry in baseline_stats:
    #                     identifier = entry["Metric"]
    #                     if identifier not in results_by_identifier:
    #                         results_by_identifier[identifier] = {}
    #                     results_by_identifier[identifier][metric] = {
    #                         "anomaly": anomaly_result["anomaly"],
    #                         "baseline_stats": entry,
    #                         "error_stats": anomaly_result["error"],
    #                     }

    #     return results_by_identifier

    # def anomaly_by_identifier(self, namespaces, app_ids, time_dict, thresholds=None):
    #     """
    #     Processes each metric and organizes results by metric identifiers.

    #     :param namespaces: List of namespace names.
    #     :param app_ids: List of application IDs.
    #     :param time_dict: Dictionary containing time-related parameters.
    #     :param thresholds: Optional dictionary to specify thresholds for each metric type.
    #     :return: Dictionary containing results organized by metric identifiers.
    #     """
    #     if thresholds is None:
    #         thresholds = {
    #             "mem": 5,
    #             "cpu": 5,
    #             "latency": 3,
    #             "5xx": 2,
    #             "traffic": 3,
    #             "5xx_pct": 2,
    #         }

    #     results_by_identifier = {}
    #     metrics = ["latency", "5xx", "traffic", "5xx_pct"]


    #     for metric in metrics:
    #         anomaly_method = getattr(self, f"anomaly_{metric}")
    #         anomaly_result = anomaly_method(namespaces, app_ids, time_dict, thresholds.get(metric))

    #         baseline_stats = anomaly_result.get("baseline_stats")
    #         if baseline_stats is not None:
    #             for entry in baseline_stats:
    #                 identifier = entry["Metric"]
    #                 if identifier not in results_by_identifier:
    #                     results_by_identifier[identifier] = {}
    #                 results_by_identifier[identifier][metric] = {
    #                     "anomaly": anomaly_result["anomaly"],
    #                     "baseline_stats": entry,
    #                     "error_stats": anomaly_result["error"],
    #                 }

    #     return results_by_identifier

if __name__ == "__main__":
    metrics = WcnpMetrics()

    # res = metrics._fetch_metrics("mem", "mx-glass", "mexico-journey-prod")
    #
    # print(res)
    # # print(Analyze.parse_data_traffic(res))
    # res =metrics.anomaly_5xx_pct(["mx-glass"], ["mexico-journey-prod"],{'n_minutes':10}, 3)
    # print(res)

    res = metrics.anomaly_by_identifier(["mx-glass","mx-glass"], ["mexico-journey-prod","mexico-ea-journey"],{'n_minutes':10})
    print(res)

    # def _construct_query(self, metric_type, namespaces, app_ids):
    #     namespace_regex = self._construct_regex(namespaces)
    #     app_name_regex = self._construct_regex(app_ids)

    #     template = {
    #         'error_5xx': (
    #             'sum by (cluster_id, app, namespace) ('
    #             'label_replace('
    #                 'label_replace('
    #                     'envoy_cluster_upstream_rq:sum_rate2m{{'
    #                         'cluster_name=~"outbound.*({app_name}).*({namespace}).svc.cluster.local", '
    #                         'response_code_class = "5xx"'
    #                     '}} * 60, '
    #                     '"app", "$1", '
    #                     '"cluster_name", "outbound.*?({app_name}).*?({namespace}).svc.cluster.local"'
    #                 '), '
    #                 '"namespace", "$2", '
    #                 '"cluster_name", "outbound.*?({app_name}).*?({namespace}).svc.cluster.local"'
    #             ')'
    #             ')'
    #         ).format_map({"app_name": app_name_regex, "namespace": namespace_regex}),

    #         '5xx_pct': (
    #             'sum by (cluster_id, app, namespace) ('
    #                 'label_replace('
    #                     'label_replace('
    #                         'avg_over_time(envoy_cluster_upstream_rq:sum_rate2m{{'
    #                             'cluster_name=~"outbound.*(mexico-journey-prod|mexico-ea-journey).*(mx-glass).svc.cluster.local", '
    #                             'cluster_name!~".*canry.*|.*cnry.*|.*canary.*", '
    #                             'response_code_class="5xx"'
    #                         '}}[5m]), '
    #                         '"app", "$1", '
    #                         '"cluster_name", "outbound.*?({app_name}).*?({namespace}).svc.cluster.local"'
    #                     '), '
    #                     '"namespace", "$2", '
    #                     '"cluster_name", "outbound.*?({app_name}).*?({namespace}).svc.cluster.local"'
    #                 ')'
    #             ') / '
    #             'sum by (cluster_id, app, namespace) ('
    #                 'label_replace('
    #                     'label_replace('
    #                         'avg_over_time(envoy_cluster_upstream_rq:sum_rate2m{{'
    #                             'cluster_name=~"outbound.*(mexico-journey-prod|mexico-ea-journey).*(mx-glass).svc.cluster.local", '
    #                             'cluster_name!~".*canry.*|.*cnry.*|.*canary.*"'
    #                         '}}[5m]), '
    #                         '"app", "$1", '
    #                         '"cluster_name", "outbound.*?({app_name}).*?({namespace}).svc.cluster.local"'
    #                     '), '
    #                     '"namespace", "$2", '
    #                     '"cluster_name", "outbound.*?({app_name}).*?({namespace}).svc.cluster.local"'
    #                 ')'
    #             ') * 100'
    #         ).format_map({"app_name": app_name_regex, "namespace": namespace_regex}),


    #         'non_2xx': (
    #             'sum by (cluster_id, app, namespace) ('
    #             'label_replace('
    #                 'label_replace('
    #                     'envoy_cluster_upstream_rq:sum_rate2m{{'
    #                         'cluster_name=~"outbound.*({app_name}).*({namespace}).svc.cluster.local", '
    #                         'response_code_class != "2xx"'
    #                     '}} * 60, '
    #                     '"app", "$1", '
    #                     '"cluster_name", "outbound.*?({app_name}).*?({namespace}).svc.cluster.local"'
    #                 '), '
    #                 '"namespace", "$2", '
    #                 '"cluster_name", "outbound.*?({app_name}).*?({namespace}).svc.cluster.local"'
    #             ')'
    #             ')'
    #         ).format_map({"app_name": app_name_regex, "namespace": namespace_regex}),

    #         'traffic': (
    #             'sum by (cluster_id, app, namespace) ('
    #             'label_replace('
    #             'label_replace('
    #                 'envoy_cluster_upstream_rq:sum_rate2m{{'
    #                     'cluster_name=~"outbound.*({app_name}).*({namespace}).svc.cluster.local"'
    #                 '}} * 60, '
    #                 '"app", "$1", '
    #                 '"cluster_name", "outbound.*?({app_name}).*?({namespace}).svc.cluster.local"'
    #             '), '
    #             '"namespace", "$2", '
    #             '"cluster_name", "outbound.*?({app_name}).*?({namespace}).svc.cluster.local"'
    #             ')'
    #             ')'
    #         ).format_map({"app_name": app_name_regex, "namespace": namespace_regex}),

    #         'latency': (
    #             'histogram_quantile(0.95, sum by (cluster_id, app, namespace, le) ('
    #                 'label_replace('
    #                     'label_replace('
    #                         'envoy_cluster_upstream_rq_time_bucket:sum_rate2m{{'
    #                             'cluster_name=~"outbound.*({app_name}).*({namespace}).svc.cluster.local", '
    #                             'cluster_id=~".*"'
    #                         '}} , '
    #                         '"app", "$1", '
    #                         '"cluster_name", "outbound.*?({app_name}).*?({namespace}).svc.cluster.local"'
    #                     '), '
    #                     '"namespace", "$2", '
    #                     '"cluster_name", "outbound.*?({app_name}).*?({namespace}).svc.cluster.local"'
    #                 ')'
    #             '))'
    #         ).format_map({"app_name": app_name_regex, "namespace": namespace_regex}),
    #     }

    #     return template[metric_type]
