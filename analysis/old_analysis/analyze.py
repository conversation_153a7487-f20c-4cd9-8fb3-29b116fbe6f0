__author__ = '<PERSON><PERSON> cheepati'
__version__ = 1.0

import logging, os, time
from logging import handlers
from libs.prometheus_client import Prometheus

logger = logging.getLogger(__name__)


class Analyze(Prometheus):
    def __init__(self, **kwargs):
        super(Analyze, self).__init__(**kwargs)
        self.is_alert = kwargs.get("is_alert")
        self.query = kwargs.get("query")

    def is_sla_breached(self,query=None):
        if not query:
            query = self.query
        end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(20100)
        status, too_many_data_points, results = self.get_query_range_data_with_meta(query=query,
                                                                                    start_time=start_time,
                                                                                    end_time=end_time, steps=300)
        if status:
            if results.get("data", dict()).get("result", False):
                data = results.get("data").get("result")[0].get("values")
                if len(data) > 0:
                    return {"status":True, "message":f"sla breached by {len(data)} time in two weeks","data":data}
        elif not status and too_many_data_points:

            return {"status": False, "message": "Too many data points", "data": None}
        return {"status": False, "message": None, "data": None}


def main():
    log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    log_format = '%(asctime)s %(filename)18s %(funcName)25s %(lineno)3d %(levelname)10s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(log_level)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)

    file_handler = handlers.RotatingFileHandler(filename=os.path.join(os.path.basename(__file__).replace('py', 'log')),
                                                maxBytes=1048576,
                                                backupCount=5)
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(fmt=formatter)
    logger.addHandler(file_handler)
    query = '''(
            (
                      sum(
                            avg_over_time(
                                        envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~"outbound.*ewallet-service-prod.*.ewallet-service.svc.cluster.local", cluster_id=~".*"}[10m]
                                        )
                           )

                /

                        sum(
                            avg_over_time(
                                        envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~"outbound.*ewallet-service-prod.*.ewallet-service.svc.cluster.local",  cluster_id=~".*"} [10m] offset 1w
                                        )
                            )

            ) - 1 

        ) *100 >100'''
    a = Analyze(query=query)
    a.is_sla_breached()


if __name__ == "__main__":
    main()
