import pandas as pd

class OneOpsParser:

    @staticmethod
    def parse_5xx(response):
        result = response['data']['result']
        aggregated_data = {}
        for entry in result:
            status_code = entry['metric']['statusCode']
            if status_code.startswith('5'):
                for timestamp, values in entry['values']:
                    aggregated_data[timestamp] = aggregated_data.get(timestamp, 0) + float(values)
        return pd.DataFrame(list(aggregated_data.items()), columns=['timestamp', '5xx'])

    @staticmethod
    def parses_latency(response):
        data = []
        aggregated_data = {}
        for result in response['data']['result']:
            host = result['metric']['origreq_host']
            for timestamp, latency in result['values']:
                if timestamp not in aggregated_data:
                    aggregated_data[timestamp] = {'Latency': 0, 'Host': host}
                latency = float(latency) * pow(10, 3)
                aggregated_data[timestamp]['Latency'] += latency
        for timestamp, value in aggregated_data.items():
            data.append({
                'Timestamp': int(timestamp),
                'Latency': value['Latency'],
                'Host': value['Host']
            })
        return pd.DataFrame(data, columns=['Timestamp', 'Latency', 'Host'])

    @staticmethod
    def parse_mem(response):
        result = response['data']['result']
        aggregated_data = {}
        for entry in result:
            for timestamp, values in entry['values']:
                aggregated_data[timestamp] = aggregated_data.get(timestamp, 0) + float(values)
        return pd.DataFrame(list(aggregated_data.items()), columns=['timestamp', 'mem'])

    @staticmethod
    def parse_cpu(response):
        data = []
        aggregated_data = {}
        for result in response['data']['result']:
            dc = result['metric']['dc']
            ooa = result['metric']['ooa']
            ooe = result['metric']['ooe']
            oop = result['metric']['oop']
            for timestamp, cpu in result['values']:
                if timestamp and dc not in aggregated_data:
                    aggregated_data[timestamp] = {'Cpu': 0, 'DC': dc, 'Ooa': ooa, 'Ooe': ooe, 'Oop': oop}
                cpu = float(cpu)
                aggregated_data[timestamp]['Cpu'] = cpu
                for timestamp, value in aggregated_data.items():
                    data.append({
                        'Timestamp': int(timestamp),
                        'Cpu': value['Cpu'],
                        'DC': value['DC'],
                        'Ooa': value['Ooa'],
                        'Oop': value['Oop']
                    })
        return pd.DataFrame(data, columns=['Timestamp', 'Cpu', 'DC', 'Ooa', 'Oop'])

    @staticmethod
    def parse_traffic(response):
        dcs = []
        timestamps = []
        traffic_values = []
        for result in response['data']['result']:
            dc = result['metric']['dc']
            for timestamp, traffic in result['values']:
                timestamps.append(pd.to_datetime(timestamp, unit='s'))
                dcs.append(dc)
                traffic_values.append(float(traffic))
        return pd.DataFrame({'Timestamp': timestamps, 'Data Center': dcs, 'Traffic': traffic_values})