import pandas as pd

class WCNPParser:

    @staticmethod
    def parse_latency(response):
        data = []
        for result in response['data']['result']:
            cluster_id = result['metric']['cluster_id']
            for value in result['values']:
                timestamp, latency = value
                data.append({
                    'timestamp': timestamp,
                    'latency': float(latency),
                    'cluster_id': cluster_id
                })
        return pd.DataFrame(data)

    @staticmethod
    def parse_traffic(response):
        data = []
        for result in response['data']['result']:
            cluster_id = result['metric']['cluster_id']
            for value in result['values']:
                timestamp, traffic = value
                data.append({
                    'timestamp': timestamp,
                    'traffic': float(traffic),
                    'cluster_id': cluster_id
                })
        return pd.DataFrame(data)

    @staticmethod
    def parse_5xx(response):
        data = []
        for result in response['data']['result']:
            cluster_id = result['metric']['cluster_id']
            if result['metric']['response_code_class'] != "5xx":
                continue
            for value in result['values']:
                timestamp, error = value
                data.append({
                    'timestamp': int(timestamp),
                    'error_5xx': float(error),
                    'cluster_id': cluster_id
                })
        return pd.DataFrame(data)

    @staticmethod
    def parse_cpu(response):
        dict = {}
        data = []
        for result in response['data']['result']:
            cluster_id = result['metric']['cluster_id']
            pod = result['metric']['pod']
            for value in result['values']:
                timestamp, error = value
                data.append({
                    'timestamp': int(timestamp),
                    'cpu': float(error),
                    'pod': pod
                })
            if cluster_id in dict.keys():
                dict[cluster_id].append(pod)
            else:
                dict[cluster_id] = [pod]
        return pd.DataFrame(data)

    @staticmethod
    def parse_mem(response):
        dict = {}
        data = []
        for result in response['data']['result']:
            cluster_id = result['metric']['cluster_id']
            pod = result['metric']['pod']
            for value in result['values']:
                timestamp, error = value
                data.append({
                    'timestamp': int(timestamp),
                    'mem': float(error),
                    'pod': pod
                })
            if cluster_id in dict.keys():
                dict[cluster_id].append(pod)
            else:
                dict[cluster_id] = [pod]
        return pd.DataFrame(data)