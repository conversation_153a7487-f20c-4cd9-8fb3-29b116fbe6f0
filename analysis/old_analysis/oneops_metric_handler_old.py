import re

import pandas as pd

from analysis.old_analysis.Analysis_handlers import Analyze
from analysis.Anomaly_detector import PrometheusAnomalyDetector
from functools import lru_cache

from libs.prometheus_client import Prometheus

class Oneops_metrics:


    def __init__(self):
        """
        Initializes the wcnp_metrics class with Prometheus handler, time frame, and component.

        """
        self._prometheus_handler = None

    @property
    def prometheus_handler(self):
        """
        Lazy loads and returns the Prometheus client handler.
        """
        if self._prometheus_handler is None:
            self._prometheus_handler = Prometheus()
        return self._prometheus_handler

    def _offset_handler(self,n_minutes,start_time=None,end_time=None,offset=0):

        if not start_time and not end_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(n_minutes)

        offset_sec = offset * 24 * 60 * 60
        if offset:
            start_time -= offset_sec
            end_time -= offset_sec


        return int(end_time),int(start_time)

    def _construct_query(self, metric_type, platform, env, assembly, tenant):
        template = {



            'latency': ((
                            'avg(origin:slb_response_latency_seconds:avg_rate5m{{mms_source="ef", '
                  'origcfg_host=~"(?i:({platform}.{env}.{assembly}.{tenant}.prod.us.walmart.net))",role=~".*"}}) '
                  'by(origreq_host)').format_map({"platform": platform, "env": env, "assembly": assembly,
                                               "tenant": tenant}
                                                 )),
            '5xx': ((
                        'sum(irate(odnd_http_response_code_total{{mms_source="ef", origcfg_host=~"(?i:({platform}.{env}.{assembly}.'
             '{tenant}.prod.us.walmart.net))",job=~".*"}}[5m]))'
             ' by (statusCode)').format_map({"platform": platform, "env": env, "assembly": assembly,
                                               "tenant": tenant}
                                            )),
            'traffic_status': ((
                                   'sum(irate(odnd_http_response_code_total{{mms_source="ef", origcfg_host=~"(?i:({platform}.{env}.{assembly}.'
             '{tenant}.prod.us.walmart.net))",job=~".*"}}[5m]))'
             ' by (statusCode)').format_map({"platform": platform, "env": env, "assembly": assembly,
                                               "tenant": tenant}
                                            )),
            'traffic': ((
                            'sum(rate(slb:dc_origcfg_host:odnd_origin_request_total{{mms_source="ef",'
         'origcfg_host=~"(?i:({platform}.*{env}.*.{assembly}.{tenant}.{env}.*.walmart.net))",'
         'role=~".*"}}[20m:1m])) by (dc)'
         ' / scalar(sum(rate(slb:dc_origcfg_host:odnd_origin_request_total{{mms_source="ef",'
         'origcfg_host=~"(?i:({platform}.*{env}.*.{assembly}.{tenant}.{env}.*.walmart.net))",'
         'role=~".*"}}[20m:1m]))) * 100 ').format_map({"platform": platform, "env": env, "assembly": assembly,
                                                                "tenant": tenant}
                                                      )),
            'cpu': ((
                        'avg(usage_idle{{oot="{tenant}", metricgroup="cpu",ooa="{assembly}",'
                  'ooe=~"{env}.*", oop="{platform}"}} )  by (ooa,oop,ooe,dc) ').
                 format_map({"tenant": tenant, "assembly": assembly, "env": env, "platform": platform}
                            )),
            'mem': ((
                        'avg(used_percent{{oot="{tenant}", metricgroup="mem",ooa="{assembly}",'
                 'ooe=~"{env}.*", oop="{platform}"}}) by (' \
            'ooa, oop, ooe, dc) ').
                 format_map({"tenant": tenant, "assembly": assembly, "env": env, "platform": platform}
                            ))
        }
        return template[metric_type]

    @lru_cache(maxsize=100)
    def _fetch_metrics(self, metric_type, platform, env, assembly, tenant, start_time=None, end_time=None, n_minutes=5,
                       steps=60, offset=0):

        query = self._construct_query(metric_type, platform, env, assembly, tenant)

        if not start_time:
            end_time, start_time = self._offset_handler(n_minutes=n_minutes, offset=offset)

        return self.prometheus_handler.get_query_range_data(query=query, start_time=start_time, end_time=end_time,
                                                            steps=steps)

    def detect_anomaly(self,metrics_method, parse_method, stat_column, utc_time_string, tenant, assembly,
                       env, platform, threshold=3, use_cluster_id=False, agg_id="pod",n_minutes=5):

        result = {"current_data": None, "baseline_stats": None, "anomaly": pd.DataFrame(), "error": None}

        try:
            end, start = Analyze.utc_to_epoch(utc_time_string)
            if end is None:
                end, start = Prometheus.get_last_n_minutes_epoch_times(n_minutes)

            # metrics = Oneops_metrics()
            # Fetch and parse current data
            current_response = metrics_method(metric_type=stat_column,tenant=tenant, assembly=assembly, env=env, platform=platform,
                                          n_minutes=10, start_time=start, end_time=end)
            print(current_response)
            if not current_response:
                raise Exception("Prometheus connection error")
            if not current_response['data']['result']:
                raise Exception("Empty response check namespace or app might be incorrect")


            current_data = parse_method(current_response)

            result["current_data"] = current_data

            # Collect and parse historical data
            history = []
            for i in range(1, 15):
                historical_response = metrics_method(metric_type=stat_column,tenant=tenant, assembly=assembly, env=env, platform=platform,
                                                 n_minutes=10, start_time=start, end_time=end, offset=i)
                data = parse_method(historical_response)
                # print("historical_response [0]: ", data[0])

                history.append(data[0])

            historical_data = pd.concat(history, ignore_index=True)



            # Anomaly detection
            historical_data_cleaned = Analyze.remove_outliers(historical_data, stat_column)

            baseline_stats = Analyze.calculate_baseline_stats2(historical_df=historical_data_cleaned,
                                                           stat_column=stat_column, use_cluster_id=use_cluster_id, agg_id=agg_id)
            result["baseline_stats"] = baseline_stats


            anomaly = Analyze.detect_anomalies2(baseline_stats=baseline_stats, current_df=current_data, threshold=threshold,
                                            stat_column=stat_column, use_cluster_id=use_cluster_id, agg_id=agg_id)


            result["anomaly"] = anomaly

        except Exception as e:
            result["error"] = str(e)

        return result


    def anomaly_5xx(self,utc_time_string, tenant, assembly, env, platform):
        anomaly = self.detect_anomaly(self._fetch_metrics, Analyze.parse_oneops_5xx, '5xx', utc_time_string, tenant, assembly, env, platform)
        # print(anomaly)
        return anomaly

    def anomaly_latency(self,utc_time_string, tenant, assembly, env, platform):

        anomaly = self.detect_anomaly(self._fetch_metrics, Analyze.parse_oneops_latency, 'latency', utc_time_string, tenant,
                                      assembly, env, platform, use_cluster_id=True, agg_id="host")

        anomaly = PrometheusAnomalyDetector()

        return anomaly

    def anomaly_mem(self, utc_time_string, tenant, assembly, env, platform):
        result = self.detect_anomaly(self._fetch_metrics, Analyze.parse_oneops_mem, 'mem', utc_time_string, tenant, assembly, env, platform, use_cluster_id=True)
        return result

    def anomaly_cpu(self, utc_time_string, tenant, assembly, env, platform):
        result = self.detect_anomaly(self._fetch_metrics, Analyze.parse_oneops_cpu, 'cpu', utc_time_string, tenant, assembly, env, platform, use_cluster_id=True)
        return result

    def anomaly_traffic(self, utc_time_string, tenant, assembly, env, platform):
        result = self.detect_anomaly(self._fetch_metrics, Analyze.parse_oneops_traffic, 'traffic', utc_time_string, tenant, assembly, env, platform, use_cluster_id=True)
        return result


if __name__ == "__main__":
    metrics = Oneops_metrics()
    traffic = metrics._fetch_metrics(metric_type="traffic", tenant='mexicoecomm', assembly='samsoms',
                                env='prod', platform='imsapp')
    #
    # print(traffic)


    print(metrics.anomaly_traffic(utc_time_string="", tenant='mexicoecomm', assembly='samsoms', env='prod',
                        platform='imsapp'))