import pandas as pd
from adtk.data import validate_series
from adtk.visualization import plot
from adtk.detector import SeasonalAD, AutoregressionAD
import matplotlib.pyplot as plt
from analysis.old_analysis.Analysis_handlers import Analyze
from analysis.wcnp_metric_handler import WcnpMetrics
plt.style.use('ggplot')
metrics = WcnpMetrics()
# Load current data
res = metrics._fetch_metrics("traffic", "mx-glass", "mexico-journey-prod",n_minutes=18000)

df_current = Analyze.parse_data_traffic(res)[0]
# df_current = pd.read_csv('current_data.csv')

# Convert epoch timestamps to datetime
# Replace 's' or 'ms' with the correct unit ('s' for seconds, 'ms' for milliseconds)
df_current['timestamp'] = pd.to_datetime(df_current['timestamp'], unit='s')

# Ensure 'traffic' is numeric
df_current['traffic'] = pd.to_numeric(df_current['traffic'], errors='coerce')

# Drop missing values
df_current.dropna(subset=['traffic'], inplace=True)

# Set timestamp as index
df_current.set_index('timestamp', inplace=True)


def detect_anomalies(ts, cluster_id):
    ts = validate_series(ts)
    ts = ts.resample('T').mean()
    ts = ts.interpolate()
    ts_smooth = ts.rolling(window=3, center=True).mean()
    ts_smooth = ts_smooth.fillna(method='bfill').fillna(method='ffill')
    seasonal_ad = SeasonalAD()
    autoreg_ad = AutoregressionAD(n_steps=1, step_size=1, c=3.0)
    seasonal_ad.fit(ts_smooth)
    autoreg_ad.fit(ts_smooth)
    anomalies_seasonal = seasonal_ad.detect(ts_smooth)
    anomalies_autoreg = autoreg_ad.detect(ts_smooth)
    anomalies = anomalies_seasonal | anomalies_autoreg
    plt.figure(figsize=(12, 6))
    plot(ts_smooth, anomaly=anomalies, anomaly_color='red', anomaly_tag='Anomaly')
    plt.title(f'Anomaly Detection for Cluster {cluster_id}')
    plt.xlabel('Timestamp')
    plt.ylabel('Traffic')
    plt.show()
    return anomalies

from adtk.detector import ThresholdAD

# def detect_anomalies(ts, cluster_id):
#     ts = validate_series(ts)
#     ts = ts.resample('H').mean()
#     ts = ts.interpolate()
#     threshold_ad = ThresholdAD(high=upper_threshold, low=lower_threshold)
#     anomalies = threshold_ad.detect(ts)
#     plt.figure(figsize=(12, 6))
#     plot(ts_smooth, anomaly=anomalies, anomaly_color='red', anomaly_tag='Anomaly')
#     plt.title(f'Anomaly Detection for Cluster {cluster_id}')
#     plt.xlabel('Timestamp')
#     plt.ylabel('Traffic')
#     plt.show()
#     return anomalies

def detect_anomalies_zscore(ts, cluster_id):
    ts = validate_series(ts)
    ts = ts.resample('min').mean()
    ts = ts.interpolate()
    ts_smooth = ts.rolling(window=3, center=True).mean()
    ts_smooth = ts_smooth.fillna(method='bfill').fillna(method='ffill')
    ts_mean = ts_smooth.mean()
    print(ts_mean)
    ts_std = ts_smooth.std()
    print(ts_std)
    z_scores = (ts_smooth - ts_mean) / ts_std
    print(z_scores)
    anomalies = z_scores.abs() > 3  # Threshold of 3 standard deviations
    # plt.figure(figsize=(12, 6))
    # plot(ts_smooth, anomaly=anomalies, anomaly_color='red', anomaly_tag='Anomaly')
    # plt.title(f'Anomaly Detection for Cluster {cluster_id}')
    # plt.xlabel('Timestamp')
    # plt.ylabel('Traffic')
    # plt.show()
    return anomalies



# Process current data
cluster_ids = df_current['cluster_id'].unique()

anomaly_results = {}

for cluster_id in cluster_ids:
    print(f"Processing Cluster {cluster_id}")
    cluster_data = df_current[df_current['cluster_id'] == cluster_id]['traffic']
    anomalies = detect_anomalies_zscore(cluster_data, cluster_id)
    anomaly_results[cluster_id] = anomalies

# Collect anomalies into DataFrame
anomalies_df = pd.DataFrame()

for cluster_id, anomalies in anomaly_results.items():
    temp_df = anomalies.rename(f'anomaly_{cluster_id}')
    anomalies_df = pd.concat([anomalies_df, temp_df], axis=1)

# Save anomalies to CSV
anomalies_df.to_csv('anomalies_detected_current_data.csv')
