import pandas as pd
import matplotlib.pyplot as plt
from statsmodels.tsa.statespace.sarimax import SARIMAX
from wcnp_metric_handler import WcnpMetrics
from datetime import datetime, timedelta
from analysis.old_analysis.Analysis_handlers import Analyze


def detect_anomalies_sarima(train_data, test_data, order=(1, 1, 1), seasonal_period=12):
    # Fit SARIMA model
    seasonal_order = (1, 1, 1, seasonal_period)
    model = SARIMAX(train_data, order=order, seasonal_order=seasonal_order)
    results = model.fit(disp=False)

    # Get predictions for test data
    predictions = results.get_forecast(steps=len(test_data))
    predicted_mean = predictions.predicted_mean
    conf_int = predictions.conf_int()

    # Reindex conf_int to match test_data index
    conf_int = conf_int.set_index(test_data.index)

    # Detect anomalies
    anomalies = test_data[(test_data < conf_int.iloc[:, 0]) | (test_data > conf_int.iloc[:, 1])]

    return anomalies, predicted_mean, conf_int

def plot_anomalies(train_data, test_data, predicted_mean, conf_int, anomalies, cluster_id):
    plt.figure(figsize=(14, 7))
    plt.plot(train_data.index, train_data, label='Training Data')
    plt.plot(test_data.index, test_data, label='Test Data')
    plt.plot(test_data.index, predicted_mean, color='orange', label='Predicted Mean')
    plt.fill_between(test_data.index, conf_int.iloc[:, 0], conf_int.iloc[:, 1], color='gray', alpha=0.2, label='Confidence Interval')
    plt.scatter(anomalies.index, anomalies, color='red', label='Anomalies')
    plt.title(f'Anomalies for Cluster ID: {cluster_id}')
    plt.legend()
    plt.show()

if __name__ == "__main__":
    metrics = WcnpMetrics()
    
    # Calculate start and end time for the last 10 days
    end_time = datetime.now()
    start_time = end_time - timedelta(days=2)

    # Convert to epoch time
    end_time_epoch = int(end_time.timestamp())
    start_time_epoch = int(start_time.timestamp())
    
    # Fetch traffic data for the last 10 days
    traffic_data_10_days = metrics._fetch_metrics("traffic", "mx-glass", "mexico-journey-prod", start_time=start_time_epoch, end_time=end_time_epoch)
    
    # Convert to pandas DataFrame
    df_10_days = Analyze.parse_data_traffic(traffic_data_10_days)[0]
    
    # Assuming 'value' column contains the traffic data
    
    # Fetch current 5-minute traffic data

    # traffic_data_5_min = metrics._fetch_metrics("traffic", "mx-glass", "mexico-journey-prod")
    
    # # Convert to pandas DataFrame
    # df_5_min = Analyze.parse_data_traffic(traffic_data_5_min)[0]
    
    # for cluster_id, group in df_10_days.groupby('cluster_id'):
    #     train_data = group['traffic']
        
    #     # Get the corresponding test data for the same cluster_id
    #     test_data = df_5_min[df_5_min['cluster_id'] == cluster_id]['traffic']
        
    #     if not test_data.empty:
    #         # Detect anomalies using SARIMA
    #         anomalies, predicted_mean, conf_int = detect_anomalies_sarima(train_data, test_data)
            
    #         print(f"Anomalies detected for cluster_id {cluster_id}:")
    #         print(anomalies)
            
    #         # Plot the anomalies
    #         plot_anomalies(train_data, test_data, predicted_mean, conf_int, anomalies, cluster_id)

    traffic_data_5_min = metrics._fetch_metrics("traffic", "mx-glass", "mexico-journey-prod")

# Convert to pandas DataFrame
    df_5_min = Analyze.parse_data_traffic(traffic_data_5_min)[0]

    for cluster_id, group in df_10_days.groupby('cluster_id'):
        train_data = group.set_index('timestamp')['traffic']
        
        # Get the corresponding test data for the same cluster_id
        test_data = df_5_min[df_5_min['cluster_id'] == cluster_id].set_index('timestamp')['traffic']
        print(cluster_id)
        print(test_data)
        if not test_data.empty:
            # Detect anomalies using SARIMA
            anomalies, predicted_mean, conf_int = detect_anomalies_sarima(train_data, test_data)
            
            print(f"Anomalies detected for cluster_id {cluster_id}:")
            print(anomalies)
            
            # Plot the anomalies
            plot_anomalies(train_data, test_data, predicted_mean, conf_int, anomalies, cluster_id)