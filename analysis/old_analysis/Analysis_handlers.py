import numpy as np
from datetime import datetime
import pandas as pd
from libs.prometheus_client import Prometheus

class Analyze:



    @staticmethod
    def parse_oneops_5xx(response):

        result = response['data']['result']
        aggregated_data = {}
        for entry in result:

            status_code = entry['metric']['statusCode']
            if status_code.startswith('5'):

                for timestamp, values in entry['values']:
                    aggregated_data[timestamp] = aggregated_data.get(timestamp, 0) + float(values)

        df = pd.DataFrame(list(aggregated_data.items()), columns=['timestamp', '5xx'])
        return df

    @staticmethod
    def parse_oneops_latency(response):
        data = []
        aggregated_data = {}

        # Iterate through the response data
        for result in response['data']['result']:
            host = result['metric']['origreq_host']
            for timestamp, latency in result['values']:
                # Ensure timestamp is a key in the dictionary
                if timestamp not in aggregated_data:
                    aggregated_data[timestamp] = {'Latency': 0, 'Host': host}

                # Update latency and host for the timestamp
                latency = float(latency) * pow(10, 3)
                aggregated_data[timestamp]['Latency'] += latency

        # Convert the aggregated data to a list of dictionaries
        for timestamp, value in aggregated_data.items():
            data.append({
                'Timestamp': int(timestamp),
                'Latency': value['Latency'],
                'Host': value['Host']
            })

        # Create a DataFrame from the list of dictionaries
        df = pd.DataFrame(data, columns=['Timestamp', 'Latency', 'Host'])

        return df

    @staticmethod
    def parse_oneops_mem(response):

        result = response['data']['result']
        aggregated_data = {}
        for entry in result:
            for timestamp, values in entry['values']:
                aggregated_data[timestamp] = aggregated_data.get(timestamp, 0) + float(values)

        df = pd.DataFrame(list(aggregated_data.items()), columns=['timestamp', 'mem'])

        return df

    @staticmethod
    def parse_oneops_cpu(response):
        data = []
        aggregated_data = {}

        # Iterate through the response data
        for result in response['data']['result']:

            dc = result['metric']['dc']
            ooa = result['metric']['ooa']
            ooe = result['metric']['ooe']
            oop = result['metric']['oop']
            for timestamp, cpu in result['values']:
                # Ensure timestamp is a key in the dictionary
                if timestamp and dc not in aggregated_data:
                    aggregated_data[timestamp] = {'Cpu': 0, 'DC': dc, 'Ooa': ooa, 'Ooe': ooe, 'Oop': oop}

                # Update cpu for the timestamp
                cpu = float(cpu)
                aggregated_data[timestamp]['Cpu'] = cpu

                # Convert the aggregated data to a list of dictionaries
                for timestamp, value in aggregated_data.items():
                    data.append({
                        'Timestamp': int(timestamp),
                        'Cpu': value['Cpu'],
                        'DC': value['DC'],
                        'Ooa': value['Ooa'],
                        'Oop': value['Oop']
                    })

        # Create a DataFrame from the list of dictionaries
        df = pd.DataFrame(data, columns=['Timestamp', 'Cpu', 'DC', 'Ooa','Oop'])

        return df

    @staticmethod
    def parse_oneops_traffic(response):
        dcs = []
        timestamps =[]
        traffic_values = []

        # Iterate through the response data
        for result in response['data']['result']:
            dc = result['metric']['dc']
            for timestamp, traffic in result['values']:
                timestamps.append(pd.to_datetime(timestamp, unit='s'))
                dcs.append(dc)
                traffic_values.append(float(traffic))

        # Creating a DataFrame
        df = pd.DataFrame({'Timestamp': timestamps, 'Data Center': dcs, 'Traffic': traffic_values})

        # Displaying the DataFrame
        return df

    @staticmethod
    def log_transform(data):
        # Apply log transformation, adding a small constant to avoid log(0)
        return np.log(data + 1)
    
    @staticmethod
    def inverse_log_transform(data):
        # Apply inverse log transformation
        return np.exp(data) - 1
    
    @staticmethod
    def _history_offset_handler(n_minutes, end_time=None):

        if not end_time:
            end_time, _ = Prometheus.get_last_n_minutes_epoch_times(n_minutes)

        start_time = end_time - 60 * 60*2

        return end_time, start_time

    @staticmethod
    def calculate_baseline_stats2(historical_df, stat_column, use_cluster_id=True,agg_id=None):
        if use_cluster_id and agg_id in historical_df.columns:
            # Group by cluster_id and calculate stats
            baseline_stats = historical_df.groupby(agg_id)[stat_column].agg(['mean', 'std']).reset_index()
        else:
            # Calculate stats for the entire dataset
            baseline_stats = historical_df[stat_column].agg(['mean', 'std']).to_frame().transpose().reset_index(
                drop=True)
        return baseline_stats

    @staticmethod
    def detect_anomalies2(current_df, stat_column, baseline_stats, threshold=3, use_cluster_id=True,agg_id=None):

        if use_cluster_id and agg_id in current_df.columns:
            # Merge with baseline statistics using cluster_id
            merged_df = pd.merge(current_df, baseline_stats, on=agg_id, how='left')
        else:
            # Replicate baseline stats across all rows
            replicated_baseline = pd.concat([baseline_stats] * len(current_df), ignore_index=True)

            merged_df = pd.concat([current_df.reset_index(drop=True), replicated_baseline], axis=1)
        # Calculate Z-scores
        merged_df['z_score'] = (merged_df[stat_column] - merged_df['mean']) / merged_df['std']
        # Identify anomalies
        anomalies = merged_df[abs(merged_df['z_score']) > threshold]
        if use_cluster_id and agg_id in current_df.columns:
            return anomalies[['timestamp', stat_column, agg_id]]
        else:
            return anomalies[['timestamp', stat_column]]
        
    @staticmethod
    def detect_tailed_anomalies(current_df, stat_column, baseline_stats, threshold=3, use_cluster_id=True, agg_id=None, tail='right'):
        if use_cluster_id and agg_id in current_df.columns:
            # Merge with baseline statistics using cluster_id
            merged_df = pd.merge(current_df, baseline_stats, on=agg_id, how='left')
        else:
            # Replicate baseline stats across all rows
            replicated_baseline = pd.concat([baseline_stats] * len(current_df), ignore_index=True)
            merged_df = pd.concat([current_df.reset_index(drop=True), replicated_baseline], axis=1)

        # Calculate Z-scores
        merged_df['z_score'] = (merged_df[stat_column] - merged_df['mean']) / merged_df['std']
        merged_df.to_csv('merged.csv', index=False)
        # Identify anomalies based on the specified tail
        if tail == 'right':
            anomalies = merged_df[merged_df['z_score'] > threshold]
        elif tail == 'left':
            anomalies = merged_df[merged_df['z_score'] < -threshold]
        else:  # 'both'
            anomalies = merged_df[abs(merged_df['z_score']) > threshold]
        print
        if use_cluster_id and agg_id in current_df.columns:
            return anomalies[['timestamp', stat_column, agg_id]]
        else:
            return anomalies[['timestamp', stat_column]]

    @staticmethod
    def utc_to_epoch(utc_time_string,duration=5):

        if isinstance(utc_time_string,tuple):
            start,end = utc_time_string
        else:
            if not utc_time_string:
                return None,None

            utc_time = datetime.strptime(utc_time_string, "%Y-%m-%dT%H:%M:%SZ")
            epoc_time = int((utc_time - datetime(1970, 1, 1)).total_seconds())
            end = epoc_time + duration*60
            start = epoc_time - duration*60
        return end, start

    @staticmethod
    def remove_outliers(df, column_name):
        # Calculate Q1, Q3 and IQR
        Q1 = df[column_name].quantile(0.01)
        Q3 = df[column_name].quantile(0.99)
        IQR = Q3 - Q1
        # Calculate the outlier thresholds
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        # Filter out the outliers
        filtered_df = df[(df[column_name] >= lower_bound) & (df[column_name] <= upper_bound)]
        return filtered_df

    @staticmethod
    def fetch_and_parse_data(fetch_func, parse_func, app_id, namespace, start, end, offset=None):
        data = fetch_func(app_id=app_id, namespace=namespace, start_time=int(start), end_time=int(end), offset=offset)
        return parse_func(data)

    @staticmethod
    def collect_historical_data(fetch_and_parse, app_id, namespace, start, end, weeks=14):
        history = []
        for i in range(1, weeks + 1):
            data = fetch_and_parse(app_id, namespace, start, end, offset=i)
            history.append(data)
        return pd.concat(history, ignore_index=True)

    @staticmethod
    def perform_anomaly_detection(analyze_class, namespace, app_id, utc_time_string, fetch_func, parse_func, metric,
                                  threshold=3):

        end, start = Analyze.utc_to_epoch(utc_time_string)
        # Fetch and parse current data
        current_data =  Analyze.fetch_and_parse_data(fetch_func, parse_func, app_id, namespace, start, end)
        # Collect historical data
        historic_data = Analyze.collect_historical_data(lambda app_id, namespace, start, end, offset=None:
                                                Analyze.fetch_and_parse_data(fetch_func, parse_func,
                                                                     app_id, namespace, start, end, offset),
                                                app_id, namespace, start, end)
        # Remove outliers
        historic_data = analyze_class.remove_outliers(historic_data, metric)
        # Calculate baseline
        base = analyze_class.calculate_baseline_stats(historic_data, metric)
        # Detect anomalies
        anomaly = analyze_class.detect_anomalies(baseline_stats=base, current_df=current_data,
                                                 threshold=threshold, stat_column=metric)
        return anomaly
    

    @staticmethod
    def collect_history(metric,fetch_data_func, parse_data_func, namespace, app_id, start, end, n_minutes, days):
        history = []
        for i in range(1, days + 1):
            data = parse_data_func(
                fetch_data_func(metric_type=metric, app_id=app_id, namespace=namespace, offset=i, start_time=start,
                                end_time=end, n_minutes=n_minutes)
            )
            history.append(data)
        historic_data = pd.concat(history, ignore_index=True)
        return historic_data
    
    # @staticmethod
    # def detect_anomaly(fetch_data_func, parse_data_func, remove_outliers_func, calculate_baseline_stats_func,
    #                detect_anomalies_func, namespace, app_id, utc_time_string, metric, agg_id, threshold=3,
    #                n_minutes=5):
    #     result = {"current_data": None, "baseline_stats": None, "anomaly": [], "error": None}
    #     try:
    #         end, start = Analyze.utc_to_epoch(utc_time_string)
    #         if end is None:
    #             end, start = Prometheus.get_last_n_minutes_epoch_times(n_minutes)

    #         response = fetch_data_func(metric_type=metric, app_id=app_id, namespace=namespace, start_time=start,
    #                                 end_time=end,
    #                                 n_minutes=n_minutes)

    #         if not response:
    #             raise Exception("Prometheus connection error")
    #         if not response['data']['result']:
    #             raise Exception("Empty response check namespace or app might be incorrect")

    #         current_data = parse_data_func(response)[0]
    #         result["current_data"] = current_data.to_dict(orient='records')  # Convert to JSON-compatible format

    #         end, start = self._history_offset_handler(n_minutes=n_minutes)
    #         historic_data = Analyze.collect_history(metric, fetch_data_func, parse_data_func, namespace, app_id, start,
    #                                             end,
    #                                             n_minutes, 14)
    #         historic_data_cleaned = remove_outliers_func(historic_data, metric)
    #         if metric == "error_5xx" or metric == "non_2xx":
    #             current_data[metric] = self.log_transform(current_data[metric])
    #             historic_data_cleaned[metric] = self.log_transform(historic_data_cleaned[metric])
    #         baseline_stats = calculate_baseline_stats_func(historic_data_cleaned, metric, True, agg_id)
    #         result["baseline_stats"] = baseline_stats.to_dict(orient='records')  # Convert to JSON-compatible format
    #         anomaly = detect_anomalies_func(baseline_stats=baseline_stats, current_df=current_data, threshold=threshold,
    #                                         stat_column=metric, use_cluster_id=True, agg_id=agg_id)
    #         if len(anomaly):
    #             result['anomaly'] = anomaly.groupby('cluster_id')[['timestamp', metric]].mean().reset_index().to_dict(orient='records')
    #         else:
    #             result["anomaly"] = []

    #     except Exception as e:
    #         result["error"] = str(e)

    #     return result
    
    @staticmethod
    def detect_anomaly(fetch_data_func, parse_data_func, remove_outliers_func, calculate_baseline_stats_func,
                       detect_anomalies_func, namespace, app_id, utc_time_string, metric, agg_id, threshold=3,
                       n_minutes=5,duration=5):
        result = {"current_data": None, "baseline_stats": None, "anomaly": [], "error": None}
        try:
            end, start = Analyze.utc_to_epoch(utc_time_string,duration=duration)
            if end is None:
                end, start = Prometheus.get_last_n_minutes_epoch_times(n_minutes)

            response = fetch_data_func(metric_type=metric, app_id=app_id, namespace=namespace, start_time=start,
                                       end_time=end, n_minutes=n_minutes)

            if not response or not response['data']['result']:
                raise ValueError("Empty response or Prometheus connection error. Check namespace or app.")

            current_data = parse_data_func(response)
            result["current_data"] = current_data.to_dict(orient='records')

            end, start = Analyze._history_offset_handler(n_minutes=n_minutes)
            historic_data = Analyze.collect_history(metric, fetch_data_func, parse_data_func, namespace, app_id, start,
                                                    end, n_minutes, 14)
            historic_data_cleaned = remove_outliers_func(historic_data, metric)

            if metric in ["error_5xx", "non_2xx"]:
                current_data[metric] = Analyze.log_transform(current_data[metric])
                historic_data_cleaned[metric] = Analyze.log_transform(historic_data_cleaned[metric])

            baseline_stats = calculate_baseline_stats_func(historic_data_cleaned, metric, True, agg_id)
            result["baseline_stats"] = baseline_stats.to_dict(orient='records')

            anomaly = detect_anomalies_func(baseline_stats=baseline_stats, current_df=current_data, threshold=threshold,
                                            stat_column=metric, use_cluster_id=True, agg_id=agg_id)

            if not anomaly.empty:
                if agg_id:
                    result['anomaly'] = anomaly.groupby(agg_id)[['timestamp', metric]].mean().reset_index().to_dict(orient='records')
                else:
                    result['anomaly'] = anomaly[['timestamp', metric]].to_dict(orient='records')

        except Exception as e:
            result["error"] = str(e)

        return result
