from common.alert.alerts_improved import Alerts
from analysis.wcnp_metric_handler import WcnpMetrics
from common.alert.wcnp_alerts_handler import Wcnp
from functools import lru_cache


class Analysis:
    _mapper = {
        "wcnp_latency": ['anamoly_latency', 'anamoly_cpu', 'anamoly_traffic', 'anamoly_5xx'],
        "wcnp_cpu": ['anamoly_traffic', 'anamoly_5xx', 'anamoly_cpu', 'pod_level_cpu_analysis'],
        "wcnp_memory": ['anamoly_mem'],
        "wcnp_traffic": ['anamoly_traffic']
    }
    _anomaly_detection = WcnpMetrics()
    _alerts = Alerts(n_minutes=10)
    _wcnp = Wcnp(n_minutes=10)

    @staticmethod
    @lru_cache(maxsize=100)
    def analyze_latency(namespace, app, tier, market):
        print(Analysis._wcnp.get_alerts_by_namespace_app(namespace=namespace, app_name=app, tier=tier, market=market))
        # print(self._alerts.get_all_alerts(tier=tier,market=market))
        analysis = Analysis.run_analysis("wcnp_latency", namespace, app)
        return analysis

    def analyze_cpu(self, namespace, app, tier, market):
        return self.run_analysis("wcnp_cpu", namespace, app)

    def analyze_memory(self, namespace, app, tier, market):
        return self.run_analysis("wcnp_memory", namespace, app)

    def analyze_traffic(self, namespace, app, tier, market):
        return self.run_analysis("wcnp_traffic", namespace, app)

    @staticmethod
    @lru_cache(maxsize=100)
    def run_analysis(metric_type, namespace, app, n_minutes=5):
        aggregated_results = {
            "metric_type": metric_type,
            "total_anomalies_detected": 0,
            "anomaly": {},
            "detailed_results": {},
            "errors": []
        }
        for method_name in Analysis._mapper[metric_type]:
            analysis_method = getattr(Analysis._anomaly_detection, method_name, None)
            if analysis_method:
                result = analysis_method(namespace=namespace, app_id=app, utc_time_string='', n_minutes=n_minutes)
                aggregated_results["detailed_results"][method_name] = result
                # Aggregate anomalies and errors
                if not result["anomaly"].empty:
                    aggregated_results["total_anomalies_detected"] += 1
                    aggregated_results['anomaly'][method_name] = result['anomaly']
                if result["error"]:
                    aggregated_results["errors"].append({method_name: result["error"]})
            else:
                aggregated_results["errors"].append({method_name: "Analysis method not found"})
        return aggregated_results

    # def analyze_latency(self,namespace,app,tier,market):
    #
    #     print(self._wcnp.get_alerts_by_namespace_app(namespace=namespace,app_name=app,tier=tier,market=market))
    #
    #     print(self._alerts.get_all_alerts(tier=tier,market=market))


if __name__ == "__main__":
    result = Analysis.analyze_latency(namespace="mx-glass", app="mexico-journey-prod", market='MX', tier='.*')
    print(result)
