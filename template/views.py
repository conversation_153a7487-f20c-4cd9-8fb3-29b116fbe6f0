import time, os
import settings
import logging
from libs import shell
from drf_yasg import openapi
from git_service.git import Git
from analysis.old_analysis.analyze import Analyze
from pre_processor.executor import Executor
from rest_framework.response import Response
from rest_framework.decorators import api_view
from drf_yasg.utils import swagger_auto_schema
from concurrent.futures import ThreadPoolExecutor
from template_engine.compile import serialize_to_dict, get_latest_templates
from slack_bot.slack import send_message
from template_engine.manager import TemplateManager
from template_engine import compile
from common.data_store.data_processor import Data

logger = logging.getLogger(__name__)
post_body = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        "namespace": openapi.Schema(type=openapi.TYPE_INTEGER),
        "app_name": openapi.Schema(type=openapi.TYPE_INTEGER),

    }
)

REMOTE_UPSTEAM = "***************************:Telemetry/mms-config.git"


@api_view(['GET'])
def ecv(request):
    return Response({"ok": True, "body": True, "status": True}, status=200)


# Create your views here.
@api_view(['GET'])
def get_all_templates(request):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        return Response({"ok": True, "body": compile.get_all_templates()}, status=200)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


@api_view(['GET'])
def required_template_placeholders(request, template):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        return Response({"ok": True, "body": compile.get_template_placeholder_variables(template)}, status=200)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


@swagger_auto_schema(method='post', request_body=post_body)  # get_template_variables_and_default_vars
@api_view(['GET', 'POST'])
def template_metadata(request, template):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        if request.method == 'POST':
            post_data = request.data
            _time = int(time.time())
            status, data = compile.generate_and_persist_file(post_data, template, _time,
                                                             latest_templates_check=True)
            if status:
                return Response({"ok": True, "body": data}, status=200)
            return Response({"ok": True, "body": data}, status=403)
        return Response({"ok": True, "body": compile.get_template_variables_and_default_vars(template)},
                        status=200)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


@swagger_auto_schema(method='post', request_body=post_body)  # get_template_variables_and_default_vars
@api_view(['POST'])
def bulk_template_metadata(request, template):
    tm = None
    try:
        post_data = request.data
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        force_pull = bool_convertor(request.query_params.get("force_pull", False))
        get_latest_templates(force_pull_required=force_pull)

        # data_store = Data(template)
        # data_store.template_default_data.get("child_templates")

        tm = TemplateManager(template, post_data)
        status, res = tm.execute()
        if status:
            return Response({"ok": True, "body": res}, status=200)
        return Response({"ok": True, "body": res}, status=400)

        # return generate_template_compiled_files(template, post_data)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)
    finally:
        try:
            tm.clean_up()
        except Exception as e:
            logger.error(f"Unable to cleanup output location and cloned location {e}")


# check
@swagger_auto_schema(method='post', request_body=post_body)  # get_template_variables_and_default_vars
@api_view(['POST'])
def preprocessor_template_results(request, template):
    try:
        _post_data = request.data
        get_latest_templates(force_pull_required=True)
        pre_processor = Executor(template, _post_data)
        errors = pre_processor.validate()
        data = pre_processor.execute()
        return Response({"ok": False, "body": {"execute_data": data, "errors": errors}}, status=400)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


def generate_compiled_template_data_meta_data(template, post_data):
    # Generate compiled version of template
    _time = int(time.time())
    _output_dir = os.path.join(settings.TEMPLATE_OUTPUT_DIR, str(_time))
    one_level_down_path = os.path.abspath(os.path.join(settings.BASE_DIR, '..'))
    cloned_to_location = os.path.join(one_level_down_path, "progress")
    res = shell.bash(f"mkdir -p {cloned_to_location}")

    if res.return_code != 0:
        return Response({"ok": True, "body": f"Unable to create a dir,throwing {res.stderr}"}, status=403)
    _files = compile.generate_compiled_files(post_data.get("apps_meta_data"), template,
                                             output_dir=_time)

    if len(_files) == 0:
        return Response({"ok": True, "body": "Compiled files are empty"}, status=400)
    return _time, _output_dir, _files, cloned_to_location


def handle_sla(post_data, success_files, failed_files, output_dir):
    if post_data.get("check_sla_alone"):
        alert_validator(success_files)
        shell.bash(f"rm -rf {output_dir}")
        return Response({"ok": True, "body": success_files}, status=200)


def create_alerts_errors():
    pass


def generate_template_compiled_files(template, data):
    try:
        tm = TemplateManager(template, data, execute_child_templates=False)
        status, res = tm.execute()
        if status:
            return Response({"ok": True, "body": res}, status=200)
        return Response({"ok": True, "body": res}, status=400)

    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


def validate_data(template_data, user_data):
    pass


def process_global_configs(data):
    for k, v in data.get("global_data_vars", dict()).items():
        for app in data.get("apps_meta_data"):
            if k in app.keys():
                # only add the element, if key is not p
                continue
            app.update({k: v})


def create_pull_request(upstream_repo_url, fork_repo_url, fork_repo_branch, title, upstream_repo_branch="main"):
    fork_repo, fork_org, fork_html_url = Git.get_org_and_repo_html_url_from_git_ssh_url(fork_repo_url)
    base_repo, base_org, base_html_url = Git.get_org_and_repo_html_url_from_git_ssh_url(upstream_repo_url)
    fork_repo_with_branch = f"{fork_org}:{fork_repo_branch}"
    pull_url = pull_create(base_org, base_repo, fork_repo_with_branch, title, base=upstream_repo_branch)
    return pull_url


def alert_validator(files):
    def process_alert_sla_breach(rule):
        a = Analyze(query=rule.get("expr"))
        rule["sla_breached"] = a.is_sla_breached()

    def data_formatter(data):
        sla_data = list()
        for group in data.get("groups"):
            for rule in group.get("rules"):
                sla_data.append({"alert": rule.get("alert"), "expr": rule.get("expr"),
                                 "sla_breach_data": rule.get("sla_breached")})
        return sla_data

    def process_one_entry_of_data(data_file_details):
        data = serialize_to_dict(data_file_details.get("compiled_file"))
        if data.get("groups"):
            if len(data.get("groups")) > 0:
                futures = []
                length = len(data.get("groups")[0].get("rules"))
                for group in data.get("groups"):
                    for rule in group.get("rules"):
                        pool = ThreadPoolExecutor(length)
                        futures.append(pool.submit(process_alert_sla_breach, rule))
                    for future in futures:
                        try:
                            future.result()
                        except Exception as e:
                            logger.exception(e)
        data_file_details["alerts_with_sla"] = data_formatter(data)

    _futures = []
    for alert_yaml_file in files:
        pool = ThreadPoolExecutor(len(files))
        _futures.append(pool.submit(process_one_entry_of_data, alert_yaml_file))
        process_one_entry_of_data(alert_yaml_file)
    for _future in _futures:
        try:
            _future.result()
        except Exception as e:
            logger.exception(e)

        # data = serialize_to_dict(alert_yaml_file.get("compiled_file"))
        # if data.get("groups"):
        #     if len(data.get("groups")) > 0:
        #         futures = []
        #         length = len(data.get("groups")[0].get("rules"))
        #         for group in data.get("groups"):
        #             for rule in group.get("rules"):
        #                 pool = ThreadPoolExecutor(length)
        #                 futures.append(pool.submit(process_alert_sla_breach, rule))
        #             for future in futures:
        #                 try:
        #                     future.result()
        #                 except Exception as e:
        #                     logger.exception(e)
        # alert_yaml_file["alerts_with_sla"] = data_formatter(data)


def bool_convertor(val):
    if type(val) != bool:
        val = val.lower()
        if val in ('y', 'yes', 't', 'true', 'on', '1'):
            return True
        elif val in ('n', 'no', 'f', 'false', 'off', '0'):
            return False
        return False
    else:
        return val
