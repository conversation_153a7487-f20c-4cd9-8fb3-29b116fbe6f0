__author__ = '<PERSON><PERSON> cheepati'
"""
Aggregates the files by upstream_git and fork_git
"""
import os
import time
import logging
from itertools import groupby
from collections import namedtuple
from libs.util import add_path_to_existing_git_path
from rest_framework.response import Response

import settings
from libs import shell
from git_service.git_fork import ForkGit
from template_engine import compile
from pre_processor.executor import Executor as PreExecutor
from libs.util import get_processing_time
from pre_processor import filter_processor

# Grouped the constants together
TemplateGroupedData = namedtuple('TemplateGroupedData',
                                 ["files",
                                  "upstream_git",
                                  "fork_git",
                                  "upstream_branch",
                                  "fork_branch"
                                  ])
TemplateCompiledData = namedtuple('TemplateCompiledData',
                                  ["output_dir",
                                   "cloned_to_location",
                                   "success_files",
                                   "failed_files"
                                   ])

logger = logging.getLogger(__name__)


class TemplateGroupByAppData:
    """Groups data by app data"""

    def __init__(self, files):
        self.files = files

    def get_group_by(self):
        """Group files by fork_repo and base_repo"""
        results = list()
        key = lambda x: (x['fork_repo'], x['base_repo'])
        sorted_data = sorted(self.files, key=key)
        grouped_data = {k: list(group) for k, group in groupby(sorted_data, key=key)}

        for k, v in grouped_data.items():
            fork_repo, base_repo = k
            results.append(
                TemplateGroupedData(files=v, upstream_git=base_repo, fork_git=fork_repo, upstream_branch='',
                                    fork_branch=''))
        return results


class TemplateSerializer:
    """Serializes template data"""

    def __init__(self, template, post_data, unique_reference):
        self.post_data = post_data
        self.template = template
        self.unique_reference = unique_reference
        self.output_dir = os.path.join(settings.TEMPLATE_OUTPUT_DIR, str(self.unique_reference))
        one_level_down_path = os.path.abspath(os.path.join(settings.BASE_DIR, '..'))
        self.cloned_to_location = os.path.join(one_level_down_path, "progress")
        shell.bash(f"mkdir -p {self.cloned_to_location}")

    def generate_compiled_files(self) -> TemplateCompiledData:
        """Generates compiled files"""
        _files = compile.generate_compiled_files(self.post_data.get("apps_meta_data"), self.template,
                                                 output_dir=self.unique_reference)
        success_files, failed_files = TemplateSerializer.filter_success_failed_processed_alerts(_files)
        return TemplateCompiledData(
            output_dir=self.output_dir,
            cloned_to_location=self.cloned_to_location,
            success_files=success_files,
            failed_files=failed_files
        )

    @staticmethod
    def filter_success_failed_processed_alerts(data):
        """Filters data based on status"""
        success_data = [d for d in data if d.get("status")]
        failed_data = [d for d in data if not d.get("status")]
        return success_data, failed_data


class TemplateExecutor:
    """
    For Given data, generates computed files
    """

    def __init__(self, template, post_data, unique_reference):
        self.pre_processor_errors = list()
        self.unique_reference = unique_reference
        self.template = template
        self.post_data = self._process_global_configs(post_data)

    def generate_compiled_files(self):
        """Generates files"""
        logger.info("Generation of Compiled files execution started")
        start_time = time.time()
        self.call_pre_processor()
        if self.pre_processor_errors:
            return self.pre_processor_errors, list()
        template_out_put = TemplateSerializer(self.template, self.post_data, self.unique_reference)
        template_compiled_data = template_out_put.generate_compiled_files()
        logger.info(
            f"Generation of Compiled files execution ended, took {get_processing_time(start_time, time.time())}")
        return self.pre_processor_errors, template_compiled_data

    @staticmethod
    def _process_global_configs(data):
        """Processes global configurations"""
        for k, v in data.get("global_data_vars", dict()).items():
            for app in data.get("apps_meta_data"):
                if k in app.keys():
                    continue
                app.update({k: v})
        return data

    def call_pre_processor(self):
        """Calls the pre-processor"""
        executor_defined, errors, post_data = self._call_pre_processors()
        if executor_defined:
            self.pre_processor_errors, self.post_data = errors, post_data

    def _call_pre_processors(self):
        """Calls pre-processors"""
        logger.info("Preprocessor call execution started")
        start_time = time.time()
        pre_processor = PreExecutor(self.template, self.post_data)
        errors = pre_processor.validate()
        data = pre_processor.execute()
        logger.info(f"Preprocessor call execution ended, took {get_processing_time(start_time, time.time())}")
        if pre_processor.connector is None:
            return False, errors, data
        return True, errors, data


class ChildTemplateMetadataProcessor:
    """
   User should define, child template details in Parent post data.
           {
           "global_data_vars": {
               "children": [
                   {
                       "template": "", // Not mandatory, if not defined looks for "__child_<template_name>"
                       "name": ""     // Not mandatory, creates main.yaml , if not present it creates new one
                       "consider_present_dir_files": Flase, default , it's false. If yes, it will consider only
                                                     present dir file.
                   }
               ]
               }
           }

   present dir (consider_present_dir_files: true): Even it's true, it checks all the compiled files data, if 'git_path'
                                             is same in all compiled files data, then it considers all the files.
                                                &
                                             is_sub_folder_required, should be false. Reason is it checks all the files
                                             in 'git_path', if one of the directory exits then it's not file. So failes
   """

    def __init__(self, parent_template, post_data, parent_compiled_data, fork_git, child_template_data):
        """
        parent_template: parent_template
        post_data: parent post_data
        compiled_data: parent compiled_data
        child_template_data: {"global_data_vars":{},"children":[{},{}]}. Taking about one of the child date of "children"

        """
        if child_template_data is None:
            self.child_template_data = dict()
        else:
            self.child_template_data = child_template_data
        self.parent_template = parent_template
        self.post_data = post_data
        self.parent_compiled_data = parent_compiled_data
        self.fork_git = fork_git
        self.child = self.add_default_child()

    def add_default_child(self):
        """
        Add below data
            - template
            - name, not provided then "main"
        Provides either of these
           - files
           - git_path
        """

        template = self.child_template_data.get("template", f"__child_{self.parent_template}")
        common_data = {"template": template}
        child_data = {"cloned_to_location": self.parent_compiled_data.cloned_to_location,
                      "git_org": self.fork_git.git.org,
                      "compiled_apps_meta_data": self.parent_compiled_data.success_files, **common_data}
        return child_data


class TemplateManager:
    """Generates template compiled files and manages Git tasks"""

    def __init__(self, template, data, execute_child_templates=True):
        self.required_child_templates_exec = execute_child_templates
        self.template = template
        self.data = data
        self.unique_reference = int(time.time() * 1000)
        self.branch = f"juno_{self.unique_reference}"
        self.fork_git = None
        self.parent_template_compiled_results = None
        self.pre_processor_errors = None
        self.success_child_files = list()
        self.success_parent_files = list()
        self._output_dir = os.path.join(settings.TEMPLATE_OUTPUT_DIR, str(self.unique_reference))

    def _execute_parent_template(self, template, data):
        is_parent_template_execution_success = True
        template_executor = TemplateExecutor(template, data, self.unique_reference)
        self.pre_processor_errors, self.parent_template_compiled_results = template_executor.generate_compiled_files()
        if len(self.pre_processor_errors) > 0:
            is_parent_template_execution_success = False
            return is_parent_template_execution_success, list()
        data_processor = TemplateGroupByAppData(self.parent_template_compiled_results.success_files)
        template_group_by_app_data = data_processor.get_group_by()

        return is_parent_template_execution_success, template_group_by_app_data

    def clean_up(self):
        cloned_to_location = self.parent_template_compiled_results.cloned_to_location
        output_location = self.parent_template_compiled_results.output_dir
        rm_clone_dir_command = f"rm -rf {cloned_to_location}/{self.unique_reference}"
        rm_output_dir = f"rm -rf {output_location}"
        res = shell.bash(rm_clone_dir_command)
        res3 = shell.bash(rm_output_dir)
        if res.return_code == 0:
            logger.info("Deleted clone data files location successfully")
        if res3.return_code == 0:
            logger.info("Deleted output files location successfully")

    def execute_parent_template(self):
        is_parent_template_execution_success, parent_template_group_by_app_data = self._execute_parent_template(
            self.template, self.data)
        if not is_parent_template_execution_success:
            return False, self.pre_processor_errors, list()

        compiled_failed_files_exist = TemplateManager.is_template_errors_occurred(self.pre_processor_errors,
                                                                                  self.parent_template_compiled_results.failed_files)
        if compiled_failed_files_exist:
            return False, self.pre_processor_errors, self.parent_template_compiled_results.failed_files
        # Grouping is only required, if more than one remote_upstream and fork_repos. Most of the cases it is
        # going to be only one.
        for _git_processed_data in parent_template_group_by_app_data:
            self.fork_git = ForkGit(cloned_to_location=self.parent_template_compiled_results.cloned_to_location,
                                    fork_git_url=_git_processed_data.fork_git,
                                    branch=self.branch, upsteam_git=_git_processed_data.upstream_git,
                                    upstream_repo_branch='main')
            self.success_parent_files.extend(_git_processed_data.files)
            self.fork_git.execute_init_git_tasks()
            self.fork_git.execute_git_add_or_delete_files(_git_processed_data.files)
            self.fork_git.git.commit("Added files")
        return True, list(), list()

    @staticmethod
    def is_template_errors_occurred(pre_processor_errors, compiled_failed_files):
        if len(pre_processor_errors) > 0 or len(compiled_failed_files) > 0:
            return True
        return False

    def generate_children_metadata(self):
        children = list()
        if len(self.data.get("children", list())) > 1:
            for child in self.data.get("children"):
                _child = ChildTemplateMetadataProcessor(self.template, self.data, self.parent_template_compiled_results,
                                                        self.fork_git, child)
                children.append(_child.child)
        else:
            _child = ChildTemplateMetadataProcessor(self.template, self.data, self.parent_template_compiled_results,
                                                    self.fork_git, None)
            children.append(_child.child)
        return children

    def execute_child_templates(self):
        children = self.generate_children_metadata()
        for child in children:
            _data = {**self.data}
            _data.update({"child": child})
            template_executor = TemplateExecutor(child.get("template"), _data, self.unique_reference)
            pre_processor_errors, template_compiled_results = template_executor.generate_compiled_files()
            is_error_occurred_status = TemplateManager.is_template_errors_occurred(pre_processor_errors,
                                                                                   template_compiled_results.failed_files)
            if is_error_occurred_status:
                return False, pre_processor_errors, template_compiled_results.failed_files
            self.success_child_files.extend(template_compiled_results.success_files)

        return True, list(), list()

    def execute_and_process_parent_template(self):
        is_success = False
        parent_template_processing_success, pre_processor_errors, failed_compile_files = self.execute_parent_template()

        if not parent_template_processing_success:
            return is_success, TemplateManager.generate_pre_process_errors_compiled_errors(pre_processor_errors,
                                                                                           failed_compile_files,
                                                                                           is_patent=True)
        success_file_count_zero, error = TemplateManager.is_success_file_count_zero(self.success_parent_files)
        if success_file_count_zero:
            return is_success, error
        git_parent_add_rm_files_has_errors, rc_res = self.is_git_add_or_rm_errors_occurred(self.success_parent_files)
        if git_parent_add_rm_files_has_errors:
            return is_success, rc_res
        return True, dict()

    def execute_and_process_child_template(self):
        is_success = False
        child_template_processing_success, c_pre_processor_errors, c_failed_compile_files = self.execute_child_templates()

        if not child_template_processing_success:
            logger.error("child process execution is failed")
            return is_success, TemplateManager.generate_pre_process_errors_compiled_errors(c_pre_processor_errors,
                                                                                           c_failed_compile_files,
                                                                                           is_patent=False)
        # Means no child process defined.
        if len(self.success_child_files) == 0 and child_template_processing_success:
            logger.info("No child process are defined")
            return True, dict()

        success_file_count_zero, error = TemplateManager.is_success_file_count_zero(self.success_child_files,
                                                                                    is_patent=False)
        if success_file_count_zero:
            return is_success, error
        git_child_add_rm_files_has_success, child_rc_res = self.is_git_add_or_rm_errors_occurred(
            self.success_child_files,
            is_patent=False)
        if git_child_add_rm_files_has_success:
            return is_success, child_rc_res
        return True, dict()

    @staticmethod
    def generate_pre_process_errors_compiled_errors(pre_processor_errors, failed_compile_files, is_patent=True):
        type_err = "parent" if is_patent else "child"
        return {"message": f"{type_err} pre_process error or compiled_errors ",
                "errors": {"pre_process_errors": pre_processor_errors,
                           "compiled_files_error": failed_compile_files}}

    @staticmethod
    def is_success_file_count_zero(success_files, is_patent=True):
        type_err = "parent" if is_patent else "child"
        message = f"{type_err} success file count is zero "
        status = True if len(success_files) == 0 else False
        return status, {"message": message, "errors": message}

    def is_git_add_or_rm_errors_occurred(self, files, is_patent=True):
        type_err = "parent" if is_patent else "child"
        parent_git_errors = self.fork_git.get_git_errors(files)
        error = f"{type_err} git add or remove errors"
        if len(parent_git_errors.get("git_add_file_errors", list())) > 0:
            return True, {"message": error, "errors": parent_git_errors.get("git_add_file_errors")}

        if len(parent_git_errors.get("git_rm_file_errors", list())) > 0:
            return True, {"message": error, "errors": parent_git_errors.get("git_rm_file_errors")}

        return False, dict()

    def execute(self):
        is_success = False
        is_parent_execution_success, error_obj = self.execute_and_process_parent_template()
        if not is_parent_execution_success:
            return is_success, error_obj
        if self.required_child_templates_exec:
            is_child_execution_success, child_error_obj = self.execute_and_process_child_template()
            if not is_child_execution_success:
                return is_success, child_error_obj
            # Only add, if child id defined
            if len(self.success_child_files) != 0 and is_child_execution_success:
                self.fork_git.execute_git_add_or_delete_files(self.success_child_files)
                self.fork_git.git.commit("Added child files")
        self.fork_git.git.push()

        dt = self.fork_git.git.create_pull_request(upstream_org=self.fork_git.upsteam_git_details.get("org"),
                                                   upstream_repo=self.fork_git.upsteam_git_details.get("repo"),
                                                   fork_org_name=self.fork_git.git.org,
                                                   fork_branch_name=self.branch, base="main", title="juno commit")

        return True, {"pull_request": dt, "upsteam_git_details": self.fork_git.upsteam_git_details,
                      "upstream_repo_branch": self.fork_git.upstream_repo_branch,
                      "fork_repo_name": self.fork_git.local_repo_name}


if __name__ == "__main__":
    # run app in debug mode on port 5000
    from logging import handlers
    import os

    log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    log_format = '%(asctime)s %(levelname)10s: [%(filename)s:%(lineno)s - %(funcName)20s() ] %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(log_level)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)

    file_handler = handlers.RotatingFileHandler(filename=os.path.join(os.path.basename(__file__).replace('py', 'log')),
                                                maxBytes=1048576,
                                                backupCount=5)
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(fmt=formatter)
    logger.addHandler(file_handler)
    body = {
        "global_data_vars": {
            "pods_restarts_current_threshold_count": 3,
            "git_path": "international-tech/intl-sre/golden-signals/rules/production/wcnp/app-owner-alerts",
            "xmatters_group": "INTL-P13N-URP",
            "team_email": "<EMAIL>",
            "slack_channel": "ca-idc-spr-engg-kitt-notify",
            "name_space": "mx-ea-urp-service",
            "market": "mx",
            "skip_xmatters": ["gslb_health_current_threshold_pct"]
        },
        "create_pull_request": True,
        "apps_meta_data": [

            {

                "app_name": "mx-ea-urp-prod",
                "alert_team_name": "intl_sre_golden_signals"
            }
        ]
    }
    tm = TemplateManager("wcnp_alerts.yaml", body)
    print(tm.execute())