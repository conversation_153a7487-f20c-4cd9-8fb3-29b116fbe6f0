import os, time, uuid
import yaml
import settings
import logging
from git_service.git import Git
from libs import shell
from datetime import datetime
from template_engine.engine import TemplateEngine
from pre_processor import filter_processor

logger = logging.getLogger(__name__)
TEMPLATE_REPO_NAME = "sre-alert-templates"
TEMPLATE_GIT_URL = "***************************:intl-ecomm-svcs/sre-alert-templates.git"
TEMPLATES_BASE = settings.TEMPLATE_BASE_DIR
TEMPLATE_NAME_POSTFIX = settings.TEMPLATE_POSTFIX
git = Git(TEMPLATE_GIT_URL, branch="main", cloned_to_location=settings.TEMPLATE_BASE_DIR)


def force_pull():
    logger.info("Verifying changes are reflected or not")
    if not git.does_pull_required():
        logger.info("Changes are reflected")
        return
    else:
        logger.warning("Changes are not reflected")
        logger.warning("Cloning the branch")
        git.clean()
        git.clone()


def record_change_time():
    file_name = "{}/{}".format(settings.BASE_DIR, settings.CONFIG_FILE)
    shell.bash("mkdir -p {}".format(settings.BASE_DIR))
    # base_command = "touch {}".format(file_name)
    with open(file_name, "a") as f:
        f.write("{}\n".format(time.time()))


def eligible_to_pull():
    """
    Don't pull each request, do it for every 15 minutes, so maintain epoch time in file and check current time
    diff is > 15 then pull
    Returns:

    """
    try:
        file_name = "{}/{}".format(settings.BASE_DIR, settings.CONFIG_FILE)
        file_exists = os.path.isfile(file_name)
        latest_processed_time = None
        if file_exists:
            command = "tail -n 1 {}".format(file_name)
            res = shell.bash(command)
            latest_processed_time = res.stdout.rstrip("\n")
            if latest_processed_time.strip() == "":
                return True
        if not latest_processed_time:
            return True
        current_time = datetime.fromtimestamp(time.time())
        start_ts = datetime.fromtimestamp(float(latest_processed_time))
        delta = current_time - start_ts

        if delta.total_seconds() >= (15 * 60):
            return True
        return False
    except Exception as e:
        return True


def _get_changes():
    git()
    if not git.does_pull_required():
        logger.info("Not found any changes, does not require pull")
        return True
    templates_location = "{}/{}".format(settings.TEMPLATE_BASE_DIR, git.working_folder_checkout_repo_name)

    is_exits = os.path.exists(templates_location)
    if not is_exits:
        git.clone()
    else:
        pull_status = git.pull()
        # some reason git pull fails, cone it
        if not pull_status:
            shell.bash(f"rm -rf {templates_location}")
            git.clone()
    return True


def get_latest_templates(force_pull_required=False):
    if force_pull_required:
        sha_file = f"{settings.BASE_DIR}/{settings.CONFIG_FILE}"
        shell.bash(f"cat /dev/null > {sha_file}")
    try:
        status = eligible_to_pull()
        templates_location = "{}/{}".format(settings.TEMPLATE_BASE_DIR, git.working_folder_checkout_repo_name)
        template_dir_exists = os.path.isdir(templates_location)
        # Sometimes, pull is not eligible because recorded latest timestamp is false positve,
        if status or not template_dir_exists:
            logger.info("Changes pulled recently, wait for 15 minutes for changes")
            # it_repo_url, git_repo_name, working_location, branch="main"
            is_changes_pulled_successfully = _get_changes()
            # changes are pulled, so re check one more time
            if not is_changes_pulled_successfully:
                force_pull()
            record_change_time()

        return templates_location
    except Exception as e:
        logger.exception(e)


# TODO: For REST API
def get_all_templates():
    template_dir = get_latest_templates()
    template = TemplateEngine(templates_repo=template_dir)
    return [t for t in template.templates if (not t.startswith(".idea/") or not t.startswith(".git/"))]


# TODO: For REST API
def get_template_variables_and_default_vars(template_name):
    try:
        template_dir = get_latest_templates()
        template = TemplateEngine(templates_repo=template_dir)
        template.initialize_template(template_name)
        _template = template.env.get_template(template.template_file_name)
        try:
            compiled_data = _template.render(**dict())
            values = yaml.safe_load(compiled_data)
        except Exception as e:
            logger.exception(e)
            values = str(e)

        default_vars = template.default_template_data
        template_vars = template.template_variables
        mandatory_vars = template.required_placeholders_variables

        return {"default_vars": default_vars, "template_vars": template_vars,
                "mandatory_vars": mandatory_vars, "content": values}
    except Exception as e:
        logger.exception(e)
        return {"exception": str(e)}


def get_template_placeholder_variables(template_name):
    template_dir = get_latest_templates()
    template = TemplateEngine(templates_repo=template_dir)
    template.initialize_template(template_name)
    # all_variables, required_variables
    return template.required_placeholders_variables


def get_alert_id():
    data = uuid.uuid4().hex
    return data


def generate_and_persist_file(data, template_name, output_dir, latest_templates_check=False,
                              template_dir=None):
    """
    This is for Single output file generation only

    latest_templates_check, not required for all the time, while processing bulk data.
    template_dir, not provided we need to pull

    Cases to pull latest is:
         if latest_templates_check true and template_dir not provided
    Args:
        data:
        template_name:
        output_dir:
        latest_templates_check:
        template_dir:

    Returns:

    """
    # to avoid circular import
    from common.alert.alertsPostDataBuilder import AlertsPostDataBuilder
    output_dir = os.path.join(settings.TEMPLATE_OUTPUT_DIR, str(output_dir))
    if latest_templates_check and not template_dir:
        template_dir = get_latest_templates()
    template = TemplateEngine(templates_repo=template_dir)
    # template.env.globals['get_alert_id'] = get_alert_id
    template.initialize_template(template_name)

    if template.is_service_valid:
        _combined_data = {**template.default_template_data, **data}
        # adding unique id for each alert only if one wasn't provided
        if "alert_id" not in _combined_data:
            _combined_data.update({"alert_id": uuid.uuid4().hex})
        _combined_data.setdefault('alert_team_name',
                                  AlertsPostDataBuilder.get_team_name(_combined_data.get('git_path')))
        status, fields = TemplateEngine.validate_user_entered_data(list(_combined_data.keys()),
                                                                   template.required_placeholders_variables)
        if not status:
            # status, output_dir, data
            return False, {"message": "Provide following parameters {}".format(fields), **_combined_data}
        try:
            # New logic to handle filters
            processed_map = filter_processor.process_filters(_combined_data)
            _combined_data.update(**processed_map)
        except Exception as e:
            logger.exception(f"Error occurred while processing pre processor {e}")

        _file_name, compiled_data = template.generate_compiled_data(template_name, _combined_data)

        # If one of the alert data is missing, won't creates alert
        is_alert_data_valid, missing_key = validate_alert_data(_combined_data)
        if not is_alert_data_valid:
            _alert_file_name = f"{output_dir}/{_file_name}"
            # Below code handles, "message" parameter.
            if _combined_data.get('alert_process_details') and \
                    _combined_data.get('alert_process_details', dict()).get('message', False):
                message = _combined_data.get('alert_process_details', dict()).get('message')
            else:
                message = f"Missing one or more required_parameters {missing_key} for {_alert_file_name}"
            return False, {"message": message, **_combined_data, "compiled_file": _alert_file_name}

        status, output = TemplateEngine.create(file_name=_file_name, output_location=output_dir,
                                               compiled_data=compiled_data, data=_combined_data)
        if not status:
            return status, {**output, **_combined_data}
        return status, {**output, **_combined_data}

    else:
        logger.error("Template not found")
        return False, {"message": "Template not found"}


def get_all_files(location):
    from os import walk
    files = next(walk(location), (None, None, []))[2]
    return [os.path.join(location, _file) for _file in files]


def validate_alert_data(data):
    try:
        error_key = None
        for k, v in data.items():
            # Skip validation for filter string fields
            if k.endswith('_filter_string'):
                continue
                
            if not v:
                if (v == 0) or (v is False) or (isinstance(v, list) and len(v) == 0):
                    continue
                error_key = k
                break
        if not error_key:
            return True, None
        return False, error_key
    except Exception as e:
        return False, str(e)


def generate_compiled_files(data: list, template_name, output_dir):
    files = list()
    template_dir = get_latest_templates()
    for _data in data:
        try:
            logger.info(f"Processing {_data}")
            status, data = generate_and_persist_file(_data, template_name,
                                                     output_dir,
                                                     template_dir=template_dir)
            files.append({**data, "status": status})
        except Exception as e:
            logger.exception(f"Processing error{e}")
            # files.append({**data, "status": False,"message":f"Processing error{e}"})

    return files


def serialize_to_dict(yml_file):
    with open(yml_file, 'r') as stream:
        try:
            d = yaml.safe_load(stream)
            return d

        except yaml.YAMLError as e:
            logger.exception(e)


if __name__ == "__main__":
    # run app in debug mode on port 5000
    from logging import handlers
    import os

    log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    log_format = '%(asctime)s %(levelname)10s: [%(filename)s:%(lineno)s - %(funcName)20s() ] %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(log_level)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)

    file_handler = handlers.RotatingFileHandler(filename=os.path.join(os.path.basename(__file__).replace('py', 'log')),
                                                maxBytes=1048576,
                                                backupCount=5)
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(fmt=formatter)
    logger.addHandler(file_handler)

    #
    # print(get_all_templates())
    # # template_variables, required_placeholders, default_vars = get_template_variables_and_default_vars(
    # #     "wcnp_alerts.yaml")
    # # get_all_templates()
    # _data = {'cluster_profile': 'canada-catalog-backend-prod',
    #          'pillar_name': 'pillar_nameCA',
    #          'slack_channel': 'slack_channel',
    #          'team_email': 'team_email',
    #          'xmatters_group': 'xmatters_group',
    #          'app_name': 'app_name_my_app',
    #          'name_space': 'my_namespace'}
    #
    # template_output_location = os.path.join(settings.TEMPLATE_OUTPUT_DIR, "my")
    # generate_and_persist_file(_data, "wcnp_alerts.yaml", output_dir=template_output_location)

    # data = [{'cluster_profile': 'canada-catalog-backend-prod',
    #          'pillar_name': 'pillar_nameCA',
    #          'slack_channel': 'slack_channel',
    #          'team_email': 'team_email',
    #          'xmatters_group': 'xmatters_group',
    #          'app_name': 'app1',
    #          'namespace': 'my_namespace'},
    #         {'cluster_profile': 'canada-catalog-backend-prod',
    #          'pillar_name': 'pillar_nameCA',
    #          'slack_channel': 'slack_channel',
    #          'team_email': 'team_email',
    #          'xmatters_group': 'xmatters_group',
    #          'app_name': 'app2',
    #          'namespace': 'my_namespace'},
    #         {'cluster_profile': 'canada-catalog-backend-prod',
    #          'pillar_name': 'pillar_nameCA',
    #          'slack_channel': 'slack_channel',
    #          'team_email': 'team_email',
    #          'xmatters_group': 'xmatters_group',
    #          'app_name': 'app3',
    #          'namespace': 'my_namespace'},
    #         {'cluster_profile': 'canada-catalog-backend-prod',
    #          'pillar_name': 'pillar_nameCA',
    #          'slack_channel': 'slack_channel',
    #          'team_email': 'team_email',
    #          'xmatters_group': 'xmatters_group',
    #          'app_name': 'app4',
    #          'namespace': 'my_namespace'},
    #         {'cluster_profile': 'canada-catalog-backend-prod',
    #          'pillar_name': 'pillar_nameCA',
    #          'slack_channel': 'slack_channel',
    #          'team_email': 'team_email',
    #          'xmatters_group': 'xmatters_group',
    #          'app_name': 'app5',
    #          'namespace2': 'my_namespace'}
    #         ]
    # generate_compiled_files(data, "wcnp_alerts.yaml", output_dir="my23")
    # def alert_validator(files):
    #     for alert_yaml_file in files:
    #         data = serialize_to_dict(alert_yaml_file.get("compiled_file"))
    #         if data.get("groups"):
    #             if len(data.get("groups")) > 0:
    #                 for rule in data.get("groups")[0].get("rules"):
    #                     from analysis import analyze
    #                     a = analyze.Analyze(query=rule.get("expr"))
    #                     rule["sla_breached"] = a.is_sla_breached()
    #         alert_yaml_file["alerts_with_sla"] = data
    #
    #
    # def alert_validator2(files):
    #     def process_alert(rule):
    #         from analysis import analyze
    #         a = analyze.Analyze(query=rule.get("expr"))
    #         rule["sla_breached"] = a.is_sla_breached()
    #
    #     def data_formatter(data):
    #         sla_data = list()
    #         for group in data.get("groups"):
    #             for rule in group.get("rules"):
    #                 sla_data.append({"alert": rule.get("alert"), "expr": rule.get("expr"),
    #                                  "sla_breach_data": rule.get("sla_breached")})
    #         return sla_data
    #
    #     for alert_yaml_file in files:
    #         data = serialize_to_dict(alert_yaml_file.get("compiled_file"))
    #         if data.get("groups"):
    #             if len(data.get("groups")) > 0:
    #                 futures = []
    #                 length = len(data.get("groups")[0].get("rules"))
    #                 for group in data.get("groups"):
    #                     for rule in group.get("rules"):
    #                         from concurrent.futures import ThreadPoolExecutor
    #                         pool = ThreadPoolExecutor(length)
    #                         futures.append(pool.submit(process_alert, rule))
    #
    #                     for future in futures:
    #                         try:
    #                             future.result()
    #                         except Exception as e:
    #                             logger.exception(e)
    #         alert_yaml_file["alerts_with_sla"] = data_formatter(data)
    #
    #
    # files = [{'compiled_file': '/Users/<USER>/git/juno/output/1681263513470933000/cart-page-ca-cart-page.yaml'}]
    # alert_validator2(files)
    # # files2 = [{'compiled_file': '/Users/<USER>/git/juno/output/1681263513470933000/cart-page-ca-cart-page.yaml'}]
    # # alert_validator(files2)
    # print(files)

    # Test data with filter strings
    test_data = {
        'global_data_vars': {
            'git_path': 'international-tech/intl-sre/golden-signals/rules/production/wcnp/app-owner-alerts',
            'team_email': '<EMAIL>'
        },
        'apps_meta_data': [{
            'market': 'ca',
            'name_space': 'mx-glass',
            'app_name': 'mexico-ea-journey-prod',
            # Add required notification channels
            'slack_channel': 'intl-sre-alerts',
            'team_email': '<EMAIL>',
            'xmatters_group': 'intl-sre-oncall',
            
            # Add missing filter string
            'latency_spike_filter_string': '',  # Add the missing filter string
            
            # Existing filter inputs
            'fivexx__exclude__cluster_id': ['wus-a123', 'scus-a244'],
            'traffic_drop__include__dc': ['eus2-prod-a36'],
            'traffic_spike__exclude__like_ip': '10.20.30',
            'fivexx__exclude__namespace': 'test-namespace',
            'galend_health_check__include__like_region': 'scus',
            'cluster_ids': ['eus2-prod-a36', 'scus-prod-a77'],
            
            # Existing filter strings
            'pod_restarts_filter_string': '',
            'cpu_usage_filter_string': '',
            'memory_usage_filter_string': '',

            'fivexx_filter_string': ', cluster_id!="wus-a123|scus-a244"',
            'traffic_drop_filter_string': ', dc="eus2-prod-a36"',
            'traffic_spike_filter_string': ', ip!~".*10.20.30.*"',
            'traffic_balance_filter_string': '',
            'galend_filter_string': ', region=~".*scus.*"',
            'quota_filter_string': '',
            'isto_ratelimit_filter_string': '',
            'isto_cpu_filter_string': '',
            'isto_memory_filter_string': '',
            'mms_scrape_filter_string': ''
        }]
    }

    # Set output location
    template_output_location = os.path.join(settings.TEMPLATE_OUTPUT_DIR, "filter_test")
    
    # Create output directory if it doesn't exist
    os.makedirs(template_output_location, exist_ok=True)
    
    # Important: Add template directory path
    template_dir = os.path.join(settings.TEMPLATE_BASE_DIR, "sre-alert-templates")
    
    # Generate template with filter strings
    result = generate_and_persist_file(
        test_data['apps_meta_data'][0],    # Pass the app metadata
        "wcnp_alerts.yaml",                 # Template name
        output_dir=template_output_location,
        template_dir=template_dir           # Add this parameter
    )
    
    print("Template generation result:", result)
    print(f"Output location: {template_output_location}")
