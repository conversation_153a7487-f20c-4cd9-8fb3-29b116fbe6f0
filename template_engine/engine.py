import os, time
import settings, yaml
import logging, jinja2schema
from jinja2 import Environment, meta, FileSystemLoader, select_autoescape
from jinja2.exceptions import TemplateNotFound
from git_service.git import Git
from libs import shell
from datetime import datetime

logger = logging.getLogger(__name__)
TEMPLATE_REPO_NAME = "sre-alert-templates"
TEMPLATE_GIT_URL = "***************************:intl-ecomm-svcs/sre-alert-templates.git"
TEMPLATES_BASE = settings.TEMPLATE_BASE_DIR
TEMPLATE_NAME_POSTFIX = settings.TEMPLATE_POSTFIX
git = Git(TEMPLATE_GIT_URL, branch="main", cloned_to_location=settings.TEMPLATE_BASE_DIR)


class TemplateEngine:
    """
    Templates processor , handles creating Yaml file using template and data
    """

    def __init__(self, templates_repo, is_alert=True):

        self.is_alert = is_alert
        self.time_stamp = int(time.time() * 1000)

        self.templates_repo = templates_repo
        # self.template_location = TemplateEngine.__get_template_location()
        self.template_location = os.path.join(self.templates_repo, "templates")
        self.data_location = os.path.join(self.templates_repo, "data")

        self.env = Environment(loader=FileSystemLoader(self.template_location), autoescape=select_autoescape())
        self.ignore_placeholders = ['user', 'group', 'alert_id']
        # Below data is pulled once template is initialized
        # self.template = template_name
        self.template_file_name = None
        self.default_template_data = None
        self.is_initialized_template_gen_pre_step = False
        self.template_variables = None
        self.required_placeholders_variables = None

    def initialize_template(self, template_name, *args, **kwargs):
        """
        Should call this function before Generating any template
        Args:
            template_name:
            *args:
            **kwargs:

        Returns:

        """

        self.template_file_name = template_name
        self.default_template_data = self.get_default_data()
        self.update_ignore_placeholders(self.default_template_data.get("ignore_params", []))
        self.template_variables = self.get_template_variables()
        self.required_placeholders_variables = self.get_required_placeholders()
        self.is_initialized_template_gen_pre_step = True
        

    def generate_compiled_data(self, template_name, data):
        """
        Generates compiled version of template
        """
        # Read the file
        if not self.is_initialized_template_gen_pre_step:
            self.initialize_template(template_name)

        _file_name = f"{self.default_template_data.get('file_name')}".format_map(data)
        _file_name = _file_name.replace("_", "-")
        _file_name = _file_name.lower().replace(" ", "")
        # Get template
        _template = self.env.get_template(self.template_file_name)

        # Get default data
        logger.debug(f"Default date for template {self.template_file_name} is {self.default_template_data}")
        logger.debug(f"User provided data {data}")

        # Merging user and default data, user provided data gets higher priorityy
        if not data:
            data = dict()
        _data = {**self.default_template_data, **data}
        compiled_data = _template.render(**_data)

        return _file_name, compiled_data

    @staticmethod
    def create(file_name, compiled_data, output_location,data):
        """
        Create required experiment using respected template
        """
        from libs.util import add_path_to_existing_git_path
        # _file_name,compiled_data = self.generate_compiled_data(template_name, data)
        if data.get("keep_generated_file_in_new_folder"):
            output_location = add_path_to_existing_git_path(data,output_location)

        if not os.path.isdir(output_location):
            shell.bash(f"mkdir -p {output_location}")
        file_obj = None
        _alert_file_name = f"{output_location}/{file_name}"
        if os.path.exists(_alert_file_name):
            return False, {"message": f"file already_exists {_alert_file_name}",
                           "compiled_file": _alert_file_name}
        try:
            file_obj = open(_alert_file_name, "a")
        except FileNotFoundError as e:
            message = f"Provide valid file location, should have access {output_location}"
            logger.error(f"{message}")
            logger.exception(message)
            file_obj.close()
            return False, {"message": message, "compiled_file": _alert_file_name}
        file_obj.write(compiled_data)
        # data = yaml.dump(my, file)
        file_obj.close()
        return True, {"message": None, "compiled_file": _alert_file_name}

    def get_template_variables(self):
        """
        For a given template, provided required template

        Example: Below given template, variable are my_name, my_age
        ---
        name: {my_name}
        age: {my_age}

        """
        try:
            # get template source
            template_source = self.__get_template()
            if not template_source:
                raise Exception("Parsed template content is none")
            # Get variables from parsed content
            parsed_content = self.env.parse(template_source)
            variables = meta.find_undeclared_variables(parsed_content)
            logger.debug(f"Template variables for {self.template_file_name} are {variables}")
            return variables
        except Exception as e:
            logger.exception(e)
            raise Exception(e)

    def get_template_variables1(self):
        """
        For a given template, provided required template

        Example: Below given template, variable are my_name, my_age
        ---
        name: {my_name}
        age: {my_age}

        """
        try:
            # get template source
            template_source = self.__get_template()
            if not template_source:
                raise Exception("Parsed template content is none")
            # Get variables from parsed content
            variables = jinja2schema.infer(template_source)
            logger.info(f"Template variables for {self.template_file_name} are {variables}")
            return variables.keys()
        except Exception as e:
            logger.exception(e)

    def get_required_placeholders(self):
        """For a given Template, there many be multiple variables which user will enter, after using default data
        i.e template_variables - default data
        """
        # self.template_variables is template variables
        if not self.template_variables:
            self.template_variables = self.get_template_variables()
        result = set(self.template_variables) - set(self.default_template_data.keys())
        # ignoring ignore_placeholders
        return [placeholder for placeholder in result if placeholder not in self.ignore_placeholders]

    @staticmethod
    def validate_user_entered_data(data: list, required_data: list):
        provided_data_keys = set(data)
        required_data = set(required_data)

        intersection = provided_data_keys.intersection(required_data)
        if intersection == required_data:
            return True, None
        missing_fields = required_data - provided_data_keys
        return False, missing_fields

    @property
    def templates(self):
        """Gets all templates for a given template loca"""

        templates = self.env.loader.list_templates()
        processed_templates = filter(lambda template: not template.endswith("_vars.yaml"), templates)
        return list(processed_templates)

    def generate_yaml_from_template(self):
        pass

    @staticmethod
    def __get_template_location():
        """Gets location of the templates and also validates location is directory or not"""
        if not os.path.isdir(TEMPLATES_BASE):
            raise Exception(f"Templates not found at {TEMPLATES_BASE}")
        return TEMPLATES_BASE

    def __get_template(self):
        """the source of the template as unicode string or a ASCII bytestring"""

        # check provided template_name is present/exists in templates, else raise exception
        if self.template_file_name not in self.templates:
            raise TemplateNotFound(
                f"   Provide valid template name - {self.template_file_name}, valid are {self.templates} ")
        try:
            source, filename, uptodate = self.env.loader.get_source(self.env, self.template_file_name)
            logger.info(f"template source {filename} {uptodate}")
            return source
        except TemplateNotFound as e:
            print(e)

    def build_template_name(self, template_name):
        if self.is_alert:
            return f"{template_name}_{TEMPLATE_NAME_POSTFIX.get('alerts')}.yaml"
        else:
            return f"{template_name}_{TEMPLATE_NAME_POSTFIX.get('dashboard')}.yaml"

    # def build_default_data_template_name(self, template_name):
    #     if self.is_alert:
    #         return f"{template_name}_{TEMPLATE_NAME_POSTFIX.get('alerts')}_vars.yaml"
    #     else:
    #         return f"{template_name}_{TEMPLATE_NAME_POSTFIX.get('dashboard')}_vars.yaml"

    @staticmethod
    def current_milli_time():
        return round(time.time() * 1000)

    @property
    def is_service_valid(self):
        if self.template_file_name not in self.templates:
            logger.error(f"Provided service related template does not exits at {self.templates_repo}")
            return False
        return True

    def update_ignore_placeholders(self, ignore_placeholders):
    # Extend the list with new items
        self.ignore_placeholders.extend(ignore_placeholders)
        # Remove duplicates by converting to a set and back to a list
        self.ignore_placeholders = list(set(self.ignore_placeholders))


    def get_default_data(self):
        """
        Reads yml data as dict
        """
        values = dict()
        # if templates_default_data_yaml_file_name not in self.templates:
        #     logger.warning(f"Defaults file does not exits {templates_default_data_yaml_file_name}")
        #     return values
        try:
            _file = f"{self.data_location}/{self.template_file_name}"
            file_exists = os.path.isfile(_file)
            if not file_exists:
                logger.warning(f"{_file} file not fount ")
                return values
            values = yaml.load(open(_file), Loader=yaml.FullLoader)
        except yaml.YAMLError as exc:
            logger.exception(exc)
        return values

    def dirs(self):
        """Provides Manual"""
        return "RAAS looks for templates,data folder in the git template, templates and data folders have " \
               "oneops and wcnp sub folder. In additions to these folders data folder contains default.yml file too"
