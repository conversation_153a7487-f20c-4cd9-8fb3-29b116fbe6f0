# Generate operator based on key
def generate_operator(key):
    """Extract operator from key pattern"""
    if '__exclude__like' in key:
        return '!~'  # Negative regex match
    elif '__exclude__' in key:
        return '!='  # Exact negative match
    elif '__include__like' in key:
        return '=~'  # Positive regex match
    elif '__include__' in key:
        return '='  # Exact match
    else:
        return None


def is_empty_or_none_or_whitespace(value):
    return value is None or (isinstance(value, str) and value.strip() == "")


# Generate value based on key and value type
def generate_value(key, value):
    """Format the value based on key pattern and value type"""
    if isinstance(value, list):
        if not value or "empty" in value:
            return "empty"
    elif is_empty_or_none_or_whitespace(value):
        return "empty"
    
    # Handle like pattern
    if 'like' in key:
        if isinstance(value, list):
            gen_val = '.*|.*'.join(value)
            return f".*{gen_val}.*"
        else:
            return f".*{value}.*"
    else:
        if isinstance(value, list):
            return '|'.join(value)
        else:
            return value


def filter_required_params(config_map):
    return {
        k: v for k, v in config_map.items()
        if '__include__' in k or '__exclude__' in k
    }


def extract_label_from_key(key):
    """Extract label name from the key pattern"""
    parts = key.split('__')
    if len(parts) >= 3:
        # Handle special case for 'like_' prefix
        last_part = parts[-1]
        if last_part.startswith('like_'):
            return last_part[5:]  # Remove 'like_' prefix
        return last_part
    return None


# Step 4: Process the filters and generate tokens
def process_filters(conf):
    """Process filters using the simplified one-key format"""
    final_token = dict()
    
    # Find all filter keys (with new format)
    filter_keys = [k for k in conf if 
                  ('__include__' in k or '__exclude__' in k) and 
                  not k.endswith('_key') and not k.endswith('_value')]
    
    for key in filter_keys:
        value = conf.get(key)
        
        # Skip empty values
        if value is None or value == "empty" or (isinstance(value, list) and not value):
            continue
            
        # Extract label and operator
        label = extract_label_from_key(key)
        
        # Check if value is a list or contains pipe character (needs regex)
        is_regex_needed = isinstance(value, list) or (isinstance(value, str) and '|' in value)
        
        # Determine the appropriate operator
        if '__exclude__' in key:
            operator = '!~' if is_regex_needed else '!='
        else:  # __include__
            operator = '=~' if is_regex_needed else '='
        
        # Format the value
        if isinstance(value, list):
            # Join multiple values with '|' for regex
            processed_value = '|'.join(str(v) for v in value)
        else:
            processed_value = str(value)
            
        if processed_value == "empty":
            continue
        
        # Create the Prometheus filter expression
        final_token[key] = f'{label}{operator}"{processed_value}"'
    
    return final_token


if __name__ == "__main__":
    pass
    # Step 5: Generate and print the final result
    # result = process_filters(config)
    # print(f"kube_resourcequota{{{', '.join(result)}}}")
