class Processor:
    def __init__(self, prometheus_handler, Validator, requests, shell, data, **kwargs):
        """
        args: Always would be leaf nodes
        """
        self.prometheus_handler = prometheus_handler()
        self.data = data
        self.kwargs = kwargs
        self.requests = requests
        self.shell = shell
        self.validator = Validator(self.data)

    def execute(self, *args, **kwargs):
        for element in self.data.get("apps_meta_data"):
            clusters = self.data.get("cluster_ids", False)
            if not clusters:
                namespace = self.validator.get_attribute_value("name_space", element)
                app_name = self.validator.get_attribute_value("app_name", element)
                clusters = self.prometheus_handler.get_wcnp_all_cluster_ids(namespace,
                                                                            app_id=app_name)
                element.update({"cluster_ids": clusters})
                if self.filter_processor:
                    processed_map = filter_processor.process_filters(element)
                    element.update(**processed_map)
                # traffic_drop_exclude_cluster_ids
        return self.data

    def validate_namespace(self, *args, **kwargs):
        return self.validator.is_attribute_present("name_space")

    def validate_app_name(self, *args, **kwargs):
        return self.validator.is_attribute_present("app_name")


if __name__ == "__main__":
    # from common.teap_handler.teap_data_handler import TeapDataHandler
    # from libs.oneOps import OneOps
    #
    # teap = TeapDataHandler().teap_data
    # data = teap.get_apps_by_platform_and_tier(platform="cosmosdb", tier="one")
    # _data = data[2]
    from libs.prometheus_client import Prometheus
    from pre_processor.validator import Validator
    import requests as requests
    from libs import shell

    post_data = {
        "global_data_vars": {
            "git_path": "international-tech/intl-sre/golden-signals/rules/production/wcnp/app-owner-alerts",
            "team_email": "<EMAIL>"
        },
        "create_pull_request": True,
        "apps_meta_data": [
            {

            "name_space": "glass-mx-gateway",
            "app_name": "glass-gateway",
            "tier": "zero",
            "alert_type": "wcnp",
            "alert_owner_category": "sre",
            "market": "MX",
            "xmatters_group": "intl-sre-oncall",
            "slack_channel": "intl-sre-golden-signal-alerts",
            "team_email": "<EMAIL>",
            "traffic_drop__exclude__cluster_ids_key_value": "uswest-prod-az-034",
            "pods_restarts_current_threshold_count": "5",
            "cpu_usage_current_threshold_pct": "85",
            "memory_usage_current_threshold_pct": "85",
            "fiveXX_trend_comparing_to_one_week_ago_threshold_pct": "50",
            "fiveXX_current_threshold_count_used_for_trend": "10",
            "fiveXX_current_threshold_pct": "20",
            "traffic_spike_comparing_to_one_week_ago_threshold_pct": "75",
            "traffic_drop_comparing_to_one_week_ago_threshold_pct": "75",
            "latency_spike_comparing_to_one_week_ago_threshold_pct": "200",
            "scus_traffic_in_balance_current_threshold_pct": "25",
            "wus_traffic_in_balance_current_threshold_pct": "25",
            "eus_traffic_in_balance_current_threshold_pct": "25",
            "rate_limit_threshold_count": "0",
            "quota_limit_threshold_pct": "95",
            "gslb_health_current_threshold_pct": "75"

            }
        ]
    }
    # prometheus_handler, Validator, requests, shell, data, **kwargs
    from pre_processor import filter_processor

    # prometheus_handler, Validator, requests, shell, data, **kwargs
    # prometheus_handler, Validator, requests, shell, data
    p = Processor(Prometheus, Validator, requests, shell, post_data,
                  filter_processor=filter_processor)
    print(p.execute())
