from libs.util import add_path_to_existing_git_path


class Validator(object):
    """
    Takes create alerts post data. and provide you attribute present in post data or not
    """

    def __init__(self, data):
        self.data = data

    def get_attribute_value(self, name: str, app_meta_data_element: dict) -> str:
        return app_meta_data_element.get(name, self.data.get("global_data_vars", dict()).get(name))

    def is_attribute_present(self, attribute: str):
        if all(self.get_attribute_value(attribute, element) for element in self.data.get("apps_meta_data", [])):
            return True, attribute, None
        else:
            return False, attribute, "Not provided"

    def add_path_and_sub_dir_to_existing_git_path(self, compiled_data, base_git_path):
        return add_path_to_existing_git_path(compiled_data, base_git_path)