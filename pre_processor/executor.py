from libs.loader import Loader
import requests
import logging, os, settings
from libs import shell
from libs.prometheus_client import Prometheus
from pre_processor.validator import Validator
from pre_processor import filter_processor

logger = logging.getLogger(__name__)


class Executor:
    def __init__(self, template, post_data, **kwargs):
        self.post_data = post_data
        self.template = template
        self.kwargs = kwargs
        self.connector = self.load_preprocessor()

    def load_preprocessor(self):
        _connector = Loader(self.template, self.post_data)
        connector = _connector.load_dynamic_executor_file()
        logger.info(connector)
        # prometheus_handler, Validator, requests, shell, data
        return connector(Prometheus, Validator, requests, shell, self.post_data,
                         filter_processor=filter_processor,
                         **self.kwargs) if connector else None

    def execute(self, *args, **kwargs):
        if not self.connector:
            return kwargs
        try:
            return self.connector.execute(*args, **kwargs)
        except AttributeError:
            return kwargs

    def validate(self, *args, **kwargs):
        errors = []
        if not self.connector:
            return errors
        try:
            return self.run_validation_methods(errors, kwargs)
        except AttributeError:
            return errors

    def run_validation_methods(self, errors, kwargs):
        methods = dir(self.connector)
        for method in methods:
            if "validate_" in method:
                method_obj = getattr(self.connector, method)
                if not isinstance(getattr(type(self.connector), method, None), property):
                    status, property_name, error = method_obj(kwargs)
                    if not status:
                        errors.append({"property": property_name, "error": error})
        return errors


if __name__ == "__main__":
    from template_engine.template_handler import get_latest_templates

    _post_data = {
        "global_data_vars": {
            "min": 20,
            "max": 50,
            "namespace": "mx-single-profile"

        },
        "create_pull_request": True,
        "apps_meta_data": [
            {
                "app_id": "mx-colony-service-prod-primary"
            }

        ]
    }
    # get_latest_templates(force_pull_required=True)
    pre_processor = Executor("cron_scale_wcnp.yaml", _post_data)
    erros = pre_processor.validate()
    data = pre_processor.execute()
    print(data)
    print(erros)