class Processor:
    def __init__(self, prometheus_handler, validator, requests, data):
        """
        args: Always would be leaf nodes
        """
        self.validator = validator(data)
        self.prometheus_handler = prometheus_handler()
        self.requests = requests
        self.data = data

    def execute(self, *args, **kwargs):
        processed_apps_meta_data = list()

        for element in self.data.get("apps_meta_data"):
            clusters = self.validator.get_attribute_value("clusters", element)
            if not clusters:
                namespace = self.validator.get_attribute_value("namespace", element)
                app_name = self.validator.get_attribute_value("app_name", element)
                clusters = self.prometheus_handler.get_wcnp_all_cluster_ids(namespace,
                                                                            app_id=app_name)

            min = self.validator.get_attribute_value("min", element)
            max = self.validator.get_attribute_value("max", element)
            task_down = {**element, "count": min, "task": "down", "wcnp_clusters": clusters}
            task_up = {**element, "count": max, "task": "up", "wcnp_clusters": clusters}
            processed_apps_meta_data.append(task_down)
            processed_apps_meta_data.append(task_up)
        self.data["apps_meta_data"] = processed_apps_meta_data
        return self.data

    def validate_min(self, *args, **kwargs):
        return self.validator.is_attribute_present("min")

    def validate_max(self, *args, **kwargs):
        return self.validator.is_attribute_present("max")

    def validate_namespace(self, *args, **kwargs):
        return self.validator.is_attribute_present("namespace")

    def validate_app_id(self, *args, **kwargs):
        return self.validator.is_attribute_present("app_name")


if __name__ == "__main__":
    # from common.teap_handler.teap_data_handler import TeapDataHandler
    # from libs.oneOps import OneOps
    #
    # teap = TeapDataHandler().teap_data
    # data = teap.get_apps_by_platform_and_tier(platform="cosmosdb", tier="one")
    # _data = data[2]
    from libs.prometheus_client import Prometheus
    from pre_processor.validator import Validator
    import requests as requests

    post_data = {
        "global_data_vars": {
            "min": 20,
            "max": 50,
            "namespace": "mx-glass"

        },
        "create_pull_request": True,
        "apps_meta_data": [
            {
                "app_name": "mexico-ea-journey-prod-primary"
            },
            {
                "app_name": "mexico-journey-prod-primary"
            }

        ]
    }

    p = Processor(Prometheus, Validator, requests, post_data)
    data = p.execute()
    print()
