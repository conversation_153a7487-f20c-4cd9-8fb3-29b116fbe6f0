{"timestamp": 1697137546.9179468, "data": [{"vulcan-app-ca-prod": {"clusters": [{"pods": 15, "mm_name": "eus2-prod-a11", "namespace": "sct-vulcan-ca", "lbs": {"gslb": "vulcan-app-ca-prod.sct-vulcan-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 4, "max_cpu": 4, "min_memory": "10G", "max_memory": "10G"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 15, "max": 20}}}, "prob": {"liveness_probs": {"probe_interval": 10, "wait": 200, "time_out": 5, "uri": "/vulcan-app"}, "ready_probs": {"probe_interval": 10, "wait": 200, "time_out": 5, "uri": "/vulcan-app"}}, "git": "https://gecgithub01.walmart.com/Shipment-Tracking/vulcan", "dynatrace_enabled": true}, {"pods": 15, "mm_name": "scus-prod-a98", "namespace": "sct-vulcan-ca", "lbs": {"gslb": "vulcan-app-ca-prod.sct-vulcan-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 4, "max_cpu": 4, "min_memory": "10G", "max_memory": "10G"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 15, "max": 20}}}, "prob": {"liveness_probs": {"probe_interval": 10, "wait": 200, "time_out": 5, "uri": "/vulcan-app"}, "ready_probs": {"probe_interval": 10, "wait": 200, "time_out": 5, "uri": "/vulcan-app"}}, "git": "https://gecgithub01.walmart.com/Shipment-Tracking/vulcan", "dynatrace_enabled": true}], "status": true}}, {"order-tracking-page-prod": {"clusters": [{"pods": 10, "mm_name": "eus2-prod-aspcl09", "namespace": "ca-order-tracking-page", "lbs": {"gslb": "order-tracking-page-prod.ca-order-tracking-page.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "512Mi", "max_memory": "512Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 25}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 60, "time_out": 10, "uri": "/healthcheck"}, "ready_probs": {"probe_interval": 5, "wait": 30, "time_out": 10, "uri": "/healthcheck"}}, "git": "https://gecgithub01.walmart.com/ca-gm/fe-microapps", "dynatrace_enabled": true}, {"pods": 10, "mm_name": "scus-prod-aspcl09", "namespace": "ca-order-tracking-page", "lbs": {"gslb": "order-tracking-page-prod.ca-order-tracking-page.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "512Mi", "max_memory": "512Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 25}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 60, "time_out": 10, "uri": "/healthcheck"}, "ready_probs": {"probe_interval": 5, "wait": 30, "time_out": 10, "uri": "/healthcheck"}}, "git": "https://gecgithub01.walmart.com/ca-gm/fe-microapps", "dynatrace_enabled": true}], "status": true}}, {"fo-update-ms-prod": {"clusters": [{"pods": 6, "mm_name": "scus-prod-a34", "namespace": "intl-fms-fo-update-mx", "lbs": {"gslb": "fo-update-ms-prod.intl-fms-fo-update-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "4Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 55}, "is_cpu_based_hpa": true, "scaling": {"min": 6, "max": 6}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-order-update-ms", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=MX -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=MX -Druntime.context.environmentType=prod -Druntime.context.appName=fulfillment-order-update-ms -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Druntime.context.appVersion=0.0.1372 -Druntime.context.system.property.override.enabled=true -Dscm.server.access.enabled=true -Djsse.enableSNIExtension=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=scus.kafka.medusa.prod.walmart.com:9092 -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap", "is_g1_gc": false}}, {"pods": 1, "mm_name": "wus-prod-a15", "namespace": "intl-fms-fo-update-mx", "lbs": {"gslb": "fo-update-ms-prod.intl-fms-fo-update-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "4Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 55}, "is_cpu_based_hpa": true, "scaling": {"min": 1, "max": 1}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-order-update-ms", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=MX -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=MX -Druntime.context.environmentType=prod -Druntime.context.appName=fulfillment-order-update-ms -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Druntime.context.appVersion=0.0.1372 -Druntime.context.system.property.override.enabled=true -Dscm.server.access.enabled=true -Djsse.enableSNIExtension=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=wus.kafka.medusa.prod.walmart.com:9092", "is_g1_gc": false}}], "status": true}}, {"req-event-ms-prod": {"clusters": [{"pods": 1, "mm_name": "eus2-prod-a21", "namespace": "intl-fms-fr-events-ca", "lbs": {"gslb": "req-event-ms-prod.intl-fms-fr-events-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "6Gi", "max_memory": "6Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 55}, "is_cpu_based_hpa": true, "scaling": {"min": 1, "max": 1}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-request-event-ms", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=CA -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=CA -Druntime.context.environmentType=prod -Druntime.context.appName=fulfillment-request-event-ms -Druntime.context.appVersion=0.0.1230 -Druntime.context.system.property.override.enabled=true -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Dscm.server.access.enabled=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Djsse.enableSNIExtension=true -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=eus2.kafka.medusa.prod.walmart.com:9092", "is_g1_gc": false}}, {"pods": 5, "mm_name": "scus-prod-a67", "namespace": "intl-fms-fr-events-ca", "lbs": {"gslb": "req-event-ms-prod.intl-fms-fr-events-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "6Gi", "max_memory": "6Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 55}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 5}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-request-event-ms", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=CA -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=CA -Druntime.context.environmentType=prod -Druntime.context.appName=fulfillment-request-event-ms -Druntime.context.appVersion=0.0.1230 -Druntime.context.system.property.override.enabled=true -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Dscm.server.access.enabled=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Djsse.enableSNIExtension=true -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=scus.kafka.medusa.prod.walmart.com:9092 -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap", "is_g1_gc": false}}], "status": true}}, {"scheduler-ms-prod": {"clusters": [{"pods": 5, "mm_name": "scus-prod-a34", "namespace": "intl-fms-scheduler-mx", "lbs": {"gslb": "scheduler-ms-prod.intl-fms-scheduler-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "6Gi", "max_memory": "6Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 55}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 5}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-scheduler-ms", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=MX -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=MX -Druntime.context.environmentType=prod -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Druntime.context.appName=fulfillment-scheduler-ms -Druntime.context.appVersion=0.0.681 -Druntime.context.system.property.override.enabled=true -Dscm.server.access.enabled=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Djsse.enableSNIExtension=true -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=scus.kafka.medusa.prod.walmart.com:9092 -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap", "is_g1_gc": false}}, {"pods": 1, "mm_name": "wus-prod-a15", "namespace": "intl-fms-scheduler-mx", "lbs": {"gslb": "scheduler-ms-prod.intl-fms-scheduler-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "6Gi", "max_memory": "6Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 55}, "is_cpu_based_hpa": true, "scaling": {"min": 1, "max": 1}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-scheduler-ms", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=MX -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=MX -Druntime.context.environmentType=prod -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Druntime.context.appName=fulfillment-scheduler-ms -Druntime.context.appVersion=0.0.681 -Druntime.context.system.property.override.enabled=true -Dscm.server.access.enabled=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Djsse.enableSNIExtension=true -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=wus.kafka.medusa.prod.walmart.com:9092", "is_g1_gc": false}}], "status": true}}, {"mx-colony-service-prod-primary": {"clusters": [{"pods": 2, "mm_name": "scus-prod-a101", "namespace": "mx-single-profile", "lbs": {"gslb": "mx-colony-service-prod.mx-single-profile.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": 1, "max_cpu": 2, "min_memory": "1Gi", "max_memory": "2Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 2, "max": 6}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 120, "time_out": 10, "uri": "/health"}, "ready_probs": {"probe_interval": 10, "wait": 120, "time_out": 10, "uri": "/health"}}, "git": "https://gecgithub01.walmart.com/MexicoEcomm/mx-colony-service", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Druntime.context.system.property.override.enabled=true -Djsse.enableSNIExtension=true -Dfile.encoding=UTF8 -Dspring.profiles.active=prod -Druntime.context.environmentType=prod -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.cloud=scus -Druntime.context.environment=prod -Druntime.context.appName=mx-colony-service -Dcom.walmart.platform.config.runOnDataCenter=scus -Dcom.walmart.platform.config.appName=mx-colony-service -Dcom.walmart.platform.config.runOnEnv=prod -Dccm.configs.dir=/etc/config -Djavax.net.ssl.trustStore=/secrets/certs.truststore -Djavax.net.ssl.trustStorePassword=Walmart@12345 -Dexternal.configs.source.dir=/secrets", "is_g1_gc": false}}, {"pods": 2, "mm_name": "wus-prod-a95", "namespace": "mx-single-profile", "lbs": {"gslb": "mx-colony-service-prod.mx-single-profile.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": 1, "max_cpu": 2, "min_memory": "1Gi", "max_memory": "2Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 2, "max": 6}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 120, "time_out": 10, "uri": "/health"}, "ready_probs": {"probe_interval": 10, "wait": 120, "time_out": 10, "uri": "/health"}}, "git": "https://gecgithub01.walmart.com/MexicoEcomm/mx-colony-service", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Druntime.context.system.property.override.enabled=true -Djsse.enableSNIExtension=true -Dfile.encoding=UTF8 -Dspring.profiles.active=prod -Druntime.context.environmentType=prod -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.cloud=wus -Druntime.context.environment=prod -Druntime.context.appName=mx-colony-service -Dcom.walmart.platform.config.runOnDataCenter=wus -Dcom.walmart.platform.config.appName=mx-colony-service -Dcom.walmart.platform.config.runOnEnv=prod -Dccm.configs.dir=/etc/config -Djavax.net.ssl.trustStore=/secrets/certs.truststore -Djavax.net.ssl.trustStorePassword=Walmart@12345 -Dexternal.configs.source.dir=/secrets", "is_g1_gc": false}}], "status": true}}, {"status-upd-ms-prod": {"clusters": [{"pods": 6, "mm_name": "scus-prod-a86", "namespace": "intl-fms-status-update-mx", "lbs": {"gslb": "status-upd-ms-prod.intl-fms-status-update-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "6Gi", "max_memory": "6Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 55}, "is_cpu_based_hpa": true, "scaling": {"min": 6, "max": 6}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-status-update-ms", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Xms4000m -Xmx4000m -Dspring.profiles.active=MX -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=MX -Druntime.context.environmentType=prod -Druntime.context.appName=fulfillment-status-update-ms -Druntime.context.appVersion=0.0.3279 -Druntime.context.system.property.override.enabled=true -Dscm.server.access.enabled=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Djsse.enableSNIExtension=true -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=scus.kafka.medusa.prod.walmart.com:9092 -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap", "is_g1_gc": false}}, {"pods": 1, "mm_name": "wus-prod-a47", "namespace": "intl-fms-status-update-mx", "lbs": {"gslb": "status-upd-ms-prod.intl-fms-status-update-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "6Gi", "max_memory": "6Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 55}, "is_cpu_based_hpa": true, "scaling": {"min": 2, "max": 2}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-status-update-ms", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Xms4000m -Xmx4000m -Dspring.profiles.active=MX -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=MX -Druntime.context.environmentType=prod -Druntime.context.appName=fulfillment-status-update-ms -Druntime.context.appVersion=0.0.3279 -Druntime.context.system.property.override.enabled=true -Dscm.server.access.enabled=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Djsse.enableSNIExtension=true -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=wus.kafka.medusa.prod.walmart.com:9092", "is_g1_gc": false}}], "status": true}}, {"ca-ims-3p-offer": {"clusters": [{"pods": 1, "mm_name": "eus2-prod-a66", "namespace": "ca-ims-adaptor", "lbs": {"gslb": "ca-ims-3p-offer-prod.ca-ims-adaptor.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": 1, "max_cpu": 2, "min_memory": "2Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "custom", "target": 20000, "query": "avg_over_time(kafka_total_lag{topic=\"uber-offer-incremental-canada-events\", consumerGroup=\"ca-ims-3p-offer-prod\", namespace=\"ca-ims-adaptor\", cluster_profile=\"prod\", mms_source=\"wcnp\", job=\"kafka-lag-monitor\"}[2m])"}, "is_cpu_based_hpa": true, "scaling": {"min": 1, "max": 25}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 180, "time_out": 10, "uri": "/ca-fcap/test"}, "ready_probs": {"probe_interval": 60, "wait": 180, "time_out": 10, "uri": "/ca-fcap/test"}}, "git": "https://gecgithub01.walmart.com/ca-gm/ims-masterdata-adp", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": "-Djsse.enableSNIExtension=true  -Dspring.profiles.active=prod -Dccm.configs.dir=/etc/config -Dsecrets.home=/etc/secrets -Druntime.context.system.property.override.enabled=true -Druntime.context.appName=ca-ims-3p-offer -Druntime.context.environmentType=prod -Dcom.walmart.platform.logging.console.enabled=true -Dcom.walmart.platform.logging.file.enabled=false -Dcom.walmart.platform.metrics.forwardToRawlog=false -Dcom.walmart.platform.logging.file.path=/dev/null -Dcom.walmart.platform.metrics.file.path=/dev/null -Dcom.walmart.platform.txnmarking.file.path=/dev/null -Dcom.walmart.platform.logging.bandwidthquota=0 -Dcom.walmart.platform.metrics.log=false -Dcom.walmart.platform.txnmarking.kafka.brokerList=null -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=null -Dcom.walmart.platform.logging.network.enabled=false -Dmanagement.endpoint.prometheus.enabled=true -Dmanagement.endpoints.web.exposure.include=*", "is_g1_gc": false}}], "status": true}}, {"mbaku-avail-lookup-ca-prod-eus": {"clusters": [{"pods": 1, "mm_name": "eus2-prod-a38", "namespace": "wakanda-mbaku-api-ca", "lbs": {"gslb": "mbaku-avail-lookup-ca-prod-eus.wakanda-mbaku-api-ca.k8s.glb.us.walmart.net", "custom_gslb": "mbaku-apiavailabilitylookup-ca.wakanda.prod.mbaku.glb.us.walmart.net"}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "1Gi", "max_memory": "2Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 40}, "is_cpu_based_hpa": true, "scaling": {"min": 1, "max": 5}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 180, "time_out": 10, "uri": "/_internal/metrics/"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/_internal/metrics/"}}, "git": "https://gecgithub01.walmart.com/wakanda/panther-wakanda-deployments", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": "-Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dscm.root.dir.sandbox=/scm", "is_g1_gc": false}}], "status": true}, "mbaku-avail-lookup-ca-prod-scus": {"clusters": [{"pods": 1, "mm_name": "scus-prod-a55", "namespace": "wakanda-mbaku-api-ca", "lbs": {"gslb": "mbaku-avail-lookup-ca-prod-scus.wakanda-mbaku-api-ca.k8s.glb.us.walmart.net", "custom_gslb": "mbaku-apiavailabilitylookup-ca.wakanda.prod.mbaku.glb.us.walmart.net"}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "1Gi", "max_memory": "2Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 40}, "is_cpu_based_hpa": true, "scaling": {"min": 1, "max": 5}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 180, "time_out": 10, "uri": "/_internal/metrics/"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/_internal/metrics/"}}, "git": "https://gecgithub01.walmart.com/wakanda/panther-wakanda-deployments", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": "-Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dscm.root.dir.sandbox=/scm", "is_g1_gc": false}}], "status": true}}, {"quest-primary": {"clusters": [{"pods": 4, "mm_name": "eus2-prod-a3", "namespace": "ca-quest", "lbs": {"gslb": "quest-prod.ca-quest.k8s.glb.us.walmart.net", "custom_gslb": "ca-quest.walmart.com"}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "1000m", "max_cpu": "1000m", "min_memory": "2Gi", "max_memory": "2Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 4, "max": 4}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 60, "time_out": 10, "uri": -1}, "ready_probs": {"probe_interval": null, "wait": 60, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/IEC-SEARCH/quest", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dspring.profiles.active=prod-cld1 -Druntime.context.cloud=eus2 -Druntime.context.system.property.override.enabled=true -Djsse.enableSNIExtension=true -Dlog4j2.formatMsgNoLookups=true -Druntime.context.environmentType=prod-cld1 -Dcom.walmart.platform.logging.profile=OTEL -Dcom.walmart.platform.telemetry.otel.enabled=true -Dcom.walmart.platform.metrics.impl.type=MICROMETER -Dcom.walmart.platform.txnmarking.otel.type=OTLPgRPC -Dcom.walmart.platform.txnmarking.otel.host=trace-collector.stg-scus-1.walmart.com -Dcom.walmart.platform.txnmarking.otel.port=80 -Dscm.server.url=http://tunr.non-prod.walmart.com/scm-app/v2 -Dccm.configs.dir=/etc/config", "is_g1_gc": false}}], "status": true}, "quest": {"clusters": [{"pods": 4, "mm_name": "uscentral-prod-az-005", "namespace": "ca-quest", "lbs": {"gslb": "quest-prod.ca-quest.k8s.glb.us.walmart.net", "custom_gslb": "ca-quest.walmart.com"}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "1000m", "max_cpu": "1000m", "min_memory": "2Gi", "max_memory": "2Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 4, "max": 4}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 60, "time_out": 10, "uri": -1}, "ready_probs": {"probe_interval": null, "wait": 60, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/IEC-SEARCH/quest", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dspring.profiles.active=prod-cld2 -Druntime.context.cloud=scus -Druntime.context.system.property.override.enabled=true -Djsse.enableSNIExtension=true -Dlog4j2.formatMsgNoLookups=true -Druntime.context.environmentType=prod-cld2 -Dcom.walmart.platform.logging.profile=OTEL -Dcom.walmart.platform.telemetry.otel.enabled=true -Dcom.walmart.platform.metrics.impl.type=MICROMETER -Dcom.walmart.platform.txnmarking.otel.type=OTLPgRPC -Dcom.walmart.platform.txnmarking.otel.host=trace-collector.stg-scus-1.walmart.com -Dcom.walmart.platform.txnmarking.otel.port=80 -Dscm.server.url=http://tunr.non-prod.walmart.com/scm-app/v2 -Dccm.configs.dir=/etc/config", "is_g1_gc": false}}], "status": true}}, {"ce-smartlists-prod": {"clusters": [{"pods": 9, "mm_name": "scus-prod-a61", "namespace": "ce-smartlists-gql-mx", "lbs": {"gslb": "ce-smartlists-prod.ce-smartlists-gql-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(\n  rate(\n    kube_pod_container_status_restarts_total{\n      namespace=\"ce-smartlists-gql-mx\",\n      job=~\"ce-smartlists-prod-canary\"\n    }[1m]\n  )\n) or on() vector(0)\n", "threshold": 2}], "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "1024M", "max_memory": "1024M"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 40}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 30}}}, "prob": {"liveness_probs": {"probe_interval": 10, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}, "ready_probs": {"probe_interval": 10, "wait": 30, "time_out": 5, "uri": "/ecv-status"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/ce-smartlists", "dynatrace_enabled": true}], "status": true}, "ce-smartlists-prod-primary": {"clusters": [{"pods": 5, "mm_name": "scus-prod-a61", "namespace": "ce-smartlists-gql-mx", "lbs": {"gslb": "ce-smartlists-prod.ce-smartlists-gql-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(\n  rate(\n    kube_pod_container_status_restarts_total{\n      namespace=\"ce-smartlists-gql-mx\",\n      job=~\"ce-smartlists-prod-canary\"\n    }[1m]\n  )\n) or on() vector(0)\n", "threshold": 2}], "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "1024M", "max_memory": "1024M"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 40}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 30}}}, "prob": {"liveness_probs": {"probe_interval": 10, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}, "ready_probs": {"probe_interval": 10, "wait": 30, "time_out": 5, "uri": "/ecv-status"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/ce-smartlists", "dynatrace_enabled": true}, {"pods": 5, "mm_name": "wus-prod-a58", "namespace": "ce-smartlists-gql-mx", "lbs": {"gslb": "ce-smartlists-prod.ce-smartlists-gql-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(\n  rate(\n    kube_pod_container_status_restarts_total{\n      namespace=\"ce-smartlists-gql-mx\",\n      job=~\"ce-smartlists-prod-canary\"\n    }[1m]\n  )\n) or on() vector(0)\n", "threshold": 2}], "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "1024M", "max_memory": "1024M"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 40}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 30}}}, "prob": {"liveness_probs": {"probe_interval": 10, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}, "ready_probs": {"probe_interval": 10, "wait": 30, "time_out": 5, "uri": "/ecv-status"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/ce-smartlists", "dynatrace_enabled": true}], "status": true}}, {"wmt-subs-core-app-primary": {"clusters": [{"pods": 4, "mm_name": "eus2-prod-a47", "namespace": "ca-smart-subs", "lbs": {"gslb": "wmt-subs-core-app-prod.ca-smart-subs.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"name": "Envoy 5xx rate", "query": "sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*{{ .Release.Name }}-canary.{{ .Release.Namespace }}.svc.cluster.local\",response_code_class=~\"5.*\",telemetry_walmart_com_metric_source=\"local\"}[1m]))/sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*{{ .Release.Name }}-canary.{{ .Release.Namespace }}.svc.cluster.local\",telemetry_walmart_com_metric_source=\"local\"}[1m])) * 100 or on() vector(0)\n", "threshold": 5}, {"name": "Envoy 4xxs rate", "query": "sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*{{ .Release.Name }}-canary.{{ .Release.Namespace }}.svc.cluster.local\",response_code_class=~\"4.*\",telemetry_walmart_com_metric_source=\"local\"}[1m]))/sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*{{ .Release.Name }}-canary.{{ .Release.Namespace }}.svc.cluster.local\",telemetry_walmart_com_metric_source=\"local\"}[1m])) * 100 or on() vector(0)\n", "threshold": 5}], "defaultMetrics": false}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": 0.4, "max_cpu": 1, "min_memory": "512Mi", "max_memory": "1024Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 4, "max": 16}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 120, "time_out": 10, "uri": "/config/health"}, "ready_probs": {"probe_interval": null, "wait": 150, "time_out": 10, "uri": "/config/health"}}, "git": "https://gecgithub01.walmart.com/intl-ecomm-svcs/wmt-subs-platform", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Druntime.context.environmentType=prod -Druntime.context.environment=ca -Druntime.context.cloud=eus2-prod-a47 -Druntime.context.appName=wmt-subs-core-app -Dtenant.id=wm_ca -Dscm.secrets.override.enabled=true -Dscm.secrets.root.dir=/etc/secrets", "is_g1_gc": false}}, {"pods": 4, "mm_name": "scus-prod-a55", "namespace": "ca-smart-subs", "lbs": {"gslb": "wmt-subs-core-app-prod.ca-smart-subs.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"name": "Envoy 5xx rate", "query": "sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*{{ .Release.Name }}-canary.{{ .Release.Namespace }}.svc.cluster.local\",response_code_class=~\"5.*\",telemetry_walmart_com_metric_source=\"local\"}[1m]))/sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*{{ .Release.Name }}-canary.{{ .Release.Namespace }}.svc.cluster.local\",telemetry_walmart_com_metric_source=\"local\"}[1m])) * 100 or on() vector(0)\n", "threshold": 5}, {"name": "Envoy 4xxs rate", "query": "sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*{{ .Release.Name }}-canary.{{ .Release.Namespace }}.svc.cluster.local\",response_code_class=~\"4.*\",telemetry_walmart_com_metric_source=\"local\"}[1m]))/sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*{{ .Release.Name }}-canary.{{ .Release.Namespace }}.svc.cluster.local\",telemetry_walmart_com_metric_source=\"local\"}[1m])) * 100 or on() vector(0)\n", "threshold": 5}], "defaultMetrics": false}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": 0.4, "max_cpu": 1, "min_memory": "512Mi", "max_memory": "1024Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 4, "max": 16}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 120, "time_out": 10, "uri": "/config/health"}, "ready_probs": {"probe_interval": null, "wait": 150, "time_out": 10, "uri": "/config/health"}}, "git": "https://gecgithub01.walmart.com/intl-ecomm-svcs/wmt-subs-platform", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Druntime.context.environmentType=prod -Druntime.context.environment=ca -Druntime.context.cloud=scus-prod-a55 -Druntime.context.appName=wmt-subs-core-app -Dtenant.id=wm_ca -Dscm.secrets.override.enabled=true -Dscm.secrets.root.dir=/etc/secrets", "is_g1_gc": false}}, {"pods": 4, "mm_name": "wus-prod-a50", "namespace": "ca-smart-subs", "lbs": {"gslb": "wmt-subs-core-app-prod.ca-smart-subs.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"name": "Envoy 5xx rate", "query": "sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*{{ .Release.Name }}-canary.{{ .Release.Namespace }}.svc.cluster.local\",response_code_class=~\"5.*\",telemetry_walmart_com_metric_source=\"local\"}[1m]))/sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*{{ .Release.Name }}-canary.{{ .Release.Namespace }}.svc.cluster.local\",telemetry_walmart_com_metric_source=\"local\"}[1m])) * 100 or on() vector(0)\n", "threshold": 5}, {"name": "Envoy 4xxs rate", "query": "sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*{{ .Release.Name }}-canary.{{ .Release.Namespace }}.svc.cluster.local\",response_code_class=~\"4.*\",telemetry_walmart_com_metric_source=\"local\"}[1m]))/sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*{{ .Release.Name }}-canary.{{ .Release.Namespace }}.svc.cluster.local\",telemetry_walmart_com_metric_source=\"local\"}[1m])) * 100 or on() vector(0)\n", "threshold": 5}], "defaultMetrics": false}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": 0.4, "max_cpu": 1, "min_memory": "512Mi", "max_memory": "1024Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 4, "max": 16}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 120, "time_out": 10, "uri": "/config/health"}, "ready_probs": {"probe_interval": null, "wait": 150, "time_out": 10, "uri": "/config/health"}}, "git": "https://gecgithub01.walmart.com/intl-ecomm-svcs/wmt-subs-platform", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Druntime.context.environmentType=prod -Druntime.context.environment=ca -Druntime.context.cloud=wus-prod-a50 -Druntime.context.appName=wmt-subs-core-app -Dtenant.id=wm_ca -Dscm.secrets.override.enabled=true -Dscm.secrets.root.dir=/etc/secrets", "is_g1_gc": false}}], "status": true}}, {"order-returns-page-prod": {"clusters": [{"pods": 5, "mm_name": "eus2-prod-aspcl09", "namespace": "ca-order-returns-page", "lbs": {"gslb": "order-returns-page-prod.ca-order-returns-page.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "512Mi", "max_memory": "512Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 15}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 60, "time_out": 10, "uri": "/healthcheck"}, "ready_probs": {"probe_interval": 5, "wait": 30, "time_out": 10, "uri": "/healthcheck"}}, "git": "https://gecgithub01.walmart.com/ca-gm/fe-microapps", "dynatrace_enabled": true}, {"pods": 5, "mm_name": "scus-prod-aspcl09", "namespace": "ca-order-returns-page", "lbs": {"gslb": "order-returns-page-prod.ca-order-returns-page.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "512Mi", "max_memory": "512Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 15}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 60, "time_out": 10, "uri": "/healthcheck"}, "ready_probs": {"probe_interval": 5, "wait": 30, "time_out": 10, "uri": "/healthcheck"}}, "git": "https://gecgithub01.walmart.com/ca-gm/fe-microapps", "dynatrace_enabled": true}], "status": true}}, {"consumer-app": {"clusters": [{"pods": 5, "mm_name": "eus2-prod-aspcl08", "namespace": "ca-order-services", "lbs": {"gslb": "consumer-app-prod.ca-order-services.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 3, "max_cpu": 3, "min_memory": "3Gi", "max_memory": "3Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 50}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 10}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 150, "time_out": 10, "uri": "/orders-consumer/event-check"}, "ready_probs": {"probe_interval": 5, "wait": 90, "time_out": 10, "uri": "/orders-consumer/event-check"}}, "git": "https://gecgithub01.walmart.com/ca-gm/ca-order-services", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Druntime.context.system.property.override.enabled=true -Druntime.context.appName=ca-order-srvc-ingestion -Druntime.context.environment=prod -Druntime.context.environmentType=prod -Druntime.context.cloud=eus2 -Dlog4j2.formatMsgNoLookups=true -Djavax.net.ssl.trustStore=/secrets/os.truststore -Djsse.enableSNIExtension=true -Djava.awt.headless=true -Dscm.server.access.enabled=true -Dscm.telemetry.enabled=false -Dscm.snapshot.enabled=true -Djavax.net.ssl.trustStorePassword=`cat /secrets/truststore-cred`", "is_g1_gc": false}}, {"pods": 5, "mm_name": "scus-prod-aspcl08", "namespace": "ca-order-services", "lbs": {"gslb": "consumer-app-prod.ca-order-services.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 3, "max_cpu": 3, "min_memory": "3Gi", "max_memory": "3Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 50}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 10}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 150, "time_out": 10, "uri": "/orders-consumer/event-check"}, "ready_probs": {"probe_interval": 5, "wait": 90, "time_out": 10, "uri": "/orders-consumer/event-check"}}, "git": "https://gecgithub01.walmart.com/ca-gm/ca-order-services", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Druntime.context.system.property.override.enabled=true -Druntime.context.appName=ca-order-srvc-ingestion -Druntime.context.environment=prod -Druntime.context.environmentType=prod -Druntime.context.cloud=scus -Dlog4j2.formatMsgNoLookups=true -Djavax.net.ssl.trustStore=/secrets/os.truststore -Djsse.enableSNIExtension=true -Djava.awt.headless=true -Dscm.server.access.enabled=true -Dscm.telemetry.enabled=false -Dscm.snapshot.enabled=true -Djavax.net.ssl.trustStorePassword=`cat /secrets/truststore-cred`", "is_g1_gc": false}}], "status": true}}, {"fulfillment-metadata-ms-prod": {"clusters": [{"pods": 2, "mm_name": "scus-prod-a34", "namespace": "intl-fms-meta-mx", "lbs": {"gslb": "fulfillment-metadata-ms-prod.intl-fms-meta-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "2Gi", "max_memory": "2Gi"}, "horizontal": {"is_enabled": false, "hpa": {"criteria": "cpuPer<PERSON>", "target": 80}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/health"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/health"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-metadata-ms", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=MX -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=MX -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Druntime.context.environmentType=prod -Druntime.context.appName=fulfillment-metadata-ms -Druntime.context.appVersion=1.0.150 -Druntime.context.system.property.override.enabled=true -Dscm.server.access.enabled=true -Dreactor.schedulers.defaultPoolSize=8 -Dscm.snapshot.enabled=true -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/null -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/null -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.includeLocation=false -Dcom.walmart.platform.logging.network.enabled=false -Dcom.walmart.platform.config.appVersion=1.0.0 -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=scus.kafka.medusa.prod.walmart.com:9092 -XX:ActiveProcessorCount=4", "is_g1_gc": false}}, {"pods": 2, "mm_name": "wus-prod-a15", "namespace": "intl-fms-meta-mx", "lbs": {"gslb": "fulfillment-metadata-ms-prod.intl-fms-meta-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "2Gi", "max_memory": "2Gi"}, "horizontal": {"is_enabled": false, "hpa": {"criteria": "cpuPer<PERSON>", "target": 80}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/health"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/health"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-metadata-ms", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=MX -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=MX -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Druntime.context.environmentType=prod -Druntime.context.appName=fulfillment-metadata-ms -Druntime.context.appVersion=1.0.150 -Druntime.context.system.property.override.enabled=true -Dscm.server.access.enabled=true -Dscm.snapshot.enabled=true -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/null -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/null -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.includeLocation=false -Dcom.walmart.platform.logging.network.enabled=false -Dcom.walmart.platform.config.appVersion=1.0.0 -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=wus.kafka.medusa.prod.walmart.com:9092 -XX:ActiveProcessorCount=4", "is_g1_gc": false}}], "status": true}}, {"lookup-app-primary": {"clusters": [{"pods": 10, "mm_name": "eus2-prod-aspcl08", "namespace": "ca-order-services", "lbs": {"gslb": "lookup-app-prod.ca-order-services.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 3, "max_cpu": 3, "min_memory": "3Gi", "max_memory": "3Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 50}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 10}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 150, "time_out": 10, "uri": "/orders-lookup/event-check"}, "ready_probs": {"probe_interval": 10, "wait": 120, "time_out": 10, "uri": "/orders-lookup/event-check"}}, "git": "https://gecgithub01.walmart.com/ca-gm/ca-order-services", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Druntime.context.system.property.override.enabled=true -Druntime.context.appName=ca-order-srvc-lookup -Druntime.context.environment=prod -Druntime.context.environmentType=prod -Druntime.context.cloud=eus2 -Dlog4j2.formatMsgNoLookups=true -Djavax.net.ssl.trustStore=/secrets/os.truststore -Djsse.enableSNIExtension=true -Djava.awt.headless=true -Dscm.server.access.enabled=true -Dscm.telemetry.enabled=false -Dscm.snapshot.enabled=true -Djavax.net.ssl.trustStorePassword=`cat /secrets/truststore-cred`", "is_g1_gc": false}}, {"pods": 5, "mm_name": "scus-prod-aspcl08", "namespace": "ca-order-services", "lbs": {"gslb": "lookup-app-prod.ca-order-services.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 3, "max_cpu": 3, "min_memory": "3Gi", "max_memory": "3Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 50}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 10}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 150, "time_out": 10, "uri": "/orders-lookup/event-check"}, "ready_probs": {"probe_interval": 10, "wait": 120, "time_out": 10, "uri": "/orders-lookup/event-check"}}, "git": "https://gecgithub01.walmart.com/ca-gm/ca-order-services", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Druntime.context.system.property.override.enabled=true -Druntime.context.appName=ca-order-srvc-lookup -Druntime.context.environment=prod -Druntime.context.environmentType=prod -Druntime.context.cloud=scus -Dlog4j2.formatMsgNoLookups=true -Djavax.net.ssl.trustStore=/secrets/os.truststore -Djsse.enableSNIExtension=true -Djava.awt.headless=true -Dscm.server.access.enabled=true -Dscm.telemetry.enabled=false -Dscm.snapshot.enabled=true -Djavax.net.ssl.trustStorePassword=`cat /secrets/truststore-cred`", "is_g1_gc": false}}], "status": true}}, {"ca-list-services-prod": {"clusters": [{"pods": 3, "mm_name": "eus2-prod-a47", "namespace": "ca-list-services", "lbs": {"gslb": "ca-list-services-prod.ca-list-services.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "1200m", "max_cpu": "1200m", "min_memory": "4Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 3, "max": 30}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 180, "time_out": 10, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": 5, "wait": 180, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/intl-ecomm-svcs/user-lists", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=prod -Druntime.context.system.property.override.enabled=true -Druntime.context.environment=prod -Druntime.context.environmentType=prod -Dcom.walmart.platform.config.runOnEnv=prod -Dcom.walmart.platform.config.appName=user-lists-app -Druntime.context.appName=user-lists-app -Djsse.enableSNIExtension=true", "is_g1_gc": false}}, {"pods": 3, "mm_name": "scus-prod-a25", "namespace": "ca-list-services", "lbs": {"gslb": "ca-list-services-prod.ca-list-services.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "1200m", "max_cpu": "1200m", "min_memory": "4Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 3, "max": 30}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 180, "time_out": 10, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": 5, "wait": 180, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/intl-ecomm-svcs/user-lists", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=prod -Druntime.context.system.property.override.enabled=true -Druntime.context.environment=prod -Druntime.context.environmentType=prod -Dcom.walmart.platform.config.runOnEnv=prod -Dcom.walmart.platform.config.appName=user-lists-app -Druntime.context.appName=user-lists-app -Djsse.enableSNIExtension=true", "is_g1_gc": false}}], "status": true}}, {"badging-prod": {"clusters": [{"pods": 25, "mm_name": "eus2-prod-aspcl09", "namespace": "ca-badging", "lbs": {"gslb": "badging-prod.ca-badging.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "2Gi", "max_memory": "2Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 25, "max": 100}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 60, "time_out": 10, "uri": "/healthcheck"}, "ready_probs": {"probe_interval": 5, "wait": 30, "time_out": 10, "uri": "/healthcheck"}}, "git": "https://gecgithub01.walmart.com/ca-gm/fe-microapps", "dynatrace_enabled": true}, {"pods": 25, "mm_name": "scus-prod-aspcl09", "namespace": "ca-badging", "lbs": {"gslb": "badging-prod.ca-badging.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "2Gi", "max_memory": "2Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 25, "max": 100}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 60, "time_out": 10, "uri": "/healthcheck"}, "ready_probs": {"probe_interval": 5, "wait": 30, "time_out": 10, "uri": "/healthcheck"}}, "git": "https://gecgithub01.walmart.com/ca-gm/fe-microapps", "dynatrace_enabled": true}], "status": true}}, {"fo-crt-ms-prod": {"clusters": [{"pods": 1, "mm_name": "eus2-prod-a60", "namespace": "intl-fms-fo-create-mgr-ca", "lbs": {"gslb": "fo-crt-ms-prod.intl-fms-fo-create-mgr-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "3Gi", "max_memory": "3Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 55}, "is_cpu_based_hpa": true, "scaling": {"min": 1, "max": 1}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-order-create-ms", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=CA -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=CA -Druntime.context.environmentType=prod -Druntime.context.appName=fulfillment-order-create-ms -Druntime.context.appVersion=0.0.812 -Druntime.context.system.property.override.enabled=true -Dscm.server.access.enabled=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Djsse.enableSNIExtension=true -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.file.format=V4Json -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=eus2.kafka.medusa.prod.walmart.com:9092", "is_g1_gc": false}}, {"pods": 5, "mm_name": "scus-prod-a92", "namespace": "intl-fms-fo-create-mgr-ca", "lbs": {"gslb": "fo-crt-ms-prod.intl-fms-fo-create-mgr-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "3Gi", "max_memory": "3Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 55}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 5}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-order-create-ms", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=CA -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=CA -Druntime.context.environmentType=prod -Druntime.context.appName=fulfillment-order-create-ms -Druntime.context.appVersion=0.0.812 -Druntime.context.system.property.override.enabled=true -Dscm.server.access.enabled=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Djsse.enableSNIExtension=true -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.kafka.brokerList=null -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Dcom.walmart.platform.metrics.kafka.brokerList=scus.kafka.medusa.prod.walmart.com:9092 -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap", "is_g1_gc": false}}], "status": true}}, {"ca-search-qus-prod": {"clusters": [{"pods": 4, "mm_name": "uscentral-prod-az-022", "namespace": "ca-search-qus", "lbs": {"gslb": "ca-search-qus-prod.ca-search-qus.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "8000m", "max_cpu": "8000m", "min_memory": "20Gi", "max_memory": "20Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 4, "max": 10}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 180, "time_out": 10, "uri": "/qus/healthcheck"}, "ready_probs": {"probe_interval": 5, "wait": 375, "time_out": 10, "uri": "/qus/healthcheck"}}, "git": "https://gecgithub01.walmart.com/ca-gm/Classification-Service", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dconfig.resource=prod.conf", "is_g1_gc": false}}, {"pods": 4, "mm_name": "useast-prod-az-008", "namespace": "ca-search-qus", "lbs": {"gslb": "ca-search-qus-prod.ca-search-qus.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "8000m", "max_cpu": "8000m", "min_memory": "20Gi", "max_memory": "20Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 4, "max": 10}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 180, "time_out": 10, "uri": "/qus/healthcheck"}, "ready_probs": {"probe_interval": 5, "wait": 375, "time_out": 10, "uri": "/qus/healthcheck"}}, "git": "https://gecgithub01.walmart.com/ca-gm/Classification-Service", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dconfig.resource=prod.conf", "is_g1_gc": false}}], "status": true}}, {"list-app-prod-primary": {"clusters": [{"pods": 3, "mm_name": "eus2-prod-a8", "namespace": "mx-list-services", "lbs": {"gslb": "list-app-prod.mx-list-services.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": "800m", "max_cpu": "1000m", "min_memory": "2Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 3, "max": 4}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 150, "time_out": 10, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": 5, "wait": 150, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/intl-ecomm-svcs/user-lists", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=prod  -Druntime.context.system.property.override.enabled=true -Druntime.context.environment=prod -Druntime.context.environmentType=prod -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.system.property.override.enabled=true -Druntime.context.cloud=eus2-prod-a8 -Dcom.walmart.platform.config.appName=mx-list-app -Druntime.context.appName=mx-list-app -Djsse.enableSNIExtension=true", "is_g1_gc": false}}, {"pods": 3, "mm_name": "scus-prod-a50", "namespace": "mx-list-services", "lbs": {"gslb": "list-app-prod.mx-list-services.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": "800m", "max_cpu": "1000m", "min_memory": "2Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 3, "max": 4}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 150, "time_out": 10, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": 5, "wait": 150, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/intl-ecomm-svcs/user-lists", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=prod  -Druntime.context.system.property.override.enabled=true -Druntime.context.environment=prod -Druntime.context.environmentType=prod -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.system.property.override.enabled=true -Druntime.context.cloud=scus-prod-a50 -Dcom.walmart.platform.config.appName=mx-list-app -Druntime.context.appName=mx-list-app -Djsse.enableSNIExtension=true", "is_g1_gc": false}}, {"pods": 3, "mm_name": "uswest-prod-az-071", "namespace": "mx-list-services", "lbs": {"gslb": "list-app-prod.mx-list-services.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": "800m", "max_cpu": "1000m", "min_memory": "2Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 3, "max": 4}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 150, "time_out": 10, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": 5, "wait": 150, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/intl-ecomm-svcs/user-lists", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=prod  -Druntime.context.system.property.override.enabled=true -Druntime.context.environment=prod -Druntime.context.environmentType=prod -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.system.property.override.enabled=true -Druntime.context.cloud=uswest-prod-az-071 -Dcom.walmart.platform.config.appName=mx-list-app -Druntime.context.appName=mx-list-app -Djsse.enableSNIExtension=true", "is_g1_gc": false}}], "status": true}}, {"post-orders-prod": {"clusters": [{"pods": 3, "mm_name": "scus-prod-a63", "namespace": "ce-post-orders-gql-mx", "lbs": {"gslb": "post-orders-prod.ce-post-orders-gql-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "experiments": [{"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(\n  rate(\n    kube_pod_container_status_restarts_total{\n      namespace=\"ce-post-orders-gql-mx\",\n      job=~\"post-orders-prod-canary\"\n    }[1m]\n  )\n) or on() vector(0)\n", "threshold": 2}], "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "2048Mi", "max_memory": "2048Mi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 3, "max": 50}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}, "ready_probs": {"probe_interval": null, "wait": 30, "time_out": 10, "uri": "/.well-known/apollo/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/ce-orders", "dynatrace_enabled": false}, {"pods": 3, "mm_name": "wus-prod-a91", "namespace": "ce-post-orders-gql-mx", "lbs": {"gslb": "post-orders-prod.ce-post-orders-gql-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "experiments": [{"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(\n  rate(\n    kube_pod_container_status_restarts_total{\n      namespace=\"ce-post-orders-gql-mx\",\n      job=~\"post-orders-prod-canary\"\n    }[1m]\n  )\n) or on() vector(0)\n", "threshold": 2}], "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "2048Mi", "max_memory": "2048Mi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 3, "max": 50}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}, "ready_probs": {"probe_interval": null, "wait": 30, "time_out": 10, "uri": "/.well-known/apollo/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/ce-orders", "dynatrace_enabled": false}], "status": true}}, {"okoye-api-lookup-ca-prod": {"clusters": [{"pods": 5, "mm_name": "eus2-prod-a38", "namespace": "wakanda-okoye-ca", "lbs": {"gslb": "okoye-api-lookup-ca-prod.wakanda-okoye-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "1500m", "max_cpu": "1500m", "min_memory": "2048Mi", "max_memory": "2048Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 50}, "is_cpu_based_hpa": true, "scaling": {"min": 1, "max": 12}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 30, "time_out": 10, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": 15, "wait": 30, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/ca-gm/wakanda-canada-deployments", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": " -Djsse.enableSNIExtension=true -Dccm.configs.dir=/etc/config -Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dscm.root.dir.sandbox=/scm -Dscm.server.access.enabled=true -Dscm.snapshot.enabled=true -Dscm.sandbox.enabled=true -Dscm.server.url=http://tunr.prod.walmart.com/scm-app/v2 -Dcom.walmart.platform.metrics.impl.type=MICROMETER -Druntime.context.system.property.override.enabled=true -Dcom.walmart.platform.logging.profile=OTEL -Dcom.walmart.platform.telemetry.otel.enabled=true -Dcom.walmart.platform.txnmarking.otel.type=OTLPgRPC -Dcom.walmart.platform.txnmarking.otel.host=trace-collector.stg-scus-1.walmart.com -Dcom.walmart.platform.txnmarking.otel.port=80 -Dcom.walmart.platform.txnmarking.logging.enabled=false", "is_g1_gc": false}}, {"pods": 1, "mm_name": "scus-prod-a56", "namespace": "wakanda-okoye-ca", "lbs": {"gslb": "okoye-api-lookup-ca-prod.wakanda-okoye-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "1500m", "max_cpu": "1500m", "min_memory": "2048Mi", "max_memory": "2048Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 50}, "is_cpu_based_hpa": true, "scaling": {"min": 1, "max": 12}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 30, "time_out": 10, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": 15, "wait": 30, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/ca-gm/wakanda-canada-deployments", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": " -Djsse.enableSNIExtension=true -Dccm.configs.dir=/etc/config -Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dscm.root.dir.sandbox=/scm -Dscm.server.access.enabled=true -Dscm.snapshot.enabled=true -Dscm.sandbox.enabled=true -Dscm.server.url=http://tunr.prod.walmart.com/scm-app/v2 -Dcom.walmart.platform.metrics.impl.type=MICROMETER -Druntime.context.system.property.override.enabled=true -Dcom.walmart.platform.logging.profile=OTEL -Dcom.walmart.platform.telemetry.otel.enabled=true -Dcom.walmart.platform.txnmarking.otel.type=OTLPgRPC -Dcom.walmart.platform.txnmarking.otel.host=trace-collector.stg-scus-1.walmart.com -Dcom.walmart.platform.txnmarking.otel.port=80 -Dcom.walmart.platform.txnmarking.logging.enabled=false", "is_g1_gc": false}}], "status": true}}, {"bashenga-http-lookup-ca-prod-eus": {"clusters": [{"pods": 2, "mm_name": "eus2-prod-a38", "namespace": "wakanda-bashenga-ca", "lbs": {"gslb": "bashenga-http-lookup-ca-prod-eus.wakanda-bashenga-ca.k8s.glb.us.walmart.net", "custom_gslb": "bashenga-http-lookup-ca.wakanda.prod.mbaku.glb.us.walmart.net"}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "500m", "max_cpu": "500m", "min_memory": "800Mi", "max_memory": "800Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 50}, "is_cpu_based_hpa": true, "scaling": {"min": 3, "max": 8}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 60, "time_out": 10, "uri": "/_internal/metrics/"}, "ready_probs": {"probe_interval": 60, "wait": 60, "time_out": 10, "uri": "/_internal/metrics/"}}, "git": "https://gecgithub01.walmart.com/ca-gm/wakanda-canada-deployments", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": "-Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dscm.root.dir.sandbox=/scm", "is_g1_gc": false}}], "status": true}, "bashenga-http-lookup-ca-prod-scus": {"clusters": [{"pods": 1, "mm_name": "scus-prod-a55", "namespace": "wakanda-bashenga-ca", "lbs": {"gslb": "bashenga-http-lookup-ca-prod-scus.wakanda-bashenga-ca.k8s.glb.us.walmart.net", "custom_gslb": "bashenga-http-lookup-ca.wakanda.prod.mbaku.glb.us.walmart.net"}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "500m", "max_cpu": "500m", "min_memory": "800Mi", "max_memory": "800Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 50}, "is_cpu_based_hpa": true, "scaling": {"min": 4, "max": 8}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 60, "time_out": 10, "uri": "/_internal/metrics/"}, "ready_probs": {"probe_interval": 60, "wait": 60, "time_out": 10, "uri": "/_internal/metrics/"}}, "git": "https://gecgithub01.walmart.com/ca-gm/wakanda-canada-deployments", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": "-Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dscm.root.dir.sandbox=/scm", "is_g1_gc": false}}], "status": true}}, {"kafka-lag-monitor": {"clusters": [{"pods": 1, "mm_name": "eus2-prod-a38", "namespace": "wakanda-okoye-ca", "lbs": {"gslb": "kafka-lag-monitor-prod.wakanda-okoye-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": 4, "max_cpu": 6, "min_memory": "4096Mi", "max_memory": "6144Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 75}, "is_cpu_based_hpa": true, "scaling": {"min": 1, "max": 1}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 30, "time_out": 10, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": 15, "wait": 30, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/ca-gm/wakanda-canada-deployments", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": " -Djsse.enableSNIExtension=true -Dccm.configs.dir=/etc/config", "is_g1_gc": false}}, {"pods": 1, "mm_name": "scus-prod-a56", "namespace": "wakanda-okoye-ca", "lbs": {"gslb": "kafka-lag-monitor-prod.wakanda-okoye-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": 4, "max_cpu": 6, "min_memory": "4096Mi", "max_memory": "6144Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 75}, "is_cpu_based_hpa": true, "scaling": {"min": 1, "max": 1}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 30, "time_out": 10, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": 15, "wait": 30, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/ca-gm/wakanda-canada-deployments", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": " -Djsse.enableSNIExtension=true -Dccm.configs.dir=/etc/config", "is_g1_gc": false}}], "status": true}}, {"killmonger-api-inventory-lookup-primary": {"clusters": [{"pods": 2, "mm_name": "eus2-prod-a38", "namespace": "wakanda-km-api-ca", "lbs": {"gslb": "killmonger-api-inventory-lookup-prod.wakanda-km-api-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "1024m", "max_cpu": "1024m", "min_memory": "4096Mi", "max_memory": "4096Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "custom", "target": 25, "query": "round(sum(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*killmonger-api-inventory-lookup.*(|-primary|-canary).wakanda-km-api-ca.svc.cluster.local\", cluster_id=~\"eus2-prod-a38\"}) by (cluster_id),0.001)"}, "is_cpu_based_hpa": true, "scaling": {"min": 2, "max": 8}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 200, "time_out": 10, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": 15, "wait": 160, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/ca-gm/wakanda-canada-deployments", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": "-Dccm.configs.dir=/etc/config -XX:+UseG1GC -XX:+UseStringDeduplication -Xms3072m -Xmx3072m", "is_g1_gc": true}}, {"pods": 2, "mm_name": "scus-prod-a56", "namespace": "wakanda-km-api-ca", "lbs": {"gslb": "killmonger-api-inventory-lookup-prod.wakanda-km-api-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "1024m", "max_cpu": "1024m", "min_memory": "4096Mi", "max_memory": "4096Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "custom", "target": 25, "query": "round(sum(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*killmonger-api-inventory-lookup.*(|-primary|-canary).wakanda-km-api-ca.svc.cluster.local\", cluster_id=~\"scus-prod-a56\"}) by (cluster_id),0.001)"}, "is_cpu_based_hpa": true, "scaling": {"min": 2, "max": 8}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 200, "time_out": 10, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": 15, "wait": 160, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/ca-gm/wakanda-canada-deployments", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": "-Dccm.configs.dir=/etc/config -XX:+UseG1GC -XX:+UseStringDeduplication -Xms3072m -Xmx3072m", "is_g1_gc": true}}], "status": true}}, {"cl-notification-service-prod": {"clusters": [{"pods": 3, "mm_name": "scus-prod-a34", "namespace": "digitalwallet"}, {"pods": 3, "mm_name": "wus-prod-a16", "namespace": "digitalwallet"}], "status": true}}, {"ca-checkout-batch-prod-kafka": {"clusters": [{"pods": 1, "mm_name": "eus2-prod-aspcl08", "namespace": "ca-checkout-batch", "lbs": {"gslb": "ca-checkout-batch-prod-kafka.ca-checkout-batch.k8s.glb.us.walmart.net", "custom_gslb": "ca-checkout-batch-wcnp-prod-kafka.walmart.com"}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": "800m", "max_cpu": "1000m", "min_memory": "2Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": null, "hpa": {"criteria": null, "target": null}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 150, "time_out": 10, "uri": "/health"}, "ready_probs": {"probe_interval": 5, "wait": 120, "time_out": 10, "uri": "/health"}}, "git": "https://gecgithub01.walmart.com/ca-gm/checkout-batch", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=prod -Druntime.context.system.property.override.enabled=true -Druntime.context.environment=prod -Druntime.context.environmentType=prod -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.cloud=eus2-kafka -Dcom.walmart.platform.config.appName=checkout-batch-app -Dscm.secrets.override.enabled=true -Dscm.secrets.root.dir=/secrets -Djavax.net.ssl.trustStore=/secrets/ca-cxo.truststore -Druntime.context.appName=checkout-batch-app -Djsse.enableSNIExtension=true -Dlog4j2.formatMsgNoLookups=true    ", "is_g1_gc": false}}, {"pods": 1, "mm_name": "scus-prod-aspcl08", "namespace": "ca-checkout-batch", "lbs": {"gslb": "ca-checkout-batch-prod-kafka.ca-checkout-batch.k8s.glb.us.walmart.net", "custom_gslb": "ca-checkout-batch-wcnp-prod-kafka.walmart.com"}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": "800m", "max_cpu": "1000m", "min_memory": "2Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": null, "hpa": {"criteria": null, "target": null}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 150, "time_out": 10, "uri": "/health"}, "ready_probs": {"probe_interval": 5, "wait": 120, "time_out": 10, "uri": "/health"}}, "git": "https://gecgithub01.walmart.com/ca-gm/checkout-batch", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=prod -Druntime.context.system.property.override.enabled=true -Druntime.context.environment=prod -Druntime.context.environmentType=prod -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.cloud=scus-kafka -Dcom.walmart.platform.config.appName=checkout-batch-app -Dscm.secrets.override.enabled=true -Dscm.secrets.root.dir=/secrets -Djavax.net.ssl.trustStore=/secrets/ca-cxo.truststore -Druntime.context.appName=checkout-batch-app -Djsse.enableSNIExtension=true -Dlog4j2.formatMsgNoLookups=true    ", "is_g1_gc": false}}], "status": true}, "ca-checkout-batch-prod-primary": {"clusters": [{"pods": 8, "mm_name": "eus2-prod-aspcl08", "namespace": "ca-checkout-batch", "lbs": {"gslb": "ca-checkout-batch-prod.ca-checkout-batch.k8s.glb.us.walmart.net", "custom_gslb": "ca-checkout-batch-wcnp-prod.walmart.com"}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": "800m", "max_cpu": "1000m", "min_memory": "2Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": null, "hpa": {"criteria": null, "target": null}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 150, "time_out": 10, "uri": "/health"}, "ready_probs": {"probe_interval": 5, "wait": 120, "time_out": 10, "uri": "/health"}}, "git": "https://gecgithub01.walmart.com/ca-gm/checkout-batch", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=prod -Druntime.context.system.property.override.enabled=true -Druntime.context.environment=prod -Druntime.context.environmentType=prod -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.cloud=eus2 -Dscm.secrets.override.enabled=true -Dscm.secrets.root.dir=/secrets -Dcom.walmart.platform.config.appName=checkout-batch-app -Druntime.context.appName=checkout-batch-app -Djsse.enableSNIExtension=true -Djavax.net.ssl.trustStore=/secrets/ca-cxo.truststore -Djavax.net.ssl.keyStore=/secrets/keystore.jks -Dlog4j2.formatMsgNoLookups=true", "is_g1_gc": false}}, {"pods": 8, "mm_name": "scus-prod-aspcl08", "namespace": "ca-checkout-batch", "lbs": {"gslb": "ca-checkout-batch-prod.ca-checkout-batch.k8s.glb.us.walmart.net", "custom_gslb": "ca-checkout-batch-wcnp-prod.walmart.com"}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": "800m", "max_cpu": "1000m", "min_memory": "2Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": null, "hpa": {"criteria": null, "target": null}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 150, "time_out": 10, "uri": "/health"}, "ready_probs": {"probe_interval": 5, "wait": 120, "time_out": 10, "uri": "/health"}}, "git": "https://gecgithub01.walmart.com/ca-gm/checkout-batch", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=prod -Druntime.context.system.property.override.enabled=true -Druntime.context.environment=prod -Druntime.context.environmentType=prod -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.cloud=scus -Dscm.secrets.override.enabled=true -Dscm.secrets.root.dir=/secrets -Dcom.walmart.platform.config.appName=checkout-batch-app -Druntime.context.appName=checkout-batch-app -Djsse.enableSNIExtension=true -Djavax.net.ssl.trustStore=/secrets/ca-cxo.truststore -Djavax.net.ssl.keyStore=/secrets/keystore.jks -Dlog4j2.formatMsgNoLookups=true", "is_g1_gc": false}}], "status": true}}, {"ca-ims-inventory-adjustments": {"clusters": [{"pods": 1, "mm_name": "eus2-prod-a66", "namespace": "ca-ims-adaptor", "lbs": {"gslb": "ca-ims-inventory-adjustments-prod.ca-ims-adaptor.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "4Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": null, "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 180, "time_out": 10, "uri": "/ca-fcap/test"}, "ready_probs": {"probe_interval": 60, "wait": 180, "time_out": 10, "uri": "/ca-fcap/test"}}, "git": "https://gecgithub01.walmart.com/ca-gm/ims-inv-inbound-adp", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": "-Djsse.enableSNIExtension=true -Dspring.profiles.active=prod -DappName=calgary_adj -Dccm.configs.dir=/etc/config -Dsecrets.home=/etc/secrets -Druntime.context.system.property.override.enabled=true -Druntime.context.appName=ca-ims-inventory-adjustments -Druntime.context.environmentType=prod -Dcom.walmart.platform.logging.console.enabled=true -Dcom.walmart.platform.logging.file.enabled=false -Dcom.walmart.platform.metrics.forwardToRawlog=false -Dcom.walmart.platform.logging.file.path=/dev/null -Dcom.walmart.platform.metrics.file.path=/dev/null -Dcom.walmart.platform.txnmarking.file.path=/dev/null -Dcom.walmart.platform.logging.bandwidthquota=0 -Dcom.walmart.platform.metrics.log=false -Dcom.walmart.platform.txnmarking.kafka.brokerList=null -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=null -Dcom.walmart.platform.logging.network.enabled=false -Dreactor.schedulers.defaultPoolSize=20 -Dreactor.schedulers.defaultBoundedElasticSize=20 -XX:ActiveProcessorCount=4 -Dmanagement.endpoint.prometheus.enabled=true -Dmanagement.endpoints.web.exposure.include=*", "is_g1_gc": false}}], "status": true}}, {"unifiedpromise-prod-primary": {"clusters": [{"pods": 10, "mm_name": "scus-prod-a97", "namespace": "mx-unified-promise", "lbs": {"gslb": "unifiedpromise-prod.mx-unified-promise.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "2m", "name": "<PERSON><PERSON><PERSON><PERSON>_Container_Restarts", "query": "kube_pod_container_status_restarts_total{namespace=\"mx-unified-promise\",container=\"unifiedpromise-prod\",pod!~\"unifiedpromise-prod-primary.*\",mms_source=\"wcnp\"}\n", "threshold": 0}, {"name": "Envoy_5xxs_rate", "query": "sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*unifiedpromise-prod-canary.mx-unified-promise.svc.cluster.local\",response_code_class=~\"5.*\",mms_source=\"wcnp\"}[2m]))\n/\nsum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*unifiedpromise-prod-canary.mx-unified-promise.svc.cluster.local\",mms_source=\"wcnp\"}[2m])) * 100 or on() vector(0)\n", "threshold": 0}, {"name": "CPU_Utilization", "query": "(max(rate(container_cpu_usage_seconds_total{mms_source=\"wcnp\",namespace=\"mx-unified-promise\",container=~\"unifiedpromise-prod\",container!=\"\",container!=\"POD\",container!~\"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|.*default-tester\",pod!~\"unifiedpromise-prod-primary.*\"}[2m])) by (namespace,pod,container,cluster_id) / min(kube_pod_container_resource_limits{resource=\"cpu\",mms_source=\"wcnp\",namespace=\"mx-unified-promise\"}) by (namespace,pod,container,cluster_id) * 100 )\n", "threshold": 30}, {"name": "Memory_Utilization", "query": "max(container_memory_working_set_bytes{cluster_id=\"scus-prod-a97\",namespace=\"mx-unified-promise\",container!=\"\",container!=\"POD\",mms_source=\"wcnp\"}\nand on(pod) kube_pod_labels{cluster_id=\"scus-prod-a97\",label_app=\"unifiedpromise-prod\",mms_source=\"wcnp\"}) by (namespace,container)\n/ \nmin(kube_pod_container_resource_limits{resource=\"memory\",namespace=\"mx-unified-promise\",container=\"unifiedpromise-prod\",mms_source=\"wcnp\"}) by (namespace,container)\n", "threshold": 0.6}, {"name": "Latency_P90", "query": "histogram_quantile(0.90, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{cluster_name=~\"outbound.*unifiedpromise-prod-canary.mx-unified-promise.svc.cluster.local\",mms_source=\"wcnp\"}) by (le,cluster_id))\n", "threshold": 2000}], "defaultMetrics": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 4, "min_memory": "12G", "max_memory": "12G"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 10}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 120, "time_out": 10, "uri": "/unified-promise/node"}, "ready_probs": {"probe_interval": null, "wait": 120, "time_out": 10, "uri": "/unified-promise/node"}}, "git": "https://gecgithub01.walmart.com/LabsSmartSourcing/unifiedpromise", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Xmx8192m -Dcom.walmart.platform.config.buId=7 -Dscm.scope.template=/environment/{buId}/cloud/node -Druntime.context.system.property.override.enabled=true -Druntime.context.appName=mosaic-ims-lite-router -Druntime.context.appVersion=1.0.1283 -Druntime.context.environmentType=prod -Druntime.context.environment=prod -Druntime.context.cloud=scus -Dcom.walmart.platform.logging.console.enabled=true -Dcom.walmart.platform.logging.file.enabled=false -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.file.path=/dev/null -Dcom.walmart.platform.logging.includeLocation=true -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.logging.kafka.brokerList=scus.kafka.medusa.prod.walmart.com:9092 -Dcom.walmart.platform.logging.kafka.enable=false -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=true -Dcom.walmart.platform.logging.logfile.path=/dev/null -Dcom.walmart.platform.logging.network.enabled=true -Dcom.walmart.platform.metrics.file.format=V3JsonMetrics -Dcom.walmart.platform.metrics.file.path=/dev/null -Dcom.walmart.platform.metrics.logfile.path=/dev/null -Dcom.walmart.platform.txnmarking.logfile.path=/dev/null", "is_g1_gc": false}}, {"pods": 5, "mm_name": "useast-prod-az-021", "namespace": "mx-unified-promise", "lbs": {"gslb": "unifiedpromise-prod.mx-unified-promise.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "2m", "name": "<PERSON><PERSON><PERSON><PERSON>_Container_Restarts", "query": "kube_pod_container_status_restarts_total{namespace=\"mx-unified-promise\",container=\"unifiedpromise-prod\",pod!~\"unifiedpromise-prod-primary.*\",mms_source=\"wcnp\"}\n", "threshold": 0}, {"name": "Envoy_5xxs_rate", "query": "sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*unifiedpromise-prod-canary.mx-unified-promise.svc.cluster.local\",response_code_class=~\"5.*\",mms_source=\"wcnp\"}[2m]))\n/\nsum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*unifiedpromise-prod-canary.mx-unified-promise.svc.cluster.local\",mms_source=\"wcnp\"}[2m])) * 100 or on() vector(0)\n", "threshold": 0}, {"name": "CPU_Utilization", "query": "(max(rate(container_cpu_usage_seconds_total{mms_source=\"wcnp\",namespace=\"mx-unified-promise\",container=~\"unifiedpromise-prod\",container!=\"\",container!=\"POD\",container!~\"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|.*default-tester\",pod!~\"unifiedpromise-prod-primary.*\"}[2m])) by (namespace,pod,container,cluster_id) / min(kube_pod_container_resource_limits{resource=\"cpu\",mms_source=\"wcnp\",namespace=\"mx-unified-promise\"}) by (namespace,pod,container,cluster_id) * 100 )\n", "threshold": 30}, {"name": "Memory_Utilization", "query": "max(container_memory_working_set_bytes{cluster_id=\"useast-prod-az-021\",namespace=\"mx-unified-promise\",container!=\"\",container!=\"POD\",mms_source=\"wcnp\"}\nand on(pod) kube_pod_labels{cluster_id=\"useast-prod-az-021\",label_app=\"unifiedpromise-prod\",mms_source=\"wcnp\"}) by (namespace,container)\n/ \nmin(kube_pod_container_resource_limits{resource=\"memory\",namespace=\"mx-unified-promise\",container=\"unifiedpromise-prod\",mms_source=\"wcnp\"}) by (namespace,container)\n", "threshold": 0.6}, {"name": "Latency_P90", "query": "histogram_quantile(0.90, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{cluster_name=~\"outbound.*unifiedpromise-prod-canary.mx-unified-promise.svc.cluster.local\",mms_source=\"wcnp\"}) by (le,cluster_id))\n", "threshold": 2000}], "defaultMetrics": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 4, "min_memory": "12G", "max_memory": "12G"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 10}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 120, "time_out": 10, "uri": "/unified-promise/node"}, "ready_probs": {"probe_interval": null, "wait": 120, "time_out": 10, "uri": "/unified-promise/node"}}, "git": "https://gecgithub01.walmart.com/LabsSmartSourcing/unifiedpromise", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Xmx8192m -Dcom.walmart.platform.config.buId=7 -Dscm.scope.template=/environment/{buId}/cloud/node -Druntime.context.system.property.override.enabled=true -Druntime.context.appName=mosaic-ims-lite-router -Druntime.context.appVersion=1.0.1283 -Druntime.context.environmentType=prod -Druntime.context.environment=prod -Druntime.context.cloud=eus2 -Dcom.walmart.platform.logging.console.enabled=true -Dcom.walmart.platform.logging.file.enabled=false -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.file.path=/dev/null -Dcom.walmart.platform.logging.includeLocation=true -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.logging.kafka.brokerList=eus2.kafka.medusa.prod.walmart.com:9092 -Dcom.walmart.platform.logging.kafka.enable=false -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=true -Dcom.walmart.platform.logging.logfile.path=/dev/null -Dcom.walmart.platform.logging.network.enabled=true -Dcom.walmart.platform.metrics.file.format=V3JsonMetrics -Dcom.walmart.platform.metrics.file.path=/dev/null -Dcom.walmart.platform.metrics.logfile.path=/dev/null -Dcom.walmart.platform.txnmarking.logfile.path=/dev/null", "is_g1_gc": false}}, {"pods": 1, "mm_name": "wus-prod-a93", "namespace": "mx-unified-promise", "lbs": {"gslb": "unifiedpromise-prod.mx-unified-promise.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "2m", "name": "<PERSON><PERSON><PERSON><PERSON>_Container_Restarts", "query": "kube_pod_container_status_restarts_total{namespace=\"mx-unified-promise\",container=\"unifiedpromise-prod\",pod!~\"unifiedpromise-prod-primary.*\",mms_source=\"wcnp\"}\n", "threshold": 0}, {"name": "Envoy_5xxs_rate", "query": "sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*unifiedpromise-prod-canary.mx-unified-promise.svc.cluster.local\",response_code_class=~\"5.*\",mms_source=\"wcnp\"}[2m]))\n/\nsum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*unifiedpromise-prod-canary.mx-unified-promise.svc.cluster.local\",mms_source=\"wcnp\"}[2m])) * 100 or on() vector(0)\n", "threshold": 0}, {"name": "CPU_Utilization", "query": "(max(rate(container_cpu_usage_seconds_total{mms_source=\"wcnp\",namespace=\"mx-unified-promise\",container=~\"unifiedpromise-prod\",container!=\"\",container!=\"POD\",container!~\"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|.*default-tester\",pod!~\"unifiedpromise-prod-primary.*\"}[2m])) by (namespace,pod,container,cluster_id) / min(kube_pod_container_resource_limits{resource=\"cpu\",mms_source=\"wcnp\",namespace=\"mx-unified-promise\"}) by (namespace,pod,container,cluster_id) * 100 )\n", "threshold": 30}, {"name": "Memory_Utilization", "query": "max(container_memory_working_set_bytes{cluster_id=\"wus-prod-a93\",namespace=\"mx-unified-promise\",container!=\"\",container!=\"POD\",mms_source=\"wcnp\"}\nand on(pod) kube_pod_labels{cluster_id=\"wus-prod-a93\",label_app=\"unifiedpromise-prod\",mms_source=\"wcnp\"}) by (namespace,container)\n/ \nmin(kube_pod_container_resource_limits{resource=\"memory\",namespace=\"mx-unified-promise\",container=\"unifiedpromise-prod\",mms_source=\"wcnp\"}) by (namespace,container)\n", "threshold": 0.6}, {"name": "Latency_P90", "query": "histogram_quantile(0.90, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{cluster_name=~\"outbound.*unifiedpromise-prod-canary.mx-unified-promise.svc.cluster.local\",mms_source=\"wcnp\"}) by (le,cluster_id))\n", "threshold": 2000}], "defaultMetrics": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 4, "min_memory": "12G", "max_memory": "12G"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 10}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 120, "time_out": 10, "uri": "/unified-promise/node"}, "ready_probs": {"probe_interval": null, "wait": 120, "time_out": 10, "uri": "/unified-promise/node"}}, "git": "https://gecgithub01.walmart.com/LabsSmartSourcing/unifiedpromise", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Xmx8192m -Dcom.walmart.platform.config.buId=7 -Dscm.scope.template=/environment/{buId}/cloud/node -Druntime.context.system.property.override.enabled=true -Druntime.context.appName=mosaic-ims-lite-router -Druntime.context.appVersion=1.0.1283 -Druntime.context.environmentType=prod -Druntime.context.environment=prod -Druntime.context.cloud=wus -Dcom.walmart.platform.logging.console.enabled=true -Dcom.walmart.platform.logging.file.enabled=false -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.file.path=/dev/null -Dcom.walmart.platform.logging.includeLocation=true -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.logging.kafka.brokerList=wus.kafka.medusa.prod.walmart.com:9092 -Dcom.walmart.platform.logging.kafka.enable=false -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=true -Dcom.walmart.platform.logging.logfile.path=/dev/null -Dcom.walmart.platform.logging.network.enabled=true -Dcom.walmart.platform.metrics.file.format=V3JsonMetrics -Dcom.walmart.platform.metrics.file.path=/dev/null -Dcom.walmart.platform.metrics.logfile.path=/dev/null -Dcom.walmart.platform.txnmarking.logfile.path=/dev/null", "is_g1_gc": false}}], "status": true}}, {"mx-item-service-prod": {"clusters": [{"pods": 12, "mm_name": "scus-prod-a2", "namespace": "mx-item-services", "lbs": {"gslb": "mx-item-service-prod.mx-item-services.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "4000m", "max_cpu": "4000m", "min_memory": "4096Mi", "max_memory": "4096Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 50}, "is_cpu_based_hpa": true, "scaling": {"min": 10, "max": 16}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 120, "time_out": 10, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": null, "wait": 120, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/MexicoEcomm/item-services", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Druntime.context.cloud=scus -Druntime.context.environmentType=prod -Druntime.context.appName=mx-item-service -Druntime.context.system.property.override.enabled=true -Dscm.snapshot.enabled=true -Dlog4j2.formatMsgNoLookups=true -Djsse.enableSNIExtension=true -Xms2048m -Xmx2048m -XX:+UseG1GC -XX:MaxMetaspaceSize=512m -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions -XX:G1NewSizePercent=15 -XX:ActiveProcessorCount=4", "is_g1_gc": true}}, {"pods": 12, "mm_name": "wus-prod-a26", "namespace": "mx-item-services", "lbs": {"gslb": "mx-item-service-prod.mx-item-services.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "4000m", "max_cpu": "4000m", "min_memory": "4096Mi", "max_memory": "4096Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 50}, "is_cpu_based_hpa": true, "scaling": {"min": 10, "max": 16}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 120, "time_out": 10, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": null, "wait": 120, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/MexicoEcomm/item-services", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Druntime.context.cloud=wus -Druntime.context.environmentType=prod -Druntime.context.appName=mx-item-service -Druntime.context.system.property.override.enabled=true -Dscm.snapshot.enabled=true -Dlog4j2.formatMsgNoLookups=true -Djsse.enableSNIExtension=true -Xms2048m -Xmx2048m -XX:+UseG1GC -XX:MaxMetaspaceSize=512m -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions -XX:G1NewSizePercent=15 -XX:ActiveProcessorCount=4", "is_g1_gc": true}}], "status": true}}, {"vulcan-adapter-ca-prod": {"clusters": [{"pods": 5, "mm_name": "eus2-prod-a11", "namespace": "sct-vulcan-ca", "lbs": {"gslb": "vulcan-adapter-ca-prod.sct-vulcan-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 4, "max_cpu": 4, "min_memory": "6G", "max_memory": "6G"}, "horizontal": {"is_enabled": false, "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}}}, "prob": {"liveness_probs": {"probe_interval": 10, "wait": 200, "time_out": 5, "uri": "/vulcan-adapter"}, "ready_probs": {"probe_interval": 10, "wait": 200, "time_out": 5, "uri": "/vulcan-adapter"}}, "git": "https://gecgithub01.walmart.com/Shipment-Tracking/vulcan", "dynatrace_enabled": true}, {"pods": 5, "mm_name": "scus-prod-a98", "namespace": "sct-vulcan-ca", "lbs": {"gslb": "vulcan-adapter-ca-prod.sct-vulcan-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 4, "max_cpu": 4, "min_memory": "6G", "max_memory": "6G"}, "horizontal": {"is_enabled": false, "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}}}, "prob": {"liveness_probs": {"probe_interval": 10, "wait": 200, "time_out": 5, "uri": "/vulcan-adapter"}, "ready_probs": {"probe_interval": 10, "wait": 200, "time_out": 5, "uri": "/vulcan-adapter"}}, "git": "https://gecgithub01.walmart.com/Shipment-Tracking/vulcan", "dynatrace_enabled": true}], "status": true}}, {"store-selector-page-prod": {"clusters": [{"pods": 10, "mm_name": "eus2-prod-aspcl09", "namespace": "ca-store-selector-page", "lbs": {"gslb": "store-selector-page-prod.ca-store-selector-page.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "512Mi", "max_memory": "512Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 10, "max": 150}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 120, "time_out": 10, "uri": "/healthcheck"}, "ready_probs": {"probe_interval": 5, "wait": 120, "time_out": 10, "uri": "/healthcheck"}}, "git": "https://gecgithub01.walmart.com/ca-gm/fe-microapps", "dynatrace_enabled": true}, {"pods": 10, "mm_name": "scus-prod-aspcl09", "namespace": "ca-store-selector-page", "lbs": {"gslb": "store-selector-page-prod.ca-store-selector-page.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "512Mi", "max_memory": "512Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 10, "max": 150}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 120, "time_out": 10, "uri": "/healthcheck"}, "ready_probs": {"probe_interval": 5, "wait": 120, "time_out": 10, "uri": "/healthcheck"}}, "git": "https://gecgithub01.walmart.com/ca-gm/fe-microapps", "dynatrace_enabled": true}], "status": true}}, {"ads-flyer-prod-primary": {"clusters": [{"pods": 3, "mm_name": "eus2-prod-a27", "namespace": "mx-sponsored-product-service", "lbs": {"gslb": "ads-flyer-prod.mx-sponsored-product-service.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "1m", "name": "Kubernetes Container Restarts", "query": "sum(rate(kube_pod_container_status_restarts_total{namespace=\"mx-sponsored-product-service\",container=\"ads-flyer-prod\",pod!~\"ads-flyer-prod-primary.*\",telemetry_walmart_com_metric_source=\"local\"}[1m])) or on() vector(0)\n", "threshold": 3}, {"interval": "1m", "name": "CPU Utilization", "query": "sum(rate(container_cpu_usage_seconds_total{telemetry_walmart_com_metric_source=\"local\", namespace=~\"mx-sponsored-product-service\", container=\"ads-flyer-prod\",pod !~\"ads-flyer-prod-primary.*\",telemetry_walmart_com_metric_source=\"local\"}[1m])) / sum(kube_pod_container_resource_limits{resource=\"cpu\",telemetry_walmart_com_metric_source=\"local\", namespace=~\"mx-sponsored-product-service\",container=\"ads-flyer-prod\",pod !~\"ads-flyer-prod-primary.*\",telemetry_walmart_com_metric_source=\"local\"})\n", "threshold": 0.6}, {"interval": "1m", "name": "Memory Utilization", "query": "max(container_memory_working_set_bytes{telemetry_walmart_com_metric_source=\"local\", namespace=~\"mx-sponsored-product-service\", container=\"ads-flyer-prod\",pod !~\"ads-flyer-prod-primary.*\",telemetry_walmart_com_metric_source=\"local\"}) / min(kube_pod_container_resource_limits{resource=\"memory\",telemetry_walmart_com_metric_source=\"local\", namespace=~\"mx-sponsored-product-service\",container=\"ads-flyer-prod\",pod !~\"ads-flyer-prod-primary.*\",telemetry_walmart_com_metric_source=\"local\"})\n", "threshold": 0.8}, {"interval": "1m", "name": "Envoy 5xxs rate", "query": "sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*ads-flyer-prod-canary.mx-sponsored-product-service.svc.cluster.local\",response_code_class=~\"5.*\",telemetry_walmart_com_metric_source=\"local\"}[1m]))/sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*ads-flyer-prod-canary.mx-sponsored-product-service.svc.cluster.local\",telemetry_walmart_com_metric_source=\"local\"}[1m])) * 100 or on() vector(0)\n", "threshold": 5}, {"interval": "1m", "name": "Envoy 4xxs rate", "query": "sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*ads-flyer-prod-canary.mx-sponsored-product-service.svc.cluster.local\",response_code_class=~\"4.*\",telemetry_walmart_com_metric_source=\"local\"}[1m]))/sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*ads-flyer-prod-canary.mx-sponsored-product-service.svc.cluster.local\",telemetry_walmart_com_metric_source=\"local\"}[1m])) * 100 or on() vector(0)\n", "threshold": 20}], "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "1500m", "max_cpu": "3000m", "min_memory": "5000Mi", "max_memory": "5000Mi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 3, "max": 10}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 300, "time_out": 2, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": 10, "wait": 60, "time_out": 2, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/Walmart-International/ads-flyer", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": " -Druntime.context.environmentType=prod -Druntime.context.environment=prod -Druntime.context.cloud=eus2-prod-a27 -Druntime.context.appName=sponsored-product-service -Druntime.context.system.property.override.enabled=true -Druntime.context.appVersion=0.0.798 -Dspring.profiles.active=prod -Djsse.enableSNIExtension=true -Dcom.walmart.platform.logging.console.enabled=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Dlog4j2.formatMsgNoLookups=true -Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dcom.walmart.platform.metrics.impl.type=MICROMETER -Dcom.walmart.platform.txnmarking.otel.type=OTLPgRPC -Dcom.walmart.platform.txnmarking.otel.port=80 -Dcom.walmart.platform.txnmarking.otel.host=trace-collector.stg-scus-1.walmart.com -XX:MaxRAMPercentage=60 -XX:InitialRAMPercentage=60 -XX:+UnlockExperimentalVMOptions -XX:G1NewSizePercent=20", "is_g1_gc": false}}, {"pods": 3, "mm_name": "scus-prod-a38", "namespace": "mx-sponsored-product-service", "lbs": {"gslb": "ads-flyer-prod.mx-sponsored-product-service.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "1m", "name": "Kubernetes Container Restarts", "query": "sum(rate(kube_pod_container_status_restarts_total{namespace=\"mx-sponsored-product-service\",container=\"ads-flyer-prod\",pod!~\"ads-flyer-prod-primary.*\",telemetry_walmart_com_metric_source=\"local\"}[1m])) or on() vector(0)\n", "threshold": 3}, {"interval": "1m", "name": "CPU Utilization", "query": "sum(rate(container_cpu_usage_seconds_total{telemetry_walmart_com_metric_source=\"local\", namespace=~\"mx-sponsored-product-service\", container=\"ads-flyer-prod\",pod !~\"ads-flyer-prod-primary.*\",telemetry_walmart_com_metric_source=\"local\"}[1m])) / sum(kube_pod_container_resource_limits{resource=\"cpu\",telemetry_walmart_com_metric_source=\"local\", namespace=~\"mx-sponsored-product-service\",container=\"ads-flyer-prod\",pod !~\"ads-flyer-prod-primary.*\",telemetry_walmart_com_metric_source=\"local\"})\n", "threshold": 0.6}, {"interval": "1m", "name": "Memory Utilization", "query": "max(container_memory_working_set_bytes{telemetry_walmart_com_metric_source=\"local\", namespace=~\"mx-sponsored-product-service\", container=\"ads-flyer-prod\",pod !~\"ads-flyer-prod-primary.*\",telemetry_walmart_com_metric_source=\"local\"}) / min(kube_pod_container_resource_limits{resource=\"memory\",telemetry_walmart_com_metric_source=\"local\", namespace=~\"mx-sponsored-product-service\",container=\"ads-flyer-prod\",pod !~\"ads-flyer-prod-primary.*\",telemetry_walmart_com_metric_source=\"local\"})\n", "threshold": 0.8}, {"interval": "1m", "name": "Envoy 5xxs rate", "query": "sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*ads-flyer-prod-canary.mx-sponsored-product-service.svc.cluster.local\",response_code_class=~\"5.*\",telemetry_walmart_com_metric_source=\"local\"}[1m]))/sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*ads-flyer-prod-canary.mx-sponsored-product-service.svc.cluster.local\",telemetry_walmart_com_metric_source=\"local\"}[1m])) * 100 or on() vector(0)\n", "threshold": 5}, {"interval": "1m", "name": "Envoy 4xxs rate", "query": "sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*ads-flyer-prod-canary.mx-sponsored-product-service.svc.cluster.local\",response_code_class=~\"4.*\",telemetry_walmart_com_metric_source=\"local\"}[1m]))/sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*ads-flyer-prod-canary.mx-sponsored-product-service.svc.cluster.local\",telemetry_walmart_com_metric_source=\"local\"}[1m])) * 100 or on() vector(0)\n", "threshold": 20}], "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "1500m", "max_cpu": "3000m", "min_memory": "5000Mi", "max_memory": "5000Mi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 3, "max": 10}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 300, "time_out": 2, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": 10, "wait": 60, "time_out": 2, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/Walmart-International/ads-flyer", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": " -Druntime.context.environmentType=prod -Druntime.context.environment=prod -Druntime.context.cloud=scus-prod-a38 -Druntime.context.appName=sponsored-product-service -Druntime.context.system.property.override.enabled=true -Druntime.context.appVersion=0.0.798 -Dspring.profiles.active=prod -Djsse.enableSNIExtension=true -Dcom.walmart.platform.logging.console.enabled=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Dlog4j2.formatMsgNoLookups=true -Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dcom.walmart.platform.metrics.impl.type=MICROMETER -Dcom.walmart.platform.txnmarking.otel.type=OTLPgRPC -Dcom.walmart.platform.txnmarking.otel.port=80 -Dcom.walmart.platform.txnmarking.otel.host=trace-collector.stg-scus-1.walmart.com -XX:MaxRAMPercentage=60 -XX:InitialRAMPercentage=60 -XX:+UnlockExperimentalVMOptions -XX:G1NewSizePercent=20", "is_g1_gc": false}}, {"pods": 3, "mm_name": "wus-prod-a16", "namespace": "mx-sponsored-product-service", "lbs": {"gslb": "ads-flyer-prod.mx-sponsored-product-service.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "1m", "name": "Kubernetes Container Restarts", "query": "sum(rate(kube_pod_container_status_restarts_total{namespace=\"mx-sponsored-product-service\",container=\"ads-flyer-prod\",pod!~\"ads-flyer-prod-primary.*\",telemetry_walmart_com_metric_source=\"local\"}[1m])) or on() vector(0)\n", "threshold": 3}, {"interval": "1m", "name": "CPU Utilization", "query": "sum(rate(container_cpu_usage_seconds_total{telemetry_walmart_com_metric_source=\"local\", namespace=~\"mx-sponsored-product-service\", container=\"ads-flyer-prod\",pod !~\"ads-flyer-prod-primary.*\",telemetry_walmart_com_metric_source=\"local\"}[1m])) / sum(kube_pod_container_resource_limits{resource=\"cpu\",telemetry_walmart_com_metric_source=\"local\", namespace=~\"mx-sponsored-product-service\",container=\"ads-flyer-prod\",pod !~\"ads-flyer-prod-primary.*\",telemetry_walmart_com_metric_source=\"local\"})\n", "threshold": 0.6}, {"interval": "1m", "name": "Memory Utilization", "query": "max(container_memory_working_set_bytes{telemetry_walmart_com_metric_source=\"local\", namespace=~\"mx-sponsored-product-service\", container=\"ads-flyer-prod\",pod !~\"ads-flyer-prod-primary.*\",telemetry_walmart_com_metric_source=\"local\"}) / min(kube_pod_container_resource_limits{resource=\"memory\",telemetry_walmart_com_metric_source=\"local\", namespace=~\"mx-sponsored-product-service\",container=\"ads-flyer-prod\",pod !~\"ads-flyer-prod-primary.*\",telemetry_walmart_com_metric_source=\"local\"})\n", "threshold": 0.8}, {"interval": "1m", "name": "Envoy 5xxs rate", "query": "sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*ads-flyer-prod-canary.mx-sponsored-product-service.svc.cluster.local\",response_code_class=~\"5.*\",telemetry_walmart_com_metric_source=\"local\"}[1m]))/sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*ads-flyer-prod-canary.mx-sponsored-product-service.svc.cluster.local\",telemetry_walmart_com_metric_source=\"local\"}[1m])) * 100 or on() vector(0)\n", "threshold": 5}, {"interval": "1m", "name": "Envoy 4xxs rate", "query": "sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*ads-flyer-prod-canary.mx-sponsored-product-service.svc.cluster.local\",response_code_class=~\"4.*\",telemetry_walmart_com_metric_source=\"local\"}[1m]))/sum(rate(envoy_cluster_upstream_rq{cluster_name=~\"outbound.*ads-flyer-prod-canary.mx-sponsored-product-service.svc.cluster.local\",telemetry_walmart_com_metric_source=\"local\"}[1m])) * 100 or on() vector(0)\n", "threshold": 20}], "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "1500m", "max_cpu": "3000m", "min_memory": "5000Mi", "max_memory": "5000Mi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 3, "max": 10}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 300, "time_out": 2, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": 10, "wait": 60, "time_out": 2, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/Walmart-International/ads-flyer", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": " -Druntime.context.environmentType=prod -Druntime.context.environment=prod -Druntime.context.cloud=wus-prod-a16 -Druntime.context.appName=sponsored-product-service -Druntime.context.system.property.override.enabled=true -Druntime.context.appVersion=0.0.798 -Dspring.profiles.active=prod -Djsse.enableSNIExtension=true -Dcom.walmart.platform.logging.console.enabled=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Dlog4j2.formatMsgNoLookups=true -Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dcom.walmart.platform.metrics.impl.type=MICROMETER -Dcom.walmart.platform.txnmarking.otel.type=OTLPgRPC -Dcom.walmart.platform.txnmarking.otel.port=80 -Dcom.walmart.platform.txnmarking.otel.host=trace-collector.stg-scus-1.walmart.com -XX:MaxRAMPercentage=60 -XX:InitialRAMPercentage=60 -XX:+UnlockExperimentalVMOptions -XX:G1NewSizePercent=20", "is_g1_gc": false}}], "status": true}}, {"ce-cph-prod-primary": {"clusters": [{"pods": 10, "mm_name": "eus2-prod-a65", "namespace": "ca-ce-cph", "lbs": {"gslb": "ce-cph-prod.ca-ce-cph.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "2m", "name": "kube_pod_container_status_restarts_total", "query": "sum(\n  rate(\n    kube_pod_container_status_restarts_total{\n      namespace=\"ca-ce-cph\",\n      job=~\"ce-cph-prod-canary\"\n    }[1m]\n  )\n) or on() vector(0)\n", "threshold": 2}, {"interval": "2m", "name": "Non 2XX Rate", "query": "sum(\n  envoy_cluster_upstream_rq:sum_rate2m{\n    cluster_name=~\"outbound.*ce-cph-prod-canary.ca-ce-cph.svc.cluster.local\",\n    response_code_class!=\"2xx\"\n  }\n) / sum(\n  envoy_cluster_upstream_rq:sum_rate2m{\n    cluster_name=~\"outbound.*ce-cph-prod-canary.ca-ce-cph.svc.cluster.local\",\n  }\n) or on() vector(0)\n", "threshold": 0}, {"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(rate(kube_pod_container_status_restarts_total{telemetry_walmart_com_metric_source=\"local\", namespace=\"ce-cph-mx-gql\",job=~\"ce-cph-prod-canary\"}[1m])) or on() vector(0)\n", "threshold": 2}, {"interval": "1m", "name": "Non 2XX Rate", "query": "sum(envoy_cluster_upstream_rq:sum_rate2m{telemetry_walmart_com_metric_source=\"local\", cluster_name=~\"outbound.*ce-cph-prod-canary.ce-cph-mx-gql.svc.cluster.local\", response_code_class!=\"2xx\"})\n/\nsum(envoy_cluster_upstream_rq:sum_rate2m{telemetry_walmart_com_metric_source=\"local\", cluster_name=~\"outbound.*ce-cph-prod-canary.ce-cph-mx-gql.svc.cluster.local\"})\nor on() vector(0)\n", "threshold": 0}, {"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(\n  rate(\n    kube_pod_container_status_restarts_total{\n      namespace=\"ca-ce-cph\",\n      job=~\"ce-cph-prod-canary\"\n    }[1m]\n  )\n) or on() vector(0)\n", "threshold": 2}], "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "2048Mi", "max_memory": "2048Mi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "custom", "target": 15, "query": "round(sum(rate(envoy_cluster_upstream_rq{cluster_name=\"outbound|4000||ce-cph-prod.ca-ce-cph.svc.cluster.local\",telemetry_walmart_com_metric_source=\"local\",mms_source=\"wcnp\",response_code_class!=\"\", cluster_id=\"eus2-prod-a65\"}[2m])), 0.001)"}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 100}}}, "prob": {"liveness_probs": {"probe_interval": 20, "wait": 120, "time_out": 5, "uri": "/server-health"}, "ready_probs": {"probe_interval": 10, "wait": 120, "time_out": 5, "uri": "/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/ce-cph", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dccm.configs.dir=/etc/config", "is_g1_gc": false}}, {"pods": 10, "mm_name": "scus-prod-a84", "namespace": "ca-ce-cph", "lbs": {"gslb": "ce-cph-prod.ca-ce-cph.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "2m", "name": "kube_pod_container_status_restarts_total", "query": "sum(\n  rate(\n    kube_pod_container_status_restarts_total{\n      namespace=\"ca-ce-cph\",\n      job=~\"ce-cph-prod-canary\"\n    }[1m]\n  )\n) or on() vector(0)\n", "threshold": 2}, {"interval": "2m", "name": "Non 2XX Rate", "query": "sum(\n  envoy_cluster_upstream_rq:sum_rate2m{\n    cluster_name=~\"outbound.*ce-cph-prod-canary.ca-ce-cph.svc.cluster.local\",\n    response_code_class!=\"2xx\"\n  }\n) / sum(\n  envoy_cluster_upstream_rq:sum_rate2m{\n    cluster_name=~\"outbound.*ce-cph-prod-canary.ca-ce-cph.svc.cluster.local\",\n  }\n) or on() vector(0)\n", "threshold": 0}, {"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(rate(kube_pod_container_status_restarts_total{telemetry_walmart_com_metric_source=\"local\", namespace=\"ce-cph-mx-gql\",job=~\"ce-cph-prod-canary\"}[1m])) or on() vector(0)\n", "threshold": 2}, {"interval": "1m", "name": "Non 2XX Rate", "query": "sum(envoy_cluster_upstream_rq:sum_rate2m{telemetry_walmart_com_metric_source=\"local\", cluster_name=~\"outbound.*ce-cph-prod-canary.ce-cph-mx-gql.svc.cluster.local\", response_code_class!=\"2xx\"})\n/\nsum(envoy_cluster_upstream_rq:sum_rate2m{telemetry_walmart_com_metric_source=\"local\", cluster_name=~\"outbound.*ce-cph-prod-canary.ce-cph-mx-gql.svc.cluster.local\"})\nor on() vector(0)\n", "threshold": 0}, {"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(\n  rate(\n    kube_pod_container_status_restarts_total{\n      namespace=\"ca-ce-cph\",\n      job=~\"ce-cph-prod-canary\"\n    }[1m]\n  )\n) or on() vector(0)\n", "threshold": 2}], "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "2048Mi", "max_memory": "2048Mi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "custom", "target": 15, "query": "round(sum(rate(envoy_cluster_upstream_rq{cluster_name=\"outbound|4000||ce-cph-prod.ca-ce-cph.svc.cluster.local\",telemetry_walmart_com_metric_source=\"local\",mms_source=\"wcnp\",response_code_class!=\"\", cluster_id=\"scus-prod-a84\"}[2m])), 0.001)"}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 100}}}, "prob": {"liveness_probs": {"probe_interval": 20, "wait": 120, "time_out": 5, "uri": "/server-health"}, "ready_probs": {"probe_interval": 10, "wait": 120, "time_out": 5, "uri": "/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/ce-cph", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dccm.configs.dir=/etc/config", "is_g1_gc": false}}], "status": true}}, {"seller-settlement": {"clusters": [{"pods": 10, "mm_name": "scus-prod-a59", "namespace": "seller-settlement", "lbs": {"gslb": "seller-settlement-prod.seller-settlement.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "1000m", "max_cpu": "2000m", "min_memory": "4Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 80}, "is_cpu_based_hpa": true, "scaling": {"min": 10, "max": 12}}}, "prob": {"liveness_probs": {"probe_interval": 10, "wait": 350, "time_out": 5, "uri": "/actuator/health/liveness"}, "ready_probs": {"probe_interval": 15, "wait": 325, "time_out": 5, "uri": "/actuator/health/readiness"}}, "git": "https://gecgithub01.walmart.com/RET-Marketplace/seller-settlement", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Djsse.enableSNIExtension=true -Druntime.context.environmentType=prod -Druntime.context.appName=sellersettlement -Druntime.context.system.property.override.enabled=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Druntime.context.cloud=scus-prod-a59", "is_g1_gc": false}}, {"pods": 10, "mm_name": "uswest-prod-az-311", "namespace": "seller-settlement", "lbs": {"gslb": "seller-settlement-prod.seller-settlement.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "1000m", "max_cpu": "2000m", "min_memory": "4Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 80}, "is_cpu_based_hpa": true, "scaling": {"min": 10, "max": 12}}}, "prob": {"liveness_probs": {"probe_interval": 10, "wait": 350, "time_out": 5, "uri": "/actuator/health/liveness"}, "ready_probs": {"probe_interval": 15, "wait": 325, "time_out": 5, "uri": "/actuator/health/readiness"}}, "git": "https://gecgithub01.walmart.com/RET-Marketplace/seller-settlement", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Djsse.enableSNIExtension=true -Druntime.context.environmentType=prod -Druntime.context.appName=sellersettlement -Druntime.context.system.property.override.enabled=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Druntime.context.cloud=uswest-prod-az-311", "is_g1_gc": false}}], "status": true}}, {"intl-p13n-prod-primary": {"clusters": [{"pods": 2, "mm_name": "cdc-prod-o4", "namespace": "ca-p13n-services", "lbs": {"gslb": "intl-p13n-prod.ca-p13n-services.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "2Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 2, "max": 20}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 90, "time_out": 10, "uri": "/index.html"}, "ready_probs": {"probe_interval": 60, "wait": 90, "time_out": 10, "uri": "/index.html"}}, "git": "https://gecgithub01.walmart.com/intl-ecomm-svcs/intl-p13n", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dcom.walmart.platform.config.runOnEnv=prod -Dccm.configs.dir=/etc/config -Dexternal.configs.source.dir=/etc/secrets -Dcom.walmart.intl-p13n.app.path=/opt/app/intl-p13n -Djsse.enableSNIExtension=true", "is_g1_gc": false}}, {"pods": 2, "mm_name": "eus2-prod-a47", "namespace": "ca-p13n-services", "lbs": {"gslb": "intl-p13n-prod.ca-p13n-services.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "2Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 2, "max": 20}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 90, "time_out": 10, "uri": "/index.html"}, "ready_probs": {"probe_interval": 60, "wait": 90, "time_out": 10, "uri": "/index.html"}}, "git": "https://gecgithub01.walmart.com/intl-ecomm-svcs/intl-p13n", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dcom.walmart.platform.config.runOnEnv=prod -Dccm.configs.dir=/etc/config -Dexternal.configs.source.dir=/etc/secrets -Dcom.walmart.intl-p13n.app.path=/opt/app/intl-p13n -Djsse.enableSNIExtension=true", "is_g1_gc": false}}, {"pods": 2, "mm_name": "scus-prod-a101", "namespace": "ca-p13n-services", "lbs": {"gslb": "intl-p13n-prod.ca-p13n-services.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "2Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 2, "max": 20}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 90, "time_out": 10, "uri": "/index.html"}, "ready_probs": {"probe_interval": 60, "wait": 90, "time_out": 10, "uri": "/index.html"}}, "git": "https://gecgithub01.walmart.com/intl-ecomm-svcs/intl-p13n", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dcom.walmart.platform.config.runOnEnv=prod -Dccm.configs.dir=/etc/config -Dexternal.configs.source.dir=/etc/secrets -Dcom.walmart.intl-p13n.app.path=/opt/app/intl-p13n -Djsse.enableSNIExtension=true", "is_g1_gc": false}}], "status": true}}, {"quest-primary": {"clusters": [{"pods": 4, "mm_name": "uscentral-prod-az-005", "namespace": "mx-quest", "lbs": {"gslb": "quest-prod.mx-quest.k8s.glb.us.walmart.net", "custom_gslb": "mx-quest.walmart.com"}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "1000m", "max_cpu": "1000m", "min_memory": "2Gi", "max_memory": "2Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 4, "max": 4}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 60, "time_out": 10, "uri": -1}, "ready_probs": {"probe_interval": null, "wait": 60, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/IEC-SEARCH/quest", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dspring.profiles.active=prod-cld1 -Druntime.context.cloud=scus -Druntime.context.system.property.override.enabled=true -Djsse.enableSNIExtension=true -Dlog4j2.formatMsgNoLookups=true -Druntime.context.environmentType=prod-cld1 -Dcom.walmart.platform.logging.profile=OTEL -Dcom.walmart.platform.telemetry.otel.enabled=true -Dcom.walmart.platform.metrics.impl.type=MICROMETER -Dcom.walmart.platform.txnmarking.otel.type=OTLPgRPC -Dcom.walmart.platform.txnmarking.otel.host=trace-collector.stg-scus-1.walmart.com -Dcom.walmart.platform.txnmarking.otel.port=80 -Dscm.server.url=http://tunr.non-prod.walmart.com/scm-app/v2 -Dccm.configs.dir=/etc/config", "is_g1_gc": false}}], "status": true}, "quest": {"clusters": [{"pods": 4, "mm_name": "uswest-prod-az-043", "namespace": "mx-quest", "lbs": {"gslb": "quest-prod.mx-quest.k8s.glb.us.walmart.net", "custom_gslb": "mx-quest.walmart.com"}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "1000m", "max_cpu": "1000m", "min_memory": "2Gi", "max_memory": "2Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 4, "max": 4}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 60, "time_out": 10, "uri": -1}, "ready_probs": {"probe_interval": null, "wait": 60, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/IEC-SEARCH/quest", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dspring.profiles.active=prod-cld2 -Druntime.context.cloud=wus2 -Druntime.context.system.property.override.enabled=true -Djsse.enableSNIExtension=true -Dlog4j2.formatMsgNoLookups=true -Druntime.context.environmentType=prod-cld2 -Dcom.walmart.platform.logging.profile=OTEL -Dcom.walmart.platform.telemetry.otel.enabled=true -Dcom.walmart.platform.metrics.impl.type=MICROMETER -Dcom.walmart.platform.txnmarking.otel.type=OTLPgRPC -Dcom.walmart.platform.txnmarking.otel.host=trace-collector.stg-scus-1.walmart.com -Dcom.walmart.platform.txnmarking.otel.port=80 -Dscm.server.url=http://tunr.non-prod.walmart.com/scm-app/v2 -Dccm.configs.dir=/etc/config", "is_g1_gc": false}}], "status": true}}, {"killmonger-api-lock-node-inv-primary": {"clusters": [{"pods": 1, "mm_name": "eus2-prod-a38", "namespace": "wakanda-km-api-ca", "lbs": {"gslb": "killmonger-api-lock-node-inv-prod.wakanda-km-api-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "2m", "name": "<PERSON><PERSON><PERSON><PERSON>_Container_Restarts", "query": "kube_pod_container_status_restarts_total{namespace=\"wakanda-km-api-ca\",container=\"killmonger-api-lock-node-inv\",pod!~\"killmonger-api-lock-node-inv-primary.*\",telemetry_walmart_com_metric_source=\"local\"}\n", "threshold": 0}, {"name": "Envoy_5xxs_rate", "query": "sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*killmonger-api-lock-node-inv-canary.wakanda-km-api-ca.svc.cluster.local\",response_code_class=~\"5.*\",telemetry_walmart_com_metric_source=\"local\",uri!~\"/actuator.*\"}[2m]))\n/\nsum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*killmonger-api-lock-node-inv-canary.wakanda-km-api-ca.svc.cluster.local\",telemetry_walmart_com_metric_source=\"local\",uri!~\"/actuator.*\"}[2m])) * 100 or on() vector(0)\n", "threshold": 0}, {"name": "CPU_Utilization", "query": "(max(rate(container_cpu_usage_seconds_total{telemetry_walmart_com_metric_source=\"local\",namespace=\"wakanda-km-api-ca\",container=~\"killmonger-api-lock-node-inv\",container!=\"\",container!=\"POD\",container!~\"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|.*default-tester\",pod!~\"killmonger-api-lock-node-inv-primary.*\"}[2m])) by (namespace,pod,container,cluster_id) / min(kube_pod_container_resource_limits{resource=\"cpu\",telemetry_walmart_com_metric_source=\"local\",namespace=\"wakanda-km-api-ca\"}) by (namespace,pod,container,cluster_id) * 100 )\n", "threshold": 75}, {"name": "Memory_Utilization", "query": "max(\ncontainer_memory_working_set_bytes{mms_source=\"wcnp\", cluster_id=~\"eus2-prod-a38\", namespace=~\"wakanda-km-api-ca\", container!=\"\"}\n) by (namespace, pod, container) / \nmin(kube_pod_container_resource_limits{resource=\"memory\", mms_source=\"wcnp\", cluster_id=~\"eus2-prod-a38\", namespace=~\"wakanda-km-api-ca\"}) by (namespace, pod, container)\nand on(pod) kube_pod_labels{mms_source=\"wcnp\", cluster_id=~\"eus2-prod-a38\", label_app=~\"killmonger-api-lock-node-inv\"}\n", "threshold": 0.9}, {"name": "Latency_P90", "query": "histogram_quantile(0.90, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{cluster_name=~\"outbound.*killmonger-api-lock-node-inv-canary.wakanda-km-api-ca.svc.cluster.local\",telemetry_walmart_com_metric_source=\"local\"}) by (le,cluster_id))\n", "threshold": 5000}], "defaultMetrics": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "5Gi", "max_memory": "5Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "custom", "target": 20, "query": "round(sum(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*killmonger-api-lock-node-inv.*(|-primary|-canary).wakanda-km-api-ca.svc.cluster.local\", cluster_id=~\"eus2-prod-a38\"}) by (cluster_id),0.001)"}, "is_cpu_based_hpa": true, "scaling": {"min": 1, "max": 5}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 120, "time_out": 10, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": 15, "wait": 90, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/ca-gm/wakanda-canada-deployments", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": "-Dccm.configs.dir=/etc/config -XX:+UnlockExperimentalVMOptions -XX:-ExcludeGeneratedReflectorsPromotion -XX:TopTierCompileThresholdMs=60000 -XX:CIMaxCompilerThreads=1 -XX:CompilerWarmupPeriodSeconds=60 -XX:CompilerWarmupExtraThreads=1 -XX:-FalconUseCompileStashing -XX:+PrintGCDetails -Xloggc:/tmp/prime_gc.log -XX:ProfileStartupLimitInSeconds=1  -Xms3072m -Xmx3072m", "is_g1_gc": false}}, {"pods": 1, "mm_name": "scus-prod-a56", "namespace": "wakanda-km-api-ca", "lbs": {"gslb": "killmonger-api-lock-node-inv-prod.wakanda-km-api-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "2m", "name": "<PERSON><PERSON><PERSON><PERSON>_Container_Restarts", "query": "kube_pod_container_status_restarts_total{namespace=\"wakanda-km-api-ca\",container=\"killmonger-api-lock-node-inv\",pod!~\"killmonger-api-lock-node-inv-primary.*\",telemetry_walmart_com_metric_source=\"local\"}\n", "threshold": 0}, {"name": "Envoy_5xxs_rate", "query": "sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*killmonger-api-lock-node-inv-canary.wakanda-km-api-ca.svc.cluster.local\",response_code_class=~\"5.*\",telemetry_walmart_com_metric_source=\"local\",uri!~\"/actuator.*\"}[2m]))\n/\nsum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*killmonger-api-lock-node-inv-canary.wakanda-km-api-ca.svc.cluster.local\",telemetry_walmart_com_metric_source=\"local\",uri!~\"/actuator.*\"}[2m])) * 100 or on() vector(0)\n", "threshold": 0}, {"name": "CPU_Utilization", "query": "(max(rate(container_cpu_usage_seconds_total{telemetry_walmart_com_metric_source=\"local\",namespace=\"wakanda-km-api-ca\",container=~\"killmonger-api-lock-node-inv\",container!=\"\",container!=\"POD\",container!~\"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|.*default-tester\",pod!~\"killmonger-api-lock-node-inv-primary.*\"}[2m])) by (namespace,pod,container,cluster_id) / min(kube_pod_container_resource_limits{resource=\"cpu\",telemetry_walmart_com_metric_source=\"local\",namespace=\"wakanda-km-api-ca\"}) by (namespace,pod,container,cluster_id) * 100 )\n", "threshold": 75}, {"name": "Memory_Utilization", "query": "max(\ncontainer_memory_working_set_bytes{mms_source=\"wcnp\", cluster_id=~\"scus-prod-a56\", namespace=~\"wakanda-km-api-ca\", container!=\"\"}\n) by (namespace, pod, container) / \nmin(kube_pod_container_resource_limits{resource=\"memory\", mms_source=\"wcnp\", cluster_id=~\"scus-prod-a56\", namespace=~\"wakanda-km-api-ca\"}) by (namespace, pod, container)\nand on(pod) kube_pod_labels{mms_source=\"wcnp\", cluster_id=~\"scus-prod-a56\", label_app=~\"killmonger-api-lock-node-inv\"}\n", "threshold": 0.9}, {"name": "Latency_P90", "query": "histogram_quantile(0.90, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{cluster_name=~\"outbound.*killmonger-api-lock-node-inv-canary.wakanda-km-api-ca.svc.cluster.local\",telemetry_walmart_com_metric_source=\"local\"}) by (le,cluster_id))\n", "threshold": 5000}], "defaultMetrics": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "5Gi", "max_memory": "5Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "custom", "target": 20, "query": "round(sum(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*killmonger-api-lock-node-inv.*(|-primary|-canary).wakanda-km-api-ca.svc.cluster.local\", cluster_id=~\"scus-prod-a56\"}) by (cluster_id),0.001)"}, "is_cpu_based_hpa": true, "scaling": {"min": 1, "max": 5}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 120, "time_out": 10, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": 15, "wait": 90, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/ca-gm/wakanda-canada-deployments", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": "-Dccm.configs.dir=/etc/config -XX:+UnlockExperimentalVMOptions -XX:-ExcludeGeneratedReflectorsPromotion -XX:TopTierCompileThresholdMs=60000 -XX:CIMaxCompilerThreads=1 -XX:CompilerWarmupPeriodSeconds=60 -XX:CompilerWarmupExtraThreads=1 -XX:-FalconUseCompileStashing -XX:+PrintGCDetails -Xloggc:/tmp/prime_gc.log -XX:ProfileStartupLimitInSeconds=1  -Xms3072m -Xmx3072m", "is_g1_gc": false}}], "status": true}}, {"ce-cph-prod": {"clusters": [{"pods": 3, "mm_name": "scus-prod-a84", "namespace": "ce-cph-mx-gql", "lbs": {"gslb": "ce-cph-prod.ce-cph-mx-gql.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "experiments": [{"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(rate(kube_pod_container_status_restarts_total{telemetry_walmart_com_metric_source=\"local\", namespace=\"ce-cph-mx-gql\",job=~\"{{ .Release.Name }}-canary\"}[1m])) or on() vector(0)\n", "threshold": 2}, {"interval": "1m", "name": "Non 2XX Rate", "query": "sum(envoy_cluster_upstream_rq:sum_rate2m{telemetry_walmart_com_metric_source=\"local\", cluster_name=~\"outbound.*{{ .Release.Name }}-canary.ce-cph-mx-gql.svc.cluster.local\", response_code_class!=\"2xx\"})\n/\nsum(envoy_cluster_upstream_rq:sum_rate2m{telemetry_walmart_com_metric_source=\"local\", cluster_name=~\"outbound.*{{ .Release.Name }}-canary.ce-cph-mx-gql.svc.cluster.local\"})\nor on() vector(0)\n", "threshold": 0}, {"name": "Latency P90", "query": "histogram_quantile(0.90, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{cluster_name=~\"outbound.*{{ .Release.Name }}-canary.*ce-cph-mx-gql.svc.cluster.local\"}) by (le,cluster_id))\n", "threshold": 2000}, {"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(rate(kube_pod_container_status_restarts_total{telemetry_walmart_com_metric_source=\"local\", namespace=\"ce-cph-mx-gql\",job=~\"ce-cph-prod-canary\"}[1m])) or on() vector(0)\n", "threshold": 2}, {"interval": "1m", "name": "Non 2XX Rate", "query": "sum(envoy_cluster_upstream_rq:sum_rate2m{telemetry_walmart_com_metric_source=\"local\", cluster_name=~\"outbound.*ce-cph-prod-canary.ce-cph-mx-gql.svc.cluster.local\", response_code_class!=\"2xx\"})\n/\nsum(envoy_cluster_upstream_rq:sum_rate2m{telemetry_walmart_com_metric_source=\"local\", cluster_name=~\"outbound.*ce-cph-prod-canary.ce-cph-mx-gql.svc.cluster.local\"})\nor on() vector(0)\n", "threshold": 0}, {"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(\n  rate(\n    kube_pod_container_status_restarts_total{\n      namespace=\"ce-cph-mx-gql\",\n      job=~\"ce-cph-prod-canary\"\n    }[1m]\n  )\n) or on() vector(0)\n", "threshold": 2}], "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "768Mi", "max_memory": "768Mi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 3, "max": 50}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 120, "time_out": 5, "uri": "/server-health"}, "ready_probs": {"probe_interval": 10, "wait": 120, "time_out": 5, "uri": "/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/ce-cph", "dynatrace_enabled": true}, {"pods": 3, "mm_name": "wus-prod-a93", "namespace": "ce-cph-mx-gql", "lbs": {"gslb": "ce-cph-prod.ce-cph-mx-gql.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "experiments": [{"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(rate(kube_pod_container_status_restarts_total{telemetry_walmart_com_metric_source=\"local\", namespace=\"ce-cph-mx-gql\",job=~\"{{ .Release.Name }}-canary\"}[1m])) or on() vector(0)\n", "threshold": 2}, {"interval": "1m", "name": "Non 2XX Rate", "query": "sum(envoy_cluster_upstream_rq:sum_rate2m{telemetry_walmart_com_metric_source=\"local\", cluster_name=~\"outbound.*{{ .Release.Name }}-canary.ce-cph-mx-gql.svc.cluster.local\", response_code_class!=\"2xx\"})\n/\nsum(envoy_cluster_upstream_rq:sum_rate2m{telemetry_walmart_com_metric_source=\"local\", cluster_name=~\"outbound.*{{ .Release.Name }}-canary.ce-cph-mx-gql.svc.cluster.local\"})\nor on() vector(0)\n", "threshold": 0}, {"name": "Latency P90", "query": "histogram_quantile(0.90, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{cluster_name=~\"outbound.*{{ .Release.Name }}-canary.*ce-cph-mx-gql.svc.cluster.local\"}) by (le,cluster_id))\n", "threshold": 2000}, {"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(rate(kube_pod_container_status_restarts_total{telemetry_walmart_com_metric_source=\"local\", namespace=\"ce-cph-mx-gql\",job=~\"ce-cph-prod-canary\"}[1m])) or on() vector(0)\n", "threshold": 2}, {"interval": "1m", "name": "Non 2XX Rate", "query": "sum(envoy_cluster_upstream_rq:sum_rate2m{telemetry_walmart_com_metric_source=\"local\", cluster_name=~\"outbound.*ce-cph-prod-canary.ce-cph-mx-gql.svc.cluster.local\", response_code_class!=\"2xx\"})\n/\nsum(envoy_cluster_upstream_rq:sum_rate2m{telemetry_walmart_com_metric_source=\"local\", cluster_name=~\"outbound.*ce-cph-prod-canary.ce-cph-mx-gql.svc.cluster.local\"})\nor on() vector(0)\n", "threshold": 0}, {"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(\n  rate(\n    kube_pod_container_status_restarts_total{\n      namespace=\"ce-cph-mx-gql\",\n      job=~\"ce-cph-prod-canary\"\n    }[1m]\n  )\n) or on() vector(0)\n", "threshold": 2}], "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "768Mi", "max_memory": "768Mi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 3, "max": 50}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 120, "time_out": 5, "uri": "/server-health"}, "ready_probs": {"probe_interval": 10, "wait": 120, "time_out": 5, "uri": "/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/ce-cph", "dynatrace_enabled": true}], "status": true}}, {"vulcan-tracking-updates-poller-ca-prod": {"clusters": [{"pods": 5, "mm_name": "eus2-prod-a11", "namespace": "sct-vulcan-ca", "lbs": {"gslb": "vulcan-tracking-updates-poller-ca-prod.sct-vulcan-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 4, "max_cpu": 4, "min_memory": "8G", "max_memory": "8G"}, "horizontal": {"is_enabled": false, "hpa": {"criteria": null, "target": null}}}, "prob": {"liveness_probs": {"probe_interval": 10, "wait": 200, "time_out": 5, "uri": "/tracking-updates-poller"}, "ready_probs": {"probe_interval": 10, "wait": 200, "time_out": 5, "uri": "/tracking-updates-poller"}}, "git": "https://gecgithub01.walmart.com/Shipment-Tracking/vulcan", "dynatrace_enabled": true}, {"pods": 5, "mm_name": "scus-prod-a98", "namespace": "sct-vulcan-ca", "lbs": {"gslb": "vulcan-tracking-updates-poller-ca-prod.sct-vulcan-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 4, "max_cpu": 4, "min_memory": "8G", "max_memory": "8G"}, "horizontal": {"is_enabled": false, "hpa": {"criteria": null, "target": null}}}, "prob": {"liveness_probs": {"probe_interval": 10, "wait": 200, "time_out": 5, "uri": "/tracking-updates-poller"}, "ready_probs": {"probe_interval": 10, "wait": 200, "time_out": 5, "uri": "/tracking-updates-poller"}}, "git": "https://gecgithub01.walmart.com/Shipment-Tracking/vulcan", "dynatrace_enabled": true}], "status": true}}, {"ca-search-ranking-prod": {"clusters": [{"pods": 4, "mm_name": "uscentral-prod-az-022", "namespace": "search-ranking", "lbs": {"gslb": "ca-search-ranking-prod.search-ranking.k8s.glb.us.walmart.net", "custom_gslb": "ca-search-ranking-service-prod.walmart.com"}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "6000m", "max_cpu": "6000m", "min_memory": "12Gi", "max_memory": "12Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 4, "max": 20}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 180, "time_out": 10, "uri": "/ranking/api/healthcheck"}, "ready_probs": {"probe_interval": 5, "wait": 180, "time_out": 10, "uri": "/ranking/api/healthcheck"}}, "git": "https://gecgithub01.walmart.com/ca-gm/search-ranking", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=prod -Druntime.context.system.property.override.enabled=true -Druntime.context.environment=prod -Druntime.context.environmentType=prod -Druntime.context.cloud=scus -Dcom.walmart.platform.config.runOnEnv=prod -Dcom.walmart.platform.config.appName=ca-search-ranking -Druntime.context.appName=ca-search-ranking-app -Djsse.enableSNIExtension=true -Dserver.port=8080 -Dlog4j2.formatMsgNoLookups=true -XX:+UseConcMarkSweepGC -Xms8192m -Xmx12288m -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:CMSInitiatingOccupancyFraction=65 -XX:+UseCMSInitiatingOccupancyOnly -XX:ParallelGCThreads=20 -XX:+UseParNewGC -XX:ConcGCThreads=5", "is_g1_gc": false}}, {"pods": 4, "mm_name": "useast-prod-az-008", "namespace": "search-ranking", "lbs": {"gslb": "ca-search-ranking-prod.search-ranking.k8s.glb.us.walmart.net", "custom_gslb": "ca-search-ranking-service-prod.walmart.com"}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": "6000m", "max_cpu": "6000m", "min_memory": "12Gi", "max_memory": "12Gi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 4, "max": 20}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 180, "time_out": 10, "uri": "/ranking/api/healthcheck"}, "ready_probs": {"probe_interval": 5, "wait": 180, "time_out": 10, "uri": "/ranking/api/healthcheck"}}, "git": "https://gecgithub01.walmart.com/ca-gm/search-ranking", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=prod -Druntime.context.system.property.override.enabled=true -Druntime.context.environment=prod -Druntime.context.environmentType=prod -Druntime.context.cloud=eus2 -Dcom.walmart.platform.config.runOnEnv=prod -Dcom.walmart.platform.config.appName=ca-search-ranking -Druntime.context.appName=ca-search-ranking-app -Djsse.enableSNIExtension=true -Dserver.port=8080 -Dlog4j2.formatMsgNoLookups=true -XX:+UseConcMarkSweepGC -Xms8192m -Xmx12288m -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:CMSInitiatingOccupancyFraction=65 -XX:+UseCMSInitiatingOccupancyOnly -XX:ParallelGCThreads=20 -XX:+UseParNewGC -XX:ConcGCThreads=5", "is_g1_gc": false}}], "status": true}}, {"post-orders-prod-primary": {"clusters": [{"pods": 3, "mm_name": "uscentral-prod-az-030", "namespace": "ca-ce-post-orders-gql", "lbs": {"gslb": "post-orders-prod.ca-ce-post-orders-gql.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": null, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "2048Mi", "max_memory": "2048Mi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 50}, "is_cpu_based_hpa": true, "scaling": {"min": 3, "max": 50}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}, "ready_probs": {"probe_interval": null, "wait": 30, "time_out": 10, "uri": "/.well-known/apollo/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/ce-orders", "dynatrace_enabled": false}, {"pods": 3, "mm_name": "useast-prod-az-017", "namespace": "ca-ce-post-orders-gql", "lbs": {"gslb": "post-orders-prod.ca-ce-post-orders-gql.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": null, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "2048Mi", "max_memory": "2048Mi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 50}, "is_cpu_based_hpa": true, "scaling": {"min": 3, "max": 50}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}, "ready_probs": {"probe_interval": null, "wait": 30, "time_out": 10, "uri": "/.well-known/apollo/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/ce-orders", "dynatrace_enabled": false}], "status": true}, "post-orders-prod-scus-primary": {"clusters": [{"pods": 3, "mm_name": "uscentral-prod-az-030", "namespace": "ca-ce-post-orders-gql", "lbs": {"gslb": "post-orders-prod-scus.ca-ce-post-orders-gql.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": null, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "2048Mi", "max_memory": "2048Mi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 50}, "is_cpu_based_hpa": true, "scaling": {"min": 3, "max": 50}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}, "ready_probs": {"probe_interval": null, "wait": 30, "time_out": 10, "uri": "/.well-known/apollo/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/ce-orders", "dynatrace_enabled": false}], "status": true}, "post-orders-prod-eus-primary": {"clusters": [{"pods": 3, "mm_name": "useast-prod-az-017", "namespace": "ca-ce-post-orders-gql", "lbs": {"gslb": "post-orders-prod-eus.ca-ce-post-orders-gql.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": null, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "2048Mi", "max_memory": "2048Mi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 50}, "is_cpu_based_hpa": true, "scaling": {"min": 3, "max": 50}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}, "ready_probs": {"probe_interval": null, "wait": 30, "time_out": 10, "uri": "/.well-known/apollo/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/ce-orders", "dynatrace_enabled": false}], "status": true}}, {"km-apibuffersetting-ca-prod-eus": {"clusters": [{"pods": 1, "mm_name": "eus2-prod-a38", "namespace": "wakanda-km-api-ca", "lbs": {"gslb": "km-apibuffersetting-ca-prod-eus.wakanda-km-api-ca.k8s.glb.us.walmart.net", "custom_gslb": "killmonger-apibuffersetting-ca.wakanda.prod.mbaku.glb.us.walmart.net"}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "1000Mi", "max_memory": "1000Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 1, "max": 2}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 60, "time_out": 10, "uri": "/_internal/metrics/"}, "ready_probs": {"probe_interval": 60, "wait": 60, "time_out": 10, "uri": "/_internal/metrics/"}}, "git": "https://gecgithub01.walmart.com/ca-gm/wakanda-canada-deployments", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": "-Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dscm.root.dir.sandbox=/scm", "is_g1_gc": false}}], "status": true}, "km-apibuffersetting-ca-prod-scus": {"clusters": [{"pods": 1, "mm_name": "scus-prod-a56", "namespace": "wakanda-km-api-ca", "lbs": {"gslb": "km-apibuffersetting-ca-prod-scus.wakanda-km-api-ca.k8s.glb.us.walmart.net", "custom_gslb": "killmonger-apibuffersetting-ca.wakanda.prod.mbaku.glb.us.walmart.net"}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "1000Mi", "max_memory": "1000Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 1, "max": 2}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 60, "time_out": 10, "uri": "/_internal/metrics/"}, "ready_probs": {"probe_interval": 60, "wait": 60, "time_out": 10, "uri": "/_internal/metrics/"}}, "git": "https://gecgithub01.walmart.com/ca-gm/wakanda-canada-deployments", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": "-Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dscm.root.dir.sandbox=/scm", "is_g1_gc": false}}], "status": true}}, {"ca-smart-comms-service-primary": {"clusters": [{"pods": 5, "mm_name": "eus2-prod-a34", "namespace": "ca-customer-care", "lbs": {"gslb": "ca-smart-comms-service-prod.ca-customer-care.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "2G", "max_memory": "2G"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 10}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 240, "time_out": 10, "uri": "/healthcheck"}, "ready_probs": {"probe_interval": null, "wait": 240, "time_out": 10, "uri": "/healthcheck"}}, "git": "https://gecgithub01.walmart.com/ca-gm/ca-smart-comms-service", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dspring.profiles.active=prod -Druntime.context.system.property.override.enabled=true -Druntime.context.environment=prod -Druntime.context.environmentType=prod -Druntime.context.cloud=eus2 -Dcom.walmart.platform.config.runOnEnv=prod -Dcom.walmart.platform.config.appName=ca-smart-comms-service -Druntime.context.appName=ca-smart-comms-service -Djsse.enableSNIExtension=true -Dcom.walmart.platform.metrics.impl.type=MICROMETER -Dcom.walmart.platform.txnmarking.otel.type=OTLPgRPC -Dcom.walmart.platform.txnmarking.otel.host=ca-smart-comms-service.ca-customer-care.eus2-prod-a34.cluster.k8s.us.walmart.net -Dcom.walmart.platform.txnmarking.otel.port=80", "is_g1_gc": false}}, {"pods": 5, "mm_name": "scus-prod-a21", "namespace": "ca-customer-care", "lbs": {"gslb": "ca-smart-comms-service-prod.ca-customer-care.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "2G", "max_memory": "2G"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 10}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 240, "time_out": 10, "uri": "/healthcheck"}, "ready_probs": {"probe_interval": null, "wait": 240, "time_out": 10, "uri": "/healthcheck"}}, "git": "https://gecgithub01.walmart.com/ca-gm/ca-smart-comms-service", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dspring.profiles.active=prod -Druntime.context.system.property.override.enabled=true -Druntime.context.environment=prod -Druntime.context.environmentType=prod -Druntime.context.cloud=scus -Dcom.walmart.platform.config.runOnEnv=prod -Dcom.walmart.platform.config.appName=ca-smart-comms-service -Druntime.context.appName=ca-smart-comms-service -Djsse.enableSNIExtension=true -Dcom.walmart.platform.metrics.impl.type=MICROMETER -Dcom.walmart.platform.txnmarking.otel.type=OTLPgRPC -Dcom.walmart.platform.txnmarking.otel.host=ca-smart-comms-service.ca-customer-care.scus-prod-a21.cluster.k8s.us.walmart.net -Dcom.walmart.platform.txnmarking.otel.port=80", "is_g1_gc": false}}], "status": true}}, {"laurentian-prod": {"clusters": [{"pods": 2, "mm_name": "eus2-prod-aspcl09", "namespace": "ca-badging", "lbs": {"gslb": "laurentian-prod.ca-badging.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "512Mi", "max_memory": "512Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 25}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 75, "time_out": 10, "uri": "/healthcheck"}, "ready_probs": {"probe_interval": 5, "wait": 60, "time_out": 10, "uri": "/healthcheck"}}, "git": "https://gecgithub01.walmart.com/ca-gm/fe-microapps", "dynatrace_enabled": true}, {"pods": 2, "mm_name": "scus-prod-aspcl09", "namespace": "ca-badging", "lbs": {"gslb": "laurentian-prod.ca-badging.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "512Mi", "max_memory": "512Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 25}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 75, "time_out": 10, "uri": "/healthcheck"}, "ready_probs": {"probe_interval": 5, "wait": 60, "time_out": 10, "uri": "/healthcheck"}}, "git": "https://gecgithub01.walmart.com/ca-gm/fe-microapps", "dynatrace_enabled": true}], "status": true}}, {"order-history-page-prod": {"clusters": [{"pods": 10, "mm_name": "eus2-prod-aspcl09", "namespace": "ca-order-history-page", "lbs": {"gslb": "order-history-page-prod.ca-order-history-page.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "512Mi", "max_memory": "512Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 20}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 60, "time_out": 10, "uri": "/healthcheck"}, "ready_probs": {"probe_interval": 5, "wait": 30, "time_out": 10, "uri": "/healthcheck"}}, "git": "https://gecgithub01.walmart.com/ca-gm/fe-microapps", "dynatrace_enabled": true}, {"pods": 8, "mm_name": "scus-prod-aspcl09", "namespace": "ca-order-history-page", "lbs": {"gslb": "order-history-page-prod.ca-order-history-page.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "512Mi", "max_memory": "512Mi"}, "horizontal": {"is_enabled": "true", "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 20}}}, "prob": {"liveness_probs": {"probe_interval": 5, "wait": 60, "time_out": 10, "uri": "/healthcheck"}, "ready_probs": {"probe_interval": 5, "wait": 30, "time_out": 10, "uri": "/healthcheck"}}, "git": "https://gecgithub01.walmart.com/ca-gm/fe-microapps", "dynatrace_enabled": true}], "status": true}}, {"fo-crt-ms-prod": {"clusters": [{"pods": 5, "mm_name": "scus-prod-a86", "namespace": "intl-fms-fo-create-mgr-mx", "lbs": {"gslb": "fo-crt-ms-prod.intl-fms-fo-create-mgr-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "3Gi", "max_memory": "3Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 55}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 5}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-order-create-ms", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=MX -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=MX -Druntime.context.environmentType=prod -Druntime.context.appName=fulfillment-order-create-ms -Druntime.context.appVersion=0.0.1772 -Druntime.context.system.property.override.enabled=true -Dscm.server.access.enabled=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Djsse.enableSNIExtension=true -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.kafka.brokerList=null -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Dcom.walmart.platform.metrics.kafka.brokerList=scus.kafka.medusa.prod.walmart.com:9092 -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap", "is_g1_gc": false}}], "status": true}}, {"ca-ims-inventory-dsv1-summary": {"clusters": [{"pods": 1, "mm_name": "eus2-prod-a66", "namespace": "ca-ims-adaptor", "lbs": {"gslb": "ca-ims-inventory-dsv1-summary-prod.ca-ims-adaptor.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "4Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": null, "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 180, "time_out": 10, "uri": "/ca-fcap/test"}, "ready_probs": {"probe_interval": 60, "wait": 180, "time_out": 10, "uri": "/ca-fcap/test"}}, "git": "https://gecgithub01.walmart.com/ca-gm/ims-inv-inbound-adp", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": "-Djsse.enableSNIExtension=true -Dspring.profiles.active=prod -DappName=calgary_adj -Dccm.configs.dir=/etc/config -Dsecrets.home=/etc/secrets -Druntime.context.system.property.override.enabled=true -Druntime.context.appName=ca-ims-inventory-dsv1-summary -Druntime.context.environmentType=prod -Dcom.walmart.platform.logging.console.enabled=true -Dcom.walmart.platform.logging.file.enabled=false -Dcom.walmart.platform.metrics.forwardToRawlog=false -Dcom.walmart.platform.logging.file.path=/dev/null -Dcom.walmart.platform.metrics.file.path=/dev/null -Dcom.walmart.platform.txnmarking.file.path=/dev/null -Dcom.walmart.platform.logging.bandwidthquota=0 -Dcom.walmart.platform.metrics.log=false -Dcom.walmart.platform.txnmarking.kafka.brokerList=null -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=null -Dcom.walmart.platform.logging.network.enabled=false -Dmanagement.endpoint.prometheus.enabled=true -Dmanagement.endpoints.web.exposure.include=*", "is_g1_gc": false}}], "status": true}}, {"vulcan-consumer-ca-prod": {"clusters": [{"pods": 12, "mm_name": "eus2-prod-a11", "namespace": "sct-vulcan-ca", "lbs": {"gslb": "vulcan-consumer-ca-prod.sct-vulcan-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 4, "max_cpu": 4, "min_memory": "8G", "max_memory": "8G"}, "horizontal": {"is_enabled": false, "hpa": {"criteria": null, "target": null}}}, "prob": {"liveness_probs": {"probe_interval": 10, "wait": 200, "time_out": 5, "uri": "/vulcan-consumer"}, "ready_probs": {"probe_interval": 10, "wait": 200, "time_out": 5, "uri": "/vulcan-consumer"}}, "git": "https://gecgithub01.walmart.com/Shipment-Tracking/vulcan", "dynatrace_enabled": true}, {"pods": 12, "mm_name": "scus-prod-a98", "namespace": "sct-vulcan-ca", "lbs": {"gslb": "vulcan-consumer-ca-prod.sct-vulcan-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 4, "max_cpu": 4, "min_memory": "8G", "max_memory": "8G"}, "horizontal": {"is_enabled": false, "hpa": {"criteria": null, "target": null}}}, "prob": {"liveness_probs": {"probe_interval": 10, "wait": 200, "time_out": 5, "uri": "/vulcan-consumer"}, "ready_probs": {"probe_interval": 10, "wait": 200, "time_out": 5, "uri": "/vulcan-consumer"}}, "git": "https://gecgithub01.walmart.com/Shipment-Tracking/vulcan", "dynatrace_enabled": true}], "status": true}}, {"fulfillment-metadata-ms-prod": {"clusters": [{"pods": 2, "mm_name": "eus2-prod-a21", "namespace": "intl-fms-meta-ca", "lbs": {"gslb": "fulfillment-metadata-ms-prod.intl-fms-meta-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "2Gi", "max_memory": "2Gi"}, "horizontal": {"is_enabled": false, "hpa": {"criteria": "cpuPer<PERSON>", "target": 80}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/health"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/health"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-metadata-ms", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=CA -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=CA -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Druntime.context.environmentType=prod -Druntime.context.appName=fulfillment-metadata-ms -Druntime.context.appVersion=1.0.184 -Druntime.context.system.property.override.enabled=true -Dscm.server.access.enabled=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Djsse.enableSNIExtension=true -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/null -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/null -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.includeLocation=false -Dcom.walmart.platform.logging.network.enabled=false -Dcom.walmart.platform.config.appVersion=1.0.0 -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=eus2.kafka.medusa.prod.walmart.com:9092 -XX:ActiveProcessorCount=4", "is_g1_gc": false}}, {"pods": 2, "mm_name": "scus-prod-a92", "namespace": "intl-fms-meta-ca", "lbs": {"gslb": "fulfillment-metadata-ms-prod.intl-fms-meta-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "2Gi", "max_memory": "2Gi"}, "horizontal": {"is_enabled": false, "hpa": {"criteria": "cpuPer<PERSON>", "target": 80}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/health"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/health"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-metadata-ms", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=CA -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=CA -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Druntime.context.environmentType=prod -Druntime.context.appName=fulfillment-metadata-ms -Druntime.context.appVersion=1.0.184 -Druntime.context.system.property.override.enabled=true -Dscm.server.access.enabled=true -Dreactor.schedulers.defaultPoolSize=8 -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Djsse.enableSNIExtension=true -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/null -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/null -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.includeLocation=false -Dcom.walmart.platform.logging.network.enabled=false -Dcom.walmart.platform.config.appVersion=1.0.0 -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=scus.kafka.medusa.prod.walmart.com:9092 -XX:ActiveProcessorCount=4", "is_g1_gc": false}}], "status": true}}, {"ca-ims-smartbuffer-data": {"clusters": [{"pods": 2, "mm_name": "eus2-prod-a66", "namespace": "ca-ims-adaptor", "lbs": {"gslb": "ca-ims-smartbuffer-data-prod.ca-ims-adaptor.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "4Gi", "max_memory": "4Gi"}, "horizontal": {"is_enabled": null, "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 180, "time_out": 10, "uri": "/actuator/health"}, "ready_probs": {"probe_interval": 60, "wait": 180, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/ca-gm/ca-ims-smart-buffer", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=prod -Dccm.configs.dir=/etc/config -Dsecrets.home=/etc/secrets -Druntime.context.system.property.override.enabled=true -Druntime.context.appName=ca-ims-smartbuffer-data -Druntime.context.environmentType=prod -Dcom.walmart.platform.logging.console.enabled=true -Dcom.walmart.platform.logging.file.enabled=false -Dcom.walmart.platform.metrics.forwardToRawlog=false -Dcom.walmart.platform.logging.file.path=/dev/null -Dcom.walmart.platform.metrics.file.path=/dev/null -Dcom.walmart.platform.txnmarking.file.path=/dev/null -Dcom.walmart.platform.logging.bandwidthquota=0 -Dcom.walmart.platform.metrics.log=false -Dcom.walmart.platform.txnmarking.kafka.brokerList=null -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=null -Dcom.walmart.platform.logging.network.enabled=false -XX:ActiveProcessorCount=4 -Dmanagement.endpoint.prometheus.enabled=true -Dmanagement.endpoints.web.exposure.include=*", "is_g1_gc": false}}], "status": true}}, {"membership-prod-primary": {"clusters": [{"pods": 5, "mm_name": "scus-prod-a61", "namespace": "ce-membership-mx", "lbs": {"gslb": "membership-prod.ce-membership-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "1m", "name": "<PERSON><PERSON><PERSON><PERSON>_Container_Restarts", "query": "kube_pod_container_status_restarts_total{namespace=\"ce-membership-mx\",container=\"membership-prod\",pod!~\"membership-prod-primary.*\",mms_source=\"wcnp\"}\n", "threshold": 0}, {"name": "Envoy_5xxs_rate", "query": "sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*membership-prod-canary.ce-membership-mx.svc.cluster.local\",response_code_class=~\"5.*\",mms_source=\"wcnp\"}[2m]))\n/\nsum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*membership-prod-canary.ce-membership-mx.svc.cluster.local\",mms_source=\"wcnp\"}[2m])) * 100 or on() vector(0)\n", "threshold": 0}, {"name": "CPU_Utilization", "query": "sum(rate(container_cpu_usage_seconds_total{cluster_id=\"scus-prod-a61\",namespace=\"ce-membership-mx\",container=\"membership-prod\",pod!~\"membership-prod-primary.*\",mms_source=\"wcnp\"}[2m]) \nand on(pod) kube_pod_labels{cluster_id=\"scus-prod-a61\",label_app=\"membership-prod\",mms_source=\"wcnp\"})\n", "threshold": 0.3}, {"name": "Memory_Utilization", "query": "max(container_memory_working_set_bytes{cluster_id=\"scus-prod-a61\",namespace=\"ce-membership-mx\",container!=\"\",container!=\"POD\",mms_source=\"wcnp\"}\nand on(pod) kube_pod_labels{cluster_id=\"scus-prod-a61\",label_app=\"membership-prod\",mms_source=\"wcnp\"}) by (namespace,container)\n/ \nmin(kube_pod_container_resource_limits{resource=\"memory\",namespace=\"ce-membership-mx\",container=\"membership-prod\",mms_source=\"wcnp\"}) by (namespace,container)\n", "threshold": 0.3}, {"name": "Latency_P90", "query": "histogram_quantile(0.90, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{cluster_name=~\"outbound.*membership-prod-canary.ce-membership-mx.svc.cluster.local\",mms_source=\"wcnp\"}) by (le,cluster_id))\n", "threshold": 2000}, {"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(\n  rate(\n    kube_pod_container_status_restarts_total{\n      namespace=\"ce-membership-mx\",\n      job=~\"membership-prod-canary\"\n    }[1m]\n  )\n) or on() vector(0)\n", "threshold": 2}], "defaultMetrics": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "2048M", "max_memory": "2048M"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 40}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 30}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 60, "time_out": 10, "uri": "/server-health"}, "ready_probs": {"probe_interval": null, "wait": 30, "time_out": 10, "uri": "/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/ce-membership", "dynatrace_enabled": true}, {"pods": 5, "mm_name": "wus-prod-a56", "namespace": "ce-membership-mx", "lbs": {"gslb": "membership-prod.ce-membership-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "1m", "name": "<PERSON><PERSON><PERSON><PERSON>_Container_Restarts", "query": "kube_pod_container_status_restarts_total{namespace=\"ce-membership-mx\",container=\"membership-prod\",pod!~\"membership-prod-primary.*\",mms_source=\"wcnp\"}\n", "threshold": 0}, {"name": "Envoy_5xxs_rate", "query": "sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*membership-prod-canary.ce-membership-mx.svc.cluster.local\",response_code_class=~\"5.*\",mms_source=\"wcnp\"}[2m]))\n/\nsum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*membership-prod-canary.ce-membership-mx.svc.cluster.local\",mms_source=\"wcnp\"}[2m])) * 100 or on() vector(0)\n", "threshold": 0}, {"name": "CPU_Utilization", "query": "sum(rate(container_cpu_usage_seconds_total{cluster_id=\"wus-prod-a56\",namespace=\"ce-membership-mx\",container=\"membership-prod\",pod!~\"membership-prod-primary.*\",mms_source=\"wcnp\"}[2m]) \nand on(pod) kube_pod_labels{cluster_id=\"wus-prod-a56\",label_app=\"membership-prod\",mms_source=\"wcnp\"})\n", "threshold": 0.3}, {"name": "Memory_Utilization", "query": "max(container_memory_working_set_bytes{cluster_id=\"wus-prod-a56\",namespace=\"ce-membership-mx\",container!=\"\",container!=\"POD\",mms_source=\"wcnp\"}\nand on(pod) kube_pod_labels{cluster_id=\"wus-prod-a56\",label_app=\"membership-prod\",mms_source=\"wcnp\"}) by (namespace,container)\n/ \nmin(kube_pod_container_resource_limits{resource=\"memory\",namespace=\"ce-membership-mx\",container=\"membership-prod\",mms_source=\"wcnp\"}) by (namespace,container)\n", "threshold": 0.3}, {"name": "Latency_P90", "query": "histogram_quantile(0.90, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{cluster_name=~\"outbound.*membership-prod-canary.ce-membership-mx.svc.cluster.local\",mms_source=\"wcnp\"}) by (le,cluster_id))\n", "threshold": 2000}, {"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(\n  rate(\n    kube_pod_container_status_restarts_total{\n      namespace=\"ce-membership-mx\",\n      job=~\"membership-prod-canary\"\n    }[1m]\n  )\n) or on() vector(0)\n", "threshold": 2}], "defaultMetrics": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "2048M", "max_memory": "2048M"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 40}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 30}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 60, "time_out": 10, "uri": "/server-health"}, "ready_probs": {"probe_interval": null, "wait": 30, "time_out": 10, "uri": "/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/ce-membership", "dynatrace_enabled": true}], "status": true}}, {"search-service-mx-prod": {"clusters": [{"pods": 5, "mm_name": "eus2-prod-a45", "namespace": "search-service-mx", "lbs": {"gslb": "search-service-mx-prod.search-service-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(rate(kube_pod_container_status_restarts_total{telemetry_walmart_com_metric_source=\"local\", namespace=\"search-service\",job=~\"search-service-prod-canary\"}[1m])) or on() vector(0)\n", "threshold": 2}, {"interval": "1m", "name": "Non 2xx rate", "query": "sum(envoy_cluster_upstream_rq:sum_rate2m{telemetry_walmart_com_metric_source=\"local\", cluster_name=~\"outbound.*search-service-prod-canary.search-service.svc.cluster.local\", response_code_class!=\"2xx\"})\n/\nsum(envoy_cluster_upstream_rq:sum_rate2m{telemetry_walmart_com_metric_source=\"local\", cluster_name=~\"outbound.*search-service-prod-canary.search-service.svc.cluster.local\"})\nor on() vector(0)\n", "threshold": 1}], "defaultMetrics": null}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "4096M", "max_memory": "4096M"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 5, "max": 30}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}, "ready_probs": {"probe_interval": 30, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/search-service", "dynatrace_enabled": false}], "status": true}, "search-service-mx-prod-primary": {"clusters": [{"pods": 25, "mm_name": "scus-prod-a63", "namespace": "search-service-mx", "lbs": {"gslb": "search-service-mx-prod.search-service-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(rate(kube_pod_container_status_restarts_total{telemetry_walmart_com_metric_source=\"local\", namespace=\"search-service\",job=~\"search-service-prod-canary\"}[1m])) or on() vector(0)\n", "threshold": 2}, {"interval": "1m", "name": "Non 2xx rate", "query": "sum(envoy_cluster_upstream_rq:sum_rate2m{telemetry_walmart_com_metric_source=\"local\", cluster_name=~\"outbound.*search-service-prod-canary.search-service.svc.cluster.local\", response_code_class!=\"2xx\"})\n/\nsum(envoy_cluster_upstream_rq:sum_rate2m{telemetry_walmart_com_metric_source=\"local\", cluster_name=~\"outbound.*search-service-prod-canary.search-service.svc.cluster.local\"})\nor on() vector(0)\n", "threshold": 1}], "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "750M", "max_memory": "750M"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 20}, "is_cpu_based_hpa": true, "scaling": {"min": 25, "max": 100}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}, "ready_probs": {"probe_interval": 10, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/search-service", "dynatrace_enabled": true}, {"pods": 25, "mm_name": "wus-prod-a27", "namespace": "search-service-mx", "lbs": {"gslb": "search-service-mx-prod.search-service-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "1m", "name": "kube_pod_container_status_restarts_total", "query": "sum(rate(kube_pod_container_status_restarts_total{telemetry_walmart_com_metric_source=\"local\", namespace=\"search-service\",job=~\"search-service-prod-canary\"}[1m])) or on() vector(0)\n", "threshold": 2}, {"interval": "1m", "name": "Non 2xx rate", "query": "sum(envoy_cluster_upstream_rq:sum_rate2m{telemetry_walmart_com_metric_source=\"local\", cluster_name=~\"outbound.*search-service-prod-canary.search-service.svc.cluster.local\", response_code_class!=\"2xx\"})\n/\nsum(envoy_cluster_upstream_rq:sum_rate2m{telemetry_walmart_com_metric_source=\"local\", cluster_name=~\"outbound.*search-service-prod-canary.search-service.svc.cluster.local\"})\nor on() vector(0)\n", "threshold": 1}], "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "750M", "max_memory": "750M"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 20}, "is_cpu_based_hpa": true, "scaling": {"min": 25, "max": 100}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}, "ready_probs": {"probe_interval": 10, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/search-service", "dynatrace_enabled": true}], "status": true}}, {"aroundme-prod-primary": {"clusters": [{"pods": 10, "mm_name": "scus-prod-a91", "namespace": "ce-around-me-mx", "lbs": {"gslb": "aroundme-prod.ce-around-me-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "1m", "name": "Kubernetes Container Restarts", "query": "kube_pod_container_status_restarts_total{namespace=\"ce-around-me-mx\",container=\"aroundme-prod\",pod!~\"aroundme-prod-primary.*\",mms_source=\"wcnp\"}\n", "threshold": 1}, {"name": "Envoy 5xxs rate", "query": "sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*aroundme-prod-canary.ce-around-me-mx.svc.cluster.local\",response_code_class=~\"5.*\",mms_source=\"wcnp\"}[2m]))\n  /\n  sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*aroundme-prod-canary.ce-around-me-mx.svc.cluster.local\",mms_source=\"wcnp\"}[2m])) * 100 or on() vector(0)\n", "threshold": 5}, {"name": "Envoy 4xxs rate", "query": "sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*aroundme-prod-canary.ce-around-me-mx.svc.cluster.local\",response_code_class=~\"4.*\",mms_source=\"wcnp\"}[2m]))\n  /\n  sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*aroundme-prod-canary.ce-around-me-mx.svc.cluster.local\",mms_source=\"wcnp\"}[2m])) * 100 or on() vector(0)\n", "threshold": 5}, {"name": "CPU Utilization ", "query": "sum(rate(container_cpu_usage_seconds_total{cluster_id=\"scus-prod-a91\",namespace=\"ce-around-me-mx\",container=\"aroundme-prod\",pod!~\"aroundme-prod-primary.*\",mms_source=\"wcnp\"}[2m])) / sum(kube_pod_container_resource_limits{resource=\"cpu\", cluster_id=\"scus-prod-a91\",namespace=\"ce-around-me-mx\",container=\"aroundme-prod\",pod!~\"aroundme-prod-primary.*\",mms_source=\"wcnp\"})\n", "threshold": 0.3}, {"name": "Memory Utilization ", "query": "max(container_memory_working_set_bytes{cluster_id=\"scus-prod-a91\",namespace=\"ce-around-me-mx\",container!=\"\",container!=\"POD\",mms_source=\"wcnp\"}\n  and on(pod) kube_pod_labels{cluster_id=\"scus-prod-a91\",label_app=\"aroundme-prod\",mms_source=\"wcnp\"}) by (namespace,container)\n  / \n  min(kube_pod_container_resource_limits{resource=\"memory\",namespace=\"ce-around-me-mx\",container=\"aroundme-prod\",mms_source=\"wcnp\"}) by (namespace,container)\n", "threshold": 0.3}, {"name": "Latency P90", "query": "histogram_quantile(0.90, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{cluster_name=~\"outbound.*aroundme-prod-canary.ce-around-me-mx.svc.cluster.local\",mms_source=\"wcnp\"}) by (le,cluster_id))\n", "threshold": 4000}], "defaultMetrics": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "2048Mi", "max_memory": "2048Mi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 40}, "is_cpu_based_hpa": true, "scaling": {"min": 10, "max": 90}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 120, "time_out": 2, "uri": "/.well-known/apollo/server-health"}, "ready_probs": {"probe_interval": 30, "wait": 120, "time_out": 2, "uri": "/.well-known/apollo/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/ce-around-me", "dynatrace_enabled": true}, {"pods": 10, "mm_name": "wus-prod-a6", "namespace": "ce-around-me-mx", "lbs": {"gslb": "aroundme-prod.ce-around-me-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "1m", "name": "Kubernetes Container Restarts", "query": "kube_pod_container_status_restarts_total{namespace=\"ce-around-me-mx\",container=\"aroundme-prod\",pod!~\"aroundme-prod-primary.*\",mms_source=\"wcnp\"}\n", "threshold": 1}, {"name": "Envoy 5xxs rate", "query": "sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*aroundme-prod-canary.ce-around-me-mx.svc.cluster.local\",response_code_class=~\"5.*\",mms_source=\"wcnp\"}[2m]))\n  /\n  sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*aroundme-prod-canary.ce-around-me-mx.svc.cluster.local\",mms_source=\"wcnp\"}[2m])) * 100 or on() vector(0)\n", "threshold": 5}, {"name": "Envoy 4xxs rate", "query": "sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*aroundme-prod-canary.ce-around-me-mx.svc.cluster.local\",response_code_class=~\"4.*\",mms_source=\"wcnp\"}[2m]))\n  /\n  sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*aroundme-prod-canary.ce-around-me-mx.svc.cluster.local\",mms_source=\"wcnp\"}[2m])) * 100 or on() vector(0)\n", "threshold": 5}, {"name": "CPU Utilization ", "query": "sum(rate(container_cpu_usage_seconds_total{cluster_id=\"wus-prod-a6\",namespace=\"ce-around-me-mx\",container=\"aroundme-prod\",pod!~\"aroundme-prod-primary.*\",mms_source=\"wcnp\"}[2m])) / sum(kube_pod_container_resource_limits{resource=\"cpu\", cluster_id=\"wus-prod-a6\",namespace=\"ce-around-me-mx\",container=\"aroundme-prod\",pod!~\"aroundme-prod-primary.*\",mms_source=\"wcnp\"})\n", "threshold": 0.3}, {"name": "Memory Utilization ", "query": "max(container_memory_working_set_bytes{cluster_id=\"wus-prod-a6\",namespace=\"ce-around-me-mx\",container!=\"\",container!=\"POD\",mms_source=\"wcnp\"}\n  and on(pod) kube_pod_labels{cluster_id=\"wus-prod-a6\",label_app=\"aroundme-prod\",mms_source=\"wcnp\"}) by (namespace,container)\n  / \n  min(kube_pod_container_resource_limits{resource=\"memory\",namespace=\"ce-around-me-mx\",container=\"aroundme-prod\",mms_source=\"wcnp\"}) by (namespace,container)\n", "threshold": 0.3}, {"name": "Latency P90", "query": "histogram_quantile(0.90, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{cluster_name=~\"outbound.*aroundme-prod-canary.ce-around-me-mx.svc.cluster.local\",mms_source=\"wcnp\"}) by (le,cluster_id))\n", "threshold": 4000}], "defaultMetrics": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "2048Mi", "max_memory": "2048Mi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 40}, "is_cpu_based_hpa": true, "scaling": {"min": 10, "max": 90}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 120, "time_out": 2, "uri": "/.well-known/apollo/server-health"}, "ready_probs": {"probe_interval": 30, "wait": 120, "time_out": 2, "uri": "/.well-known/apollo/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/ce-around-me", "dynatrace_enabled": true}], "status": true}}, {"mx-sng-app-prod-primary": {"clusters": [{"pods": 2, "mm_name": "eus2-prod-a5", "namespace": "mx-scanandgo", "lbs": {"gslb": "mx-sng-app-prod.mx-scanandgo.k8s.glb.us.walmart.net", "custom_gslb": "prod.mx-sng.glb.us.walmart.net"}, "canary": {"enabled": true, "experiments": [{"interval": "2m", "name": "<PERSON><PERSON><PERSON><PERSON>_Container_Restarts", "query": "kube_pod_container_status_restarts_total{namespace=\"mx-scanandgo\",container=\"mx-sng-app-prod\",pod!~\"mx-sng-app-prod-primary.*\",mms_source=\"wcnp\"}\n", "threshold": 1}, {"name": "Envoy_5xxs_rate", "query": "sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*mx-sng-app-prod-canary.mx-scanandgo.svc.cluster.local\",response_code_class=~\"5.*\",mms_source=\"wcnp\"}[2m]))\n/\nsum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*mx-sng-app-prod-canary.mx-scanandgo.svc.cluster.local\",mms_source=\"wcnp\"}[2m])) * 100 or on() vector(0)\n", "threshold": 5}, {"name": "CPU_Utilization", "query": "(max(rate(container_cpu_usage_seconds_total{mms_source=\"wcnp\",namespace=\"mx-scanandgo\",container=~\"mx-sng-app-prod\",container!=\"\",container!=\"POD\",container!~\"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|.*default-tester\",pod!~\"mx-sng-app-prod-primary.*\"}[2m])) by (namespace,pod,container,cluster_id) / min(kube_pod_container_resource_limits{resource=\"cpu\",mms_source=\"wcnp\",namespace=\"mx-scanandgo\"}) by (namespace,pod,container,cluster_id) * 100 )\n", "threshold": 70}, {"name": "Memory_Utilization", "query": "max(container_memory_working_set_bytes{cluster_id=\"eus2-prod-a5\",namespace=\"mx-scanandgo\",container!=\"\",container!=\"POD\",mms_source=\"wcnp\"}\nand on(pod) kube_pod_labels{cluster_id=\"eus2-prod-a5\",label_app=\"mx-sng-app-prod\",mms_source=\"wcnp\"}) by (namespace,container)\n/ \nmin(kube_pod_container_resource_limits{resource=\"memory\",namespace=\"mx-scanandgo\",container=\"mx-sng-app-prod\",mms_source=\"wcnp\"}) by (namespace,container)\n", "threshold": 0.7}, {"name": "Latency_P90", "query": "histogram_quantile(0.90, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{cluster_name=~\"outbound.*mx-sng-app-prod-canary.mx-scanandgo.svc.cluster.local\",mms_source=\"wcnp\"}) by (le,cluster_id))\n", "threshold": 5000}], "defaultMetrics": false}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": "500m", "max_cpu": 2, "min_memory": "1Gi", "max_memory": "3Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 2, "max": 2}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 150, "time_out": 5, "uri": "/mx-sng-app/health"}, "ready_probs": {"probe_interval": null, "wait": 150, "time_out": 5, "uri": "/mx-sng-app/pods-health"}}, "git": "https://gecgithub01.walmart.com/Walmart-International/mx-scanandgo-develop", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Druntime.context.environmentType=prod -Druntime.context.appName=mx-sng-app -Druntime.context.cloud=wus-prod -Druntime.context.system.property.override.enabled=true -Dscm.snapshot.enabled=true -Duser.timezone=Mexico/General -Dcom.walmart.platform.metrics.impl.type=MICROMETER -Dlog4j2.formatMsgNoLookups=true -Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dcom.walmart.platform.txnmarking.otel.type=LOGGING -Dscm.server.url=http://tunr.prod.walmart.com/scm-app/v2 -Dcom.walmart.platform.telemetry.otel.enabled=true -Djsse.enableSNIExtension=true -Dscm.root.dir=/tmp/scm -Dccm.configs.dir=/etc/config -Dexternal.configs.source.dir=/etc/secrets", "is_g1_gc": false}}, {"pods": 2, "mm_name": "scus-prod-a14", "namespace": "mx-scanandgo", "lbs": {"gslb": "mx-sng-app-prod.mx-scanandgo.k8s.glb.us.walmart.net", "custom_gslb": "prod.mx-sng.glb.us.walmart.net"}, "canary": {"enabled": true, "experiments": [{"interval": "2m", "name": "<PERSON><PERSON><PERSON><PERSON>_Container_Restarts", "query": "kube_pod_container_status_restarts_total{namespace=\"mx-scanandgo\",container=\"mx-sng-app-prod\",pod!~\"mx-sng-app-prod-primary.*\",mms_source=\"wcnp\"}\n", "threshold": 1}, {"name": "Envoy_5xxs_rate", "query": "sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*mx-sng-app-prod-canary.mx-scanandgo.svc.cluster.local\",response_code_class=~\"5.*\",mms_source=\"wcnp\"}[2m]))\n/\nsum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*mx-sng-app-prod-canary.mx-scanandgo.svc.cluster.local\",mms_source=\"wcnp\"}[2m])) * 100 or on() vector(0)\n", "threshold": 5}, {"name": "CPU_Utilization", "query": "(max(rate(container_cpu_usage_seconds_total{mms_source=\"wcnp\",namespace=\"mx-scanandgo\",container=~\"mx-sng-app-prod\",container!=\"\",container!=\"POD\",container!~\"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|.*default-tester\",pod!~\"mx-sng-app-prod-primary.*\"}[2m])) by (namespace,pod,container,cluster_id) / min(kube_pod_container_resource_limits{resource=\"cpu\",mms_source=\"wcnp\",namespace=\"mx-scanandgo\"}) by (namespace,pod,container,cluster_id) * 100 )\n", "threshold": 70}, {"name": "Memory_Utilization", "query": "max(container_memory_working_set_bytes{cluster_id=\"scus-prod-a14\",namespace=\"mx-scanandgo\",container!=\"\",container!=\"POD\",mms_source=\"wcnp\"}\nand on(pod) kube_pod_labels{cluster_id=\"scus-prod-a14\",label_app=\"mx-sng-app-prod\",mms_source=\"wcnp\"}) by (namespace,container)\n/ \nmin(kube_pod_container_resource_limits{resource=\"memory\",namespace=\"mx-scanandgo\",container=\"mx-sng-app-prod\",mms_source=\"wcnp\"}) by (namespace,container)\n", "threshold": 0.7}, {"name": "Latency_P90", "query": "histogram_quantile(0.90, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{cluster_name=~\"outbound.*mx-sng-app-prod-canary.mx-scanandgo.svc.cluster.local\",mms_source=\"wcnp\"}) by (le,cluster_id))\n", "threshold": 5000}], "defaultMetrics": false}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": 1, "max_cpu": 4, "min_memory": "3Gi", "max_memory": "6Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 60}, "is_cpu_based_hpa": true, "scaling": {"min": 2, "max": 4}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 150, "time_out": 5, "uri": "/mx-sng-app/health"}, "ready_probs": {"probe_interval": null, "wait": 150, "time_out": 5, "uri": "/mx-sng-app/pods-health"}}, "git": "https://gecgithub01.walmart.com/Walmart-International/mx-scanandgo-develop", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Druntime.context.environmentType=prod -Druntime.context.appName=mx-sng-app -Druntime.context.cloud=scus-prod -Druntime.context.system.property.override.enabled=true -Dscm.snapshot.enabled=true -Duser.timezone=Mexico/General -Dcom.walmart.platform.metrics.impl.type=MICROMETER -Dlog4j2.formatMsgNoLookups=true -Dio.strati.RuntimeContext=io.strati.impl.runtime.context.RuntimeContextEnv -Dcom.walmart.platform.txnmarking.otel.type=LOGGING -Dscm.server.url=http://tunr.prod.walmart.com/scm-app/v2 -Dcom.walmart.platform.telemetry.otel.enabled=true -Djsse.enableSNIExtension=true -Dscm.root.dir=/tmp/scm -Dccm.configs.dir=/etc/config -Dexternal.configs.source.dir=/etc/secrets", "is_g1_gc": false}}], "status": true}}, {"content-layout-service-prod-primary": {"clusters": [{"pods": 42, "mm_name": "scus-prod-a61", "namespace": "content-layout-service-mx", "lbs": {"gslb": "content-layout-service-prod.content-layout-service-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "2048M", "max_memory": "2048M"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 20}, "is_cpu_based_hpa": true, "scaling": {"min": 25, "max": 100}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}, "ready_probs": {"probe_interval": 10, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/content-layout-service", "dynatrace_enabled": true}, {"pods": 38, "mm_name": "wus-prod-a58", "namespace": "content-layout-service-mx", "lbs": {"gslb": "content-layout-service-prod.content-layout-service-mx.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "defaultMetrics": true}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "2048M", "max_memory": "2048M"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 20}, "is_cpu_based_hpa": true, "scaling": {"min": 25, "max": 100}}}, "prob": {"liveness_probs": {"probe_interval": 30, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}, "ready_probs": {"probe_interval": 10, "wait": 60, "time_out": 10, "uri": "/.well-known/apollo/server-health"}}, "git": "https://gecgithub01.walmart.com/ce-orchestration/content-layout-service", "dynatrace_enabled": true}], "status": true}}, {"status-upd-ms-prod": {"clusters": [{"pods": 6, "mm_name": "scus-prod-a67", "namespace": "intl-fms-status-update-ca", "lbs": {"gslb": "status-upd-ms-prod.intl-fms-status-update-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 2, "max_cpu": 2, "min_memory": "6Gi", "max_memory": "6Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 55}, "is_cpu_based_hpa": true, "scaling": {"min": 6, "max": 6}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/health"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/health"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-status-update-ms", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Xms4000m -Xmx4000m -Dspring.profiles.active=CA -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=CA -Druntime.context.environmentType=prod -Druntime.context.appName=fulfillment-status-update-ms -Druntime.context.appVersion=0.0.3270 -Druntime.context.system.property.override.enabled=true -Dscm.server.access.enabled=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Djsse.enableSNIExtension=true -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=scus.kafka.medusa.prod.walmart.com:9092 -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap", "is_g1_gc": false}}], "status": true}}, {"learning-search-prod": {"clusters": [{"pods": 4, "mm_name": "uscentral-prod-az-021", "namespace": "learning-search"}, {"pods": 4, "mm_name": "useast-prod-az-008", "namespace": "learning-search"}, {"pods": 4, "mm_name": "uswest-prod-az-024", "namespace": "learning-search"}], "status": true}}, {"fo-update-ms-prod": {"clusters": [{"pods": 6, "mm_name": "eus2-prod-a60", "namespace": "intl-fms-fo-update-ca", "lbs": {"gslb": "fo-update-ms-prod.intl-fms-fo-update-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "2Gi", "max_memory": "2Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 55}, "is_cpu_based_hpa": true, "scaling": {"min": 2, "max": 3}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-order-update-ms", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=CA -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=CA -Druntime.context.environmentType=prod -Druntime.context.appName=fulfillment-order-update-ms -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Druntime.context.appVersion=0.0.519 -Druntime.context.system.property.override.enabled=true -Dscm.server.access.enabled=true -Djsse.enableSNIExtension=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=eus2.kafka.medusa.prod.walmart.com:9092", "is_g1_gc": false}}, {"pods": 2, "mm_name": "scus-prod-a67", "namespace": "intl-fms-fo-update-ca", "lbs": {"gslb": "fo-update-ms-prod.intl-fms-fo-update-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "2Gi", "max_memory": "2Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 55}, "is_cpu_based_hpa": true, "scaling": {"min": 2, "max": 3}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-order-update-ms", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=CA -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=CA -Druntime.context.environmentType=prod -Druntime.context.appName=fulfillment-order-update-ms -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Druntime.context.appVersion=0.0.519 -Druntime.context.system.property.override.enabled=true -Dscm.server.access.enabled=true -Djsse.enableSNIExtension=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=scus.kafka.medusa.prod.walmart.com:9092 -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap", "is_g1_gc": false}}], "status": true}}, {"scheduler-ms-prod": {"clusters": [{"pods": 2, "mm_name": "eus2-prod-a60", "namespace": "intl-fms-scheduler-ca", "lbs": {"gslb": "scheduler-ms-prod.intl-fms-scheduler-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "6Gi", "max_memory": "6Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 55}, "is_cpu_based_hpa": true, "scaling": {"min": 2, "max": 3}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-scheduler-ms", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=CA -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=CA -Druntime.context.environmentType=prod -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Druntime.context.appName=fulfillment-scheduler-ms -Druntime.context.appVersion=0.0.373 -Druntime.context.system.property.override.enabled=true -Dscm.server.access.enabled=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Djsse.enableSNIExtension=true -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=eus2.kafka.medusa.prod.walmart.com:9092", "is_g1_gc": false}}, {"pods": 2, "mm_name": "scus-prod-a67", "namespace": "intl-fms-scheduler-ca", "lbs": {"gslb": "scheduler-ms-prod.intl-fms-scheduler-ca.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 1, "max_cpu": 1, "min_memory": "6Gi", "max_memory": "6Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 55}, "is_cpu_based_hpa": true, "scaling": {"min": 2, "max": 3}}}, "prob": {"liveness_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}, "ready_probs": {"probe_interval": 60, "wait": 120, "time_out": 10, "uri": "/app/healthCheck"}}, "git": "https://gecgithub01.walmart.com/fulfillment-core-services/fulfillment-scheduler-ms", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=CA -Dcom.walmart.platform.config.runOnEnv=prod -Druntime.context.environment=CA -Druntime.context.environmentType=prod -Dscm.scope.template=/environment/cloudEnvironment/cloud/node -Druntime.context.appName=fulfillment-scheduler-ms -Druntime.context.appVersion=0.0.373 -Druntime.context.system.property.override.enabled=true -Dscm.server.access.enabled=true -Dscm.snapshot.enabled=true -Dscm.root.dir=/tmp/scm -Djsse.enableSNIExtension=true -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=false -Dcom.walmart.platform.logging.input.level=INFO -Dcom.walmart.platform.metrics.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.logfile.path=/dev/stdout -Dcom.walmart.platform.txnmarking.logfile.path=/dev/stdout -Dcom.walmart.platform.logging.file.format=V4Json -Dcom.walmart.platform.logging.kafka.brokerList=null -Dcom.walmart.platform.metrics.kafka.brokerList=scus.kafka.medusa.prod.walmart.com:9092 -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap", "is_g1_gc": false}}], "status": true}}, {"ca-ims-mfc-inventory-adp": {"clusters": [{"pods": 1, "mm_name": "eus2-prod-a66", "namespace": "ca-ims-adaptor", "lbs": {"gslb": "ca-ims-mfc-inventory-adp-prod.ca-ims-adaptor.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": false, "defaultMetrics": null}, "scaling": {"vertical": {"is_enabled": false, "min_cpu": 0.5, "max_cpu": 1, "min_memory": "2Gi", "max_memory": "2Gi"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 50}, "is_cpu_based_hpa": true, "scaling": {"min": 2, "max": 6}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 60, "time_out": 10, "uri": -1}, "ready_probs": {"probe_interval": null, "wait": 60, "time_out": 10, "uri": "/actuator/health"}}, "git": "https://gecgithub01.walmart.com/ca-gm/ca-ims-mfc-inventory-adp", "dynatrace_enabled": false, "jvm": {"JAVA_OPTS": "-Dspring.profiles.active=prod -Dccm.configs.dir=/etc/config -Dsecrets.home=/etc/secrets -Dmanagement.endpoint.prometheus.enabled=true -Dmanagement.endpoints.web.exposure.include=*", "is_g1_gc": false}}], "status": true}}, {"nostra-primary": {"clusters": [{"pods": 2, "mm_name": "scus-prod-a38", "namespace": "mx-pns-nostra", "lbs": {"gslb": "nostra-prod.mx-pns-nostra.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "2m", "name": "<PERSON><PERSON><PERSON><PERSON>_Container_Restarts", "query": "kube_pod_container_status_restarts_total{namespace=\"mx-pns-nostra\",container=\"nostra\",pod!~\"nostra-primary.*\",mms_source=\"wcnp\"}\n", "threshold": 0}, {"name": "Envoy_5xx_rate", "query": "sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*nostra-canary.mx-pns-nostra.svc.cluster.local\",response_code_class=~\"5.*\",mms_source=\"wcnp\"}[2m]))\n/\nsum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*nostra-canary.mx-pns-nostra.svc.cluster.local\",mms_source=\"wcnp\"}[2m])) * 100 or on() vector(0)\n", "threshold": 0}, {"name": "CPU_Utilization", "query": "(max(rate(container_cpu_usage_seconds_total{mms_source=\"wcnp\",namespace=\"mx-pns-nostra\",container=~\"nostra\",container!=\"\",container!=\"POD\",\ncontainer!~\"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|.*default-tester\",pod!~\"nostra-primary.*\"}[2m])) \nby (namespace,pod,container,cluster_id) / min(kube_pod_container_resource_limits{resource=\"cpu\",mms_source=\"wcnp\",namespace=\"mx-pns-nostra\"}) by (namespace,pod,container,cluster_id) * 100 )\n", "threshold": 30}, {"name": "Memory_Utilization", "query": "max(container_memory_working_set_bytes{cluster_id=\"scus-prod-a38\",namespace=\"mx-pns-nostra\",container!=\"\",container!=\"POD\",mms_source=\"wcnp\"}\nand on(pod) kube_pod_labels{cluster_id=\"scus-prod-a38\",label_app=\"nostra\",mms_source=\"wcnp\"}) by (namespace,container)\n/ \nmin(kube_pod_container_resource_limits{resource=\"memory\",namespace=\"mx-pns-nostra\",container=\"nostra\",mms_source=\"wcnp\"}) by (namespace,container)\n", "threshold": 0.3}, {"name": "Latency_P90", "query": "histogram_quantile(0.90, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{cluster_name=~\"outbound.*nostra-canary.mx-pns-nostra.svc.cluster.local\",mms_source=\"wcnp\"}) by (le,cluster_id))\n", "threshold": 2000}], "defaultMetrics": false}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": 1, "max_cpu": 4, "min_memory": "4G", "max_memory": "12G"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 2, "max": 2}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 240, "time_out": 10, "uri": "/nostra/health"}, "ready_probs": {"probe_interval": null, "wait": 240, "time_out": 10, "uri": "/nostra/health"}}, "git": "https://gecgithub01.walmart.com/MexicoEcomm/nostra", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Xmx4096m -Dspring.profiles.active=prod -Dcom.walmart.platform.config.runOnEnv=prod -Drun.jvmArguments=\"-Xdebug\" -Djsse.enableSNIExtension=true -Dhttps.protocols=\"TLSv1.2\" -Dcom.walmart.platform.config.appName=nostra -Dscm.scope.template=/environment/dataCenter/node -Druntime.context.appName=nostra -Druntime.context.appVersion=0.0.145 -Druntime.context.environmentType=prod -Druntime.context.environment=prod -Druntime.context.cloud=wcnp-scus-prod-a38 -Dscm.server.access.enabled=true -Druntime.context.system.property.override.enabled=true -Dcom.walmart.platform.logging.file.enabled=false -Dcom.walmart.platform.logging.file.path=/dev/null -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=true -Dscm.server.url=http://tunr.prod.walmart.com/scm-app/v2 -Dcom.walmart.platform.txnmarking.logging.enabled=true -Dcom.walmart.platform.logging.profile=CONSOLE", "is_g1_gc": false}}, {"pods": 2, "mm_name": "useast-prod-az-021", "namespace": "mx-pns-nostra", "lbs": {"gslb": "nostra-prod.mx-pns-nostra.k8s.glb.us.walmart.net", "custom_gslb": null}, "canary": {"enabled": true, "experiments": [{"interval": "2m", "name": "<PERSON><PERSON><PERSON><PERSON>_Container_Restarts", "query": "kube_pod_container_status_restarts_total{namespace=\"mx-pns-nostra\",container=\"nostra\",pod!~\"nostra-primary.*\",mms_source=\"wcnp\"}\n", "threshold": 0}, {"name": "Envoy_5xx_rate", "query": "sum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*nostra-canary.mx-pns-nostra.svc.cluster.local\",response_code_class=~\"5.*\",mms_source=\"wcnp\"}[2m]))\n/\nsum(rate(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~\"outbound.*nostra-canary.mx-pns-nostra.svc.cluster.local\",mms_source=\"wcnp\"}[2m])) * 100 or on() vector(0)\n", "threshold": 0}, {"name": "CPU_Utilization", "query": "(max(rate(container_cpu_usage_seconds_total{mms_source=\"wcnp\",namespace=\"mx-pns-nostra\",container=~\"nostra\",container!=\"\",container!=\"POD\",\ncontainer!~\"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|.*default-tester\",pod!~\"nostra-primary.*\"}[2m])) \nby (namespace,pod,container,cluster_id) / min(kube_pod_container_resource_limits{resource=\"cpu\",mms_source=\"wcnp\",namespace=\"mx-pns-nostra\"}) by (namespace,pod,container,cluster_id) * 100 )\n", "threshold": 30}, {"name": "Memory_Utilization", "query": "max(container_memory_working_set_bytes{cluster_id=\"useast-prod-az-021\",namespace=\"mx-pns-nostra\",container!=\"\",container!=\"POD\",mms_source=\"wcnp\"}\nand on(pod) kube_pod_labels{cluster_id=\"useast-prod-az-021\",label_app=\"nostra\",mms_source=\"wcnp\"}) by (namespace,container)\n/ \nmin(kube_pod_container_resource_limits{resource=\"memory\",namespace=\"mx-pns-nostra\",container=\"nostra\",mms_source=\"wcnp\"}) by (namespace,container)\n", "threshold": 0.3}, {"name": "Latency_P90", "query": "histogram_quantile(0.90, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{cluster_name=~\"outbound.*nostra-canary.mx-pns-nostra.svc.cluster.local\",mms_source=\"wcnp\"}) by (le,cluster_id))\n", "threshold": 2000}], "defaultMetrics": false}, "scaling": {"vertical": {"is_enabled": true, "min_cpu": 1, "max_cpu": 4, "min_memory": "4G", "max_memory": "12G"}, "horizontal": {"is_enabled": true, "hpa": {"criteria": "cpuPer<PERSON>", "target": 70}, "is_cpu_based_hpa": true, "scaling": {"min": 2, "max": 2}}}, "prob": {"liveness_probs": {"probe_interval": null, "wait": 240, "time_out": 10, "uri": "/nostra/health"}, "ready_probs": {"probe_interval": null, "wait": 240, "time_out": 10, "uri": "/nostra/health"}}, "git": "https://gecgithub01.walmart.com/MexicoEcomm/nostra", "dynatrace_enabled": true, "jvm": {"JAVA_OPTS": "-Xmx4096m -Dspring.profiles.active=prod -Dcom.walmart.platform.config.runOnEnv=prod -Drun.jvmArguments=\"-Xdebug\" -Djsse.enableSNIExtension=true -Dhttps.protocols=\"TLSv1.2\" -Dcom.walmart.platform.config.appName=nostra -Dscm.scope.template=/environment/dataCenter/node -Druntime.context.appName=nostra -Druntime.context.appVersion=0.0.145 -Druntime.context.environmentType=prod -Druntime.context.environment=prod -Druntime.context.cloud=wcnp-eus-prod -Dscm.server.access.enabled=true -Druntime.context.system.property.override.enabled=true -Dcom.walmart.platform.logging.file.enabled=false -Dcom.walmart.platform.logging.file.path=/dev/null -Dcom.walmart.platform.logging.kafka.topic.rawlog.enabled=true -Dscm.server.url=http://tunr.prod.walmart.com/scm-app/v2 -Dcom.walmart.platform.txnmarking.logging.enabled=true -Dcom.walmart.platform.logging.profile=CONSOLE", "is_g1_gc": false}}], "status": true}}]}