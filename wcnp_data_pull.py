import os
import logging
import json
from ruamel import yaml
import yaml as ryaml
from collections import OrderedDict

logger = logging.getLogger(__name__)
ALERT_TYPE = {"app-owner", "sre"}
CONVERT_NAME = {"alert_team": "alert_team_name", "mms_xmatters_group": "xmatters_group",
                "mms_slack_channel": "slack_channel", "mms_email": "team_email"}
THRESHOLDS = "_threshold_"
THRESHOLDS_KEY = "thresholds"
PREFIX = "international-tech/intl-sre/golden-signals/rules/production"


# extract labels and threshold
class ExtractFileFields:
    def __init__(self):
        self.result = dict()

    def load_files(self, _path):
        files = OrderedDict()

        files_list = os.listdir(_path)
        for each_file in files_list:
            files[each_file] = self.read_yaml(f"{_path}/{each_file}")

        return files

    def read_yaml(self, _yaml_path):
        values = OrderedDict()

        try:
            logger.info(f"Reading yaml file {_yaml_path}")
            file_exists = os.path.isfile(_yaml_path)
            if not file_exists:
                logger.warning(f"{_yaml_path} file not found ")
                return values
            values = yaml.safe_load(open(_yaml_path))
        except yaml.YAMLError as exc:
            logger.exception(exc)
            return values
        if values is None:
            return OrderedDict()

        return values

    def convert_to_json_file(self, alert_type):
        list1 = list(self.result.values())
        file_name = alert_type + "_data.json"
        with open(file_name, 'w') as f:
            json.dump(list1, f,indent=4, sort_keys=True,ensure_ascii=False)

    # extract fields from files
    def extract_fields(self, _alert_file, _type, _service, _keys):
        print(f"Scanning file: {_alert_file}")
        file_name = _alert_file.rsplit("/", 1)[1].split(".")[0]
        file_content = self.read_yaml(_alert_file)

        # to avoid some files have illegal chars
        if len(file_content) > 0:
            app = dict()
            thresholds = dict()

            for rule in file_content['groups'][0]['rules']:
                for key, val in rule['labels'].items():
                    # don't check THRESHOLDS keys
                    if THRESHOLDS in key:
                        thresholds[key] = int(val)

                    if key in _keys:
                        # update and convert if needed
                        if key in CONVERT_NAME.keys():
                            app[CONVERT_NAME[key]] = val
                        else:
                            app[key] = val

            if len(thresholds) > 0:
                app[THRESHOLDS_KEY] = thresholds
            self.result[file_name] = app

            # if _service == "wcnp":
            #     self.result[file_name]["git_path"] = f"{PREFIX}/wcnp/{_type}-alerts"
            # else:
            #     self.result[file_name]["git_path"] = f"{PREFIX}/managed-services/{_service}/{_type}-alerts"

    def extract_fields_files(self, path, keys):
        service = os.path.basename(os.path.normpath(path))
        # get sre/app-owners folder
        alert_type_list = [file.name for file in os.scandir(path) if file.is_dir()]
        for alert_type in alert_type_list:
            # sre or app-owner
            _type = alert_type.rsplit("-", 1)
            if _type[0] in ALERT_TYPE:
                # go inside alert file dir and scan all files
                alerts_file_list = os.listdir(f"{path}/{alert_type}")
                print(f"Scanning files under Service: {path} with Alert type: {alert_type}, "
                      f"files count: {len(alerts_file_list)}")
                for alert_file in alerts_file_list:
                    self.extract_fields(f"{path}/{alert_type}/{alert_file}", _type[0], service, keys)

            # convert to json and clear old results
            self.convert_to_json_file(_type[0])
            self.result = dict()

    def create_inventory_files(self, input_json, template_name):
        json_file = open(input_json)
        json_array = json.load(json_file)
        file_name = ""
        result = dict()

        for item in json_array:
            if "app_name" in item:
                val = item["app_name"]
                git_path = item["git_path"]

                if "sre" in os.path.basename(os.path.normpath(git_path)):
                    file_name = f"sre_{template_name}_alerts.yaml"
                else:
                    file_name = f"app_owner_{template_name}_alerts.yaml"

                result[val] = item

        # save
        f = open(os.getcwd() + "/" + file_name, 'w')
        ryaml.dump(result, f, default_flow_style=False, sort_keys=False, explicit_start=True)


if __name__ == "__main__":
    # alert_files_path = "/Users/<USER>/git/mms-config/international-tech/intl-sre/golden-signals/rules" \
    #                    "/production/wcnp"
    # filter_keys = { "alert_team", "mms_slack_channel", "mms_email", "mms_xmatters_group", "namespace", "app_name",
    #                 "tier", "market"}
    # update = ExtractFileFields()
    # update.extract_fields_files(alert_files_path, filter_keys)
    # update.create_inventory_files("/Users/<USER>/git/intl-sre-scripts/sre_data.json", "wcnp")
    import requests,json

    data = {'images': ['00004549688377L2.jpg', '00004549688377L3.jpg', '00004549688377L4.jpg', '00004549688377L.jpg',
                       '00007891717024L1.jpg', '00007891717024L2.jpg', '00007891717024L4.jpg', '00007891717024L.jpg',
                       '00007891717025L1.jpg', '00007891717025L2.jpg'], 'sleep_interval': 50, 'banner': 'od'}
    headers = {'Content-type': 'application/json', 'User-Agent': 'WMTPerformance'}
    URL = "http://127.0.0.1:8000/api/sre/flush/v1/pipeline/"
    resp = requests.post(URL, json=data, headers=headers,verify=False)
    print(resp)
