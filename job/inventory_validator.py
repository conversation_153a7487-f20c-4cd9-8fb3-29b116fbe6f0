import logging, os, time
from libs import shell
import uuid, yaml, settings
import concurrent.futures
from libs.shell import bash
from git_service.git import Git
from git_service import exceptions
from collections import defaultdict
from settings import MMS_REPO_URL, MMS_REPO_NAME, MMS_ORG_NAME, SRE_REPO_NAME, SRE_REPO_URL, SRE_ORG_NAME
from common.alert.update_threshold import RepoProcessor
from template_engine.manager import TemplateManager
from template_engine.compile import get_latest_templates, get_template_variables_and_default_vars
from template_engine.engine import TemplateEngine

SRE_INVENTORY_DIR = "data_store/inventory"
ALLOWED_SERVICES = {"wcnp", "cosmos", "meghacache", "cassandra", "sql", "kafka", "solr", "oneops", "oracle"}
logger = logging.getLogger(__name__)


def service_mapper(service, k, v):
    try:
        template_vars = get_template_variables_and_default_vars(f"{service}_alerts.yaml")
        if template_vars is None:
            raise ValueError("Template variables not found")

        default_vars = template_vars.get('default_vars')
        if default_vars is None:
            raise ValueError("Default variables not found in template")

        file_name = default_vars.get("file_name")
        if file_name is None:
            raise ValueError("File name not found in default variables")

        file_name = file_name.replace('.yaml', '')
        file_name = file_name.format(**v)

        return file_name
    except KeyError as e:
        return f"Missing key in variables: {e}"
    except ValueError as e:
        return str(e)
    except Exception as e:
        return f"An error occurred: {e}"


class InventoryValidator:
    def __init__(self,working_folder=None):
        if working_folder:
            self.working_folder = working_folder
        else:
            self.working_folder = os.path.join(settings.TEMPLATE_BASE_DIR, "sre-alert-templates", SRE_INVENTORY_DIR)

    def validate(self):
        if not self.inventory:
            return False
        return True

    def inventory(self):

        files = self.list_all_files()
        mapper = {}

        for file in files:

            path = file.split("/")

            if len(path) > 1:
                file_name = path[-1]
                # service name is of form {service}_alerts
                service = path[-2].split("_alerts")[0]
            else:
                continue
            if service not in ALLOWED_SERVICES:
                continue
            if service not in mapper:
                mapper[service] = [file_name]
            else:
                mapper[service].append(file_name)

        return mapper

    def list_all_files(self):

        cmd_line = f"find {self.working_folder} -type f"
        alert_files = bash(cmd_line, True)
        alert_files = alert_files.stdout
        if len(alert_files) > 0:
            return alert_files
        else:
            raise exceptions.GitBashCommandFailedToExecute(f"No files founded: {self.working_folder}")

    def get_file_path(self, service, file_name):
        prefix = os.path.join(self.working_folder, service + '_' + "alerts")
        file_with_path = os.path.join(prefix, file_name)
        return file_with_path

    def validate_service(self, service, files=None):

        get_latest_templates(True)
        result = {}
        if service not in ALLOWED_SERVICES:
            raise ValueError(f"Service '{service}' is not allowed.")

        inv_files = self.inventory()[service]

        if not files:
            files = inv_files

        if not all(file in inv_files for file in files):
            raise ValueError(f"Invalid file names: {files}")

        for file in files:
            error_apps = self.validate_file(service, self.get_file_path(service, file))
            if error_apps:
                result[file] = error_apps

        return result

    def validate_file(self, service, file_path):
    # Load YAML file from path
        result = []
        try:
            with open(file_path, 'r') as file:
                inv_file = yaml.safe_load(file)
        except Exception as e:
            return [str(e)]

        # Get template mandatory variables
        template_vars = get_template_variables_and_default_vars(f"{service}_alerts.yaml")
        mandatory_vars = template_vars.get("mandatory_vars")
        file_name = template_vars.get('default_vars').get("file_name")
        # Ensure inv_file and mandatory_vars are not None
        if inv_file is None or mandatory_vars is None:
            return ["Invalid inventory file or mandatory variables"]

        file_name = file_name.replace('.yaml', '')
        # Check if all mandatory variables are present in the file for each key
        for key, item in inv_file.items():
            if item is None:
                result.append(f"{key} : item is None")
                continue

            missing_vars = [var for var in mandatory_vars if var not in item]
            if missing_vars:
                result.append(f"Missing variables for {key}: {', '.join(missing_vars)}")

            try:
                valid_key = file_name.format(**item)
            except KeyError as e:
                result.append(f"Missing key in item for {key}: {e}")
                continue

            # Check if the modified file_name matches the key
            if valid_key != key:
                result.append(f"key should be {valid_key} but found {key}")


        return result


if __name__ == "__main__":
    iv = InventoryValidator()
    res = iv.validate_service("meghacache")
    print(res.keys())

    with open("mega.yaml", "w") as f:
        yaml.dump(res, f)

    res = iv.validate_service("cosmos")
    print(res.keys())

    with open("cosmos.yaml", "w") as f:
        yaml.dump(res, f)

    res = iv.validate_service("wcnp")
    print(res.keys())

    with open("wcnp.yaml", "w") as f:
        yaml.dump(res, f)

    res = iv.validate_service("cassandra")
    print(res.keys())

    with open("cassandra.yaml", "w") as f:
        yaml.dump(res, f)

    res = iv.validate_service("sql")
    print(res.keys())

    with open("sql.yaml", "w") as f:
        yaml.dump(res, f)

    res = iv.validate_service("kafka")
    print(res.keys())

    with open("kafka.yaml", "w") as f:
        yaml.dump(res, f)

    res = iv.validate_service("solr")
    print(res.keys())

    with open("solr.yaml", "w") as f:
        yaml.dump(res, f)

    res = iv.validate_service("oneops")
    print(res.keys())

    with open("oneops.yaml", "w") as f:
        yaml.dump(res, f)

    res = iv.validate_service("oracle")
    print(res.keys())

    with open("oracle.yaml", "w") as f:
        yaml.dump(res, f)


   