import logging, os, time
from libs import shell
import uuid, yaml, settings
import concurrent.futures
from libs.shell import bash
from git_service.git import Git
from git_service import exceptions
from collections import defaultdict
from settings import MMS_REPO_URL, MMS_REPO_NAME, MMS_ORG_NAME, SRE_REPO_NAME, SRE_REPO_URL, SRE_ORG_NAME
from common.alert.update_threshold import RepoProcessor
from template_engine.manager import TemplateManager
from common.alert.alertsPostDataBuilder import AlertsPostDataBuilder
from job.inventory_validator import service_mapper
from libs.util import get_processing_time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from contextlib import contextmanager
from yaml.parser import ParserError
# from utils.views import create_alert_data

# ALERT_CONFIG_URL = "***************************:intl-ecomm-svcs/mms-config.git"
# ALERT_REPO_NAME = "mms-config"

ALERT_CONFIG_URL = MMS_REPO_URL
ALERT_REPO_NAME = MMS_REPO_NAME
ALERT_OWNER = "m0c00jt"
DEFAULT_SIZE = 80
EXCLUDE_TYPE = {"performance", "asda", "mm"}
SCANNED_FILE_TYPE = {"app_owner", "sre"}
ALLOWED_SERVICES = {"wcnp", "cosmos", "meghacache", "cassandra", "sql", "kafka", "solr", "oneops", "oracle","wcp"}
MMS_SRE_DIR = "international-tech/intl-sre/golden-signals/rules/production"
SRE_INVENTORY_DIR = "data_store/inventory"
THRESHOLDS = "_threshold_"
THRESHOLDS_KEY = "thresholds"

logger = logging.getLogger(__name__)

def validate_post_data(service, alerts: dict):
    validated_alerts = {}

    for k, v in alerts.items():
        key = service_mapper(service, k, v)
        validated_alerts[key] = v

    return validated_alerts

@dataclass
class AlertConfig:
    service: str
    file_name: str
    alert_owner: str
    git_path: str
    
class InventoryWatcher:

    def __init__(self):
        self.sre_repo = RepoProcessor(SRE_REPO_URL, SRE_REPO_NAME, SRE_ORG_NAME, SRE_INVENTORY_DIR)
        self.working_folder = self.sre_repo.working_folder
        # self.working_folder = os.path.join(settings.TEMPLATE_BASE_DIR, "sre-alert-templates", SRE_INVENTORY_DIR)
        # remove last path and replace with data
        self.defualt_folder = os.path.join(self.sre_repo.cloned_to_location, SRE_REPO_NAME, "data")
        # self.defualt_folder = os.path.join(settings.TEMPLATE_BASE_DIR, "sre-alert-templates", "data")
        self.logger = logging.getLogger(__name__)

    @contextmanager
    def error_handling(self, operation: str):
        """Context manager for consistent error handling"""
        try:
            yield
        except FileNotFoundError as e:
            self.logger.error(f"File not found during {operation}: {str(e)}")
            raise
        except ParserError as e:
            self.logger.error(f"YAML parsing error during {operation}: {str(e)}")
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error during {operation}: {str(e)}")
            raise

    def load_yaml_safely(self, file_path: str) -> Dict:
        """Safely load YAML file with error handling"""
        with self.error_handling("yaml_loading"):
            with open(file_path, 'r') as file:
                return yaml.safe_load(file)

    def validate_service(self, service: str) -> bool:
        """Validate if service is allowed"""
        if service not in ALLOWED_SERVICES:
            self.logger.warning(f"Invalid service requested: {service}")
            return False
        return True

    def list_all_files(self):

        cmd_line = f"find {self.working_folder} -type f"
        alert_files = bash(cmd_line, True)
        alert_files = alert_files.stdout
        if len(alert_files) > 0:
            return alert_files
        else:
            raise exceptions.GitBashCommandFailedToExecute(f"No files founded: {self.working_folder}")

    def inventory(self):

        files = self.list_all_files()
        mapper = {}

        for file in files:

            path = file.split("/")

            if len(path) > 1:
                file_name = path[-1]
                # service name is of form {service}_alerts
                service = path[-2].split("_alerts")[0]
            else:
                continue
            if service not in ALLOWED_SERVICES:
                continue
            if service not in mapper:
                mapper[service] = [file_name]
            else:
                mapper[service].append(file_name)

        return mapper

    def get_git_path(self, service, owner):

        try:
            default_data = yaml.safe_load(open(os.path.join(self.defualt_folder, f"{service}_alerts.yaml")))
            if owner == "sre":
                return default_data.get("git_path")
            else:
                path_parts = default_data.get("git_path").split(os.sep)
                path_parts[-1] = 'app-owner-alerts'
                return os.sep.join(path_parts)
        except Exception as e:
            logger.error(f"Error occurred while getting git path: {str(e)}")
            return Exception(e)

    def get_file_path(self, service, file_name):
        prefix = os.path.join(self.working_folder, service + '_' + "alerts")
        file_with_path = os.path.join(prefix, file_name)
        return file_with_path

    def search_file(self, service, search_query: dict):

        service_inventory = self.inventory()[service]
        prefix = os.path.join(self.working_folder, service + '_' + "alerts")

        files_matched = []
        for file in service_inventory:
            try:
                inv_file = yaml.safe_load(open(os.path.join(prefix, file)))

                for key in inv_file:
                    app = inv_file[key]
                    if all(item in app.items() for item in search_query.items()):
                        files_matched.append(file)
                        break
            except Exception as e:
                # print(f"Error occurred while processing file {file}: {str(e)}")
                continue

        return files_matched

    def matched_alerts(self, service, file_name, search_query: dict) -> list:

        file_with_path = self.get_file_path(service, file_name)
        matched_apps = []
        try:
            with open(file_with_path, 'r') as file:
                inv_file = yaml.safe_load(file)

            for key in inv_file:
                app = inv_file[key]
                app['file_name'] = file_name
                if all(item in app.items() for item in search_query.items()):
                    matched_apps.append({key: app})
            return matched_apps
        except Exception as e:
            return Exception(e)

    def disable_alerts(self, service, file_name, alerts: dict):

        post_body = {"global_data_vars": {}, "create_pull_request": True, "apps_meta_data": []}

        file_with_path = self.get_file_path(service, file_name)
        try:
            inv_file = yaml.safe_load(open(file_with_path))
            alert_owner = ""
            if file_name.split(".")[0] == f"app_owner_{service}_alerts":
                post_body["global_data_vars"] = {"git_path": self.get_git_path(service, "app")}
                alert_owner = "app"
            if file_name.split(".")[0] == f"sre_{service}_alerts":
                alert_owner = "sre"
                post_body["global_data_vars"] = {"git_path": self.get_git_path(service, "sre")}

            for key in alerts:
                app = inv_file[key]
                app['skip_rules'] = app.get('skip_rules', []) + alerts[key]
                post_body["apps_meta_data"].append(app)
                if alert_owner:
                    app['alert_owner_category'] = alert_owner

            return self.recreate_alerts(post_body, f"{service}_alerts.yaml")

        except Exception as e:
            return Exception(e)

    def get_alert_owner_and_git_path(self, service, file_name):
        alert_owner = ""
        git_path = ""
        if file_name.split(".")[0] == f"app_owner_{service}_alerts":
            git_path = self.get_git_path(service, "app")
            alert_owner = "app"
        elif file_name.split(".")[0] == f"sre_{service}_alerts":
            git_path = self.get_git_path(service, "sre")
            alert_owner = "sre"
        return alert_owner, git_path

    def prepare_post_body(self, alerts, alert_owner, git_path):
        post_body = {"create_pull_request": True, "apps_meta_data": []}
        
        # If git_path is provided, add it to global_data_vars
        if git_path:
            post_body["global_data_vars"] = {"git_path": git_path}
            if not alert_owner:
                if git_path.endswith('sre-alerts'):
                    alert_owner = 'sre'
                elif 'app-owner-alerts' in git_path:
                    alert_owner = 'app'
        
        # Process each alert
        for alert in alerts:
            for key, app in alert.items():
                # Add default skip_rules if not present
                if 'skip_rules' not in app:
                    app['skip_rules'] = ["empty"]
                
                # If no global git_path, verify each alert has its own git_path
                if not git_path and 'git_path' not in app:
                    raise ValueError(f"No git_path found in global or alert: {key}")
                
                # Set alert_owner_category if not already present
                if not app.get('alert_owner_category'):
                    # Try to determine from app's git_path
                    if alert_owner:
                        app['alert_owner_category'] = alert_owner
                    elif 'git_path' in app:
                        if app['git_path'].endswith('sre-alerts'):
                            app['alert_owner_category'] = 'sre'
                        elif 'app-owner-alerts' in app['git_path']:
                            app['alert_owner_category'] = 'app'
                    
                    # If neither is available, raise error
                    else:
                        raise ValueError(f"Cannot determine alert_owner_category for alert: {key}")
                
                # Add the processed alert to apps_meta_data
                post_body["apps_meta_data"].append({**app, **app.get("thresholds", dict())})
        
        return post_body

    def edit_alerts(self, service, file_name, alerts: list, flag=False):
        res = {}
        file_with_path = self.get_file_path(service, file_name)
        try:
            inv_file = yaml.safe_load(open(file_with_path))
            for alert in alerts:
                for key, app in alert.items():
                    if key in inv_file:
                        inv_file[key].update({field: app[field] for field in app if field in inv_file[key]})
                        if 'skip_rules' in app:
                            inv_file[key]['skip_rules'] = app['skip_rules']

            if flag:
                # update inv with new alerts
                alert_owner, git_path = self.get_alert_owner_and_git_path(service, file_name)
                post_body = self.prepare_post_body(alerts, alert_owner, git_path)

                mms_status, mms_res = self.recreate_alerts(post_body, f"{service}_alerts.yaml")
                if mms_status:
                    res['mms'] = mms_res['pull_request']
                else:
                    res['mms'] = "Failed to update alerts in MMS"
            
            return res
        except Exception as e:
            return Exception(e)

    def bulk_alert_edit(
        self,
        service: str,
        search_query: Dict[str, Any],
        file_name: str,
        alert_rules: List[str],
        skip_flags: List[str],
        edit_fields: Dict[str, Any],
        enable: bool = False,
        inv_flag: bool = False
    ) -> Dict[str, Any]:
        """
        Edit alerts in bulk with improved error handling and validation
        
        Args:
            service: Service name to edit alerts for
            search_query: Query to find matching alerts
            file_name: Name of the file containing alerts
            alert_rules: List of alert rules to apply
            skip_flags: List of flags to skip
            edit_fields: Fields to edit in matched alerts
            enable: If True, remove alert_rules from skip_flags
            inv_flag: Flag to trigger additional processing
            
        Returns:
            Dict containing operation results and PR links
        """
        if not self.validate_service(service):
            return {"error": f"Invalid service: {service}"}

        with self.error_handling("bulk_alert_edit"):
            res = {}
            matched_alerts = self.matched_alerts(service, file_name, search_query)
            matched_alerts = {k: v for item in matched_alerts for k, v in item.items()}
            matched_alerts = validate_post_data(service, matched_alerts)
            file_with_path = self.get_file_path(service, file_name)

            try:

                if inv_flag:
                    with open(file_with_path, 'r') as file:
                        inv_file = yaml.safe_load(file)

                    for key in matched_alerts:
                        if key in inv_file:
                            for sf in skip_flags:
                                if enable:
                                    # Remove alert_rules from skip_flags if they exist
                                    if sf in inv_file[key] and isinstance(inv_file[key][sf], list):
                                        original_rules = inv_file[key][sf]
                                        inv_file[key][sf] = [rule for rule in original_rules if rule not in alert_rules]
                                        if not inv_file[key][sf]:
                                            # Remove the skip_flag if no alert_rules remain
                                            del inv_file[key][sf]
                                else:
                                    # Add alert_rules to skip_flags
                                    if sf not in inv_file[key]:
                                        inv_file[key][sf] = alert_rules
                                    else:
                                        prev = inv_file[key][sf]
                                        # Ensure no duplicates
                                        inv_file[key][sf] = list(set(prev + alert_rules))
                            if edit_fields:
                                inv_file[key].update({k: v for k, v in edit_fields.items() if k in inv_file[key]})
                    sre_res = self.yaml_dump(inv_file, file_with_path)
                    res['sre'] = sre_res['pull_request']

                alert_owner, git_path = self.get_alert_owner_and_git_path(service, file_name)
                post_body = self.prepare_post_body([matched_alerts], alert_owner, git_path)
                for app in post_body["apps_meta_data"]:
                    for sf in skip_flags:
                        app[sf] = list(set(app.get(sf, []) + alert_rules))
                mms_status, mms_res = self.recreate_alerts(post_body, f"{service}_alerts.yaml")
                if mms_status:
                    res['mms'] = mms_res['pull_request']
                else:
                    res['mms'] = "Failed to update alerts in MMS"


            except FileNotFoundError:
                logging.error(f"File not found: {file_with_path}")
                return {"error": "File not found"}
            except yaml.YAMLError as e:
                logging.error(f"Error parsing YAML file: {e}")
                return {"error": "Error parsing YAML file"}
            except KeyError as e:
                logging.error(f"Key error: {e}")
                return {"error": f"Key error: {e}"}
            except Exception as e:
                logging.error(f"An unexpected error occurred: {e}")
                return {"error": "An unexpected error occurred"}

            return res

    def recreate_alerts(self, post_data: Dict, template: str) -> Tuple[bool, Dict]:
        """
        Recreate alerts with improved cleanup and logging
        
        Args:
            post_data: Data for alert recreation
            template: Template name
            
        Returns:
            Tuple of (success_status, result_dict)
        """
        tm = None
        try:
            with self.error_handling("recreate_alerts"):
                logger.info("Recreation of alerts , execution started calling TemplateManager")
                start_time = time.time()
                tm = TemplateManager(template, post_data, execute_child_templates=False)
                status, res = tm.execute()
                logger.info(
                    f"Recreation of alerts, execution ended calling TemplateManager ended, took {get_processing_time(start_time, time.time())}")
                return status, res
                # return generate_template_compiled_files(template, post_data)
        except Exception as e:
            logger.exception(e)
            return Exception(e)
        finally:
            if tm:
                self.cleanup_template_manager(tm)

    def cleanup_template_manager(self, tm: Any) -> None:
        """Safely cleanup template manager resources"""
        try:
            tm.clean_up()
        except Exception as e:
            self.logger.error(f"Template manager cleanup failed: {str(e)}")

    def yaml_dump(self, data, file_name):
        with open(file_name, 'w') as file:
            yaml.dump(data, file)

        return self.create_pr()

    def clean_up(self):
        # Perform cleanup actions here
        self.sre_repo.clean_up()

    def __del__(self):
        self.clean_up()

    def create_pr(self):
        result = self.sre_repo.submit_to_create_pr('intl-ecomm-svcs', 'sre-templates',
                                                   "alert modification", 'SRE Update')
        return result



    def list_service_files(self, service):
        """
        Get list of all alert files for a specific service
        
        Args:
            service (str): Name of the service
            
        Returns:
            dict: Dictionary containing:
                - files: List of alert files
                - total_count: Total number of files
                - path: Full path to service alerts
        """
        try:
            service_inventory = self.inventory()[service]
            prefix = os.path.join(self.working_folder, service + '_' + "alerts")
            
            files_info = []
            for file in service_inventory:
                try:
                    file_path = os.path.join(prefix, file)
                    inv_file = yaml.safe_load(open(file_path))
                    
                    # Get basic file info
                    files_info.append({
                        "filename": file,
                        "full_path": file_path,
                        "alert_count": len(inv_file.keys()) if inv_file else 0,
                        "last_modified": os.path.getmtime(file_path)
                    })
                except Exception as e:
                    continue
                
            return {
                "service": service,
                "files": files_info,
                "total_count": len(files_info),
                "alerts_path": prefix
            }
        except KeyError:
            return {
                "service": service,
                "error": f"Service '{service}' not found",
                "available_services": list(self.inventory().keys())
            }


if __name__ == "__main__":
    update = InventoryWatcher()
    # print(update.sre_repo.working_folder)

    # mapper = update.search_file("wcnp", {"name_space": "ca-home-page"})
    # print(mapper)
    # print(update.get_git_path("wcnp","app"))
    # print(update.search_file("wcnp",{"name_space": "ca-home-page"}))
    # apps = update.matched_alerts("wcnp","app_owner_wcnp_alerts.yaml", {"name_space": "ca-home-page"})

    # res = update.disable_alerts("wcnp","app_owner_wcnp_alerts.yaml",
    #                              { "home-page-ca-home-page":["disable_alert"],
    #                                       "bsp-render-ca-bsp-render":["gslb_health_current_threshold_pct"]})

    res = update.bulk_alert_edit("wcnp", {"name_space": "ca-ims-adaptor"},
                                 "app_owner_wcnp_alerts.yaml", [
                                     "cpu_usage_current_threshold_pct",
                                     "eus_traffic_in_balance_current_threshold_pct",
                                     "fiveXX_current_threshold_count_used_for_trend",
                                     "fiveXX_current_threshold_pct",
                                     "fiveXX_trend_comparing_to_one_week_ago_threshold_pct",
                                     "latency_spike_comparing_to_one_week_ago_threshold_pct",
                                     "memory_usage_current_threshold_pct",
                                     "pods_restarts_current_threshold_count",
                                     "scus_traffic_in_balance_current_threshold_pct",
                                     "traffic_drop_comparing_to_one_week_ago_threshold_pct",
                                     "traffic_spike_comparing_to_one_week_ago_threshold_pct",
                                     "wus_traffic_in_balance_current_threshold_pct"
                                 ],
                                 ["skip_email"], {}
                                 )

    # print(res)
