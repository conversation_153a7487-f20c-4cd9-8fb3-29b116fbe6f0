import yaml
from deepdiff import DeepDiff


def compare(latest, main):
    _latest = read_yaml(latest)
    _main = read_yaml(main)
    # Compare the two dictionaries
    diff = DeepDiff(_main, _latest, ignore_order=True)

    # Display the differences
    print(diff)


def read_yaml(_file):
    import yaml

    # Load YAML from a file
    with open(_file, 'r') as file:
        data = yaml.safe_load(file)
        return data


if __name__ == "__main__":
    import sys, os, datetime

    compare( "/Users/<USER>/git/juno/output/latest.yaml","/Users/<USER>/git/juno/output/main.yaml")
