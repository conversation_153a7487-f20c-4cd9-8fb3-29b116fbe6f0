import os
import requests
import sys

sys.path.append('common/data_store')
script_dir = os.path.dirname(os.path.realpath(__file__))

# Add the parent directory of the current script's directory to sys.path
sys.path.append(os.path.join(script_dir, '..'))
# from slack_bot.slack import WMTSlack
# from slack_bot import slack
# from git_service.git import Git
from slack_bot import slack
from git_service.git import Git
from common.alert import sync_data
import uuid
import json
import argparse
import settings
import logging

logger = logging.getLogger(__name__)

try:
    with open('processed_shas.json', 'r') as f:
        processed_shas = json.load(f)
except FileNotFoundError:
    processed_shas = []


class GitHubRepoWatcher:

    # Function to initialize the GitHubRepoWatcher
    def __init__(self):
        self.headers = {"Authorization": f"token {settings.API_TOKEN}"}

    # Function to get the files changed in a commit
    def get_commit_files(self, sha, commit_url):
        """
        Function to get the files changed in a commit
        """
        url = f"{commit_url}/{sha}"
        try:
            response = requests.get(url, headers=self.headers, timeout=None)
            response.raise_for_status()  # Raises HTTPError for bad responses
            commit_data = response.json()
            return commit_data["files"]
        except requests.RequestException as e:
            logger.error('Failed to fetch commit details: {}'.format(str(e)))
            return None

    # Function to check if the commit is new
    def is_new_commit(self, sha, repo):
        if sha in processed_shas:
            logger.info('SHA {} already processed.'.format(sha))
            return False
        else:
            processed_shas.append(sha)
            with open('processed_shas.json', 'w') as f:
                json.dump(processed_shas, f)
            return True

    def sre_template_data(self, base_url, data_template):
        for entry in data_template:
            for key, filename in entry.items():
                filename = os.path.basename(filename)
                input_file_name = filename.split("_")[0]
                if input_file_name == "sre":
                    # url = f"{base_url}/api/alerts/template/{key}.yaml/custom_inventory_file/{filename}/tier/all/"
                    self.trigger_api(key, filename, True)
                else:
                    # url = f"{base_url}/api/alerts/template/{key}.yaml/custom_inventory_file/{filename}/tier/all/"
                    self.trigger_api(key, filename, False)

    # Function to trigger the APIs for the files changed in the commit
    def trigger_apis_for_files(self, base_url, data_template, repo):
        logger.info('Processed Shas: {}'.format(processed_shas))
        if data_template and repo == "sre-templates":
            self.sre_template_data(base_url, data_template)

    # Function for slack message
    def send_slack_message(self, message):
        try:
            slack.send_message(channel="dummy_test_vn55p0j", message=message)
        except requests.exceptions.RequestException as e:
            logger.error('Error sending message to slack: {}'.format(str(e)))

    # Function to trigger the API
    def trigger_api(self, key, filename, is_sre_sla):
        logger.info('Triggering... ')
        try:
            template = key + ".yaml"
            data = sync_data.execute_custom_inventory_data_file(template, filename, tier=None, is_sre_sla=is_sre_sla,
                                                                force_pull=False, sre_override_xmatters_and_slack=True)

            slack_data = data[1].get("body")
            self.send_slack_message(str(slack_data))

        except requests.RequestException as e:
            logger.error('Failed to send POST request: {}'.format(str(e)))
            return None

    def get_repo_url(self, org, repo, folder_access):
        return f"{settings.BASE_URL}/{org}/{repo}/commits?path={folder_access}"

    # Function to check for updates in the repository
    def check_for_updates(self, org, repo, folder, latest_sha):
        template_data = []
        url = self.get_repo_url(org, repo, folder)
        get_latest_commit_details_base_url = f"{settings.BASE_URL}/{org}/{repo}/commits"

        response = requests.get(url, headers=self.headers, timeout=None)
        if response.status_code == 200:
            commit_data = response.json()
            if commit_data:
                print("Commit data found.", latest_sha)
                logger.info('Commit data found. {}'.format(latest_sha))
                # latest_sha = commit_data[0].get('sha')
                if latest_sha and self.is_new_commit(latest_sha, repo):
                    files_changed = self.get_commit_files(latest_sha, get_latest_commit_details_base_url)
                    if files_changed:
                        for file in files_changed:
                            template_data.append(
                                {
                                    os.path.basename(
                                        os.path.dirname(file.get("filename"))
                                    ): file.get("filename")
                                }
                            )
                    else:
                        logger.error('No files found or error occurred.')

                    self.trigger_apis_for_files(settings.JUNO_BASE_URL, template_data, repo)
                else:
                    logger.error('No new commit detected.')
            else:
                logger.error('No commit data found.')
        else:
            logger.error('Error Response: {}'.format(response.text))


# Main function
# if __name__ == "__main__":
#     git = Git(
#         settings.SRE_REPO_URL,
#         cloned_to_location=os.path.join(settings.TEMPLATE_OUTPUT_DIR, str(uuid.uuid4())),
#     )
#     git()
#     git.clone()
#
#     # Create the parser
#     parser = argparse.ArgumentParser(description='Process SHA input.')
#
#     # Add the arguments
#     parser.add_argument('SHA', metavar='SHA', type=str, help='the SHA to process')
#
#     # Execute the parse_args() method
#     args = parser.parse_args()
#
#     latest_commit_sha = args.SHA
#     # latest_commit_sha = git.get_latest_sha_from_remote_for_folder(ORG_NAME,REPO_NAME,FOLDER_ACCESS)
#     if (Git.check_sha_in_past_commits(settings.SRE_ORG_NAME, settings.SRE_REPO_NAME, settings.SRE_FOLDER_ACCESS,
#                                       latest_commit_sha)):
#         logger.info('Latest commit SHA: {}'.format(latest_commit_sha))
#         watcher = GitHubRepoWatcher()
#         watcher.check_for_updates(settings.SRE_ORG_NAME, settings.SRE_REPO_NAME, settings.SRE_FOLDER_ACCESS,
#                                   latest_commit_sha)
#     else:
#         logger.error('Please provide valid SHA. Exiting......')