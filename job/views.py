from rest_framework.decorators import api_view
from rest_framework.response import Response
from django.http import JsonResponse
from django.core.paginator import Paginator
import json
from django.core.cache import cache
import hashlib
from rest_framework import status
import job.sre_to_mms
import job.mms_to_sre
from job.mms_to_sre import GitHubRepoWatcher
from slack_bot.slack import WMTSlack
import settings
import uuid
from git_service.git import Git
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from job.inventory_watcher import InventoryWatcher
from job.inventory_validator import InventoryValidator
import asyncio
import threading
from datetime import datetime

slack = WMTSlack()

logger = logging.getLogger(__name__)

# In-memory job storage
JOB_STATUSES = {}

@api_view(['POST'])
def sre_to_mms_sync(request):
    try:
        post_data = request.data
        latest_commit_sha = post_data.get('sha')
        if not latest_commit_sha:
            return Response({"ok": False, "body": {"message": "SHA is required."}}, status=status.HTTP_400_BAD_REQUEST)

        logger.info(f"User called {request.get_full_path()} for sre to mms sync")

        if Git.check_sha_in_past_commits(settings.SRE_ORG_NAME, settings.SRE_REPO_NAME, settings.SRE_FOLDER_ACCESS,
                                         latest_commit_sha):
            sre_watcher = sync.sre_to_mms.GitHubRepoWatcher()
            sre_watcher.check_for_updates(settings.SRE_ORG_NAME, settings.SRE_REPO_NAME, settings.SRE_FOLDER_ACCESS,
                                          latest_commit_sha)
            return Response({"ok": True, "body": "Synced"}, status=status.HTTP_200_OK)
        else:
            return Response({"ok": False, "body": {"message": "Invalid SHA provided."}},
                            status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in sre_to_mms_sync: {str(e)}", exc_info=True)
        return Response({"ok": False, "body": {"message": "An error occurred during synchronization."}},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def mms_to_sre_sync(request):
    mms_watcher = None
    try:
        post_data = request.data
        latest_commit_sha = post_data.get('sha')
        if not latest_commit_sha:
            return JsonResponse({"ok": False, "body": {"message": "SHA is required."}},
                                status=status.HTTP_400_BAD_REQUEST)

        slack.send_message("juno_logs", text=f"User called {request.get_full_path()} for mms to sre sync")
        mms_watcher = sync.mms_to_sre.GitHubRepoWatcher()
        if Git.check_sha_in_past_commits(settings.MMS_ORG_NAME, settings.MMS_REPO_NAME, settings.MMS_FOLDER_ACCESS,
                                         latest_commit_sha):
            logger.info(f"Latest commit SHA: {latest_commit_sha}")

            mms_watcher.check_for_sha_updates(settings.MMS_ORG_NAME, settings.MMS_REPO_NAME, settings.MMS_FOLDER_ACCESS,
                                              latest_commit_sha)
        else:
            return JsonResponse({"ok": False, "body": {"message": "Please provide valid SHA. Exiting......"}},
                                status=status.HTTP_400_BAD_REQUEST)

        return JsonResponse({"ok": True, "body": "Synced"}, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Error syncing MMS to SRE: {str(e)}", exc_info=True)
        mms_watcher.clean_up()
        return JsonResponse({"ok": False, "body": {"message": str(e)}}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
def matched_alerts_view(request, service=None, file_name=None):
    inventory_watcher = InventoryWatcher()
    try:
        search_query = request.data.get('search_query', {})
        page = int(request.data.get('page', 1))
        page_size = int(request.data.get('page_size', 10))
        service = request.data.get('service', service)
        file_name = request.data.get('file_name', file_name)
        results = []
        # filename is list
        for file in file_name:
            results.extend(inventory_watcher.matched_alerts(service, file, search_query))

        paginator = Paginator(results, page_size)
        paginated_results = paginator.get_page(page)

        return Response({
            'results': list(paginated_results),
            'page': page,
            'page_size': page_size,
            'total_results': paginator.count,
            'num_pages': paginator.num_pages
        })
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    finally:
        inventory_watcher.clean_up()


@api_view(['POST'])
def search_alerts_view(request):
    inventory_watcher = InventoryWatcher()
    try:
        search_query = request.data.get('search_query', {})
        page = int(request.data.get('page', 1))
        page_size = int(request.data.get('page_size', 10))
        service = request.data.get('service', "")
        results = inventory_watcher.search_file(service, search_query)

        paginator = Paginator(results, page_size)
        paginated_results = paginator.get_page(page)

        return Response({
            'results': list(paginated_results),
            'page': page,
            'page_size': page_size,
            'total_results': paginator.count,
            'num_pages': paginator.num_pages
        })
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    finally:
        inventory_watcher.clean_up()


# @api_view(['POST'])
# def search_match_view(request):
#     inventory_watcher = InventoryWatcher()
#     try:
#         search_query = request.data.get('search_query', {})
#         page = int(request.data.get('page', 1))
#         page_size = int(request.data.get('page_size', 10))
#         service = request.data.get('service', "")
#         files = inventory_watcher.search_file(service, search_query)
#         results = []
#         #filename is list
#         for file in files:
#             results.extend(inventory_watcher.matched_alerts(service, file, search_query))

#         paginator = Paginator(results, page_size)
#         paginated_results = paginator.get_page(page)

#         return Response({
#             'results': list(paginated_results),
#             'page': page,
#             'page_size': page_size,
#             'total_results': paginator.count,
#             'num_pages': paginator.num_pages
#         })
#     except Exception as e:
#         return Response({"ok": False, "body": {"message": str(e)}}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
#     finally:
#         inventory_watcher.clean_up()

@api_view(['POST'])
def search_match_view(request):
    inventory_watcher = None
    try:
        search_query = request.data.get('search_query', {})
        page = int(request.data.get('page', 1))
        page_size = int(request.data.get('page_size', 10))
        service = request.data.get('service', "")

        # Convert search_query dict to a JSON string
        search_query_str = json.dumps(search_query, sort_keys=True)

        # Generate a hash of the search_query_str
        search_query_hash = hashlib.md5(search_query_str.encode('utf-8')).hexdigest()

        # Generate a unique cache key using the hash
        cache_key = f"search_match_{service}_{search_query_hash}_{page}_{page_size}"

        # Check if the results are already cached
        cached_results = cache.get(cache_key)
        if cached_results:
            return Response(cached_results)

        inventory_watcher = InventoryWatcher()
        files = inventory_watcher.search_file(service, search_query)
        results = []
        # filename is list
        for file in files:
            results.extend(inventory_watcher.matched_alerts(service, file, search_query))

        paginator = Paginator(results, page_size)
        paginated_results = paginator.get_page(page)

        response_data = {
            'results': list(paginated_results),
            'page': page,
            'page_size': page_size,
            'total_results': paginator.count,
            'num_pages': paginator.num_pages
        }

        # Cache the results
        cache.set(cache_key, response_data, timeout=300)  # Cache timeout set to 5 minutes (300 seconds)

        return Response(response_data)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    finally:
        if inventory_watcher is not None:
            inventory_watcher.clean_up()


@api_view(['POST'])
def disable_alerts_view(request):
    try:
        service = request.data.get('service', "")
        file_name = request.data.get('file_name', "")
        alerts = request.data.get('alerts', {})
        inventory_watcher = InventoryWatcher()
        res = inventory_watcher.disable_alerts(service, file_name, alerts)
        return Response({"ok": True, "body": f"Disabled alerts {res}"}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
def edit_alerts_view(request):
    inventory_watcher = InventoryWatcher()
    try:
        service = request.data.get('service', "")
        # file_name = request.data.get('file_name', "")
        alerts = request.data.get('alerts', {})
        file_names = {}
        res = []
        mms_flag = request.data.get('mms_flag', False)
        for alert in alerts:
            for k, v in alert.items():
                if 'file_name' in v:
                    # remove file_name from alert
                    file = v.pop('file_name')
                    if file not in file_names:
                        file_names[file] = [alert]
                    elif alert not in file_names[file]:
                        file_names[file].append(alert)

        for file_name, alerts in file_names.items():
            try:
                res.append(inventory_watcher.edit_alerts(service, file_name, alerts, mms_flag))
            except Exception as e:
                return Response({"ok": False, "body": {"message": str(e)}}, status=status.HTTP_400_BAD_REQUEST)

        return Response({"ok": True, "body": f"Edited alerts {res}"}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=status.HTTP_400_BAD_REQUEST)
    finally:
        inventory_watcher.clean_up()


@api_view(['POST'])
def bulk_edit_alerts_view(request):
    try:
        service = request.data.get('service', "")
        file_names = request.data.get('file_names', [])
        search_query = request.data.get('search', {})
        alert_rules = request.data.get('alert_rules', [])
        skip_rules = request.data.get('skip_rules', {})
        edit_values = request.data.get('edit_values', {})
        enable = request.data.get('enable', False)
        inv_flag = request.data.get('inv_flag', False)

        # use futures to run the edit_alerts in parallel
        res = {}
        for file in file_names:
            try:
                inventory_watcher = InventoryWatcher()
                res[file] = inventory_watcher.bulk_alert_edit(service, search_query, file, alert_rules,
                                                              skip_rules, edit_values, enable, inv_flag)
            except Exception as e:
                res[file] = {"error": str(e)}

        return Response({"ok": True, "body": f"Alerts updated{res}"}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
def get_validator_results_view(request):
    try:
        service = request.query_params.get('service', "")

        # Validate required parameter
        if not service:
            return Response({"ok": False, "body": {"message": "Service parameter is required"}},
                            status=status.HTTP_400_BAD_REQUEST)

        inventory_validator = InventoryValidator()
        results = inventory_validator.validate_service(service)

        return Response({"ok": True, "body": results}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=status.HTTP_400_BAD_REQUEST)
    


@api_view(['GET'])
def get_files_by_service(request):
    try:
        service = request.query_params.get("service")
        if not service:
            return Response({"ok": False, "body": {"message": "Service parameter is required"}}, status=400)
            
        inventory_watcher = InventoryWatcher()
        files = inventory_watcher.list_service_files(service)
        return Response({"ok": True, "body": files}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


def run_in_background(func, job_id):
    """Run a function in a background thread and log results"""
    try:
        # Update job status to running
        JOB_STATUSES[job_id] = {
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "end_time": None,
            "result": None,
            "error": None
        }
        
        # Run the actual function
        result = func()
        
        # Update job status to completed
        JOB_STATUSES[job_id].update({
            "status": "completed",
            "end_time": datetime.now().isoformat(),
            "result": "Success"
        })
        
        logging.info(f"Background job {job_id} completed successfully: {result}")
        return result
    except Exception as e:
        # Update job status to failed
        JOB_STATUSES[job_id].update({
            "status": "failed",
            "end_time": datetime.now().isoformat(),
            "error": str(e)
        })
        
        logging.error(f"Background job {job_id} failed: {str(e)}")
        logging.exception(e)
        return None

@api_view(['POST'])
def wcnp_sync(request):
    """Sync WCNP alerts from MMS to SRE templates"""
    job_id = str(uuid.uuid4())
    
    # Start background thread
    thread = threading.Thread(
        target=run_in_background,
        args=(lambda: GitHubRepoWatcher().process_wcnp(), job_id),
        daemon=True
    )
    thread.start()
    
    return Response({
        "status": "accepted", 
        "message": "WCNP sync started in background",
        "job_id": job_id
    }, status=202)  # 202 Accepted

@api_view(['POST'])
def wcp_sync(request):
    """Sync WCP alerts from MMS to SRE templates"""
    job_id = str(uuid.uuid4())
    
    # Start background thread
    thread = threading.Thread(
        target=run_in_background,
        args=(lambda: GitHubRepoWatcher().process_wcp(), job_id),
        daemon=True
    )
    thread.start()
    
    return Response({
        "status": "accepted", 
        "message": "WCP sync started in background",
        "job_id": job_id
    }, status=202)  # 202 Accepted

@api_view(['GET'])
def job_status(request, job_id):
    """Get the status of a background job"""
    if job_id in JOB_STATUSES:
        return Response(JOB_STATUSES[job_id], status=200)
    else:
        return Response({"status": "not_found", "message": f"Job {job_id} not found"}, status=404)

@api_view(['POST'])
def sql_sync(request):
    """Sync SQL alerts from MMS to SRE templates"""
    job_id = str(uuid.uuid4())
    
    thread = threading.Thread(
        target=run_in_background,
        args=(lambda: GitHubRepoWatcher().process_sql_alerts(), job_id),
        daemon=True
    )
    thread.start()
    
    return Response({
        "status": "accepted", 
        "message": "SQL alerts sync started in background",
        "job_id": job_id
    }, status=202)

@api_view(['POST'])
def megacache_sync(request):
    """Sync Megacache alerts from MMS to SRE templates"""
    job_id = str(uuid.uuid4())
    
    thread = threading.Thread(
        target=run_in_background,
        args=(lambda: GitHubRepoWatcher().process_megacache_alerts(), job_id),
        daemon=True
    )
    thread.start()
    
    return Response({
        "status": "accepted", 
        "message": "Megacache alerts sync started in background",
        "job_id": job_id
    }, status=202)

@api_view(['POST'])
def oracle_sync(request):
    """Sync Oracle alerts from MMS to SRE templates"""
    job_id = str(uuid.uuid4())
    
    thread = threading.Thread(
        target=run_in_background,
        args=(lambda: GitHubRepoWatcher().process_oracle_alerts(), job_id),
        daemon=True
    )
    thread.start()
    
    return Response({
        "status": "accepted", 
        "message": "Oracle alerts sync started in background",
        "job_id": job_id
    }, status=202)

@api_view(['POST'])
def kafka_sync(request):
    """Sync Kafka alerts from MMS to SRE templates"""
    job_id = str(uuid.uuid4())
    
    thread = threading.Thread(
        target=run_in_background,
        args=(lambda: GitHubRepoWatcher().process_kafka_alerts(), job_id),
        daemon=True
    )
    thread.start()
    
    return Response({
        "status": "accepted", 
        "message": "Kafka alerts sync started in background",
        "job_id": job_id
    }, status=202)

@api_view(['POST'])
def cosmos_sync(request):
    """Sync Cosmos alerts from MMS to SRE templates"""
    job_id = str(uuid.uuid4())
    
    thread = threading.Thread(
        target=run_in_background,
        args=(lambda: GitHubRepoWatcher().process_cosmos(), job_id),
        daemon=True
    )
    thread.start()
    
    return Response({
        "status": "accepted", 
        "message": "Cosmos alerts sync started in background",
        "job_id": job_id
    }, status=202)

@api_view(['POST'])
def cassandra_sync(request):
    """Sync Cassandra alerts from MMS to SRE templates"""
    job_id = str(uuid.uuid4())
    
    thread = threading.Thread(
        target=run_in_background,
        args=(lambda: GitHubRepoWatcher().process_cassandra(), job_id),
        daemon=True
    )
    thread.start()
    
    return Response({
        "status": "accepted", 
        "message": "Cassandra alerts sync started in background",
        "job_id": job_id
    }, status=202)

@api_view(['POST'])
def oneops_sync(request):
    """Sync Oneops alerts from MMS to SRE templates"""
    job_id = str(uuid.uuid4())
    
    thread = threading.Thread(
        target=run_in_background,
        args=(lambda: GitHubRepoWatcher().process_oneops(), job_id),
        daemon=True
    )
    thread.start()
    
    return Response({
        "status": "accepted", 
        "message": "Oneops alerts sync started in background",
        "job_id": job_id
    }, status=202)

@api_view(['POST'])
def custom_sync(request):
    """Sync alerts from a custom path"""
    job_id = str(uuid.uuid4())
    
    # Extract parameters from request body
    custom_path = request.data.get('custom_path')
    template_name = request.data.get('template_name', 'wcnp_alerts.yaml')
    service_type = request.data.get('service_type', 'wcnp')
    alert_type = request.data.get('alert_type')
    custom_output_name = request.data.get('custom_output_name')
    
    # Validate required parameters
    if not custom_path:
        return Response({
            "status": "error",
            "message": "custom_path parameter is required"
        }, status=400)
    
    # Start background thread
    thread = threading.Thread(
        target=run_in_background,
        args=(lambda: GitHubRepoWatcher().process_custom_path(
            custom_path, template_name, service_type, alert_type, custom_output_name
        ), job_id),
        daemon=True
    )
    thread.start()
    
    return Response({
        "status": "accepted", 
        "message": f"Custom sync started in background for {custom_path}",
        "job_id": job_id
    }, status=202)