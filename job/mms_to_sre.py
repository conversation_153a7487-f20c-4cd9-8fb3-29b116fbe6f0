from slack_bot.slack import WMTSlack
from common.data_store import read_mms_config
from common.alert.update_threshold import UpdateThreshold
import settings
import logging, os, requests, sys, json, time
from template_engine.compile import get_template_variables_and_default_vars

script_dir = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(script_dir, '..'))

sys.path.append('common/data_store')

logger = logging.getLogger(__name__)
slack = WMTSlack()

# Load processed_shas from a file
try:
    with open('processed_shas_mms.json', 'r') as f:
        processed_shas_mms = json.load(f)
except FileNotFoundError:
    processed_shas_mms = []


class GitHubRepoWatcher:
    BASE_URL = "https://gecgithub01.walmart.com/api/v3/repos"

    # Function to initialize the GitHubRepoWatcher
    def __init__(self):
        self.headers = {"Authorization": f"token {settings.API_TOKEN}"}
        self.update_sre = UpdateThreshold()
        self.mms_repo = self.update_sre.mms_repo.cloned_to_location

    @staticmethod
    def get_owners_category(category_string):
        if "sre-alerts" == category_string.rstrip().strip():
            return "sre"
        return "app"

    def handle_mms_files(self, files_with_alert_group, template_name, key_mapper, mandatory_params,
                         template_alert_sla_names,
                         user_provided_params_map=None):
        update_mms = read_mms_config.ExtractFileFields(template_name)
        final_mms_data = []
        failed_mms_data = []
        for mms_alert_file_info in files_with_alert_group:
            for alert_group, mms_alert_file in mms_alert_file_info.items():
                _user_provided_params_map = None
                alert_path = self.mms_repo + "/mms-config/" + os.path.dirname(mms_alert_file)
                # Old templates does not have alert_owner_category, to support .
                _alert_group = GitHubRepoWatcher.get_owners_category(alert_group)
                if not user_provided_params_map:
                    _user_provided_params_map = {"alert_owner_category": _alert_group}
                else:
                    _user_provided_params_map = {"alert_owner_category": _alert_group, **user_provided_params_map}
                _file = f"{alert_path}/{mms_alert_file}"
                logger.info(f"Extracting data from following files {_file} ")
                status, mms_data = update_mms.extract_fields_from_alert_yaml_files(alert_path,
                                                                                   os.path.basename(mms_alert_file),
                                                                                   mandatory_params,
                                                                                   key_mapper, template_alert_sla_names,
                                                                                   _user_provided_params_map)
                if not status:
                    mms_data["file"] = _file
                    failed_mms_data.append(mms_data)
                    continue
                final_mms_data.append(mms_data)
        for _data in failed_mms_data:
            logger.info("******** Following files are failed to process *********")
            logger.info(_data.get("file"))

        return final_mms_data, failed_mms_data

    # Function to trigger the APIs for the files changed in the commit
    def process_mms_files(self, files_with_alert_group, template_name, key_mapper, mandatory_params, key_string,
                          template_alert_sla_names, user_provided_params_map=None):
        """
        Process MMS files and generate alerts
        """
        processed_files, failed_mms_data = self.handle_mms_files(
            files_with_alert_group, 
            template_name, 
            key_mapper,
            mandatory_params, 
            template_alert_sla_names,
            user_provided_params_map
        )

        # Get custom output name from user_provided_params_map if it exists
        custom_output_name = user_provided_params_map.get('custom_output_name') if user_provided_params_map else None
        
        # Pass the custom_output_name to sync_inventory_file
        response = self.update_sre.sync_inventory_file(processed_files, key_string, custom_output_name)
        self.send_slack_message(str(response))
        return response, failed_mms_data

    # Function for slack message
    def send_slack_message(self, message):
        try:
            slack.send_message(channel="dummy_test_vn55p0j", text=message)
        except requests.exceptions.RequestException as e:
            logger.error('Error sending message to slack: {}'.format(str(e)))

    def get_repo_url(self, org, repo, folder_access):
        return f"{self.BASE_URL}/{org}/{repo}/commits?path={folder_access}"

    def get_all_files(self, folder_or_file):
        template_data = []
        path = os.path.join(self.mms_repo, "mms-config", folder_or_file)
        try:
            if os.path.isfile(path):
                # Process single file
                relative_path = os.path.relpath(path, os.path.join(self.mms_repo, "mms-config"))
                template_data.append({os.path.basename(os.path.dirname(relative_path)): relative_path})
            else:
                # Process folder
                for root, dirs, files in os.walk(path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        _path = os.path.join(self.mms_repo, "mms-config")
                        exclude_path_path = os.path.join(self.mms_repo, "mms-config")
                        remaining_path = os.path.relpath(file_path, exclude_path_path)
                        template_data.append({os.path.basename(os.path.dirname(remaining_path)): remaining_path})
            logger.info('Template Data: {}'.format(template_data))
        except Exception as e:
            logger.error(f"An error occurred while listing files in folder: {e}")
        return template_data

    def check_mms_folder_for_updates_and_insert_in_inventory(self, folder_or_file, template_name, key_mapper,
                                                             mandatory_params, key_string, template_alert_sla_names,
                                                             user_provided_params_map=None):
        files_with_alert_group = self.get_all_files(folder_or_file)
        return self.process_mms_files(files_with_alert_group, template_name, key_mapper,
                                      mandatory_params, key_string,
                                      template_alert_sla_names,
                                      user_provided_params_map)

    def clean_up(self):
        self.update_sre.clean_up()

    @staticmethod
    def get_alert_names(template_data):
        alert_names = list()
        groups = template_data.get("content", dict()).get("groups", list())
        for group in groups:
            for rule in group.get("rules", list()):
                if "record" in rule:
                    continue
                labels = rule.get("labels").keys()
                data = [label for label in labels if "_threshold_" in label]
                if "alert_sla_name" in rule.get("labels"):
                    alert_names.append(rule.get("labels").get("alert_sla_name"))
                elif len(data) > 0:
                    alert_names.append(data[0])
        return alert_names

    def process_mms_to_inventory(self, yaml_files_dir, template_name, key_mappers, additional_params_keys,
                                 user_provided_params_map=None):
        template_data = get_template_variables_and_default_vars(template_name)
        _mandatory_params = template_data.get("mandatory_vars")
        key_string = template_data.get("default_vars").get("file_name").rstrip(".yaml")
        _mandatory_params.extend(additional_params_keys)
        _alert_names = GitHubRepoWatcher.get_alert_names(template_data)
        pr_data, failed_mms_data = self.check_mms_folder_for_updates_and_insert_in_inventory(yaml_files_dir,
                                                                                             template_name, key_mappers,
                                                                                             _mandatory_params,
                                                                                             key_string, _alert_names,
                                                                                             user_provided_params_map)
        if len(failed_mms_data) > 0:
            logger.info("**********************************************************************")
            logger.info("Below are the files failed to process. Process manually")
            logger.info("**********************************************************************")
            failed_files = "\n".join(d["file"] for d in failed_mms_data)
            logger.info(failed_files)

    def process_wcnp(self):
        loc = "international-tech/intl-sre/golden-signals/rules/production/wcnp"
        key_mapper = {"name_space": "namespace"}
        # alert_owner_category required while creating inventory file name
        # alert_type: depending on this we create wcnp alert or not using while creating inventory file name
        additional_params = ["tier", "market", "alert_owner_category", "alert_type","alert_id"]
        template_name = "wcnp_alerts.yaml"
        user_provided_params_map = {"alert_type": "wcnp"}
        self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params,
                                      user_provided_params_map)

    def process_sql_alerts(self):
        loc = "international-tech/intl-sre/golden-signals/rules/production/managed-services/sql"
        key_mapper = {}
        # alert_owner_category required while creating inventory file name
        # alert_type: depending on this we create wcnp alert or not using while creating inventory file name
        additional_params = ["tier", "market", "alert_owner_category", "alert_type", "is_wcnp_env"]
        template_name = "sql_alerts.yaml"
        user_provided_params_map = {"alert_type": "sql"}
        self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params,
                                      user_provided_params_map)

    #
    def process_megacache_alerts(self):
        loc = "international-tech/intl-sre/golden-signals/rules/production/managed-services/megacache"
        key_mapper = {}
        # alert_owner_category required while creating inventory file name
        # alert_type: depending on this we create wcnp alert or not using while creating inventory file name
        additional_params = ["tier", "market", "alert_owner_category", "alert_type", "is_wcnp_env"]
        template_name = "meghacache_alerts.yaml"
        user_provided_params_map = {"alert_type": 'meghacache'}
        self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params,
                                      user_provided_params_map)

    def process_oracle_alerts(self):
        loc = "international-tech/intl-sre/golden-signals/rules/production/managed-services/oracle/app-owner-alerts"
        key_mapper = {}
        # alert_owner_category required while creating inventory file name
        # alert_type: depending on this we create wcnp alert or not using while creating inventory file name
        additional_params = ["tier", "market", "alert_owner_category", "alert_type"]
        template_name = "oracle_alerts.yaml"
        user_provided_params_map = {"alert_type": 'oracle', "alert_owner_category": "sre"}
        self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params,
                                      user_provided_params_map)

    def process_kafka_alerts(self):
        loc = "international-tech/intl-sre/golden-signals/rules/production/managed-services/kafka"
        key_mapper = {}
        # alert_owner_category required while creating inventory file name
        # alert_type: depending on this we create wcnp alert or not using while creating inventory file name
        additional_params = ["tier", "market", "alert_owner_category", "alert_type","kafka_cluster_name"]
        template_name = "kafka_alerts.yaml"
        user_provided_params_map = {"alert_type": 'kafka'}
        self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params,
                                      user_provided_params_map)

    def process_cosmos(self):
        loc = "international-tech/intl-sre/golden-signals/rules/production/managed-services/cosmos"
        key_mapper = {}
        # key_mapper = {"name_space": "namespace"}
        # alert_owner_category required while creating inventory file name
        # alert_type: depending on this we create wcnp alert or not using while creating inventory file name
        additional_params = ["tier", "market", "alert_owner_category", "alert_type"]
        template_name = "cosmos_alerts.yaml"
        user_provided_params_map = {"alert_type": "cosmos"}
        self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params,
                                      user_provided_params_map)

    def process_cassandra(self):
        loc = "international-tech/intl-sre/golden-signals/rules/production/managed-services/cassandra"
        key_mapper = {}
        # key_mapper = {"name_space": "namespace"}
        # alert_owner_category required while creating inventory file name
        # alert_type: depending on this we create wcnp alert or not using while creating inventory file name
        additional_params = ["tier", "market", "alert_owner_category", "alert_type"]
        template_name = "cassandra_alerts.yaml"
        user_provided_params_map = {"alert_type": "cassandra"}
        self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params,
                                      user_provided_params_map)

    def process_oneops(self):
        loc = "international-tech/intl-sre/golden-signals/rules/production/oneops"
        key_mapper = {}
        # key_mapper = {"name_space": "namespace"}
        # alert_owner_category required while creating inventory file name
        # alert_type: depending on this we create wcnp alert or not using while creating inventory file name
        additional_params = ["tier", "market", "alert_owner_category", "alert_type", "env"]
        template_name = "oneops_alerts.yaml"
        user_provided_params_map = {"alert_type": "oneops"}
        self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params,
                                      user_provided_params_map)

    def process_wcp(self):
        """Process WCP alerts using the standard inventory process"""
        loc = "international-tech/intl-sre/golden-signals/rules/production/wcp"
        key_mapper = {"name_space": "namespace"}  # Same as WCNP
        additional_params = ["tier", "market", "alert_owner_category", "alert_type", "alert_id"]
        template_name = "wcnp_alerts.yaml"
        user_provided_params_map = {"alert_type": "wcp"}
        
        return self.process_mms_to_inventory(
            yaml_files_dir=loc,
            template_name=template_name,
            key_mappers=key_mapper,
            additional_params_keys=additional_params,
            user_provided_params_map=user_provided_params_map
        )

    def process_custom_path(self, custom_file_path, template_name, service_type="wcnp", alert_type=None, custom_output_name=None):
        """Process custom path using the standard inventory process
        
        Args:
            custom_file_path: Path to the custom file
            template_name: Name of the template to use
            service_type: Type of service (wcnp, oneops, etc)
            alert_type: Optional alert type override
            custom_output_name: Optional custom name for the inventory file
        """
        # Service-specific configurations
        service_configs = {
            "wcnp": {
                "key_mapper": {"name_space": "namespace"},
                "additional_params": ["tier", "market", "alert_owner_category", "alert_type", "alert_id"],
                "default_alert_type": "wcnp"
            },
            "oneops": {
                "key_mapper": {},
                "additional_params": ["tier", "market", "alert_owner_category", "alert_type", "env"],
                "default_alert_type": "oneops"
            }
        }

        if service_type not in service_configs:
            raise ValueError(f"Unsupported service type: {service_type}. Must be one of {list(service_configs.keys())}")

        config = service_configs[service_type]
        
        # Use service-specific alert_type if not provided
        if alert_type is None:
            alert_type = config["default_alert_type"]

        user_provided_params_map = {
            "alert_type": alert_type,
            "custom_output_name": custom_output_name  # Add custom filename to params
        }

        return self.process_mms_to_inventory(
            yaml_files_dir=custom_file_path,
            template_name=template_name,
            key_mappers=config["key_mapper"],
            additional_params_keys=config["additional_params"],
            user_provided_params_map=user_provided_params_map
        )

    def process_backroom_ims(self):
        """Process Backroom IMS alerts"""
        loc = "international-tech/intl-sre-backroom-ims/golden-signals/rules/production/wcnp/"
        key_mapper = {"name_space": "namespace"}
        additional_params = ["tier", "market", "alert_owner_category", "alert_type", "alert_id"]
        template_name = "wcnp_alerts.yaml"
        user_provided_params_map = {
            "alert_type": "wcnp",
            "custom_output_name": "wcnp_alerts_backroom_ims.yaml"
        }
        return self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params, user_provided_params_map)

    def process_club_receiving(self):
        """Process Club Receiving alerts"""
        loc = "international-tech/intl-sre-club-receiving/golden-signals/rules/production/wcnp/"
        key_mapper = {"name_space": "namespace"}
        additional_params = ["tier", "market", "alert_owner_category", "alert_type", "alert_id"]
        template_name = "wcnp_alerts.yaml"
        user_provided_params_map = {
            "alert_type": "wcnp",
            "custom_output_name": "wcnp_alerts_club_receiving.yaml"
        }
        return self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params, user_provided_params_map)

    def process_ei_bnm(self):
        """Process EI BNM alerts"""
        loc = "international-tech/intl-sre-ei-bnm/golden-signals/rules/production/wcnp/"
        key_mapper = {"name_space": "namespace"}
        additional_params = ["tier", "market", "alert_owner_category", "alert_type", "alert_id"]
        template_name = "wcnp_alerts.yaml"
        user_provided_params_map = {
            "alert_type": "wcnp",
            "custom_output_name": "wcnp_alerts_ei_bnm.yaml"
        }
        return self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params, user_provided_params_map)

    def process_location_ims(self):
        """Process Location IMS alerts"""
        loc = "international-tech/intl-sre-location-ims/golden-signals/rules/production/wcnp/"
        key_mapper = {"name_space": "namespace"}
        additional_params = ["tier", "market", "alert_owner_category", "alert_type", "alert_id"]
        template_name = "wcnp_alerts.yaml"
        user_provided_params_map = {
            "alert_type": "wcnp",
            "custom_output_name": "wcnp_alerts_location_ims.yaml"
        }
        return self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params, user_provided_params_map)

    def process_mfc_bnm(self):
        """Process MFC BNM alerts"""
        loc = "international-tech/intl-sre-mfc-bnm/golden-signals/rules/production/wcnp/"
        key_mapper = {"name_space": "namespace"}
        additional_params = ["tier", "market", "alert_owner_category", "alert_type", "alert_id"]
        template_name = "wcnp_alerts.yaml"
        user_provided_params_map = {
            "alert_type": "wcnp",
            "custom_output_name": "wcnp_alerts_mfc_bnm.yaml"
        }
        return self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params, user_provided_params_map)

    def process_moc_sams(self):
        """Process MOC SAMS alerts"""
        loc = "international-tech/intl-sre-moc-sams/golden-signals/rules/production/wcnp/"
        key_mapper = {"name_space": "namespace"}
        additional_params = ["tier", "market", "alert_owner_category", "alert_type", "alert_id"]
        template_name = "wcnp_alerts.yaml"
        user_provided_params_map = {
            "alert_type": "wcnp",
            "custom_output_name": "wcnp_alerts_moc_sams.yaml"
        }
        return self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params, user_provided_params_map)

    def process_rss(self):
        """Process RSS alerts"""
        loc = "international-tech/intl-sre-rss/golden-signals/rules/production/wcnp/"
        key_mapper = {"name_space": "namespace"}
        additional_params = ["tier", "market", "alert_owner_category", "alert_type", "alert_id"]
        template_name = "wcnp_alerts.yaml"
        user_provided_params_map = {
            "alert_type": "wcnp",
            "custom_output_name": "wcnp_alerts_rss.yaml"
        }
        return self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params, user_provided_params_map)

    def process_salesfloor_ims(self):
        """Process Salesfloor IMS alerts"""
        loc = "international-tech/intl-sre-salesfloor-ims/golden-signals/rules/production/wcnp/"
        key_mapper = {"name_space": "namespace"}
        additional_params = ["tier", "market", "alert_owner_category", "alert_type", "alert_id"]
        template_name = "wcnp_alerts.yaml"
        user_provided_params_map = {
            "alert_type": "wcnp",
            "custom_output_name": "wcnp_alerts_salesfloor_ims.yaml"
        }
        return self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params, user_provided_params_map)

    def process_store_receiving_bnm(self):
        """Process Store Receiving BNM alerts"""
        loc = "international-tech/intl-sre-store-receiving-bnm/golden-signals/rules/production/wcnp/"
        key_mapper = {"name_space": "namespace"}
        additional_params = ["tier", "market", "alert_owner_category", "alert_type", "alert_id"]
        template_name = "wcnp_alerts.yaml"
        user_provided_params_map = {
            "alert_type": "wcnp",
            "custom_output_name": "wcnp_alerts_store_receiving_bnm.yaml"
        }
        return self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params, user_provided_params_map)

    def process_stores_storeims(self):
        """Process Stores StoreIMS alerts"""
        loc = "international-tech/intl-sre-stores-storeims/golden-signals/rules/production/wcnp/"  # Note: uses app-alerts
        key_mapper = {"name_space": "namespace"}
        additional_params = ["tier", "market", "alert_owner_category", "alert_type", "alert_id"]
        template_name = "wcnp_alerts.yaml"
        user_provided_params_map = {
            "alert_type": "wcnp",
            "custom_output_name": "wcnp_alerts_stores_storeims.yaml"
        }
        return self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params, user_provided_params_map)

    def process_stride_bnm(self):
        """Process Stride BNM alerts"""
        loc = "international-tech/intl-sre-stride-bnm/golden-signals/rules/production/wcnp/"
        key_mapper = {"name_space": "namespace"}
        additional_params = ["tier", "market", "alert_owner_category", "alert_type", "alert_id"]
        template_name = "wcnp_alerts.yaml"
        user_provided_params_map = {
            "alert_type": "wcnp",
            "custom_output_name": "wcnp_alerts_stride_bnm.yaml"
        }
        return self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params, user_provided_params_map)

    def process_transportation_services(self):
        """Process Transportation Services alerts"""
        loc = "international-tech/intl-sre-transportation-services/golden-signals/rules/production/wcnp/"
        key_mapper = {"name_space": "namespace"}
        additional_params = ["tier", "market", "alert_owner_category", "alert_type", "alert_id"]
        template_name = "wcnp_alerts.yaml"
        user_provided_params_map = {
            "alert_type": "wcnp",
            "custom_output_name": "wcnp_alerts_transportation_services.yaml"
        }
        return self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params, user_provided_params_map)

    def process_cpc_ca(self):
        """Process CPC Canada alerts"""
        loc = "international-tech/intl-cpc-ca/golden-signals/rules/production/wcnp/"
        key_mapper = {"name_space": "namespace"}
        additional_params = ["tier", "market", "alert_owner_category", "alert_type", "alert_id"]
        template_name = "wcnp_alerts.yaml"
        user_provided_params_map = {
            "alert_type": "wcnp",
            "custom_output_name": "wcnp_alerts_cpc_ca.yaml"
        }
        return self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params, user_provided_params_map)

    def process_cpc_mx(self):
        """Process CPC Mexico alerts"""
        loc = "international-tech/intl-cpc-mx/golden-signals/rules/production/wcnp/"
        key_mapper = {"name_space": "namespace"}
        additional_params = ["tier", "market", "alert_owner_category", "alert_type", "alert_id"]
        template_name = "wcnp_alerts.yaml"
        user_provided_params_map = {
            "alert_type": "wcnp",
            "custom_output_name": "wcnp_alerts_cpc_mx.yaml"
        }
        return self.process_mms_to_inventory(loc, template_name, key_mapper, additional_params, user_provided_params_map)


if __name__ == "__main__":
    # Setup logging
    log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(log_level)
    log_format = '%(asctime)s %(filename)25s %(lineno)3d %(levelname)10s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setLevel(level=logging.DEBUG)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)


    # try:
    #     logger.info("Processing WCNP alerts")
    #     watcher = GitHubRepoWatcher()
    #     watcher.process_wcnp()
    # except Exception as e:
    #     logger.error(f"Error processing Backroom IMS alerts: {str(e)}")

    # try:
    #     logger.info("Processing WCP alerts")
    #     watcher = GitHubRepoWatcher()
    #     watcher.process_wcp()
    # except Exception as e:
    #     logger.error(f"Error processing Backroom IMS alerts: {str(e)}")

    # Process each service with a new watcher instance
    try:
        logger.info("Processing Backroom IMS alerts")
        watcher = GitHubRepoWatcher()
        watcher.process_backroom_ims()
    except Exception as e:
        logger.error(f"Error processing Backroom IMS alerts: {str(e)}")

    try:
        logger.info("Processing Club Receiving alerts")
        watcher = GitHubRepoWatcher()
        watcher.process_club_receiving()
    except Exception as e:
        logger.error(f"Error processing Club Receiving alerts: {str(e)}")

    try:
        logger.info("Processing EI BNM alerts")
        watcher = GitHubRepoWatcher()
        watcher.process_ei_bnm()
    except Exception as e:
        logger.error(f"Error processing EI BNM alerts: {str(e)}")

    try:
        logger.info("Processing Location IMS alerts")
        watcher = GitHubRepoWatcher()
        watcher.process_location_ims()
    except Exception as e:
        logger.error(f"Error processing Location IMS alerts: {str(e)}")

    try:
        logger.info("Processing CPC Canada alerts")
        watcher = GitHubRepoWatcher()
        watcher.process_cpc_ca()
    except Exception as e:
        logger.error(f"Error processing CPC Canada alerts: {str(e)}")

    try:
        logger.info("Processing CPC Mexico alerts")
        watcher = GitHubRepoWatcher()
        watcher.process_cpc_mx()
    except Exception as e:
        logger.error(f"Error processing CPC Mexico alerts: {str(e)}")

    try:
        logger.info("Processing MFC BNM alerts")
        watcher = GitHubRepoWatcher()
        watcher.process_mfc_bnm()
    except Exception as e:
        logger.error(f"Error processing MFC BNM alerts: {str(e)}")

    try:
        logger.info("Processing MOC SAMS alerts")
        watcher = GitHubRepoWatcher()
        watcher.process_moc_sams()
    except Exception as e:
        logger.error(f"Error processing MOC SAMS alerts: {str(e)}")

    try:
        logger.info("Processing RSS alerts")
        watcher = GitHubRepoWatcher()
        watcher.process_rss()
    except Exception as e:
        logger.error(f"Error processing RSS alerts: {str(e)}")

    try:
        logger.info("Processing Salesfloor IMS alerts")
        watcher = GitHubRepoWatcher()
        watcher.process_salesfloor_ims()
    except Exception as e:
        logger.error(f"Error processing Salesfloor IMS alerts: {str(e)}")

    try:
        logger.info("Processing Store Receiving BNM alerts")
        watcher = GitHubRepoWatcher()
        watcher.process_store_receiving_bnm()
    except Exception as e:
        logger.error(f"Error processing Store Receiving BNM alerts: {str(e)}")

    try:
        logger.info("Processing Stores StoreIMS alerts")
        watcher = GitHubRepoWatcher()
        watcher.process_stores_storeims()
    except Exception as e:
        logger.error(f"Error processing Stores StoreIMS alerts: {str(e)}")

    try:
        logger.info("Processing Stride BNM alerts")
        watcher = GitHubRepoWatcher()
        watcher.process_stride_bnm()
    except Exception as e:
        logger.error(f"Error processing Stride BNM alerts: {str(e)}")

    try:
        logger.info("Processing Transportation Services alerts")
        watcher = GitHubRepoWatcher()
        watcher.process_transportation_services()
    except Exception as e:
        logger.error(f"Error processing Transportation Services alerts: {str(e)}")

    logger.info("Completed processing all services")

    
