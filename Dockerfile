ARG basePythonOSVersion=3.10
FROM hub.docker.prod.walmart.com/library/python:${basePythonOSVersion}

## add
RUN python3 -m pip config set global.index-url https://repository.walmart.com/repository/pypi-proxy/simple/
RUN python3 -m pip config set global.trusted-host repository.walmart.com
## add

ENV PYTHONUNBUFFERED=1
ENV http_proxy "http://sysproxy.wal-mart.com:8080"
ENV https_proxy "http://sysproxy.wal-mart.com:8080"

## add
RUN apt-get update && apt-get install -y apt-transport-https
RUN apt-get install -y gnupg
RUN apt-get install build-essential -y
RUN apt update && apt install -y python3-dev build-essential libssl-dev libffi-dev cmake
WORKDIR /app

## add
RUN version=$(grep '^VERSION_ID=' /etc/os-release | awk -F'"' '{ print $2"."0 }') && \
    codename=$(grep '^VERSION_CODENAME=' /etc/os-release | awk -F'=' '{ print $2 }') && \
    echo "Setup Debian ${version} ${codename} package repositories" && \
    echo "deb [trusted=yes] http://ark-repos.wal-mart.com/ark/apt/published/debian/${version}/direct/soe/noenv/os/ $codename main" > /etc/apt/sources.list && \
    echo "deb [trusted=yes] http://ark-repos.wal-mart.com/ark/apt/published/debian/${version}/direct/soe/noenv/updates/ $codename-updates main" >> /etc/apt/sources.list && \
    echo "deb [trusted=yes] http://ark-repos.wal-mart.com/ark/apt/published/debian/${version}/direct/soe/noenv/security/ $codename-updates main" >> /etc/apt/sources.list && \
    echo "deb [trusted=yes] http://ark-repos.wal-mart.com/ark/apt/published/debian/${version}/direct/soe/noenv/third-party/ $codename main" >> /etc/apt/sources.list && \
    echo "deb [trusted=yes] http://ark-repos.wal-mart.com/ark/apt/published/debian/${version}/direct/soe/noenv/wm-apps/ $codename main" >> /etc/apt/sources.list
## add

ENV APP_USER app
ENV APP_GROUP app
ENV artifact_version 2.10.3
# RUN deb http://archive.debian.org/debian-security stretch/updates main
# RUN apt update
# RUN apt install vim
ENV CB_ENV prod
ENV DB qa
ENV MODE api
ENV IP 127.0.0.1
RUN mkdir -p /app/juno/logs
ADD . /app/juno/
RUN chmod -R 777  /app/juno
RUN groupadd -g 10000 $APP_GROUP && \
    useradd -u 10000 -d /app -s /bin/sh -g $APP_GROUP $APP_USER && \
    chown -R $APP_USER:$APP_USER /app
RUN chown -R $APP_USER:$APP_USER /app/juno/logs
RUN chmod -R 777  /app/juno/logs
USER 10000
RUN pip install -i https://pypi.ci.artifacts.walmart.com/artifactory/api/pypi/pythonhosted-pypi-release-remote/simple  --upgrade pip --user
RUN pip install -i https://pypi.ci.artifacts.walmart.com/artifactory/api/pypi/pythonhosted-pypi-release-remote/simple -r /app/juno/requirements.txt --user
WORKDIR /app/juno/
RUN chmod +x /app/juno/docker-init.sh
EXPOSE 8080

ENTRYPOINT [ "/app/juno/docker-init.sh" ]
