import requests, logging
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime

logger = logging.getLogger(__name__)

anomaly_alerts_url = "http://sp-mercury-api.walmartlabs.com/v1/marketplace/anomalies/anomaly-alerts"


class Anomaly:
    def __init__(self, offer_id, offer_status, availability_status, available_quantity, item_id, product_category,
                 seller_id, seller_type, store_id, price, last_price, alert_source, event_type, is_whitelisted,
                 tenant_id, is_suppressed):
        self.offer_id = offer_id
        self.offer_status = offer_status
        self.availability_status = availability_status
        self.available_quantity = available_quantity
        self.item_id = item_id
        self.product_category = product_category
        self.seller_id = seller_id
        self.store_id = store_id
        self.seller_type = seller_type
        self.price = price
        self.last_price = last_price
        self.alert_source = alert_source
        self.event_type = event_type
        self.is_whitelisted = is_whitelisted
        self.tenant_id = tenant_id
        self.is_suppressed = is_suppressed

    def __eq__(self, other):
        if not isinstance(other, Anomaly):
            # don't attempt to compare against unrelated types
            return NotImplemented

        return self.offer_id == other.offer_id and \
               self.offer_status == other.offer_status and \
               self.availability_status == other.availability_status and \
               self.available_quantity == other.available_quantity and \
               self.item_id == other.item_id and \
               self.product_category == other.product_category and \
               self.seller_id == other.seller_id and \
               self.price == other.price and \
               self.last_price == other.last_price and \
               self.event_type == other.event_type and \
               self.is_whitelisted == other.is_whitelisted and \
               self.tenant_id == other.tenant_id and \
               self.is_suppressed == other.is_suppressed


def get_anomaly_alerts(offer_id, priority=1, days_old=50):
    body = {"offerId": offer_id, "priority": priority, "daysOld": days_old}
    response = requests.post(anomaly_alerts_url, json=body, verify=False)
    if response.ok:
        _response = get_latest_anomaly(response.json())
        return offer_id, _response


def get_latest_anomaly(data):
    latest_dict = max(data, key=lambda x: to_epoch_time(x['lastPriceCreatedOn']))
    return latest_dict


def to_epoch_time(date_str):
    dt_object = datetime.strptime(date_str, '%Y-%m-%dT%H:%M:%S.%fZ')
    return dt_object.timestamp()


def get_anomaly_alerts_by_item_and_offer(offer_id, item_id=None, seller_id=None, priority=1, days_old=50, retry=3):
    response = None
    body = {"offerId": offer_id, "priority": priority, "daysOld": days_old}
    for iteration in range(retry):
        response = requests.post(anomaly_alerts_url, json=body, verify=False)
        if response.ok:
            break
        elif response.status_code==404:
            return offer_id, dict()
        else:
            logger.warning("Offer anomaly endpoint throwing {} for {}", format(response.status_code, offer_id))

    if response:
        anomalies = list()
        data = response.json()
        if item_id or seller_id:
            for anomaly in data:
                if anomaly.get("itemId") == item_id:
                    anomalies.append(anomaly)
                if anomaly.get('sellerId') == seller_id:
                    anomalies.append(anomaly)
            data = anomalies
        _response = get_latest_anomaly(data)
        return offer_id, _response
    return offer_id, dict()


def get_all_anomaly_alerts(offers):
    futures = list()
    threads = 20 if len(offers) > 20 else len(offers)
    pool = ThreadPoolExecutor(threads)
    for offer_id in offers:
        futures.append(pool.submit(get_anomaly_alerts, offer_id))

    for _future in futures:
        try:
            res = _future.result()
        except Exception as e:
            logger.exception(e)


def get_all_anomaly_alerts_item_seller(data):
    response = list()
    futures = list()
    threads = 20 if len(data) > 20 else len(data)
    pool = ThreadPoolExecutor(threads)
    for _data in data:
        futures.append(pool.submit(get_anomaly_alerts_by_item_and_offer, _data.get("offer_id", None),
                                   _data.get("item_id", None),
                                   _data.get("seller_id", None)))
    for _future in futures:
        try:
            offer_id, anomalies = _future.result()
            if not anomalies:
                data = {"offerId": offer_id, "note": "No anomaly"}
            else:
                data = anomalies
            response.append(data)
        except Exception as e:
            logger.exception(e)
    return response


if __name__ == '__main__':
    import json

    # status, res = get_anomaly_alerts("F4AF6F88008133768D5C690386D4244E")
    data = [{"offer_id":"FD2C3C4F055637549E4631F8C8B7C398"},
            {"offer_id":"14256FF091A235298FBB1BB2D0B83A43"},
            {"offer_id":"85DFAE320FE73B8BB155BAD6A65CA04B"},
            {"offer_id":"33624902369835D0814D162F564E9C04"},
            {"offer_id":"04B2C0C70495319DB1D06A6DDB1A75B2"},
            {"offer_id":"27BD9C03A3D737BA8C4B5A8F563ADC46"},
            {"offer_id":"9EF4F17A9EB338639F64D23FCBBE0FF1"},
            {"offer_id": "BBBC911D33BA34A4A7FF4FC19E208C71"},
            {"offer_id": "F4AF6F88008133768D5C690386D4244E"}
            ]
    # status, res = get_anomaly_alerts_by_item_and_offer("F4AF6F88008133768D5C690386D4244E",seller_id='F3BCF15BD01043D8A448870C937A88E1')
    status = get_all_anomaly_alerts_item_seller(data)
    print(status)
    # as_list = res
    # as_Set = frozenset(json.dumps(as_list))

    print()
