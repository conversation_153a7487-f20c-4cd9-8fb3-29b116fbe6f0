import logging, requests
import os.path
import time
from libs.item_service import ItemService
from rest_framework.response import Response
from rest_framework.decorators import api_view
from drf_yasg.utils import swagger_auto_schema
from pathlib import Path
import settings
from libs.ad_group_validation import does_user_belongs_to_ad_group
from libs.atg_client import ATG
from libs.oneOps import OneOps
from i5.i5_handler import process_upcs, process_urls, pull_all_canada_images
from i5.cache_flush import flush_akamai_cp_flush
from libs.iro_service_handler import <PERSON><PERSON><PERSON>acheHand<PERSON>
from django.http import HttpResponse
from i5.i5_handler import process_upcs, process_urls, process_mx_images
from i5.cache_flush import flush_akamai_cp_flush
from libs.iro_service_handler import <PERSON>ha<PERSON>ache<PERSON>and<PERSON>
from rest_framework.parsers import FileUpload<PERSON><PERSON><PERSON>, <PERSON>Part<PERSON>ars<PERSON>, Form<PERSON>arser, JSONParser
from rest_framework.decorators import parser_classes
from slack_bot.slack import send_message
from concurrent.futures import Thread<PERSON><PERSON><PERSON>xecutor
from settings import TEMPLATE_OUTPUT_DIR, ASYNC
from libs.shell import bash
from i5.cache_flush import flush
import csv
from libs.prometheus_client import Prometheus, Analysis
from sre.utils import get_anomaly_alerts, get_all_anomaly_alerts_item_seller
from common.alert.alerts_improved import Alerts
from datetime import datetime
from libs.async_executor import async_job_handler
from libs.async_executor import task
from kitt_handler.git.repo_handler import GitRepoHandler
from libs.carrier_method_service import CarrierMethodService

logger = logging.getLogger(__name__)
SSH_SERVER = ASYNC.get("SSH_SERVER")
SSH_SERVER_BASE_FOLDER = ASYNC.get("SSH_SERVER_BASE_FOLDER")
UPLOAD_FILE_NAME = ASYNC.get("UPLOAD_FILE_NAME")
US_DOMAIN = "https://i5-mx.walmartimages.com"


@swagger_auto_schema(method='post')  # get_template_variables_and_default_vars
@api_view(['POST'])
def sams_bcdc_switch_enable_rest_app(request):
    send_message("juno_logs", f"User called , {request.get_full_path()}")
    # oneops_org, oneops_assembly, oneops_plarform, oneops_env
    return handle_bcdc_switch(request, enable=True, check_value="Y", oneops_org="mexicoecomm",
                              oneops_assembly="samsestore", oneops_plarform="restapp",
                              oneops_env="prod")


@swagger_auto_schema(method='post')  # get_template_variables_and_default_vars
@api_view(['POST'])
def sams_bcdc_switch_disable_rest_app(request):
    send_message("juno_logs", f"User called , {request.get_full_path()}")
    return handle_bcdc_switch(request, enable=False, check_value="N", oneops_org="mexicoecomm",
                              oneops_assembly="samsestore", oneops_plarform="restapp",
                              oneops_env="prod")


@api_view(['GET'])
def sams_bcdc_switch_status_rest_app(request):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        results, pct_values = process_bcdc_switch_status("mexicoecomm", "samsestore", "restapp", "prod")
        return Response({"ok": True, "body": {"results": results, "pct_processed": pct_values}}, status=200)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


@swagger_auto_schema(method='post')  # get_template_variables_and_default_vars
@api_view(['POST'])
def sams_bcdc_switch_enable_mobi_app(request):
    send_message("juno_logs", f"User called , {request.get_full_path()}")
    return handle_bcdc_switch(request, enable=True, check_value="Y", oneops_org="mexicoecomm",
                              oneops_assembly="samsmob", oneops_plarform="mobiapp",
                              oneops_env="prod")


@swagger_auto_schema(method='post')
@api_view(['POST'])
def spotlight_hook(request):
    logger.info(request.data)
    send_message("juno_logs", f"User called , {request.data}")
    return Response({"ok": False, "body": request.data}, status=200)


@swagger_auto_schema(method='post')  # get_template_variables_and_default_vars
@api_view(['POST'])
def sams_bcdc_switch_disable_mobi_app(request):
    send_message("juno_logs", f"User called , {request.get_full_path()}")
    return handle_bcdc_switch(request, enable=False, check_value="N", oneops_org="mexicoecomm",
                              oneops_assembly="samsmob", oneops_plarform="mobiapp",
                              oneops_env="prod")


@api_view(['GET'])
def sams_bcdc_switch_status_mobi_app(request):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        results, pct_values = process_bcdc_switch_status("mexicoecomm", "samsmob", "mobiapp", "prod")
        return Response({"ok": True, "body": {"results": results, "pct_processed": pct_values}}, status=200)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


def bool_convertor(val):
    if type(val) != bool:
        val = val.lower()
        if val in ('y', 'yes', 't', 'true', 'on', '1'):
            return True
        elif val in ('n', 'no', 'f', 'false', 'off', '0'):
            return False
        return False
    else:
        return val


def process_bcdc_switch(enable, check_value, pct_value, oneops_org, oneops_assembly, oneops_plarform, oneops_env):
    """
    Process bcdc switch
    enable=True, respective check_value "Y"
    enable=False, respective check_value "N"
    """
    # hosts = get_hosts("mexicoecomm", "samsestore", "restapp", "prod")
    hosts = get_hosts(oneops_org, oneops_assembly, oneops_plarform, oneops_env)
    atg = ATG(hosts=hosts)
    if enable:
        hosts, pct = atg.enable_paypal_switch(pct_value)
    else:
        hosts, pct = atg.dis_paypal_switch(pct_value)

    return atg._get_status(check_value=check_value), pct


def process_bcdc_switch_status(oneops_org, oneops_assembly, oneops_plarform, oneops_env):
    """
    Process bcdc switch
    enable=True, respective check_value "Y"
    enable=False, respective check_value "N"
    """
    # hosts = get_hosts("mexicoecomm", "samsestore", "restapp", "prod")
    hosts = get_hosts(oneops_org, oneops_assembly, oneops_plarform, oneops_env)
    atg = ATG(hosts=hosts)
    return atg.get_status()


def get_hosts(org, assembly, platform, env):
    oneops = OneOps()
    data = oneops.get_hostname_and_ip(org, assembly, platform, env)
    return [host_map.get("hostname") for host_map in data]


def handle_bcdc_switch(request, enable, check_value, oneops_org, oneops_assembly, oneops_plarform, oneops_env):
    access_ad_group = ["intl-core-sre"]
    try:
        by_pass = bool_convertor(request.query_params.get("by_pass", False))
        user_id = request.data.get("user_id")
        user_email = request.data.get("email")
        display_name = request.data.get("display_name")
        pct_value = float(request.data.get("pct_value"))
        if not pct_value:
            return Response({"ok": False, "body": {"message": "Provide 'pct_value', how much percentage of "
                                                              "enable/disable"}}, status=400)

        is_user_has_ad_group_have_access = does_user_belongs_to_ad_group(user_id, access_ad_group)
        if not by_pass:
            if not is_user_has_ad_group_have_access:
                return Response({"ok": True, "body": {"message": "User does not have access, "
                                                                 "should be part of {}".format(access_ad_group)}},
                                status=401)
        logger.info(f"Sams BCDC switch enable operation performed by {display_name}, user_id is {user_id},"
                    f" email is {user_email}")
        results, pct_done = process_bcdc_switch(enable=enable, check_value=check_value, pct_value=pct_value,
                                                oneops_org=oneops_org, oneops_assembly=oneops_assembly,
                                                oneops_plarform=oneops_plarform, oneops_env=oneops_env)

        return Response({"ok": True, "body": {"results": results,
                                              "pct_processed": pct_done}}, status=200)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


@swagger_auto_schema(method='post')  # get_template_variables_and_default_vars
@api_view(['POST'])
def flush_i5_cache_and_optimize_image_using_upcs(request):
    try:
        try:
            send_message("juno_logs", f"User called , {request.get_full_path()} {request.get_host()}")
        except Exception as e:
            send_message("juno_logs", f"User called , {request.get_full_path()}")
            pass
        upcs = request.data.get("upcs")
        banner = request.data.get("banner")
        v2 = request.data.get("v2", True)
        sleep_interval = request.data.get("sleep_interval", 2)
        default_url_size = request.data.get("default_url_size", 110)
        process_all_pixel_variations = request.data.get("process_all_pixel_variations", True)
        required_torbit_flush = request.data.get("required_torbit_flush", False)
        config = request.data.get("config", False)
        required_torbit_flush = request.data.get("required_torbit_flush", False)
        data = process_i5_flow(upcs, banner, process_all_pixel_variations, sleep_interval=sleep_interval,
                               is_images=False, v2=v2, default_url_size=default_url_size,
                               config=config, required_torbit_flush=required_torbit_flush,
                               scraping_required=True)

        return Response({"ok": True, "body": {"results": data}}, status=200)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


@swagger_auto_schema(method='post')  # get_template_variables_and_default_vars
@api_view(['POST'])
def flush_akamai_urls(request):
    urls = request.data.get("urls", [])
    banner = request.data.get("banner")

    if not banner:
        return Response({"ok": False, "body": "Banner not provided"}, status=400)

    if not urls:
        return Response({"ok": False, "body": "No URLs provided"}, status=400)

    us_urls, other_urls = ([url for url in urls if url.startswith(US_DOMAIN)],
                           [url for url in urls if not url.startswith(US_DOMAIN)])

    aka_status, aka_urls = [], []
    for batch, tenant in [(us_urls, "us"), (other_urls, banner)]:
        if batch:
            status, _, processed, _ = flush(batch, tenant, required_torbit_flush=False)
            aka_status += status
            aka_urls += processed

    return Response({"ok": True, "body": {"status": aka_status, "urls": aka_urls}}, status=200)


@swagger_auto_schema(method='post')  # get_template_variables_and_default_vars
@api_view(['POST'])
def flush_i5_cache_and_optimize_image(request):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        images = request.data.get("images")
        banner = request.data.get("banner")
        sleep_interval = request.data.get("sleep_interval", 2)
        default_url_size = request.data.get("default_url_size", 110)
        scraping_required = request.data.get("scraping_required", False)
        v2 = request.data.get("v2", True)
        process_all_pixel_variations = request.data.get("process_all_pixel_variations", True)
        config = request.data.get("config", False)
        required_torbit_flush = request.data.get("required_torbit_flush", False)

        is_image = True
        # if len(images) > 0:
        #     if len(images[0]) > 14:
        #         is_image = True

        if banner in ["od", "ea", "sams"] and is_image:
            data = process_mx_images(images, banner, v2=v2, default_url_size=default_url_size, config=config,
                                     required_torbit_flush=False,
                                     ignore_kafka_call=False, sleep_interval=sleep_interval)
        else:
            data = process_i5_flow(images, banner, process_all_pixel_variations, sleep_interval=sleep_interval,
                                   is_images=True, v2=v2, default_url_size=default_url_size,

                                   config=config, required_torbit_flush=required_torbit_flush,
                                   scraping_required=scraping_required)

        return Response({"ok": True, "body": {"results": data}}, status=200)

    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


# flush_akamai_cp_flush
@swagger_auto_schema(method='post')  # get_template_variables_and_default_vars
@api_view(['POST'])
def flush_akamai_cp_codes(request):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        cp_codes = request.data.get("cp_codes")
        banner = request.data.get("banner")

        data = flush_akamai_cp_flush(cp_codes=cp_codes, banner=banner)
        return Response({"ok": True, "body": {"results": data}}, status=200)

    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


def process_i5_flow(images_or_upcs, banner, process_all_pixel_variations, sleep_interval=15, is_images=False,

                    v2=True, default_url_size=50, config=None, required_torbit_flush=False, scraping_required=True):
    """
    scraping_required: False, for images. Means we are not going to make a api call pull the images.
                        only used for pipe line API.

    """

    images = images_or_upcs
    banner = banner
    if not images or not banner:
        return Response({"ok": False, "body": "provide upcs and banner details"}, status=400)

    if not images or not banner:
        return Response({"ok": False, "body": "provide upcs and banner details"}, status=400)

    if banner == "ca" and not is_images:
        images = pull_all_canada_images(images)
        is_images = True
        scraping_required = False
    # process_urls(imgs, banner="od", scrolling_required=True, process_all_pixel_variations=True)
    return process_urls(images, banner=banner, scrolling_required=scraping_required,
                        process_all_pixel_variations=process_all_pixel_variations,
                        sleep_interval=sleep_interval, is_images=is_images, v2=v2, default_url_size=default_url_size,
                        config=config, required_torbit_flush=required_torbit_flush)


@swagger_auto_schema(method='post')  # get_template_variables_and_default_vars
@api_view(['POST'])
def iro_item_cache_delete_by_upcs(request):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        upcs = request.data.get("upcs")
        banner = request.data.get("banner")
        is_item_id = request.data.get("is_item_flush", False)
        results = process_iro_cache_flush(upcs, banner, call_item_flush_procedure=is_item_id)
        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


@swagger_auto_schema(method='post')  # get_template_variables_and_default_vars
@api_view(['POST'])
def iro_item_cache_get_by_upc(request):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        upcs = request.data.get("upcs")
        banner = request.data.get("banner")
        is_item_id = request.data.get("is_item_flush", False)
        results = get_iro_cache_flush_data(upcs, banner, is_item_id=is_item_id, call_item_flush_procedure=True)
        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


@swagger_auto_schema(method='post')  # get_template_variables_and_default_vars
@api_view(['POST'])
def pno_cache_delete_by_item(request):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        upcs = request.data.get("upcs_stores")
        banner = request.data.get("banner")
        is_item_id = request.data.get("is_item_flush", False)
        results = process_iro_cache_flush(upcs, banner, call_item_flush_procedure=False)
        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


@swagger_auto_schema(method='post')  # get_template_variables_and_default_vars
@api_view(['POST'])
def check_items_sellable_item(request):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        upcs = request.data.get("upcs_stores")
        banner = request.data.get("banner")
        results = process_iro_cache_flush(upcs, banner, call_item_flush_procedure=False)
        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


@swagger_auto_schema(method='post')  # get_template_variables_and_default_vars
@api_view(['POST'])
def pno_cache_get_by_item(request):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        upcs = request.data.get("upcs_stores")
        banner = request.data.get("banner")
        is_item_id = request.data.get("is_item_flush", False)
        results = process_iro_cache_flush(upcs, banner, call_item_flush_procedure=False)
        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


def serialize_containers_response(data):
    res = list()
    for _data in data:
        for app_id, app_info in _data.items():
            for container in app_info.get('containers'):
                res.append({"namespace": app_info.get('namespace'), "app": app_id,
                            "clusters": ",".join(app_info.get('clusters')), "container": container})
    return res


@swagger_auto_schema(method='post')  # get_template_variables_and_default_vars
@api_view(['POST'])
def get_containers(request):
    try:
        res = list()
        app_details = request.data.get("apps_meta_data", None)
        if not app_details:
            return Response({"ok": True, "body": {"message": "provide app_details as list "}}, status=400)

        prometheus = Prometheus()
        for data in request.data.get("apps_meta_data"):
            res.append(prometheus.get_containers(namespace=data.get("namespace"), app_id=data.get("app_id")))
        response = HttpResponse(
            content_type="text/csv",
            headers={"Content-Disposition": 'attachment; filename="somefilename.csv"'},
        )
        _res = serialize_containers_response(res)

        writer = csv.writer(response)
        writer.writerow(["Namespace", "APP", "Clusters", "Container"])
        for row in _res:
            writer.writerow([row.get("namespace"), row.get("app"), row.get("clusters"), row.get("container")])

        # with open(dst_file, 'rb') as fh:
        #     response = HttpResponse(fh.read(), content_type=mime)
        #     response['Content-Disposition'] = f'attachment; filename={file_name}'
        # return response
        return response
    except Exception as e:
        return Response({"ok": False, "body": {"message": e}}, status=400)


def process_iro_cache_flush(upcs, banner, call_item_flush_procedure=True):
    futures = list()
    results = list()
    pool = ThreadPoolExecutor(len(upcs))
    megha = MeghaCacheHandler()
    ignore_details_from_payload = True
    for upc in upcs:
        if call_item_flush_procedure:
            # banner, item_id, is_item_id=True, ignore_details_from_payload=True
            # megha.delete_item_cache(banner, upc, is_item_id, ignore_details_from_payload)
            futures.append(pool.submit(megha.delete_item_cache, banner, upc, ignore_details_from_payload))
        else:
            # megha.delete_pno_cache(banner, upc.get("upc"), upc.get("store_id"), is_item_id,
            #                       ignore_details_from_payload)
            futures.append(
                pool.submit(megha.delete_pno_cache, banner, upc.get("upc"), upc.get("store_id"),
                            ignore_details_from_payload))
    for future in futures:
        try:
            results.append(future.result())
        except Exception as e:
            logger.exception(e)
    return results


def get_iro_cache_flush_data(upcs, banner, is_item_id, call_item_flush_procedure=True):
    futures = list()
    results = list()
    ignore_details_from_payload = False
    pool = ThreadPoolExecutor(len(upcs))
    megha = MeghaCacheHandler()

    for upc in upcs:
        if call_item_flush_procedure:
            futures.append(pool.submit(megha.get_item_cache_data, banner, upc, is_item_id, ignore_details_from_payload))
        else:
            futures.append(
                pool.submit(megha.get_pno_cache_data, banner, upcs.get("upc"), upcs.get("store_id"), is_item_id,
                            ignore_details_from_payload))
    for future in futures:
        try:
            results.append(future.result())
        except Exception as e:
            logger.exception(e)
    return results


@api_view(['POST'])
def get_iro_details(request):
    """
    Fetch item details including seller availability quantity
    
    Request body:
    {
        "items_or_upcs": ["upc1", "upc2", ...],  # or use "data" field
        "banner": "banner_value"                 # required
    }
    """
    try:
        items_or_upcs = request.data.get("items_or_upcs", request.data.get("data"))
        banner = request.data.get("banner")
        
        if not items_or_upcs:
            return Response({"ok": False, "body": "Missing required items_or_upcs or data parameter"}, status=400)
            
        if not banner:
            return Response({"ok": False, "body": "Missing required banner parameter"}, status=400)
        
        logger.info(f"Getting IRO details for {len(items_or_upcs)} items with banner {banner}")
            
        futures = list()
        results = list()
        pool = ThreadPoolExecutor(max(1, min(len(items_or_upcs), 20)))  # Limit thread pool size
        megha = MeghaCacheHandler()

        # Submit all requests to thread pool
        for upc in items_or_upcs:
            futures.append(pool.submit(megha.get_item_details, banner, upc))
            
        # Process results
        for i, future in enumerate(futures):
            try:
                result = future.result()
                logger.debug(f"Got result for item {i+1}/{len(futures)}: {len(result)} offers")
                results.extend(result)
            except Exception as e:
                logger.exception(f"Error processing result for item {i+1}/{len(futures)}: {str(e)}")
                
        logger.info(f"Processed {len(results)} total offers for {len(items_or_upcs)} items")
        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        logger.exception(f"Error in get_iro_details: {str(e)}")
        return Response({"ok": False, "body": f"Error occurred while processing request: {str(e)}"}, status=400)


@api_view(['POST'])
@parser_classes([MultiPartParser, FormParser, JSONParser, FileUploadParser])
def upload(request):
    _file = request.FILES.getlist('file')[0]
    _meta = request.data.get('meta')
    if not _file:
        return Response({"ok": True, "body": "User not uploaded a file"}, status=200)
    logger.info("Start uploading file to {}".format(_file))
    unique_reference = int(time.time() * 10000)
    file_name = f"file_{unique_reference}"
    bash(f"mkdir -p {TEMPLATE_OUTPUT_DIR}")
    src_upload_file = os.path.join(TEMPLATE_OUTPUT_DIR, file_name)

    config = async_job_handler.get_configs(unique_reference)
    task.JobExecutor.write_json_file(config.get("source_meta_data_file"), _meta)
    msg = f"use request_id to check the status of the job 'api/sre/job/{unique_reference}/' and job is " \
          f"executing at {SSH_SERVER}"

    try:
        with open(config.get("source_data_file"), 'wb+') as destination:
            for chunk in _file.chunks():
                destination.write(chunk)
                logger.info("Finished uploading file {}".format(_file.name))
        try:
            logger.info(f"Starting SCP {src_upload_file} to rsync server {SSH_SERVER_BASE_FOLDER}")
            async_job_handler.upload_data(unique_reference)
            async_job_handler.upload_metadata(unique_reference)
            async_job_handler.execute_async_task(unique_reference)
        except Exception as e:
            logger.exception(e)
            Response({"ok": True, "body": f"Error occurred while processing upload file {e}"}, status=200)
    except Exception as e:
        logger.exception(e)
        Response({"ok": True, "body": f"Error occurred while processing upload file {e}"}, status=200)
    finally:
        bash(f"rm -rf {config.get('source_data_file')}")
        bash(f"rm -rf {config.get('source_meta_data_file')}")
    return Response({"ok": True, "body": {"job_id": unique_reference, "message": msg}}, status=200)


@api_view(['GET'])  # get_template_variables_and_default_vars
def check_batch_job_status_by_item(request, job_id):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        status = async_job_handler.is_job_completed(job_id)
        return Response({"ok": True, "body": {"is_job_completed": status}}, status=200)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)


def is_job_completed(job_id):
    command = f"find {SSH_SERVER_BASE_FOLDER} -type f -name '*_{job_id}_*'"
    ssh_response = bash(f"ssh {SSH_SERVER} {command}", read_lines=True)
    if ssh_response.return_code == 0:
        for _file in ssh_response.stdout:
            if f"{SSH_SERVER_BASE_FOLDER}/completed" in _file:
                return True, _file
    return False, None


@api_view(['GET'])
def download_app_details(request, job_id):
    try:
        status = async_job_handler.is_job_completed(job_id)
        if status:
            _response = async_job_handler.get_response(job_id)
            return Response({"ok": True, "body": {"is_job_completed": status, "data": _response, "job_id": job_id}},
                            status=200)
        if not status:
            return Response({"ok": True, "body": {"is_job_completed": status, "job_id": job_id}},
                            status=200)
        return Response({"ok": False, "body": {"message": "ERROR occurred "}}, status=400)

    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


def get_mime_type(file_name):
    import magic
    mime = magic.from_buffer(open(file_name, "rb").read(2048), mime=True)
    return mime


@api_view(['POST'])
def get_item_anomaly_alerts(request):
    try:
        # priority=1, days_old=50
        offer_id = request.data.get("offer_id")
        priority = request.data.get("priority", 1)
        days_old = request.data.get("days_old", 50)
        if not offer_id:
            return Response({"ok": False, "body": {"message": "provide offer_id"}}, status=400)
        offer_id, response = get_anomaly_alerts(offer_id, priority=priority, days_old=days_old)
        return Response({"ok": False, "body": response}, status=200)

    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def get_item_anomaly_alerts_batch(request):
    try:

        offer_ids = request.data.get('data')
        response = get_all_anomaly_alerts_item_seller(offer_ids)
        return Response({"ok": False, "body": response}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def get_item_visibility_batch(request):
    try:
        item_service = ItemService()
        upcs = request.data.get('data')
        banner = request.data.get("banner")
        response = item_service.get_item_details(banner, upcs)
        return Response({"ok": False, "body": response}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def flush_fastly_cache(request):
    try:
        # priority=1, days_old=50
        uris = request.data.get("uris")
        market = request.data.get("market")
        end_point = settings.FASTLY.get("end_point").format_map(
            {"service_id": settings.FASTLY.get("service_ids").get(market)})

        body = {"surrogate_keys": uris}

        response = requests.post(end_point, json=body, verify=False, headers=settings.FASTLY.get("headers"))
        if response.ok:
            return Response({"ok": True, "body": response.json()}, status=200)
        return Response({"ok": False, "body": response.json()}, status=400)

    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def get_alerts_by_criteria(request):
    try:
        criteria = request.data.get("criteria", {"mms_xmatters_group": "intl-sre-oncall"})
        component = request.data.get("component")
        n_hours = request.data.get("n_hours", 1)
        n_minutes = int(n_hours) * 60
        start_epoch = request.data.get("start_epoch", None)
        end_epoch = request.data.get("end_epoch", None)
        include_latest_timestamp = request.data.get("include_latest_timestamp", False)

        if start_epoch:
            start_epoch = int(start_epoch)
        if end_epoch:
            end_epoch = int(end_epoch)

        alerts = Alerts.get_alerts_by_criteria(
            component=component,
            criteria=criteria,
            n_minutes=n_minutes,
            start_epoch=start_epoch,
            end_epoch=end_epoch
        )

        # Add latest timestamps if requested
        if include_latest_timestamp and alerts:
            Alerts._add_latest_origin_timestamp(alerts)

        return Response({"ok": True, "body": alerts}, status=200)
    except Exception as e:
        logger.exception("Error in get_alerts_by_criteria: %s", str(e))
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['GET'])
def get_alert_by_id(request):
    try:
        alert_id = request.data.get("alert_id")
        n_hours = request.data.get("n_hours", 1)
        n_minutes = int(n_hours) * 60
        start_epoch = request.data.get("start_epoch", None)
        end_epoch = request.data.get("end_epoch", None)
        if start_epoch:
            start_epoch = int(start_epoch)
        if end_epoch:
            end_epoch = int(end_epoch)

        alert = Alerts.get_alert_by_id(alert_id, n_minutes, start_epoch, end_epoch)
        return Response({"ok": True, "body": alert}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


def analyze_promql_data(request, apps):
    payload = request.data
    days = payload.get("days")
    tenant = payload.get("tenant", "mexico")
    prod = payload.get("prod", True)
    duration_minutes = payload.get("duration_minutes", 6)
    baseline_threshold_cpu = payload.get("baseline_threshold_cpu", 10)
    baseline_threshold_mem = payload.get("baseline_threshold_mem", 10)
    cpu_deviation_threshold = payload.get("cpu_deviation_threshold", 30)
    mem_deviation_threshold = payload.get("mem_deviation_threshold", 30)

    analyze = Analysis(apps=apps, days=days, country=tenant)
    analyze.set_environment(prod)
    if len(analyze.days_meta) == 0:
        return Response({"ok": False, "body": {"message": f"Select 'days' to be greater than {days} "}}, status=400)
    analyze.fetch()
    results = analyze.analyze(duration_minutes=duration_minutes,
                              baseline_threshold_cpu=baseline_threshold_cpu,
                              baseline_threshold_mem=baseline_threshold_mem,
                              cpu_deviation_threshold=cpu_deviation_threshold,
                              mem_deviation_threshold=mem_deviation_threshold)
    # metrics = prometheus.analyze_capacity(namespace, app_id)
    return Response({"ok": True, "body": results}, status=200)


@api_view(['POST'])
def analyze_metrics_by_apps(request):
    try:
        apps = request.data.get("apps")
        # apps = [{"namespace": "sct-rap", "app": "rap-orchestration-prod-tg2-async"},
        #         {"namespace": "mx-glass", "app": "mexico-ea-journey-prod"}]
        return analyze_promql_data(request, apps)

    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def analyze_metrics_by_namespace_app(request):
    try:
        namespace = request.data.get("namespace")
        app_id = request.data.get("app_id")
        apps = [{"namespace": namespace, "app": app_id}]
        return analyze_promql_data(request, apps)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def update_kitt_file(request):
    try:
        namespace = request.data.get("namespace")
        app_id = request.data.get("app_id")
        wcnp_profile = request.data.get("wcnp_profile", "prod")
        min_pods = request.data.get("min_pods")
        min_cpu = request.data.get("min_cpu")
        min_memory = request.data.get("min_memory")
        env_name = request.data.get("env_name")
        dry_run = request.data.get("dry_run")
        artifact = request.data.get("artifact")
        file = request.data.get("file")
        create_pr = request.data.get("create_pr", True)  # Default to creating PR

        if dry_run and create_pr:
            return Response({"ok": False, "body": {"message": "To create a PR ,'dry_run' should be false "}}, status=400)

        if not namespace or not app_id:
            return Response({"ok": False, "body": {"message": "Requires valid 'namespace' and 'app_id' "}},
                            status=400)

        if not min_pods and not min_cpu and not min_memory  :
            return Response({"ok": False, "body": {"message": "Please provide either 'min_pods', 'min_cpu' or "
                                                              "'min_memory'"}},status=400)

        git = GitRepoHandler.get_scm_details(namespace, app_id, wcnp_profile)
        handler = GitRepoHandler(git, "****************************************", location=None)
        try:
            status = handler.process_repository()
            if  not status:
                return Response({"ok": False, "body": {"message": f"User 'm0c00jt' does not have access to this "
                                                                  f"repo {git} and Fork also disabled for this repo. Either "
                                                                  "add use to this repo as contributor or enable fork"
                                                       }}, status=400)
            result = handler.update_hpa_configurations(
                min_pods,
                min_cpu,
                min_memory,
                env_name,
                dry_run,
                artifact,
                file,
                create_pr
            )

            # Show summary of results
            if result["num_updated"] > 0:
                action = "committed" if not create_pr else "updated with PR"
                logger.info(f"\nSuccessfully {action} {result['num_updated']} files")
                if result.get("pr_url"):
                    logger.info(f"Pull request: {result.get('pr_url')}")
                elif result.get("commit_hash"):
                    logger.info(f"Commit: {result.get('commit_hash')}")
            else:
                logger.info(f"\n{result.get('message', 'No files were updated')}")

            logger.info(f"\nFiles processed: {len(result['files_used'])}")
            for f in result['files_used']:
                logger.info(f"  - {f['name']}")

            return Response({"ok": True, "body": result}, status=200)
        except Exception as e:
            logger.info(f"Error: {str(e)}")
            return Response({"ok": False, "body": {"message": str(e)}}, status=400)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def get_pods_metrics(request):
    try:
        prometheus = Prometheus()
        apps = request.data.get("apps")
        start_time = request.data.get("start_time")
        end_time = request.data.get("end_time")
        prod = request.data.get("prod", True)
        prometheus.set_environment(prod)
        futures = list()
        metric_results = list()
        size = 10 if len(apps) > 10 else len(apps)
        for data in apps:
            # metrics = prometheus.get_running_pods_count_recent(namespace=data.get("namespace"),
            #                                                    app_label=data.get("app_id"),
            #                                                    start_time=start_time, end_time=end_time)
            pool = ThreadPoolExecutor(size)
            futures.append(pool.submit(prometheus.summarize_runner_pods, namespace=data.get("namespace"),
                                       app_id=data.get("app_id"),
                                       start_time=start_time, end_time=end_time))
        for future in futures:
            try:
                day_results = future.result()
                metric_results.append(day_results)
            except Exception as e:
                pass
        # metrics = prometheus.analyze_capacity(namespace, app_id)
        return Response({"ok": True, "body": metric_results}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def get_carrier_methods(request):
    """
    Retrieve carrier methods and distributor details for one or more distributors/sellers
    with cross-validation between DCC and MCSE systems
    
    Request body:
    {
        "distributor_id": "***********",              # Single distributor ID
        "distributor_ids": ["***********", "***********"],  # Multiple distributor IDs (optional)
        "business_unit": "mx"                         # Optional, defaults to "mx" (or use "sams")
    }
    
    Response includes carrier methods with flags:
    - is_dcc: true if method exists in DCC system
    - is_mcse: true if method exists in MCSE system
    - mcse_details: additional details from MCSE if available
    """
    try:
        distributor_id = request.data.get('distributor_id')
        distributor_ids = request.data.get('distributor_ids')
        business_unit = request.data.get('business_unit', 'mx')
        
        # Handle different input formats
        if distributor_ids:
            # Use provided list
            ids_to_process = distributor_ids
        elif distributor_id:
            # Use single ID
            ids_to_process = distributor_id
        else:
            return Response({"ok": False, "body": {"message": "Either distributor_id or distributor_ids is required"}}, status=400)
        
        service = CarrierMethodService()
        result = service.get_carrier_methods(ids_to_process, business_unit)
        
        if result.get('status') == 'error':
            return Response({"ok": False, "body": {"message": result.get('message')}}, status=400)
        
        return Response({"ok": True, "body": result.get('data')}, status=200)
    except Exception as e:
        logging.exception(e)
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)


@api_view(['POST'])
def get_carrier_methods_cross_validation(request):
    """
    Retrieve and cross-validate carrier methods for one or more distributors/sellers
    between DCC and MCSE systems
    
    Request body:
    {
        "distributor_id": "***********",              # Single distributor ID
        "distributor_ids": ["***********", "***********"],  # Multiple distributor IDs (optional)
        "business_unit": "mx"                         # Optional, defaults to "mx" (or use "sams")
    }
    
    Response includes carrier methods with flags:
    - is_dcc: true if method exists in DCC system
    - is_mcse: true if method exists in MCSE system
    - mcse_details: additional details from MCSE if available
    """
    try:
        distributor_id = request.data.get('distributor_id')
        distributor_ids = request.data.get('distributor_ids')
        business_unit = request.data.get('business_unit', 'mx')
        
        # Handle different input formats
        if distributor_ids:
            # Use provided list
            ids_to_process = distributor_ids
        elif distributor_id:
            # Use single ID
            ids_to_process = distributor_id
        else:
            return Response({"ok": False, "body": {"message": "Either distributor_id or distributor_ids is required"}}, status=400)
        
        service = CarrierMethodService()
        result = service.get_carrier_methods_cross_validation(ids_to_process, business_unit)
        
        if result.get('status') == 'error':
            return Response({"ok": False, "body": {"message": result.get('message')}}, status=400)
        
        return Response({"ok": True, "body": result.get('data')}, status=200)
    except Exception as e:
        logging.exception(e)
        return Response({"ok": False, "body": {"message": str(e)}}, status=400)
