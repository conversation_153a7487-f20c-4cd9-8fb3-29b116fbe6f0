import settings
from libs import util
from libs import shell
from pathlib import Path
import os, logging, requests, json
from git_service import exceptions
from requests.auth import HTTPBasicAuth
from urllib.parse import urlparse, urlencode
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)
NONAME = "****************************************"
USERID = "m0c00jt"
# NONAME = "****************************************"
# USERID = "vn55p0j"
GIT_END_POINT = 'https://gecgithub01.walmart.com'


class GitException(Exception):
    """Base class for other exceptions"""
    pass


class GitApi(object):
    def __init__(self, org, repo, **kwargs):
        super(GitApi, self).__init__()
        self.git_host = kwargs.get("url", GIT_END_POINT)
        self.org = org
        self.repo = repo
        self.headers = {'content-type': 'application/json'}
        self.baseAuth = HTTPBasicAuth(kwargs.get('user', USERID),
                                      kwargs.get('noname', NONAME))
        self.pull_request_api_url = None

    def get_repo_contributors(self):
        url_tail = f"api/v3/repos/{self.org}/{self.repo}/collaborators"
        url = "{}/{}".format(self.git_host, url_tail)
        # url = "https://gecgithub01.walmart.com/api/v3/repos/intl-ecomm-svcs/mms-config/collaborators"

        logger.debug('   Get Pull requests url is  {}'.format(url))
        response = requests.get(url, auth=self.baseAuth, headers=self.headers,
                                verify=False)
        response_json = response.json()
        return response_json

    def is_user_has_contributor_access(self, user, **kwargs):
        url_tail = f"api/v3/repos/{self.org}/{self.repo}/collaborators/{user}"
        url = "{}/{}".format(self.git_host, url_tail)
        # url = "https://gecgithub01.walmart.com/api/v3/repos/intl-ecomm-svcs/mms-config/collaborators/vsolank"

        logger.debug('   Get Pull requests url is  {}'.format(url))
        response = requests.get(url, auth=self.baseAuth, headers=self.headers,
                                verify=False)
        if response.status_code == 204:
            return True
        return False

    def get_user_write_permissions(self, user, **kwargs):
        url_tail = f"api/v3/repos/{self.org}/{self.repo}/collaborators/{user}/permission"
        url = "{}/{}".format(self.git_host, url_tail)
        # url = "https://gecgithub01.walmart.com/api/v3/repos/intl-ecomm-svcs/mms-config/collaborators/m0c00jt/permission"

        logger.debug('   Get Pull requests url is  {}'.format(url))
        response = requests.get(url, auth=self.baseAuth, headers=self.headers,
                                verify=False)
        if response.ok:
            resp = response.json()
            permissions = resp.get("permission")
            if permissions in ["write", "maintain", "admin"]:
                return True
        return False

    def provide_access(self, user, **kwargs):
        status = self.is_user_has_contributor_access(user, **kwargs)
        if not status:
            status, error_message = self.provide_contributor_access(user, **kwargs)
            if status:
                return True, error_message
        return False, "User already have the access"

    def provide_contributor_access(self, user, **kwargs):
        if not self.is_repo_valid():
            return False, f"either org or repo is invalid"

        url_tail = f"api/v3/repos/{self.org}/{self.repo}/collaborators/{user}"
        url = "{}/{}".format(self.git_host, url_tail)
        # url = "https://gecgithub01.walmart.com/api/v3/repos/intl-ecomm-svcs/mms-config/collaborators/m0b03wl"
        data = {"permission": "maintain"}

        logger.debug('   Get Pull requests url is  {}'.format(url))
        response = requests.put(url, auth=self.baseAuth, headers=self.headers, data=json.dumps(data),
                                verify=False)
        if response.status_code == 204:
            return True, None
        return False, "Something went wrong"

    def is_org_valid(self, org):
        """
        Won't work, gives us only 30 results set
        """
        # /orgs/{org}/repos
        url_tail = f"api/v3/organizations"
        url = "{}/{}".format(self.git_host, url_tail)

        logger.debug('   Get Pull requests url is  {}'.format(url))
        response = requests.get(url, auth=self.baseAuth, headers=self.headers, verify=False)
        if response.ok:
            for _org in response.json():
                print(_org)
                orgs = [_org for _org in response.json() if _org.get("login") == org]
            # if len() > 0:
            #     return True
        return False

    def is_repo_valid(self, **kwargs):
        url_tail = f"api/v3/repos/{self.org}/{self.repo}"
        url = "{}/{}".format(self.git_host, url_tail)

        logger.debug('   Get Pull requests url is  {}'.format(url))
        response = requests.get(url, auth=self.baseAuth, headers=self.headers, verify=False)
        if response.ok:
            if response.json().get("name", None):
                return True
        return False

    def add_reviewers(self, reviewers=None):
        logger.info(" **** Started git reviewers task ****")
        url = "{}/requested_reviewers".format(self.pull_request_api_url)
        data = {
            "reviewers": reviewers
        }
        logger.debug('git reviewers request url is  {}'.format(url))
        logger.debug('git reviewers, body url is  {}'.format(json.dumps(data)))
        response = requests.post(url, auth=self.baseAuth, data=json.dumps(data), headers=self.headers,
                                 verify=False)
        if response.ok:
            response = response.json()
            logger.debug('git reviewers response body is {}'.format(json.dumps(response)))
            logger.info("  -- Finished, git reviewers request")
        else:
            logger.error("Response code {}".format(response.status_code))
            raise GitException("Request , response code is {}, should be 200/201".format(response.status_code))

    def get_pull_requests(self, org, repo, head, state, base='main'):
        """

        Args:
            base: Pull request compared too, mostly master/main branch
            head: Newly created branch with new changes
            state: Pull request state

        Returns:

        """
        logger.info(" **** Started git pull all requests rest call ****")
        # api/v3/repos/LabsSearch/{}/pulls
        uri_base = "{}/{}".format(self.git_host, 'api/v3/repos/{}/{}/pulls'.format(org, repo))
        uri_parameters = 'base={}&head={}&state={}'.format(base, head, state)
        url = '{}?{}'.format(uri_base, uri_parameters)
        logger.debug('   Get Pull requests url is  {}'.format(url))
        response = requests.get(url, auth=self.baseAuth, headers=self.headers,
                                verify=False)
        if response.ok:
            response = response.json()
            logger.debug('   All pull requests are {}'.format(json.dumps(response)))
            logger.info("  -- Finished, get pull request with success")
            return response
        else:
            logger.error("Response code {}".format(response.status_code))
            logger.error("  -- Finished, get pull request with errors")
            raise GitException("Request , response code is {}, should be 200/201".format(response.status_code))

    def pre_check_task_to_create_pull_request(self, org, repo, base, head, state='open'):
        """
        Before creating an pull request, we need to check weather we have an existing pull request for the base
        branch and head branch. If yes, then Git only allows one pull request per head and base combination.

        Example:
            base: main
            head: oor

            if one of the pull request is already there then it won't allow to create one more.

        Args:
            head: Newly created branch with new changes
            base: Pull request compared too, mostly master/main branch
            state: open state or close state

        Returns:

        """
        # g.pre_check_task_to_create_pull_request("mms-config", base="main",
        # head="intl-ecomm-svcs:juno_1680722062418469799", state="open")
        response = self.get_pull_requests(org, repo, head, state, base=base)
        logger.info(" Checking existing pull request ")
        if len(response) > 0:
            pulls = [pull['html_url'] for pull in response]
            logger.error(" Found open pull requst/requests , please close ,  before creating new pull request")
            logger.error(
                "  For branch {} , Found open pull requst/requests {},please close ,  before creating new pull request".format(
                    head, pulls))
            return False, pulls
        logger.info(" Done, checking existing pull request. Status: success ")
        return True, list()

    def create_pull_request(self, upstream_org, upstream_repo, fork_org_name, fork_branch_name, base, title):

        """

        Args:
           upstream_org: org name. If we are creating PR against upstream, then org will be upstream_org. Else fork/local org name
           upstream_repo: org name. If we are creating PR against upstream, then repo will be upstream_repo. Else fork/local org name
           fork_org_name: fork branch org
           fork_branch_name: Newly created branch with new changes
           base: Pull request compared too, mostly master/main branch
           title: title for pull request

        Returns:

        """
        branch_name = f"{fork_org_name}:{fork_branch_name}"
        status, has_pulls = self.pre_check_task_to_create_pull_request(upstream_org, upstream_repo, base, branch_name)
        if len(has_pulls) > 0:
            return has_pulls[0]
        logger.info(" **** Started git pull request rest call ****")
        url = "{}/{}".format(self.git_host, f'api/v3/repos/{upstream_org}/{upstream_repo}/pulls')
        data = {"title": title,
                "head": branch_name,
                "base": base
                }
        logger.debug('Pull request url is  {}'.format(url))
        logger.debug('Pull body url is  {}'.format(json.dumps(data)))
        response = requests.post(url, auth=self.baseAuth, data=json.dumps(data), headers=self.headers,
                                 verify=False)
        if response.ok:
            response = response.json()
            self.pull_request_api_url = response['url']
            url = response["html_url"]
            logger.debug('Create pull request response body is {}'.format(json.dumps(response)))
            logger.info("  -- Finished, pull request is {}".format(url))
            return url
        elif response.status_code == 422:
            response = response.json()
            logger.error(response.get("message"))
        else:
            logger.error("Response code {}".format(response.status_code))
            return False

    def get_file_content(self, name=None, git_link_with_file=None):
        if name:
            logger.info(" **** Started git pull request rest call ****")
            url = "{}/{}".format(self.git_host, name)

            logger.info('File request url is  {}'.format(url))
        elif git_link_with_file:
            git_map = Git.get_git_repo_and_file(git_link_with_file)
            url = git_map.get("raw")
        else:
            raise Exception("Provide either name or git_link_with_file ")

        response = requests.get(url, auth=self.baseAuth, headers=self.headers,
                                verify=False)
        if response.ok:
            response = response.content.decode('UTF-8')
            # logger.debug('Git file content is {}'.format(json.dumps(response)))
            logger.info("Successfully receive file content")
            return response
        elif response.status_code == 404:
            logger.info("File not found ")
            return None
        else:
            logger.error("Response code {}".format(response.status_code))
            logger.error("  -- Finished, to get git file content with errors")
            raise GitException("Request , response code is {}, should be 200/201".format(response.status_code))

    def get_file_content_using_url(self, url):
        response = requests.get(url, auth=self.baseAuth, headers=self.headers,
                                verify=False)
        if response.ok:
            response = response.content.decode('UTF-8')
            # logger.debug('Git file content is {}'.format(json.dumps(response)))
            logger.info("Successfully receive file content")
            return response
        elif response.status_code == 404:
            logger.info("File not found ")
            return None
        else:
            logger.error("Response code {}".format(response.status_code))
            logger.error("  -- Finished, to get git file content with errors")
            raise GitException("Request , response code is {}, should be 200/201".format(response.status_code))

    @staticmethod
    def get_git_repo_and_file(git_url: str) -> dict:
        """
        Get Git repo details
        Args:
            git_url: Git url
        Returns:
        """
        path = None
        base_git_repo_url, branch_with_uri = '', ''
        if git_url.endswith("git"):
            return {"repo": git_url, "branch": None, "script": None}
        elif "/blob/" in git_url:
            base_git_repo_url, branch_with_uri = git_url.split("/blob/")
            path = urlparse(base_git_repo_url).path
        elif "/tree/" in git_url:
            base_git_repo_url, branch_with_uri = git_url.split("/tree/")
            path = urlparse(base_git_repo_url).path
        branch, *_file_path = branch_with_uri.split("/")
        script_path = '/'.join(_file_path)

        # add raw to path
        schema, git_host, *paths = [token for token in base_git_repo_url.split('/') if token]
        raw_path = f"{schema}//{git_host}/raw/{'/'.join(paths)}"
        ssh_repo = f"git@{git_host}:{'/'.join(paths)}.git"
        repo_name = paths[-1]
        return {"http_git": f"{base_git_repo_url.rstrip('/')}.git", "branch": branch, "script": script_path,
                "ssh_git": ssh_repo, 'raw': f"{raw_path}/{branch}/{script_path}", "repo_name": repo_name,
                "owner_with_repo": path}


class Git(GitApi):
    """
    This class is used to check out the production-playbooks.git. Steps should be

    1. git = GitCheckout('m0c00jt','created experiments for <namespace> namespace for <app_id> app in <env>
              environment',[cpu.yaml, disk.yaml, 100_pod_kill.yaml ])
    2. git()
    3. git.clone()
    4. git.checkout()
    5. git.add()
    6. git.commit()
    7. git.push()

    Get branch name: git.branch
    Get branch name: git.repo_location
    """

    root_location = os.path.abspath(os.path.expanduser(os.path.expandvars("~")))
    threshold = settings.GIT.get('threshold_disk_free')
    ssh_config = settings.GIT.get('ssh_config')
    ssh_key = settings.GIT.get('ssh_key')
    ssh_config_chmod_command = 'chmod 600 {}'.format(ssh_config)
    # git -c core.sshCommand="ssh -i ~/.ssh/gieo_rsa" clone ***************************:CE-PERFREL/raas.git
    core_ssh_command = f' -c core.sshCommand="ssh -i {ssh_key}" -c user.name="m0c00jt" ' \
                       f' -c user.email="<EMAIL>" '

    # git_repo_name = settings.GIT.get('raas_repo_name')
    # git_org = settings.GIT.get('org')  # CE-PERFREL
    # git_repo = f'***************************:{git_org}/{git_repo_name}.git'

    def __init__(self, git_repo_url, cloned_to_location, branch="main", **kwargs):
        """

        Args:
            git_repo_url: Git repo url
            repo_name: name of the working dir and same name will be used for
            cloned_to_location:
            branch:
            time_stamp:
            **kwargs:
        """
        self.repo, self.org, self.html_url = Git.get_org_and_repo_html_url_from_git_ssh_url(git_repo_url)
        super(Git, self).__init__(self.org, self.repo, **kwargs)
        # branch is used for working_dir
        self.working_folder_checkout_repo_name = self.repo
        # ***************************:CE-PERFREL/rass.git
        self.git_repo_url = git_repo_url
        self.branch = branch
        self.cloned_to_location = cloned_to_location
        self.repo_location = "{}/{}".format(self.cloned_to_location, self.working_folder_checkout_repo_name)
        self.is_commit_failed = False
        self.is_push_failed = False
        self.git_html_url = self.git_repo_url.replace(":", "/").replace("git@", "https://").replace(".git", "")

    def __call__(self, *args, **kwargs):
        """
        Sets all required configs, should be called before executing any function
        """
        logger.info("Check ssh config file exists or not ")
        try:
            if not Git.check_ssh_config_exists():
                logger.exception(exceptions.GitSSHConfigFileDoesNotExists(
                    "ssh config file not found at {}".format(Git.ssh_config)))
                return False

            logger.info(f"Creating working directory: {self.cloned_to_location}")
            working_location_result = shell.bash(f'mkdir -p {self.cloned_to_location}')
            if not Git.check_bash_output(working_location_result):
                logger.exception(
                    exceptions.GitBashCommandFailedToExecute(f"Creating working directory failed to execute,command: "
                                                             f"{working_location_result.command} "
                                                             f"error: {working_location_result.stderr}"))
                return False

            logger.info(f"Working dir {self.cloned_to_location} has enough disk space ")
            if not self.has_required_disk_space(Git.threshold):
                logger.exception(
                    exceptions.GitWorkingDirDoesNotHaveEnoughSpace("Working dir {} does not have enough disk space "
                                                                   "{}".format(self.cloned_to_location,
                                                                               Git.threshold)))
                return False
        except Exception as e:
            logger.exception(f"Exception occurred {e}, so deleting the working dir ")
            shell.bash(f'rm -rf {self.cloned_to_location}')
            return False

        return True

    def get_latest_sha_from_existing_file_system(self):
        """
        Get sha value from current branch, which might have little old code or behind the remote master code.
        Returns:

        """
        base_command = " rev-parse --verify HEAD"
        bash_raas_git_cone_command_result = shell.bash(
            f'''cd {self.cloned_to_location}/{self.working_folder_checkout_repo_name} && git {Git.core_ssh_command} {base_command}''')
        sha = bash_raas_git_cone_command_result.stdout.rstrip("\n")
        logger.info("Latest git sha is {}".format(sha))
        return sha

    def get_latest_sha_from_remote(self):
        """
        Gets remote latest sha values
        Returns:

        """
        base_command = """ ls-remote {} HEAD""".format(self.git_repo_url)
        bash_raas_git_cone_command_result = shell.bash(
            f'''cd {self.cloned_to_location}/{self.working_folder_checkout_repo_name} && git {Git.core_ssh_command} {base_command}''')
        if bash_raas_git_cone_command_result.return_code != 0:
            return None
        sha = bash_raas_git_cone_command_result.stdout.rstrip("\n").split()

        if len(sha) == 0:
            return None
        logger.info("Latest git sha is {}".format(sha[0]))
        return sha[0]
    @staticmethod
    def get_sha_list_from_past_week(ORG_NAME, REPO_NAME, folder):
        """
        Get the list of SHAs from the past week from a remote repository for a specific folder.

        Parameters:
        folder (str): The path to the folder.

        Returns:
        list: The list of SHAs from the past week.
        """
        # GitHub API URL for commits of a repository
        url = f"https://gecgithub01.walmart.com/api/v3/repos/{ORG_NAME}/{REPO_NAME}/commits"

        # Calculate the timestamp for one week ago
        one_week_ago = (datetime.now() - timedelta(weeks=1)).isoformat()
        # print("One week ago:", one_week_ago)

        # Parameters: path for the folder and since set to one week ago
        params = {"path": folder, "since": one_week_ago}

        headers = {'Authorization' : f'token {NONAME}'}

        # Make the request
        response = requests.get(url, params=params, headers=headers)

        # If the request was successful
        if response.status_code == 200:
            # Get the SHAs from the commit data
            data = response.json()
            if data:
                return [commit["sha"] for commit in data]
            else:
                print(f"No commits found for folder {folder} in the past week")
                return []
        else:
            print(f"Request failed with status code {response.status_code}")
            return None

    @staticmethod
    def get_sha_list_from_past_month( ORG_NAME, REPO_NAME, folder):
        """
        Get the list of SHAs from the past month from a remote repository for a specific folder.

        Parameters:
        folder (str): The path to the folder.

        Returns:
        list: The list of SHAs from the past month.
        """
        # GitHub API URL for commits of a repository
        url = f"https://gecgithub01.walmart.com/api/v3/repos/{ORG_NAME}/{REPO_NAME}/commits"

        # Calculate the timestamp for one month ago
        one_month_ago = (datetime.now() - timedelta(weeks=6)).isoformat()

        # Parameters: path for the folder and since set to one month ago
        params = {"path": folder, "since": one_month_ago}

        headers = {'Authorization' : f'token {NONAME}'}

        # Make the request
        response = requests.get(url, params=params, headers=headers)

        # If the request was successful
        if response.status_code == 200:
            # Get the SHAs from the commit data
            data = response.json()
            if data:
                return [commit["sha"] for commit in data]
            else:
                print(f"No commits found for folder {folder} in the past month")
                return []
        else:
            print(f"Request failed with status code {response.status_code}")
            return None
    @staticmethod
    def check_sha_in_past_commits(ORG_NAME, REPO_NAME, folder, sha_to_check):
        """
        Check if a given SHA is in the list of SHAs from the past week or the past month.

        Parameters:
        folder (str): The path to the folder.
        sha_to_check (str): The SHA to check.

        Returns:
        str: A message indicating whether the SHA was found or not.
        """
        # Get the list of SHAs from the past week
        sha_list_week = Git.get_sha_list_from_past_week(ORG_NAME, REPO_NAME, folder)

        # If the SHA is in the list from the past week
        if sha_to_check in sha_list_week:
            return True

        # Get the list of SHAs from the past month
        sha_list_month = Git.get_sha_list_from_past_month(ORG_NAME, REPO_NAME, folder)

        # If the SHA is in the list from the past month
        if sha_to_check in sha_list_month:
            return True

        # If the SHA is not in either list
        return False
    
    def does_pull_required(self):
        latest_sha = self.get_latest_sha_from_remote()

        # Returns none, means repo  not present local host
        if not latest_sha:
            return True
        current_sha = self.get_latest_sha_from_existing_file_system()
        if not current_sha or not latest_sha:
            return True
        elif latest_sha != current_sha:
            return True
        else:
            return False

    def clone(self):
        try:
            logger.info("Changing ssh_config permissions")
            bash_config_chmod_result = shell.bash(Git.ssh_config_chmod_command)
            if not Git.check_bash_output(bash_config_chmod_result):
                raise exceptions.GitBashCommandFailedToExecute(f"ssh_config_chmod_command failed to execute,command: "
                                                               f"{bash_config_chmod_result.command} "
                                                               f"error: {bash_config_chmod_result.stderr}")

            logger.info(f"Cloning playbook git: {self.git_repo_url}")
            bash_raas_git_cone_command_result = shell.bash(
                f'''cd {self.cloned_to_location} && git {Git.core_ssh_command} clone {self.git_repo_url}''')
            if not Git.check_bash_output(bash_raas_git_cone_command_result):
                raise exceptions.GitBashCommandFailedToExecute(f"Cloning playbook git failed to execute,command: "
                                                               f"{bash_raas_git_cone_command_result.command} "
                                                               f"error: {bash_raas_git_cone_command_result.stderr}")
            self.git_conf_user_and_email()
            return True
        except Exception as e:
            logger.exception(e)

    def get_add_upsteam(self, location, upsteam_git_url):
        logger.info(f"Fetch upsteam : {upsteam_git_url}")
        bash_raas_git_cone_command_result = shell.bash(
            f'''cd {location} && git {Git.core_ssh_command} remote add upstream {upsteam_git_url}''')
        if not Git.check_bash_output(bash_raas_git_cone_command_result):
            raise exceptions.GitBashCommandFailedToExecute(f"remote add upstream playbook git failed to "
                                                           f"execute,command: "
                                                           f"{bash_raas_git_cone_command_result.command} "
                                                           f"error: {bash_raas_git_cone_command_result.stderr}")
        return

    def get_git_config_values(self, location):
        logger.info(f"Get all branches ")
        bash_raas_git_cone_command_result = shell.bash(
            f'''cd {location} && git {Git.core_ssh_command} config --list ''', read_lines=True)
        if not Git.check_bash_output(bash_raas_git_cone_command_result):
            raise exceptions.GitBashCommandFailedToExecute(f"git branch -vv "
                                                           f"execute,command: "
                                                           f"{bash_raas_git_cone_command_result.command} "
                                                           f"error: {bash_raas_git_cone_command_result.stderr}")
        return bash_raas_git_cone_command_result.stdout

    def get_fetch_upsteam(self, location, upsteam_git_url):
        logger.info(f"Fetch upsteam : {upsteam_git_url}")
        bash_raas_git_cone_command_result = shell.bash(
            f'''cd {location} && git {Git.core_ssh_command} fetch upstream ''')
        if not Git.check_bash_output(bash_raas_git_cone_command_result):
            raise exceptions.GitBashCommandFailedToExecute(f"git pull upstream "
                                                           f"execute,command: "
                                                           f"{bash_raas_git_cone_command_result.command} "
                                                           f"error: {bash_raas_git_cone_command_result.stderr}")
        return

    @staticmethod
    def run_git_command(location, command):
        logger.info(f"Git : {command}")
        bash_raas_git_cone_command_result = shell.bash(
            f'''cd {location} && git {Git.core_ssh_command} {command} ''')
        if not Git.check_bash_output(bash_raas_git_cone_command_result):
            raise exceptions.GitBashCommandFailedToExecute(f"unable to run git {command} "
                                                           f"execute,command: "
                                                           f"{bash_raas_git_cone_command_result.command} "
                                                           f"error: {bash_raas_git_cone_command_result.stderr}")
        return

    def git_conf_user_and_email(self):
        logger.info("Setting user context for the git")
        command_user = f'''cd {self.repo_location} && git {Git.core_ssh_command} 
        clone config --global user.name "m0c00jt"'''
        command_email = f'''cd {self.repo_location} && git {Git.core_ssh_command} 
        clone config --global user.email "<EMAIL>"'''

        shell.bash(command_user)
        shell.bash(command_email)

    def checkout(self):
        logger.info(f"Checkout playbook git to {self.branch}: {self.git_repo_url}")
        checkout_command = f'git {Git.core_ssh_command} checkout -b {self.branch}'
        full_checkout_command = f'cd {self.repo_location} && {checkout_command}'
        bash_raas_git_checkout_command_result = shell.bash(full_checkout_command)
        if not Git.check_bash_output(bash_raas_git_checkout_command_result):
            logger.error(f"stderr is {bash_raas_git_checkout_command_result.stderr}")
            logger.error(f"stdout is {bash_raas_git_checkout_command_result.stdout}")
            raise exceptions.GitBashCommandFailedToExecute(f"Checkout playbook git failed to execute, command: "
                                                           f"{full_checkout_command} "
                                                           f"error: {bash_raas_git_checkout_command_result.stderr}")
        return self.branch

    def remote_checkout(self, remote_branch):
        logger.info(f"Checkout playbook git to {self.branch}: {self.git_repo_url}")
        checkout_command = f'git {Git.core_ssh_command} checkout -b {self.branch} upstream/{remote_branch}'
        full_checkout_command = f'cd {self.repo_location} && {checkout_command}'
        bash_raas_git_checkout_command_result = shell.bash(full_checkout_command)
        if not Git.check_bash_output(bash_raas_git_checkout_command_result):
            logger.error(f"stderr is {bash_raas_git_checkout_command_result.stderr}")
            logger.error(f"stdout is {bash_raas_git_checkout_command_result.stdout}")
            raise exceptions.GitBashCommandFailedToExecute(f"Checkout playbook git failed to execute, command: "
                                                           f"{full_checkout_command} "
                                                           f"error: {bash_raas_git_checkout_command_result.stderr}")
        return self.branch

    def _add(self, file_name):
        """
        Add the given file to git
        """
        _add_file = f'git {Git.core_ssh_command} add {file_name}'
        git_add_command = f'cd {self.cloned_to_location}/{self.working_folder_checkout_repo_name} && {_add_file}'
        logger.info(f"Add command is {git_add_command}")
        bash_git_add_command_result = shell.bash(git_add_command)
        if not Git.check_bash_output(bash_git_add_command_result):
            message = f"Git add {file_name} failed to execute for the branch {self.branch}"
            logger.error(f"{message}, reason is {bash_git_add_command_result.stderr}")
            return False
        logger.info(f"Successfully added file  {file_name} to branch {self.cloned_to_location}")
        return True

    def _rm(self, file_name):
        """
        Delete the given file to git
        """
        _add_file = f'git {Git.core_ssh_command} rm {file_name}'
        git_add_command = f'cd {self.cloned_to_location}/{self.working_folder_checkout_repo_name} && {_add_file}'
        logger.info(f"rm command is {git_add_command}")
        bash_git_add_command_result = shell.bash(git_add_command)
        if not Git.check_bash_output(bash_git_add_command_result):
            message = f"Git rm {file_name} failed to execute for the branch {self.branch}"
            logger.error(f"{message}, reason is {bash_git_add_command_result.stderr}")
            return False
        logger.info(f"Successfully deleted file  {file_name} to branch {self.cloned_to_location}")
        return True

    def add(self, add_file):
        status = self._add(add_file)
        if not status:
            logger.error(f"Unable to add all the files for the branch: {self.branch}")
            return False
        logger.info("Git file addition finished ")
        return True

    def rm(self, add_file):
        status = self._rm(add_file)
        if not status:
            logger.error(f"Unable to all all the file for the branch: {self.branch}")
            return False
        logger.info("Git file deletion finished ")
        return True

    def commit(self, commit_message):
        logger.info(f"Committing the changes to branch: {self.branch}")
        git_add_command = f'cd {self.cloned_to_location}/{self.working_folder_checkout_repo_name} && ' \
                          f'git {Git.core_ssh_command} commit -m "{commit_message}"'
        logger.info(f"Commit command is {git_add_command}")
        bash_git_commit_command_result = shell.bash(git_add_command)
        if not Git.check_bash_output(bash_git_commit_command_result):
            self.is_commit_failed = True
            logger.error(f"stderr is {bash_git_commit_command_result.stderr}")
            logger.error(f"stdout is {bash_git_commit_command_result.stdout}")
            logger.exception(
                exceptions.GitCommitErrorOccurred(f"Unable to commit files to git, for the branch {self.branch}"))

    def push(self):
        logger.info(f"Pushing changes of branch {self.branch} to git ")
        logger.info(f"Before git push, checking git commit is success for the branch {self.branch}")
        if self.is_commit_failed:
            logger.info("Git commit failed, so nothing to push. Aborting git push for the branch {self.branch}")
            return

        git_add_command = f'cd {self.cloned_to_location}/{self.working_folder_checkout_repo_name} && ' \
                          f'git {Git.core_ssh_command} push -u origin {self.branch}'
        logger.info(f"Push command is {git_add_command}")
        bash_git_push_command_result = shell.bash(git_add_command)
        if not Git.check_bash_output(bash_git_push_command_result):
            self.is_push_failed = True
            logger.exception(
                exceptions.GitPushFailed(f"Unable to push the changes to git for the branch {self.branch}"))

    def rebase(self):
        """
        Rebase always with main.
        """
        logger.info(f"Rebase of {self.branch} with main ")
        git_add_command = f'cd {self.cloned_to_location}/{self.working_folder_checkout_repo_name} && ' \
                          f'git {Git.core_ssh_command} rebase master'
        bash_git_rebase_command_result = shell.bash(git_add_command)
        if not Git.check_bash_output(bash_git_rebase_command_result):
            raise exceptions.GitPushFailed(f"Unable rebase with main for {self.branch}")

    def pull(self):
        logger.info(f"Pulling the changes to local repo ")

        git_add_command = f'cd {self.repo_location} && git {Git.core_ssh_command} pull'
        bash_git_pull_command_result = shell.bash(git_add_command)
        if not Git.check_bash_output(bash_git_pull_command_result):
            logger.exception(exceptions.GitPushFailed("Unable to pull the changes local"))
            return False
        return True

    def stash(self):
        logger.info(f"Stash the changes to local repo ")

        git_add_command = f'cd {self.repo_location} && git {Git.core_ssh_command} stash'
        bash_git_pull_command_result = shell.bash(git_add_command)
        if not Git.check_bash_output(bash_git_pull_command_result):
            logger.exception(exceptions.GitPushFailed("Unable to stash the changes local"))
            return False
        return True

    def _merge(self):
        logger.info(f"Rebase of {self.branch} with main ")
        git_add_command = f'cd {self.cloned_to_location}/{self.working_folder_checkout_repo_name} && ' \
                          f'git {Git.core_ssh_command} merge --no-ff --no-commit {self.branch}'
        bash_git_rebase_command_result = shell.bash(git_add_command)
        if not Git.check_bash_output(bash_git_rebase_command_result):
            raise exceptions.GitPushFailed(f"Unable rebase with main for {self.branch}")

    def check_merge_conflicts_with_status(self):
        logger.info(f"Rebase of {self.branch} with main ")
        git_add_command = f'cd {self.cloned_to_location}/{self.working_folder_checkout_repo_name} && ' \
                          f'git {Git.core_ssh_command} status'
        bash_git_rebase_command_result = shell.bash(git_add_command)
        if not Git.check_bash_output(bash_git_rebase_command_result):
            raise exceptions.GitPushFailed(f"Unable rebase with main for {self.branch}")

    def merge(self):
        """
        https://stackoverflow.com/questions/5601931/what-is-the-best-and-safest-way-to-merge-a-git-branch-into-master
        git checkout main
        git pull
        git checkout test
        git pull
        git rebase -i main
        git checkout main
        git merge --no-ff --no-commit test
        git status
        git commit -m 'merge test branch'
        git push
        """

    def clean(self):
        logger.info(f"Deleting working dir {self.cloned_to_location}/{self.working_folder_checkout_repo_name}")
        clean_working_dir_command = f'rm -rf {self.cloned_to_location}/{self.working_folder_checkout_repo_name}'
        clean_working_dir_command_result = shell.bash(clean_working_dir_command)
        if not Git.check_bash_output(clean_working_dir_command_result):
            raise exceptions.GitBashCommandFailedToExecute(f"Unable remove git working dir {self.cloned_to_location}")

    @staticmethod
    def check_ssh_config_exists():
        my_file = Path('{}/.ssh/config'.format(Git.root_location))
        if my_file.is_file():
            return True
        return False

    def has_required_disk_space(self, threshold=1500):
        _, _, free = util.working_dir_usage(self.cloned_to_location)
        free_disk_size_in_mb = util.bytes_to_mb(free)
        if free_disk_size_in_mb >= threshold:
            return True
        return False

    @staticmethod
    def check_bash_output(bash_result):
        if bash_result.return_code == 0:
            return True
        return False

    @staticmethod
    def get_org_and_repo_html_url_from_git_ssh_url(git_url):
        try:
            repo = git_url.split("/")[-1]
            repo = repo.replace(".git", '')
            org = git_url.split(":")[-1].split("/")[0]
            html_url = git_url.replace(":", "/").replace("git@", "https://").replace(".git", "")
            return repo, org, html_url
        except Exception as e:
            logger.exception(e)
            return None, None, None

    def status(self):
        base_command = " status -s -b"
        bash_raas_git_cone_command_result = shell.bash(
            f'''cd {self.cloned_to_location}/{self.working_folder_checkout_repo_name} && git {Git.core_ssh_command} {base_command}''',
            read_lines=True)
        sha = bash_raas_git_cone_command_result.stdout
        logger.info("Git status is  {}".format(sha))
        return sha

    def get_branch(self):
        base_command = " branch"
        bash_raas_git_cone_command_result = shell.bash(
            f'''cd {self.cloned_to_location}/{self.working_folder_checkout_repo_name} && git {Git.core_ssh_command} {base_command}''',
            read_lines=True)
        res = bash_raas_git_cone_command_result.stdout
        logger.info("Git status is  {}".format(res))
        return res

    @staticmethod
    def normalize_status_output(git_status_output):
        output = list()
        for index, _file in enumerate(git_status_output):
            if index == 0:
                continue
            tokens = _file.split()
            if len(tokens) >= 1:
                output.append(tokens[1])
        return output

    def encode_search_parameters(self, search_para):
        search_params = {
            "q": "",
            "per_page": 100
        }

        if type(search_para) is dict:
            search_params["q"] = urlencode(search_para)
        elif type(search_para) is list:
            search_params["q"] = "%20".join(search_para)

        return search_params

    def search_code(self, query_params):
        url = f"{self.git_host}/api/v3/search/code"
        response = requests.get(url, auth=self.baseAuth, params=query_params, verify=False)

        return response


if __name__ == "__main__":
    # g = Git("", "", "", project="Telemetry")
    # g.pre_check_task_to_create_pull_request("mms-config", base="main", head="intl-ecomm-svcs:juno_1680722062418469799",
    #                                         state="open")
    # g.create_pull_request("mms-config", base="main", head="intl-ecomm-svcs:juno_1680722062418469799",
    #                       title="adding alerts")
    # g.pre_check_task_to_create_pull_request("mms-config", base="main", head="intl-ecomm-svcs:juno_1680722062418469799",
    #                                         state="open")
    # g = GitApi()
    # g.get_repo_contributors(owner='',org='',repo='')
    # g.provide_contributor_access()
    # g.is_repo_valid("intl-ecomm-svcs", "juno")
    git = Git("***************************:intl-ecomm-svcs/sre-templates.git",
              "/Users/<USER>/Downloads/working_april_15")
    git()
    git.clone()
    ssh = git.get_latest_sha_from_existing_file_system()

    print(ssh)
