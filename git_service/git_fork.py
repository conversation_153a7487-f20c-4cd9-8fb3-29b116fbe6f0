import time
import logging, os
from libs import shell
from git_service.git import Git
from collections import OrderedDict
from libs.util import path_leaf, extract_filenames
from libs.util import add_path_to_existing_git_path

logger = logging.getLogger(__name__)


class ForkGit:
    def __init__(self, cloned_to_location, fork_git_url, branch, upsteam_git, upstream_repo_branch='main'):
        self.fork_git_url = fork_git_url
        self.cloned_to_location_with_working_dir = os.path.join(cloned_to_location, branch)
        self.cloned_to_location = cloned_to_location
        self._prepare_working_dir()
        self.git = Git(fork_git_url, cloned_to_location=self.cloned_to_location_with_working_dir, branch=branch)
        # not required
        u_repo, u_org, u_html_url = self.git.get_org_and_repo_html_url_from_git_ssh_url(upsteam_git)
        self.upsteam_git_details = {"url": upsteam_git, "repo": u_repo, "org": u_org, "html": u_html_url}
        self.upstream_repo_branch = upstream_repo_branch
        self.local_repo_name = branch

    def _prepare_working_dir(self):
        shell.bash(f"mkdir -p {self.cloned_to_location_with_working_dir}")

    @staticmethod
    def _process_git_errors(files):
        add_failed_files = [file.get("git", {}).get("add", {}) for file in files if
                            not file.get("git", {}).get("add", {}).get("status", True)]
        remove_failed_files = [file.get("git", {}).get("remove", {}) for file in files if
                               not file.get("git", {}).get("remove", {}).get("status", True)]
        return {"git_add_file_errors": add_failed_files, "git_rm_file_errors": remove_failed_files}

    @staticmethod
    def get_git_errors(files):
        return ForkGit._process_git_errors(files)

    def add_files(self, files):
        add = Add(self.git, files, self.cloned_to_location_with_working_dir)
        add()
        return add.files

    def remove_files(self, files):
        remove = Remove(self.git, files, self.cloned_to_location_with_working_dir)
        remove()
        return remove.files

    def clone_fork_branch(self):
        self._prepare_working_dir()
        return self.git.clone()

    def pull_fork_branch(self):
        self.git.pull()

    def stash_fork_branch(self):
        self.git.stash()

    def make_local_copy_from_master_repo(self):
        master_git = Git(self.fork_git_url, cloned_to_location=self.cloned_to_location)
        master_copy = os.path.join(self.cloned_to_location, master_git.repo)
        if not os.path.isdir(master_copy):
            master_git.clone()
        self._prepare_working_dir()
        shell.bash(f"cp -r {master_copy} {self.cloned_to_location_with_working_dir}/.")
        self.git.stash()
        self.git.pull()

    def checkout_fork_branch(self):
        self._prepare_working_dir()
        self.git.checkout()

    def checkout_remote_branch(self):
        self._prepare_working_dir()
        self.git.remote_checkout(self.upstream_repo_branch)

    def remove_mms_working_dir(self):
        shell.bash(f"rm -rf {self.cloned_to_location_with_working_dir}")

    @staticmethod
    def is_add_file_criteria_met(file_data):
        status = all([
            file_data.get("status", False),
            file_data.get("compiled_file", False),
            file_data.get("alert_process_details", {}).get("create_or_update_alert_status", True)
        ])
        if not status:
            logger.warning(f"Unable to process {file_data}")
        return status

    @staticmethod
    def check_all_staged_files_added_to_git(compiled_temp_files, staged_files):
        return functools.reduce(lambda x, y: x and y, map(lambda p, q: p == q, extract_filenames(compiled_temp_files),
                                                          extract_filenames(staged_files)), True)

    def has_required_disk_space(self):
        return self.git.has_required_disk_space(1500)

    def get_repo_location(self):
        return f"{self.cloned_to_location_with_working_dir}/{self.git.repo}"

    def add_fetch_upsteam(self, repo_location):
        self.git.get_add_upsteam(repo_location, self.upsteam_git_details.get("url"))
        self.git.get_fetch_upsteam(repo_location, self.upsteam_git_details.get("url"))

    @staticmethod
    def _check_upstream_configured_or_not(response):
        return any("remote.upstream.url" in line for line in response)

    def execute_init_git_tasks(self):
        """
        1. Checks disk space
        2. Clones Branch
        3. Set upstream
        4. checkout
        """

        if not self.has_required_disk_space():
            # diskspace, fork_success
            return False, False

        if not self.clone_fork_branch():
            # diskspace, fork_success
            return True, False

        repo_location = self.get_repo_location()
        try:
            self.add_fetch_upsteam(repo_location)
        except Exception as e:
            logger.warning(f"Git remote add upstream or get_pull_upsteam failed {e}")

        config = self.git.get_git_config_values(repo_location)
        if ForkGit._check_upstream_configured_or_not(config):
            self.checkout_remote_branch()
        else:
            self.checkout_fork_branch()

        self.git_merge_upstream_considering_only()
        return True, True

    def execute_git_add_or_delete_files(self, files):
        self.add_files(files)
        self.remove_files(files)
        return files

    def git_merge_upstream_considering_only(self):
        # resolving any conflicts by keeping the changes from your current branch (the ours strategy).
        self.git.run_git_command(self.get_repo_location()," merge -X ours upstream/main ")
        # overwrite the contents of the current branch with the contents from the upstream/main branch f
        self.git.run_git_command(self.get_repo_location()," checkout upstream/main -- . ")
        return


class Add:
    def __init__(self, git, files, cloned_to_location_with_working_dir):
        self.cloned_to_location_with_working_dir = cloned_to_location_with_working_dir
        self.repo_name = git.repo
        self.git = git
        self.files = files

    def __call__(self):
        copy_to_location = f"{self.cloned_to_location_with_working_dir}/{self.repo_name}"

        for _file in self.files:
            _file = self.init_git_add(_file)

            if not ForkGit.is_add_file_criteria_met(_file):
                continue
            git_path = add_path_to_existing_git_path(_file, _file.get("git_path"))

            if not self.create_directory_if_not_exists(copy_to_location, git_path, _file):
                continue

            if not self.copy_file_to_git(_file, copy_to_location, git_path):
                continue

            if not self.add_file_to_git(_file, git_path):
                continue

            _file["git"]["add"]["status"] = True
            _file["git"]["add"]["message"] = None

    def init_git_add(self, _file):
        _file["git"] = {"add": {}}
        return _file

    def create_directory_if_not_exists(self, copy_to_location, git_path, _file):
        if not os.path.isdir(os.path.join(copy_to_location, git_path)):
            res = shell.bash(f"mkdir -p {copy_to_location}/{git_path}")
            if res.return_code != 0:
                self.set_git_add_status(_file, False, f"Unable to create a directory at {copy_to_location}/{git_path}")
                return False
        return True

    def copy_file_to_git(self, _file, copy_to_location, git_path):
        compiled_file_with_path = _file.get("compiled_file")
        if not os.path.isfile(compiled_file_with_path):
            self.set_git_add_status(_file, False,
                                    f"File {compiled_file_with_path} not able to find, so not adding to git ")
            return False
        res = shell.bash(f"cp {compiled_file_with_path} {copy_to_location}/{git_path}/.")
        if res.return_code != 0:
            self.set_git_add_status(_file, False, f"File {compiled_file_with_path} not able to copy to git repo ")
            return False
        return True

    def add_file_to_git(self, _file, git_path):
        compiled_file_with_path = _file.get("compiled_file")
        status = self.git.add("./{}/{}".format(git_path, path_leaf(compiled_file_with_path)))
        if not status:
            self.set_git_add_status(_file, False, f"File {compiled_file_with_path} not able to add to git ")
            return False
        return True

    def set_git_add_status(self, _file, status, message):
        _file["git"]["add"]["status"] = status
        _file["git"]["add"]["message"] = message
        logger.warning(message)


class Remove:
    def __init__(self, git, files, cloned_to_location_with_working_dir):
        self.cloned_to_location_with_working_dir = cloned_to_location_with_working_dir
        self.repo_name = git.repo
        self.git = git
        self.files = files

    def __call__(self):
        copy_to_location = self._get_copy_to_location()

        for _file in self.files:
            self._initialize_git_remove(_file)

            if self._should_skip_file(_file):
                continue

            if self._should_delete_file(_file):
                file_with_location = self._get_file_with_location(_file, copy_to_location)

                if not self._file_exists(file_with_location):
                    self._log_file_not_found(_file, file_with_location)
                    continue

                if not self._delete_file(file_with_location):
                    self._log_file_deletion_failed(_file, file_with_location)
                    continue

                if not self._remove_file_from_git(_file):
                    self._log_git_remove_failed(_file)
                    continue

                self._mark_file_as_removed(_file)

    def _get_copy_to_location(self):
        return f"{self.cloned_to_location_with_working_dir}/{self.repo_name}"

    def _initialize_git_remove(self, _file):
        _file["git"] = _file.get("git", dict())
        _file["git"]["remove"] = dict()

    def _should_skip_file(self, _file):
        return ForkGit.is_add_file_criteria_met(_file)

    def _should_delete_file(self, _file):
        return _file.get("alert_process_details", dict()).get("delete_alert_file_status", False)

    def _get_file_with_location(self, _file, copy_to_location):
        compiled_file_with_path = _file.get("compiled_file")
        git_path = _file.get("git_path")
        full_file = "{}/{}".format(git_path, path_leaf(compiled_file_with_path))
        return os.path.join(copy_to_location, full_file)

    def _file_exists(self, file_with_location):
        return os.path.isfile(file_with_location)

    def _log_file_not_found(self, _file, file_with_location):
        message = f"Unable to delete the file, file not found at {file_with_location}"
        logger.exception(message)
        _file["git"]["remove"]["status"] = False
        _file["git"]["remove"]["message"] = message

    def _delete_file(self, file_with_location):
        rm_command = f"rm -rf {file_with_location}"
        res = shell.bash(rm_command)
        return res.return_code == 0

    def _log_file_deletion_failed(self, _file, file_with_location):
        message = f"File unable to delete {file_with_location}"
        logger.warning(message)
        _file["git"]["remove"]["status"] = False
        _file["git"]["remove"]["message"] = message

    def _remove_file_from_git(self, _file):
        git_path = _file.get("git_path")
        compiled_file_with_path = _file.get("compiled_file")
        return self.git.rm("./{}/{}".format(git_path, path_leaf(compiled_file_with_path)))

    def _log_git_remove_failed(self, _file):
        compiled_file_with_path = _file.get("compiled_file")
        message = f"File {compiled_file_with_path} not able to remove/delete to git"
        _file["git"]["remove"]["status"] = False
        _file["git"]["remove"]["message"] = message

    def _mark_file_as_removed(self, _file):
        _file["git"]["remove"]["status"] = True
        _file["git"]["remove"]["message"] = None


if __name__ == "__main__":
    from logging import handlers
    import os

    log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    log_format = '%(asctime)s %(levelname)10s: [%(filename)s:%(lineno)s - %(funcName)20s() ] %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(log_level)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)

    file_handler = handlers.RotatingFileHandler(filename=os.path.join(os.path.basename(__file__).replace('py', 'log')),
                                                maxBytes=1048576,
                                                backupCount=5)
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(fmt=formatter)
    logger.addHandler(file_handler)
    # # Handles new branch, where templates need to push
    # time_stamp = time.time_ns() * 10000
    # template = "wcnp"
    # repo_name = f"{template}_{time_stamp}"
    # # f = ForkGit(GIT_URL, REPO_NAME, working_location=settings.BASE_DIR)
    # f = ForkGit(GIT_URL, REPO_NAME, working_location=settings.TEMPLATE_OUTPUT_DIR)
    # f.clone_fork_branch()
    #
    # # Handles Templates
    # template_dir = get_latest_templates()
    # template = TemplateEngine("wcnp", template_location=template_dir)
    # templates = [t for t in template.templates if
    #              t.endswith(".yaml") and (not t.startswith(".idea/") or not t.startswith(".git/"))]
    # print(templates)
    # print(template.template_variables)
    # print("required templates variables", template.required_placeholders)
    # if template.is_service_valid:
    #     """
    #     Cassandra:
    #              cluster, market, tier are mandatory parameters.
    #              cluster mapping is "ooa" in promQL
    #     """
    #     template.create(data={'cluster': 'canada-catalog-backend-prod',
    #                           'market': 'CA',
    #                           'tier': 'tier-1'})
    # # l = ["/Users/<USER>/git/monitoring_and_alerting/output/1679017276970_1679017277024_wcnp_alerts.yaml"]
    # # template_dir = get_latest_templates()
    # # template = TemplateEngine("wcnp", template_location=template_dir)
    # _files, _failed_files, _success_files = f.add_files(l, template.default_template_data)
    # f.validate_add_files(_success_files)
    # f.reprocess_failed_files(l, _failed_files)
    _files = [
        {'message': None, 'compiled_file': '/Users/<USER>/git/juno/output/1711848449/mx-urp-prod-primary-mx-urp.yaml',
         'base_repo': '***************************:Telemetry/mms-config.git',
         'fork_repo': '***************************:intl-ecomm-svcs/mms-config.git',
         'git_path': 'international-tech/intl-sre/golden-signals/rules/production/wcnp/app-owner-alerts',
         'file_name': '{app_name}-{namespace}.yaml', 'mms_source': 'wcnp', 'cluster_profile': 'prod',
         'job': 'kube-state-metrics', 'alert_owner_category': 'sre', 'tier': 'tier-0', 'market': 'mx',
         'alert_team': 'intl_sre_golden_signals', 'pods_restarts_current_threshold_count': 3,
         'cpu_usage_current_threshold_pct': 85, 'memory_usage_current_threshold_pct': 85,
         'fiveXX_trend_comparing_to_one_week_ago_threshold_pct': 50,
         'fiveXX_current_threshold_count_used_for_trend': 20, 'fiveXX_current_threshold_pct': 50,
         'traffic_spike_comparing_to_one_week_ago_threshold_pct': 75,
         'traffic_drop_comparing_to_one_week_ago_threshold_pct': 75,
         'latency_spike_comparing_to_one_week_ago_threshold_pct': 100,
         'scus_traffic_in_balance_current_threshold_pct': 10, 'wus_traffic_in_balance_current_threshold_pct': 10,
         'eus_traffic_in_balance_current_threshold_pct': 10, 'rate_limit_threshold_count': 0,
         'quota_limit_threshold_pct': 95, 'pods_restarts_current_threshold_count_alerting_interval': 5,
         'fiveXX_trend_comparing_to_one_week_ago_threshold_pct_alerting_interval': 5,
         'fiveXX_current_threshold_pct_alerting_interval': 5,
         'traffic_spike_comparing_to_one_week_ago_threshold_pct_alerting_interval': 5,
         'traffic_drop_comparing_to_one_week_ago_threshold_pct_alerting_interval': 5,
         'scus_traffic_in_balance_current_threshold_pct_alerting_interval': 5,
         'wus_traffic_in_balance_current_threshold_pct_alerting_interval': 5,
         'eus_traffic_in_balance_current_threshold_pct_alerting_interval': 5,
         'rate_limit_threshold_count_alerting_interval': 5, 'quota_limit_threshold_pct_alerting_interval': 5,
         'latency_spike_comparing_to_one_week_ago_threshold_pct_alerting_interval': 5,
         'memory_usage_current_threshold_pct_alerting_interval': 5,
         'cpu_usage_current_threshold_pct_alerting_interval': 5, 'gslb_health_current_threshold_pct': 75,
         'namespace': 'mx-urp', 'app_name': 'mx-urp-prod-primary', 'alert_team_name': 'mx-colony-service-prod-primary',
         'xmatters_group': 'INTL-P13N-URP', 'team_email': '<EMAIL>',
         'slack_channel': 'ca-idc-spr-engg-kitt-notify', 'alert_id': '048dd378afb1446fbd5ba364756bcc2b',
         'status': True}]

    # REPO_NAME = "ops-scripts"
    # GIT_URL = "***************************:LabsSearch/ops-scripts"
    branch = f"juno_{int(time.time())}"
    # branch = "juno_1711850714"
    g = ForkGit(cloned_to_location="/Users/<USER>/Downloads/1213313",
                fork_git_url='***************************:intl-ecomm-svcs/mms-config.git',
                branch=branch, upsteam_git='***************************:Telemetry/mms-config.git',
                upstream_repo_branch='main')
    g.execute_init_git_tasks()
    g.execute_git_add_or_delete_files(_files)
    g.git.commit("Added files")
    g.git.get_branch()
    g.git.push()
    data = g._process_git_errors(_files)
    g.git.create_pull_request(upstream_org=g.upsteam_git_details.get("org"),
                              upstream_repo=g.upsteam_git_details.get("repo"),
                              fork_org_name=g.git.org,
                              fork_branch_name=branch, base="main", title="juno commit")
