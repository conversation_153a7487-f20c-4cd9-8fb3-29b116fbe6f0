__author__ = '<PERSON><PERSON> cheepati'
"""
This is Agent, It run on each VM.
"""
import logging, subprocess, time
from collections import namedtuple

logger = logging.getLogger(__name__)

SSHResult = namedtuple('SSHResult',
                       ["command",
                        "return_code",
                        "pid",
                        "stdout",
                        "stderr",
                        "is_bg_process"
                        ])


def is_process_running(process_name, parent_process="java", retries=3):
    """
    Checks whether Process is running or not. If the process is running then returns True
    if not running then return False
    Args:
        process_name: Process Name
        parent_process:
        retries: Number of time JMeter retries

    Returns:

    """
    for i in range(retries):
        try:

            res = bash(command=f"ps -ef|grep {process_name}|grep -v grep|grep -v python3|grep {parent_process}",
                       read_lines=True)
            logger.info("is_process_running {}".format(res))
            if i > 0:
                logger.info("Retrying of ({}) to get status of the test,rc {}".format(i, res.return_code))

            has_pid = False
            if res.return_code == 0:
                if len(res.stdout) > 0:
                    has_pid = True

            if has_pid and res.return_code == 0:
                logger.info(f"is_process_running true, has_pid {has_pid} res.return_code {res.return_code}")
                return True, res.stdout
            # # When process is not running, but required status as
            # elif not has_pid and res.return_code == 1 and return_code_1_as_success:
            #     return True, host, res.stdout
            elif not has_pid and res.return_code == 1:
                logger.info(f"is_process_running False, not has_pid {has_pid} res.return_code {res.return_code}")
                return False, res.stdout
            if (i + 1) == retries:
                return False, res.stdout
        except Exception as e:
            logger.exception(e)


def _result(command, return_code, pid, stdout, stderr, is_bg_process=False):
    result = None
    try:
        result = SSHResult(command, return_code, pid, stdout, stderr, is_bg_process)
    except Exception as e:
        logger.error('Unable to run the command in with command %s and exception is %s' % (command, e))
    return result


def _sub_process(command):
    process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    return process


def bash(command, read_lines=False, back_ground=False):
    """
     Executes Bash command in Local machine or localhost
    Args:
        command: Executes the command on local system
        read_lines:
        back_ground:

    Returns:

    """
    try:
        shell_results = _sub_process(command)
        if back_ground:
            return_code = shell_results.poll()
            time.sleep(5)
            response = _result(command, return_code,
                               shell_results.pid, stdout=None, stderr=None, is_bg_process=True)
            return response

        (stdout, stderr) = shell_results.communicate()
        if read_lines:
            response = _result(command, shell_results.returncode, shell_results.pid,
                               stdout.decode("utf-8").splitlines(),
                               stderr, is_bg_process=False)
        else:
            response = _result(command, shell_results.returncode, shell_results.pid, stdout.decode("utf-8"),
                               stderr, is_bg_process=False)

        return response
    except Exception as e:
        logger.error('Unable to run the command in with command %s and exception is %s' % (command, e))
        logger.exception(e)


def bash_bg(command):
    """
    Executes Bash command in Local machine or localhost
    :param command: Command to execute
    :rtype: str
    :return:
    """
    try:
        shell_results = _sub_process(command)
        return_code = shell_results.poll()
        logger.debug("Bg process return_code is  {}".format(return_code))
        response = _result(command, return_code,
                           shell_results.pid, stdout=None, stderr=None, is_bg_process=True)

        return response
    except Exception as e:
        logger.error('Unable to run the command in with command %s and exception is %s' % (command, e))


def _return_exit_code(status):
    if status:
        logger.info("success")
        exit(0)
    logger.info("Failed")
    exit(1)


def task_logger(message):
    logger.info("===========================================================================")
    logger.info(message)
    logger.info("===========================================================================")


def command_logger(message):
    logger.info("****************** {} ******************".format(message))


def get_files(location):
    """
    Gets all files in a given location
    """
    common = "cd {} && ls".format(location)

    # Check dir, exits
    dir_exists = check_file_or_dir_exists(dir_name=location)
    if not dir_exists:
        logger.info("Location/dir does not exits {}".format(dir_exists))
        return list()
    res = bash(common, read_lines=True)
    return res.stdout


def check_file_or_dir_exists(file_name=None, dir_name=None):
    """
    Checks weather dir or file exits
    """
    if dir_name:
        command = "[ -d {} ] && echo 'true' || echo 'false'".format(dir_name)
    else:
        command = "[ -f {} ] && echo 'true' || echo 'false'".format(file_name)
    status = bash(command=command)
    # Dir might be soft link, to handle that
    if status.stdout.strip() == "false" and dir_name:
        command = " find -L {} -type l".format(dir_name)
        status = bash(command=command)
        if status.return_code == 0:
            return True
    if status.stdout.strip() == "true":
        return True
    else:
        return False


if __name__ == '__main__':
    # run app in debug mode on port 5000
    from logging import handlers
    import os

    log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    log_format = '%(asctime)s %(levelname)10s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(log_level)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)

    file_handler = handlers.RotatingFileHandler(filename=os.path.join(os.path.basename(__file__).replace('py', 'log')),
                                                maxBytes=1048576,
                                                backupCount=5)
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(fmt=formatter)
    logger.addHandler(file_handler)
