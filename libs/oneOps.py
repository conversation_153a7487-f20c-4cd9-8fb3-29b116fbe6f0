# This script will handle most of the operations stuff
__author__ = '<PERSON><PERSON> cheepati'

import pprint
import concurrent.futures
import re, sys, json, logging, requests, shlex, subprocess, time, argparse, base64

logger = logging.getLogger(__name__)
divider = 100 * "#"
API_TOKEN = "dS4ueyGGVqvv6CqyEiVr"
ONEOPS_SERVER = "https://oneops.prod.walmart.com"
BASE_URL = "http://sre.api.prod.walmart.com/api/v1/datalake/active-taeps"
HEADERS = {'Content-Type': 'application/json'}
ONEOPS_APIS = {
    'get_all_orgs': 'account/organizations',
    'get_users': '{org}/organization/users.json',
    'get_all_clouds': '{org}/clouds',
    'get_cloud_name': '{org}/clouds/{cloud}',
    'procedures': '{org}/operations/procedures',
    'procedure_status': '{org}/operations/procedures/{procedure_id}',
    'platforms': '{org}/assemblies/{assembly}/operations/environments/{environment}/platforms.json',
    'platform_details': '{org}/assemblies/{assembly}/operations/environments/{environment}/platforms/platform.json',
    'get_all_platforms': '{org}/assemblies/{assembly}/design/platforms',
    'get_computes': '{platform_base}/components/compute/instances.json?instances_state=all',
    'get_target_instances_by_component': '{platform_base}/component/{component_id}/instances.json',
    'get_target_cids_for_clouds': '{platform_base}/component/*********/instances.json',
    'get_component_info': '{platform_base}/component.json?instances_state=all',
    'get_all_components': '{platform_base}/components.json?instances_state=all',
    'get_clouds_by_env': '',
    'get_vip': '{platform_base}/component/fqdn/instances.json?instances_state=all',
    'get_variables': '{platform_base}/variables',
    'cloud_priority': '{platform_base}/cloud_priority',
    'cloud_status': '{org}/assemblies/{assembly}/operations/environments/{environment}/platforms/{platform}.json',
    'give_access': '{org}/organization/users'

}


class OneOpsVariables(object):
    """ This class consists for all OneOps variables and auth keys"""

    api_token = base64.b64encode(API_TOKEN.encode()).decode()
    auth_header = {
        'Authorization': 'Basic {}'.format(api_token),
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }

    def __init__(self, **kwargs):
        self.org = kwargs.get('org', 'polaris')
        self.environment = kwargs.get('environment')
        self.platform = kwargs.get('platform')
        self.assembly = kwargs.get('assembly')
        self.cloud = kwargs.get('cloud')
        self.dc = kwargs.get('dc')
        self.service = kwargs.get('service')


class OneOps(OneOpsVariables):

    def __init__(self, **kwargs):
        super(OneOps, self).__init__(**kwargs)

    def authenticate_request(self, uri, method="get", data=None):
        """This fuction will return json response for all api reqs """
        try:
            url = "{}/{}".format(ONEOPS_SERVER, uri)
            try:
                if data:
                    logger.info("{},{}".format(method, data))
                    response = requests.request(method=method, url=url, json=data, headers=self.auth_header,
                                                verify=False)
                else:
                    response = requests.request(method=method, url=url, headers=self.auth_header)
            except Exception as e:
                raise Exception("Unable to connect or response is non-200 ")

            if response.ok:
                return response
            elif response.status_code == 422:
                raise Exception("One of the task is already executing")
            else:
                raise Exception("Unable to connect or response is non-200 ")
        except Exception as e:
            logger.error("{}".format(str(e)))
            sys.exit(1)

    def check_org_exits(self, org):
        try:
            uri = f"{ONEOPS_SERVER}/{org}.json"
            response = requests.get(uri, headers=self.auth_header,verify=False)
        except Exception as e:
            raise Exception("Unable to connect or response is non-200 ")
        if response.status_code == 404:
            return False, org
        else:
            return True, org

    def uri_builder(self, api_key, service=None, **env_details):
        """ This fucntion will replace the oneops placeholders with given variables """
        org = env_details.get("org") if env_details.get("org") else self.org
        environment = env_details.get("environment") if env_details.get("environment") else self.environment
        service = service if service else self.service
        platform = env_details.get("platform") if env_details.get("platform") else self.platform
        assembly = env_details.get("assembly") if env_details.get("assembly") else self.assembly

        # if not service and not platform:
        #     raise Exception("Provide either Service or platform and  assembly")

        # if api_key not in ONEOPS_APIS:
        #     raise Exception("api_key not found")

        # if (not platform or not assembly) and service:
        #     assembly, platform = OneOps.get_platform_assembly(service)
        if platform:
            env_details.update({"platform": platform})
        if assembly:
            env_details.update({"assembly": assembly})

        env_details.update({"org": org, "environment": environment})
        base_uri = ONEOPS_APIS.get(api_key).format_map(env_details)
        return base_uri.format(**env_details)

    def get_component_instances(self, service=None, component_name=None):
        service = service if service else self.service
        component_id = self.get_component_id(service, component_name=component_name)
        uri = self.uri_builder(api_key="get_target_instances_by_component", service=service,
                               component_id=component_id)
        response = self.authenticate_request(uri).json()
        return response

    def get_component_details_by_cid(self, cid, component_name=None, service=None):
        response = self.get_component_instances(component_name=component_name, service=service)
        return filter(lambda compute: str(compute["ciId"]).strip() == str(cid).strip(), response)

    def get_component_details_by_key(self, component_name, key, value, service=None):
        response = self.get_component_instances(component_name=component_name, service=service)
        return filter(lambda compute: str(value).strip() in str(compute[key]).strip(), response)

    def get_public_by_cid(self, cid, component_name=None, service=None):
        component = self.get_component_details_by_cid(component_name=component_name, cid=cid, service=service)
        ciName_end_2_tokens = '-'.join(component[0].get("ciName").split("-")[-2:])
        compute_component = self.get_component_details_by_key(component_name="compute", key="ciName",
                                                              value=ciName_end_2_tokens, service=service)
        return map(lambda compute: compute["ciAttributes"]["public_ip"], compute_component)[0]

    def get_component_ids_with_clouds(self, service=None, component_name=None):
        component_instance = self.get_component_instances(service=service, component_name=component_name)
        cloud = {}
        for instance in component_instance:
            if instance["cloud"]["toCi"]["ciName"] in cloud:
                cloud[instance["cloud"]["toCi"]["ciName"]]["host_cids"].append(instance["ciId"])
            else:
                cloud[instance["cloud"]["toCi"]["ciName"]] = {"cloud_cid": instance["cloud"]["toCi"]["ciId"],
                                                              "host_cids": [instance["ciId"]]}
        return cloud

    def get_component_ids_by_dc(self, service=None, component_name=None):
        component_clouds_ids = self.get_component_ids_with_clouds(service=service, component_name=component_name)
        _dc = {}
        for cloud, cid_info in component_clouds_ids.items():
            dc = " ".join(re.findall("[a-zA-Z]+", cloud.split("-")[1]))
            if dc in _dc:
                _dc[dc].append({cloud: cid_info})
            else:
                _dc[dc] = [{cloud: cid_info}]
        return _dc

    def get_clouds_by_env(self):
        """ This function will return a list of clouds used in the env"""
        _url = self.url_builder(self.oneops_urls['get_clouds_by_env'])
        response = self.authenticate_request(_url).json()
        clouds = map(lambda cloud: cloud.get('toCi').get('ciName'), response.get('consumes'))
        return clouds

    # def get_component_id(self, service=None, component_name=None):
    #     service = service if service else self.service
    #     _url = self.uri_builder("get_all_components")
    #     response = self.authenticate_request(_url).json()
    #     if not component_name:
    #         component_name = SERVICE_META_DATA.get(service).get("restart_component_name").strip()
    #     components = list(filter(lambda d: d['ciName'] == component_name, response))
    #     return components[0].get("ciId")

    def get_ciIds(self):
        _url = self.url_builder(self.oneops_urls['get_component_info'])
        response = self.authenticate_request(_url).json()
        cids = map(lambda cloud: cloud.get('ciId'), response.get('consumes'))
        return cids

    def execute_component_task(self, component_name=None, action="restart", dc=None, cloud=None, service=None,
                               wait_to_finish=True, poll=45, background=False):
        not_finished = False
        procedure_id = self.execute_procedure(dc=dc, cloud=cloud, service=service, component_name=component_name,
                                              action=action)
        if background:
            return not_finished, procedure_id
        logger.info("{} task started".format(action))

        if wait_to_finish:
            while not not_finished:
                time.sleep(poll)
                not_finished = not self.is_procedure_active(procedure_id)
                logger.info("{} task in-progress".format(action))
        logger.info("{} task finished".format(action))
        return not_finished, procedure_id

    @staticmethod
    def _extract_component_value(dc_details, key, cloud=None):
        cids = []
        for _cloud in dc_details:
            cloud_name, cloud_value = _cloud.items()[0]
            if cloud_name == cloud:
                cids.extend(cloud_value.get(key.strip()))
            else:
                cids.extend(cloud_value.get(key.strip()))
        return cids

    @staticmethod
    def _extract_component_by_dc(component_info_by_dc, key, cloud=None, dc=None):
        cids = []
        for _dc in component_info_by_dc:
            if _dc == dc:
                cids.extend(OneOps._extract_component_value(component_info_by_dc.get(_dc), key=key, cloud=cloud))
            elif not dc:
                cids.extend(OneOps._extract_component_value(component_info_by_dc.get(_dc), key=key, cloud=cloud))
        return cids

    def execute_procedure(self, dc=None, cloud=None, service=None, component_name=None, action="restart"):
        _url = self.uri_builder(api_key="procedures")
        component_id = self.get_component_id(service, component_name=component_name)
        cids_by_dcs = self.get_component_ids_by_dc(service=service, component_name=component_name)
        cids = OneOps._extract_component_by_dc(cids_by_dcs, key="host_cids", cloud=cloud, dc=dc)
        data = {
            "cms_procedure": {
                "procedureCiId": "0",
                "procedureName": action,
                "ciId": component_id,
                "procedureState": "active",
                "definition": {"flow": [
                    {"targetIds": cids,
                     "relationName": "base.RealizedAs",
                     "direction": "from",
                     "actions": [{"actionName": action, "stepNumber": 1, "isCritical": True}]
                     }],
                    "name": action}
            }
        }
        data['cms_procedure']['definition'] = str(data['cms_procedure']['definition']).replace("'", "\"")
        response = self.authenticate_request(_url, method="post", data=data).json()
        return response["procedureId"]

    def is_procedure_active(self, procedure_id):
        uri = self.uri_builder(api_key="procedure_status", procedure_id=procedure_id)
        response = self.authenticate_request(uri).json()
        if response.get("procedureState").lower() == "active":
            return True
        else:
            return False

    def get_procedure_execution_failed_instances(self, procedure_id):
        uri = self.uri_builder(api_key="procedure_status", procedure_id=procedure_id)
        response = self.authenticate_request(uri).json()
        if response.get("procedureState").lower() != "active":
            cloud_computes = map(lambda compute: compute.get('ciId'),
                                 filter(lambda compute: compute.get('actionState') == "failed", response["actions"]))
            return {"is_completed": True, "failed_computes": cloud_computes, "total_computes": len(response["actions"])}
        else:
            return {"is_completed": False, "failed_computes": [], "total_computes": len(response["actions"])}

    def get_cloud_id(self, cloud):
        """This fucntion will return cloudid for a given cloud"""
        self.clouds = cloud
        _url = self.url_builder(self.oneops_urls['get_cloud_name'])
        response = self.authenticate_request(_url).json()
        return response.get('ciId')

    def get_available_platforms(self):
        """ This Function will get all availlable platforms excluding the disabled ones """
        _url = self.uri_builder(self.oneops_urls['get_all_platforms'])
        pass

    def give_access(self, user_id):
        is_user_valid = self.is_user_valid(user_id)
        if not is_user_valid:
            return False, "invalid user"
        status, team_id = self.get_team_id()
        if not status:
            raise Exception("Unable to get team id")
        api_token = base64.b64encode(API_TOKEN.encode()).decode()
        auth_header = {
            'Authorization': 'Basic {}'.format(api_token),
            'Accept': '*/*;q=0.5, text/javascript, application/javascript, application/ecmascript, application/x-ecmascript',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        }
        url = f"{ONEOPS_SERVER}/{self.org}/organization/users"
        data = f"utf8=%E2%9C%93&username={user_id}&comments=&teams%5B%5D={team_id}&commit=Add+User"
        response = requests.request(method='post', url=url, data=data, headers=auth_header,
                                    verify=False)
        if response.ok:
            return True, None
        return False, "Something went wrong"

    def get_team_id(self, team="org-transitiononly"):
        url = f"{ONEOPS_SERVER}/{self.org}/organization/teams.json"
        response = requests.request(method='get', url=url, headers=self.auth_header,
                                    verify=False)
        if response.ok:
            _team = [__team for __team in response.json() if __team.get("name") == team][-1]
            if _team.get("id") > 0:
                return True, _team.get("id")
        return False, None

    def is_user_valid(self, user_id):
        url = f"{ONEOPS_SERVER}/registrations/lookup?login={user_id}"
        response = requests.request(method='get', url=url, headers=self.auth_header,
                                    verify=False)
        if response.ok:
            if len(response.json()) > 0:
                return True
        return False

    def get_vips(self):
        """ This function will return the glb of the env """
        _url = self.uri_builder(api_key="get_vip")
        response = self.authenticate_request(_url).json()
        legacy_gslb = response[0]['ciAttributes']['legacy_gslb']
        glb = legacy_gslb if legacy_gslb else json.loads(response[0]['ciAttributes']['gslb_vnames']).values()[0]
        cloud_vips = {}
        for res in response:
            entries = res.get("ciAttributes").get("entries", "legacy_entries")
            cloud = res.get('cloud').get('toCi').get('ciName')
            vip = ''.join([k for k, v in json.loads(entries).items() if self.cloud_regex.search_name_field(k)])
            cloud_vips[cloud] = vip

        return {
            'cloud_vips': cloud_vips,
            'glb': glb
        }

    @staticmethod
    def get_component_uri(org, assembly, platform, env, component):
        return f'/{org}/assemblies/{assembly}/operations/environments/{env}/platforms/{platform}/' \
               f'components/{component}/instances.json?instances_state=all'

    def get_hostname_and_ip(self, org, assembly, platform, env):
        results = list()
        uri = OneOps.get_component_uri(org, assembly, platform, env, "hostname")
        try:
            url = f"{ONEOPS_SERVER}/{uri}"
            r = requests.get(url, headers=self.auth_header, verify=False, timeout=10)
            res = r.json()
            for component in res:
                data = component.get('ciAttributes', {}).get('entries')
                cloud_primary = component.get('cloud', {}).get('relationAttributes', {}).get("priority")
                if cloud_primary != '1':
                    continue

                for host, ip in json.loads(data).items():
                    results.append({"hostname": host, "ip": ip[0]})
                    break
            return results
        except Exception as e:
            logger.exception(e)
            return {}

    def daemon_service(self, component_name=None, action="restart", dc=None, cloud=None, service=None,
                       wait_to_finish=True, background=False):
        task_status = True
        service = service if service else self.service
        cloud = cloud if cloud else self.cloud
        dc = dc if dc else self.dc
        status, procedure_id = self.execute_component_task(component_name=component_name, action=action, dc=dc,
                                                           cloud=cloud, service=service, wait_to_finish=wait_to_finish,
                                                           background=background)
        if background:
            return {"procedure_id": procedure_id, "background": background}

        procedure_status = self.get_procedure_execution_failed_instances(procedure_id)
        public_ips = []
        if procedure_status.get("is_completed"):
            public_ips = []
            for cid in procedure_status.get("failed_computes"):
                task_status = False
                public_ip = self.get_public_by_cid(cid=cid)
                logger.error("Unable to {} in {}".format(action, public_ip))
                public_ips.append(public_ip)
        return {"size": procedure_status.get("total_computes"), "failed_hosts": public_ips, "success": task_status}

    def get_procedure_status(self, procedure_id):
        return self.get_procedure_execution_failed_instances(procedure_id)

    def get_computes(self, cloud=None, dc=None):
        """ This function returns the computes, if clouds are specified, output will be per cloud"""
        cids = self.get_component_ids_by_dc(component_name="compute")
        _clouds = set()
        for _dc, clouds_data in cids.items():
            if _dc == dc:
                for cloud in clouds_data:
                    _clouds.add(cloud.keys()[0])
            elif not dc:
                for cloud in clouds_data:
                    _clouds.add(cloud.keys()[0])

        all_computes = {}
        response = self.get_component_instances(component_name="compute")
        for cloud in list(_clouds):
            cloud_computes = map(lambda compute: compute.get('ciAttributes').get('public_ip'),
                                 filter(lambda compute: compute.get('cloud').get('toCi').get('ciName') == cloud,
                                        response))
            all_computes[cloud] = cloud_computes
        return all_computes

    def get_all_computes(self, cloud=None, dc=None):
        nodes_with_cloud = self.get_computes()
        __nodes = []
        for nodes in nodes_with_cloud.values():
            __nodes.extend(nodes)
        return __nodes

    def get_unhealthy_computes(self):
        """ This fuction will return all unhealthy computes """
        _url = self.uri_builder(api_key=ONEOPS_APIS.get("get_computes"))
        response = self.authenticate_request(_url)
        computes = map(lambda compute: compute.get('ciAttributes').get('public_ip'),
                       filter(lambda compute: compute.get('opsState') == 'unhealthy',
                              response.json()))
        return computes

    def get_cloud_status(self, assembly, environment, platform):
        """ This fuction will return all unhealthy computes """
        _url = self.uri_builder(api_key="cloud_status", assembly=assembly, environment=environment, platform=platform)
        response = self.authenticate_request(_url)
        return response.json()

    def get_platforms(self, assembly, environment):
        platforms = list()
        _url = self.uri_builder(api_key="platforms", assembly=assembly, environment=environment)
        response = self.authenticate_request(_url)
        for platform in response.json():
            if platform["ciAttributes"]["is_active"] == "true":
                platforms.append(platform["ciName"])
        return platforms

    # def get_platform(self, assembly, environment, platform):
    #     platforms = list()
    #     _url = self.uri_builder(api_key="platform_details", assembly=assembly, environment=environment,
    #                             platform=platform)
    #     response = self.authenticate_request(_url)
    #     res = response.json()
    #     for platform in response.json():
    #         if platform["ciAttributes"]["is_active"] == "true":
    #             platforms.append(platform["ciName"])
    #     return platforms

    def get_secondary_clouds(self, assembly, environment, platform=None):
        logger.info("****************************************************")
        logger.info(f"Assembly: {assembly}, env {environment} ")
        logger.info("****************************************************")
        if not platform:
            platforms = self.get_platforms(assembly, environment)
        else:
            platforms = list()
            platforms.append(platform)

        for platform in platforms:
            logger.info(f"************** Platform {platform}*******************")

            secondary_cloud = dict()
            primary_cloud = dict()
            results = self.get_cloud_status(assembly, environment, platform)
            for comsume in results["consumes"]:
                ci_name = comsume["toCi"]["ciName"]
                updated = int(comsume["toCi"]["updated"] / 1000)
                status = comsume["relationAttributes"]["adminstatus"]
                if comsume["relationAttributes"]["priority"] == "2":
                    secondary_cloud[ci_name] = {"updated": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(updated)),
                                                "status": status}
                else:
                    primary_cloud[ci_name] = {"updated": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(updated)),
                                              "status": status}

            logger.info("*********** Secondary Clouds are ***********")
            for k, v in secondary_cloud.items():
                logger.info(f'{k}, last updated is {v}')
            logger.info("*********** Primary Clouds are ***********")
            for k, v in primary_cloud.items():
                logger.info(f'{k}, last updated is {v}')

    def read_env_file(self, file_name):
        f = open(file_name)
        lines = [line.rstrip() for line in f]
        f.close()
        return lines

    @staticmethod
    def get_all_orgs():
        try:
            r = requests.get(f"{BASE_URL}/{'tenants'}", headers=HEADERS, verify=False, timeout=10)
            return r.json().get("body").get("result")
        except Exception as e:
            logger.exception(e)
            return None


def check_arg():
    parser = argparse.ArgumentParser(prog='Azure CLI')
    subparsers = parser.add_subparsers(help='commands')

    args_create = subparsers.add_parser('secondary', help='Get Secondary cloud details')
    args_create.set_defaults(which='secondary')
    # create parameters
    # assembly, environment, platform
    args_create.add_argument('-o', '--org', metavar='\b', required=True, help='Org Name')
    args_create.add_argument('-a', '--assembly', metavar='\b', required=True, help='Assembly Name')
    args_create.add_argument('-e', '--environment', metavar='\b', required=True, help='Environment name')
    args_create.add_argument('-p', '--platform', metavar='\b', required=False, help='Platform name')

    args_file_sec = subparsers.add_parser('secondary-file', help='Get Secondary cloud details using file')
    args_file_sec.set_defaults(which='secondary-file')
    # create parameters
    # assembly, environment, platform
    args_file_sec.add_argument('-o', '--org', metavar='\b', required=True, help='Org Name')
    args_file_sec.add_argument('-f', '--file-name', metavar='\b', required=True, help='File Name')

    args_file_all = subparsers.add_parser('secondary-file-all-platforms', help='Get Secondary cloud details using file')
    args_file_all.set_defaults(which='secondary-file-all-platforms')
    # create parameters
    # assembly, environment, platform
    args_file_all.add_argument('-o', '--org', metavar='\b', required=True, help='Org Name')
    args_file_all.add_argument('-f', '--file-name', metavar='\b', required=True, help='File Name')

    parser.add_argument('-d', '--debug', action='store_true', help='Verbose mode')

    return parser.parse_args()


def main():
    # args = check_arg()
    # if args.debug:
    #     log_level = logging.DEBUG
    # else:
    log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    log_format = '%(lineno)3d %(levelname)10s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)
    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(logging.INFO)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)
    oneops = OneOps(org="mexicoecomm")
    platforms = list()
    url = "OrderFulfillment1111.json"
    # "mexicoecomm", "samsestore", "qa", "restapp"
    data = oneops.get_hostname_and_ip("mexicoecomm", "samsestore", "restapp", "prod")
    print(data)

    # response = oneops.authenticate_request(url)
    # print(response)
    # if args.which == 'secondary':
    #     oneops = OneOps(org=args.org)
    #     oneops.get_secondary_clouds(assembly=args.assembly, environment=args.environment, platform=args.platform)
    # elif args.which == 'secondary-file':
    #     oneops = OneOps(org=args.org)
    #     data = oneops.read_env_file(file_name=args.file_name)
    #     for line in data:
    #         assembly, env, platform = line.split(",")
    #         try:
    #             oneops.get_secondary_clouds(assembly, env, platform)
    #         except Exception as e:
    #             logger.error(e)


if __name__ == "__main__":
    main()
