import json, os
import logging, math
import requests
from requests.auth import HTTPBasic<PERSON>uth
from concurrent.futures import ThreadPoolExecutor
import re
from datetime import datetime

logger = logging.getLogger(__name__)
ATG_UNAME = "admin"
ATG_NONAME = "2021@Walmex"

# QA
# ATG_UNAME = "admin"
# ATG_NONAME = "Blackmoon@2023"

header = {
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-language": "en-US,en;q=0.9,es;q=0.8,zh-CN;q=0.7,zh;q=0.6",
    "cache-control": "max-age=0",
    "content-type": "application/x-www-form-urlencoded",
    "upgrade-insecure-requests": "1"
}
compiled_exp = re.compile(r"enable_paypal_payment_mode=(.+?)")
gsa_exp = re.compile(
    r'''propertyName=enabled">enabled<\/a><\/td><td><span style='white-space:pre'>false<\/span><\/td><td>boolean<\/td><\/tr>''')

gsa_exp_val = re.compile(
    r'''propertyName=enabled">enabled<\/a><\/td><td><span style='white-space:pre'>(.+?)<\/span><\/td><td>boolean<\/td><\/tr>''')


class ATG:
    def __init__(self, hosts, port=8080):
        self.hosts = hosts
        self.port = port
        self.paypal_feature_switch = "dyn/admin/nucleus/com/walmart/wmx/common/WMXFeatureSwitch"
        self.paypal_cache_key = "enable_paypal_payment_mode"
        self.cache_invalidation_count = 0

    def process_paypal_switch(self, host, enable=False):
        status, jsessionid, exception_occurred = self.get_jsession(host)
        if not status:
            logger.error(f"Jsession call failed for {host}")
            logger.warning(f"Not making paypal_switch for {host}")
            return False, exception_occurred
        url = f"http://{host}:{self.port}/{self.paypal_feature_switch}/;jsessionid={jsessionid}?propertyName=configurationCache.enable_paypal_payment_mode"
        if enable:
            mode = "Y"
        else:
            mode = "N"
        return self._handle_paypal_switch(url, mode), False

    def get_jsession(self, host):
        start_time = datetime.now()
        url = f"http://{host}:{self.port}/dyn/admin/nucleus/com/walmart/wmx/common/WMXFeatureSwitch/"
        try:
            logger.info(f"*** Making Get JSession call for {host} ***")
            res = requests.get(url, auth=HTTPBasicAuth(ATG_UNAME, ATG_NONAME), headers=header, verify=False, timeout=60)
            if res.ok:
                cookies = res.cookies
                jsessionid = cookies.get("JSESSIONID")
                logger.info(f"JSession call took {datetime.now() - start_time} for {host}")
                return True, jsessionid, False
            logger.info(f"JSession call failed took {datetime.now() - start_time} for {host}")
            return False, None, False
        except Exception as e:
            logger.exception(f"Unable to get JSession details for {url} ")
            logger.info(f"JSession call failed took {datetime.now() - start_time} for {host}")
            return False, None, True

    def _invalidate_cache(self, host):
        start_time = datetime.now()
        status, jsessionid, exception_occurred = self.get_jsession(host)
        if not status:
            logger.error(f"Jsession call failed for {host}")
            logger.warning(f"Not making paypal_switch for {host}")
            return False, exception_occurred
        in_uri = f"http://{host}:{self.port}/dyn/admin/nucleus/com/walmart/wmx/common/WMXFeatureSwitch/;jsessionid={jsessionid}"
        body = f"invokeMethod=invalidateConfigurationCache&submit=Invoke+Method"
        logger.info(f"Making cache invalidate call for {host}")
        res = requests.post(in_uri, auth=HTTPBasicAuth(ATG_UNAME, ATG_NONAME), data=body, verify=False, headers=header)
        if res.ok:
            # logger.info("Successfully invalidated_cache {url} , payload {body}")
            logger.info(f"Making cache invalidate call took {datetime.now() - start_time} for {host}")
            return True, False
        return False, False

    def invalidate_cache(self, hosts=None):
        results = list()
        futures = []
        if not hosts:
            hosts = self.hosts

        pool = ThreadPoolExecutor(len(hosts))
        for host in hosts:
            logger.info(f"Initialized, get status call for {host}")
            futures.append(pool.submit(self._invalidate_cache, host))

        for future in futures:
            try:
                status = future.result()
                results.append(status)
            except Exception as e:
                logger.exception(e)
        if results.count(True) == len(hosts):
            return True
        return False

    @staticmethod
    def _handle_paypal_switch(url, mode):
        start_time = datetime.now()
        body = f"propertyName=configurationCache.enable_paypal_payment_mode&newValue={mode}&change=Change+Value"
        logger.info(f"Making update paypal call {url} , payload {body}")
        res = requests.post(url, auth=HTTPBasicAuth(ATG_UNAME, ATG_NONAME), data=body, headers=header, verify=False)
        if res.ok:
            logger.info(f"Successfully executed the call url {url} , payload {body}")
            logger.info(f"Making update paypal call took mdde (mode) {datetime.now() - start_time} for {url}")
            return True
        return False

    def get_paypal_switch_status(self, host):
        """
        If status is True , means it enabled. Else disabled
        """
        start_time = None
        try:
            status, jsessionid, exception_occurred = self.get_jsession(host)
            if not status:
                logger.error(f"Jsession call failed for {host}")
                logger.warning(f"Not making Get configurationCache for {host}")
                return False, None, host, exception_occurred

            url = f"http://{host}:{self.port}/{self.paypal_feature_switch}/;jsessionid={jsessionid}?propertyName=configurationCache"
            start_time = datetime.now()
            logger.info(f"Making Get configurationCache payload call {host}")
            res = requests.get(url, auth=HTTPBasicAuth(ATG_UNAME, ATG_NONAME), headers=header, verify=False)
            if res.ok:
                data = res.text
                filtered_data = compiled_exp.findall(data)
                logger.info(f"Making Get configurationCache payload call took {datetime.now() - start_time} for {host}")
                if len(filtered_data) == 0:
                    return False, None, host, False
                return True, filtered_data[0], host, False
            return False, None, host, False
        except Exception as e:
            logger.exception(f"Making Get configurationCache {e}")
            return False, None, host, True

    def _get_component_status(self, host, validation_url, validation_regex):
        """
        If status is True , means it enabled. Else disabled
        """
        start_time = None
        cache_in_validation_required = False
        try:
            status, jsessionid, exception_occurred = self.get_jsession(host)
            if not status:
                logger.error(f"Jsession call failed for {host}")
                logger.warning(f"Not making Get configurationCache for {host}")
                return False, host, exception_occurred, cache_in_validation_required

            url = f"http://{host}:{self.port}/{validation_url}/;jsessionid={jsessionid}"
            start_time = datetime.now()
            logger.info(f"Making Get configurationCache payload call {host}")
            res = requests.get(url, auth=HTTPBasicAuth(ATG_UNAME, ATG_NONAME), headers=header, verify=False)
            if res.ok:
                data = res.text
                filtered_data = validation_regex.findall(data)
                # filtered_data_val = gsa_exp_val.findall(data)
                logger.info(f"Making Get configurationCache payload call took {datetime.now() - start_time} for {host}")
                if len(filtered_data) == 0:
                    return False, host, False, cache_in_validation_required
                else:
                    return True, host, False, cache_in_validation_required
            return False, host, False, True
        except Exception as e:
            logger.exception(f"Making Get configurationCache {e}")
            return False, host, True, False

    def get_not_enabled_hosts(self):
        check_value = "N"
        hosts = list()
        data = self._get_status(check_value=check_value)
        for host in data.get("data"):
            if host.get("response") == check_value:
                hosts.append(host.get("host"))

        return hosts, data

    def get_enabled_hosts(self):
        check_value = "Y"
        hosts = list()
        data = self._get_status(check_value=check_value)
        for host in data.get("data"):
            if host.get("response") == check_value:
                hosts.append(host.get("host"))

        return hosts, data

    def _get_status(self, check_value="Y"):
        results = list()
        failed_hosts = list()
        futures = []
        pool = ThreadPoolExecutor(len(self.hosts))

        start_time = datetime.now()
        logger.info(f"Making _get_status call")

        # for host in self.hosts:
        #     status, response, hos = self.get_paypal_switch_status(host)
        #     _res = {
        #         "host": host,
        #         "status": status,
        #         "response": response
        #     }
        #     results.append(_res)

        for host in self.hosts:
            logger.info(f"Initialized, get status call for {host}")
            futures.append(pool.submit(self.get_paypal_switch_status, host))

        for future in futures:
            try:
                status, response, host, exception_occurred = future.result()
                _res = {
                    "host": host,
                    "status": status,
                    "is_exception_occurred": exception_occurred,
                    "response": response
                }
                results.append(_res)
            except Exception as e:
                logger.exception(e)
        logger.info(f"Making _get_status call took {datetime.now() - start_time}")
        return ATG._process_property_values(results, check_value)

    def get_status(self):
        logger.info("********************* Started executing get_status call **********************")
        enabled_hosts, data = self.get_enabled_hosts()
        hosts = [_data for _data in data.get("data") if not _data.get('is_exception_occurred')]
        pct = self.value_to_pct(len(enabled_hosts), len(hosts))
        # data = self._get_status(check_value="Y")
        required_rerun = False
        logger.info("Looping to check weather invalidate_cache required or not")
        for _data in data.get("data"):
            if _data.get('response') is None and not _data.get('is_exception_occurred'):
                logger.info(f"**** Calling  invalidate_cache method, payload is {_data}  *******")
                self.invalidate_cache()
                self.cache_invalidation_count = self.cache_invalidation_count + 1
                break
        if required_rerun and self.cache_invalidation_count == 1:
            logger.info(f"**** Re executing get_status call  *******")
            return self.get_status()
        logger.info(f"**** Re turing results from  get_status {data} *******")
        return data, pct

    def get_component_status(self, url, regex_exp):
        logger.info("********************* Started executing gsa_invalidator_service call **********************")

        results = list()
        futures = []
        pool = ThreadPoolExecutor(len(self.hosts))

        start_time = datetime.now()

        for host in self.hosts:
            logger.info(f"Initialized, get status call for {host}")
            futures.append(pool.submit(self._get_component_status, host, url, regex_exp))

        for future in futures:
            try:
                status, host, exception_occurred, cache_in_validation_required = future.result()
                results.append({"status": status, "host": host, "exception_occurred": exception_occurred,
                                "cache_in_validation_required": cache_in_validation_required})
            except Exception as e:
                logger.exception(e)

        hosts = [_data for _data in results if not _data.get('is_exception_occurred')]
        cache_in_validation_required_hosts = [_data for _data in results if
                                              not _data.get('cache_in_validation_required')]

        return hosts, cache_in_validation_required_hosts, results

    def process_component_status(self, uri, validate_regx):
        required_rerun = False
        working_hosts, cache_in_validation_required_hosts, results = self.get_component_status(uri, validate_regx)
        if len(cache_in_validation_required_hosts) > 0:
            self.invalidate_cache(cache_in_validation_required_hosts)
            required_rerun = True
        if required_rerun:
            working_hosts, cache_in_validation_required_hosts, results = self.get_component_status(uri, validate_regx)
        return working_hosts, cache_in_validation_required_hosts, results

    def get_gsa_invalidator_service_component_status(self):
        gsa_url = "dyn/admin/nucleus/atg/dynamo/service/GSAInvalidatorService"
        validate_regx = gsa_exp
        return self.process_component_status(gsa_url, validate_regx)

    def execute_component_modify_process(self, status_uri, status_check_regx, modify_uri, modify_payload):
        working_hosts, cache_in_validation_required_hosts, results = self.process_component_status(status_uri,
                                                                                                   status_check_regx)
        results = self.modify_component_status(working_hosts, modify_uri, modify_payload)
        return results

    def modify_gsa_invalidator_service_component_status_to_true(self):
        modify_uri = "dyn/admin/nucleus/atg/dynamo/service/GSAInvalidatorService"
        modify_payload = "propertyName=enabled&newValue=true&change=Change+Value"
        status_uri = "dyn/admin/nucleus/atg/dynamo/service/GSAInvalidatorService"
        status_check_regx = gsa_exp
        return self.execute_component_modify_process(status_uri, status_check_regx, modify_uri, modify_payload)

    def modify_gsa_invalidator_service_component_status_to_false(self):
        modify_uri = "dyn/admin/nucleus/atg/dynamo/service/GSAInvalidatorService"
        modify_payload = "propertyName=enabled&newValue=false&change=Change+Value"
        status_uri = "dyn/admin/nucleus/atg/dynamo/service/GSAInvalidatorService"
        status_check_regx = gsa_exp
        return self.execute_component_modify_process(status_uri, status_check_regx, modify_uri, modify_payload)

    def modify_gsa_invalidator_receiver_component_status_to_true(self):
        modify_uri = "dyn/admin/nucleus/atg/dynamo/service/GSAInvalidationReceiver"
        modify_payload = "propertyName=enabled&newValue=true&change=Change+Value"
        status_uri = "dyn/admin/nucleus/atg/dynamo/service/GSAInvalidationReceiver"
        status_check_regx = gsa_exp
        return self.execute_component_modify_process(status_uri, status_check_regx, modify_uri, modify_payload)

    def modify_gsa_invalidator_receiver_component_status_to_false(self):
        modify_uri = "dyn/admin/nucleus/atg/dynamo/service/GSAInvalidationReceiver"
        modify_payload = "propertyName=enabled&newValue=false&change=Change+Value"
        status_uri = "dyn/admin/nucleus/atg/dynamo/service/GSAInvalidationReceiver"
        status_check_regx = gsa_exp
        return self.execute_component_modify_process(status_uri, status_check_regx, modify_uri, modify_payload)

    def _modify_component_status(self, host, uri, payload):
        try:
            status, jsessionid, exception_occurred = self.get_jsession(host)
            if not status:
                logger.error(f"Jsession call failed for {host}")
                logger.warning(f"Not making Get configurationCache for {host}")
                return False

            url = f"http://{host}:{self.port}/{uri}/;jsessionid={jsessionid}"
            start_time = datetime.now()
            logger.info(f"Making Get configurationCache payload call {host}")
            res = requests.get(url, auth=HTTPBasicAuth(ATG_UNAME, ATG_NONAME), data=payload, headers=header,
                               verify=False)
            if res.ok:
                logger.info(f"modify component call took {datetime.now() - start_time} for {host}")
                return True
            return False
        except Exception as e:
            logger.exception(f"Making Get configurationCache {e}")
            return False

    def modify_component_status(self, hosts, uri, payload):
        results = list()
        futures = []
        pool = ThreadPoolExecutor(len(hosts))

        start_time = datetime.now()

        for host in hosts:
            logger.info(f"Initialized, get status call for {host}")
            futures.append(pool.submit(self._modify_component_status, host, uri, payload))

        for future in futures:
            try:
                status, host, exception_occurred, cache_in_validation_required = future.result()
                results.append({"status": status, "host": host})
            except Exception as e:
                logger.exception(e)
        logger.info(f"modify component call took {datetime.now() - start_time}")
        return [_data for _data in results if _data.get('status')], futures

    @staticmethod
    def _process_property_values(data, check_value="Y"):
        """
        Process, Y or N for all the list. If one is different then other return false
        """
        res = list()
        for _data in data:
            if type(_data) is list:
                for _v in _data:
                    if _v.get('response') == check_value:
                        res.append(check_value)
            else:
                if _data.get('response') == check_value:
                    res.append(check_value)
        count = res.count(check_value)
        if len(data) == count:
            status = True
        else:
            status = False
        return {"data": data, "overall_status": status}

    def handle_paypal_switch(self, hosts, enable=False, pct_value=100):

        from concurrent.futures import ThreadPoolExecutor
        futures = []
        pct_to_num = self.pct_to_value(pct_value, len(hosts))
        if pct_to_num > len(self.hosts):
            pct_to_num = len(self.hosts)
        hosts = hosts[:pct_to_num]
        pool = ThreadPoolExecutor(len(hosts))

        for host in hosts:
            logger.info(f"Initialized, get status call for {host}")
            futures.append(pool.submit(self.process_paypal_switch, host, enable))

        for future in futures:
            try:
                future.result()
            except Exception as e:
                logger.exception(e)

    def enable_paypal_switch(self, pct_value=100):
        all_disabled_hosts, results_data = self.get_not_enabled_hosts()
        if len(all_disabled_hosts) == 0:
            return self.hosts, 100
        self.handle_paypal_switch(all_disabled_hosts, enable=True, pct_value=pct_value)
        enabled_hosts, data = self.get_enabled_hosts()
        hosts = [_data for _data in data.get("data") if not _data.get('is_exception_occurred')]
        return enabled_hosts, self.value_to_pct(len(enabled_hosts), len(hosts))

    def dis_paypal_switch(self, pct_value=100):
        all_enabled_hosts, results_data = self.get_enabled_hosts()
        if len(all_enabled_hosts) == 0:
            return self.hosts, 100
        self.handle_paypal_switch(all_enabled_hosts, enable=False, pct_value=pct_value)
        disabled_hosts, data = self.get_not_enabled_hosts()
        hosts = [_data for _data in data.get("data") if not _data.get('is_exception_occurred')]
        return disabled_hosts, self.value_to_pct(len(disabled_hosts), len(hosts))

    def pct_to_value(self, pct, total):
        try:
            return math.ceil((pct * total) / float(100))
        except ZeroDivisionError:
            return 0

    def value_to_pct(self, processed_hosts, total):
        return (processed_hosts / total) * 100




if __name__ == "__main__":
    from libs.oneOps import OneOps


    def get_hosts(org, assembly, platform, env):
        oneops = OneOps()
        data = oneops.get_hostname_and_ip(org, assembly, platform, env)
        return [host_map.get("hostname") for host_map in data]


    # atg = ATG(hosts=["restapp-334818145-1-1212575235.qa.samsestore.mexicoecomm.ndcstg2.prod.walmart.com",
    #                  "restapp-334818145-2-1280872355.qa.samsestore.mexicoecomm.ndcstg2.prod.walmart.com"])
    # print(atg.handle_paypal_switch(enable=False, pct_value=50))
    # print(atg.get_status(check_value="N"))
    # print(atg.get_enabled_hosts())
    # print(atg.dis_paypal_switch(1))
    # print(atg.enable_paypal_switch(1))
    hosts = get_hosts("mexicoecomm", "samsestore", "restapp", "canary")
    atg = ATG(hosts=hosts)
    atg.get_gsa_invalidator_service_status()
