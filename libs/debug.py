from django.shortcuts import render
import time, os, logging

from slack_bot.slack import send_message
import csv
from libs.prometheus_client import Prometheus
from template.views import generate_template_compiled_files
from teap.views import process_search_namespace_assembly_db_field

logger = logging.getLogger(__name__)

pro = Prometheus()
sub_details = pro.get_cosmos_subscription()


def write_data_to_csv_file(data: list, file_name_with_path: str):
    """
     data: is list of list data, Ex: [["SN", "Movie", "Protagonist"],[1, "Lord of the Rings", "Frodo Baggins"]]
    """

    with open(file_name_with_path, 'w', newline='') as file:
        writer = csv.writer(file)
        writer.writerows(data)
        # for list_data in data:
        #     writer.writerow(list_data)


def get_subscription_and_rc_group(resource_name):
    for sub, rcs in sub_details.items():
        for rc in rcs:
            if resource_name in rc:
                return sub, rc
    return None, None


def get_user_app_inventory_details(resource_name):
    app_details = process_search_namespace_assembly_db_field(resource_name)
    for app in app_details:
        for key,val in app.items():
            rc = key
            for _app in val:
                print(_app)



def build_cosmos_metadata(resource_name):
    sub, rc_group = get_subscription_and_rc_group(resource_name)
    # "xmatters_group",
    # "subscription_name",
    # "alert_id",
    # "alert_team_name",
    # "team_email",
    # "resource_group",
    # "slack_channel",
    # "app_name",
    # "namespace"


# @api_view(['POST'])
@api_view(['GET'])
def create_cosmos_alerts(request, resource_group):
    try:
        get_user_app_inventory_details(resource_group)
        # post_data = request.data
        # return create_alerts(template="cosmos_alerts.yaml", data=post_data)
    except Exception as e:
        logger.exception(e)
        return Response({"ok": False, "body": str(e)}, status=400)

if __name__ == "__main__":
    get_user_app_inventory_details("gamora-ias-prod-azure-cosmosdb")