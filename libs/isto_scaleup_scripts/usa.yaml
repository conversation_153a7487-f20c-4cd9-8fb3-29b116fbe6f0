name: Istio Scaler Steps - 45k
security:
  allowedGroups:
  - SRE-ENG
  - GTP_PSDO_SRE
steps:
- name: step-1
  services:
  - name: step-1
    wcnp-scaler:
    - clusters:
      - cluster: cac-prod-a1
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 24 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: cae-prod-a1
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 24 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: cneast-prod-az-001
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: cnnorth-prod-az-001
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a1
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a10
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 9 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a11
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 41 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a12
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a13
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a14
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a15
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a16
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a17
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a18
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 176 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a19
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a2
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a20
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a21
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a22
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 13 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a23
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 21 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a24
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a25
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a27
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 14 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a28
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a29
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a3
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 60 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a30
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 130 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a31
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 11 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a32
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a33
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a34
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 39 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a36
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a37
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a38
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a39
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a4
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a40
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a41
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 18 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a42
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a43
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a44
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 24 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a45
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 50 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a46
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 20 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a47
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a48
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a5
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 36 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a50
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 104 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a51
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a52
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a53
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a54
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 77 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a56
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 36 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a57
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a58
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a59
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a6
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a60
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a61
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a62
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a63
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a64
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 50 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a65
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 34 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a66
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 48 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a7
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a8
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 57 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a9
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 19 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-aglass1
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-aglass2
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-ahipaa01
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-ahipaa02
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 7 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-ahipaa03
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-aspcl08
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 30 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-aspcl09
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a1
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a10
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a100
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 29 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a101
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a11
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 51 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a12
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a13
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 90 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a14
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a15
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a16
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a17
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a18
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 60 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a19
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a2
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a20
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a21
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a22
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a23
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a25
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a26
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a27
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 176 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a28
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a29
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a3
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a30
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a31
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a32
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a33
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a34
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 15 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a35
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a36
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 32 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a37
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a38
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 18 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a39
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a4
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a40
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a41
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 9 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a42
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 36 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a43
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 47 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a44
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a45
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 45 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a46
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a47
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a48
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a49
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 45 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a5
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 25 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a50
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 18 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a51
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a52
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 127 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a53
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a54
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a55
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a56
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a57
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 7 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a58
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a59
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a6
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 51 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a60
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a61
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a62
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 72 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a63
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a64
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 46 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a65
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 52 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a66
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a67
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a69
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a7
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a70
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a71
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a72
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a73
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a74
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a75
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a76
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a77
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a78
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 128 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a8
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a80
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a81
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a82
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a83
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a84
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 196 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a85
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 22 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a86
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a87
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 63 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a88
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a89
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 18 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a9
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 28 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a90
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 18 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a91
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a92
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a93
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a94
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a95
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 30 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a96
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a97
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 17 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a98
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a99
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-aglass1
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-aglass2
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-ahipaa01
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-ahipaa02
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-ahipaa03
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-aspcl08
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 30 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-aspcl09
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-awcnpsvcs01
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uks-prod-a1
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 20 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uksouth-prod-az-001
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 24 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: ukw-prod-a1
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 20 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: ukwest-prod-az-001
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 24 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-001
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-002
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 22 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-003
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
- name: step-2
  services:
  - name: step-2
    wcnp-scaler:
    - clusters:
      - cluster: uscentral-prod-az-004
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-005
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-006
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-007
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 9 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-008
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 31 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-009
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-010
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-011
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-012
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-013
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-014
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 18 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-015
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 28 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-016
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-017
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 268 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-021
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-022
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 22 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-023
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-024
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 40 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-025
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-026
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-027
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-028
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-029
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 107 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-030
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-031
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 28 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-032
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-033
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 114 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-034
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-035
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-036
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-037
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 21 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-038
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 189 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-300
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 405 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-301
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 173 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-302
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 327 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-303
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-304
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-305
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 24 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-306
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 324 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-307
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 167 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-308
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 87 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-309
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-310
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 125 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-311
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 450 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-312
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-313
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 64 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-314
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 35 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-315
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-316
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-317
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 64 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-318
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-319
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 210 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-320
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 47 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-321
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 64 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-322
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 64 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-323
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-324
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-325
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 264 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-326
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 28 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-327
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 40 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-328
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 28 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-329
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 123 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-330
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 20 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-331
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 117 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-332
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-333
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 405 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-334
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 90 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-335
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-336
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-337
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-338
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 261 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-339
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-340
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-341
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-342
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-343
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 32 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-344
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 81 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-345
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-346
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 22 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-347
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-348
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 38 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-349
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 99 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-350
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 123 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-hipaa001
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-hipaa002
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-hipaa003
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-gke-001
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-gke-002
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 140 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-gke-003
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-gke-004
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-gke-hipaa01
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral1-prod-gke01
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral1-prod-gke02
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 19 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral1-prod-gke03
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-001
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-002
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-003
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 62 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-004
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-005
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-006
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-007
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-008
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 72 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-009
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 196 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-012
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 196 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-013
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 29 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-014
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-015
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 9 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-016
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 138 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-017
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-018
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-019
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 27 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-020
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-021
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 64 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-300
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 327 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-301
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 156 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-302
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 254 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-303
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-304
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-305
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 18 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-306
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 325 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-307
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 273 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-308
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 27 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-309
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 46 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-310
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 140 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-311
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 450 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-312
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 51 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-313
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 112 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-314
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 46 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-315
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 20 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-316
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 104 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-317
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 364 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-318
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 58 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-319
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-320
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 7 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-321
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 48 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-322
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 31 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-323
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 72 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-324
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 156 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-325
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 27 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-326
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-327
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-328
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 7 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-329
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 334 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-330
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 51 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-331
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 18 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-332
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 17 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-333
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 24 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-334
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 57 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-335
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-337
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 117 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-338
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-339
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 27 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-340
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 54 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-c2c01
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-hipaa001
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-gke-001
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-gke-003
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 130 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-gke-c2c01
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-gke-hipaa01
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast4-prod-gke01
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 21 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast4-prod-gke02
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-001
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-002
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 32 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-003
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 9 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-004
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 72 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-005
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 129 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-006
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 176 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-007
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-008
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 24 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-009
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-010
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-011
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 80 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-012
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 116 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-013
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-014
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-015
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-016
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 176 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-017
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-018
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-019
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 93 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-020
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 116 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-021
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-022
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-023
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 42 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-024
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-025
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
- name: step-3
  services:
  - name: step-3
    wcnp-scaler:
    - clusters:
      - cluster: uswest-prod-az-026
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-027
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-028
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 45 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-029
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 14 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-030
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 15 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-031
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-032
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 13 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-033
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 25 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-034
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 14 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-035
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-036
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-037
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 11 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-038
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-039
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 125 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-040
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 84 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-041
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 14 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-042
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-043
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-044
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 7 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-045
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-046
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 51 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-047
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-048
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-049
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-050
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 218 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-053
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 218 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-054
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-055
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 14 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-056
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 33 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-057
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-058
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-059
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 7 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-060
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 137 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-061
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-062
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-063
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-064
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 31 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-065
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-066
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 82 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-067
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-068
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-069
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-070
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 7 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-071
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-072
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-073
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 14 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-074
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 24 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-075
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-300
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 364 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-301
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 174 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-302
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 291 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-303
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 22 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-304
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 17 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-305
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 24 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-306
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 364 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-307
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 294 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-308
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 32 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-309
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 20 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-310
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 189 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-311
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 90 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-312
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-313
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 450 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-314
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 135 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-315
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 32 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-316
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 291 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-317
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 210 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-318
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-319
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 93 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-320
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 294 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-321
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-322
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 81 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-323
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 22 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-324
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 75 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-325
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 140 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-326
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-327
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 25 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-328
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-329
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 27 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-330
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 36 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-331
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 81 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-332
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 36 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-333
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 364 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-334
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 30 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-335
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-336
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 170 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-337
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-338
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-339
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-340
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 23 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-341
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-342
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 27 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-344
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 155 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-345
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 28 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-346
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 101 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-c2c01
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-hipaa001
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-hipaa002
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-az-hipaa003
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-gke-001
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-gke-002
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 108 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-gke-004
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-gke-c2c01
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest-prod-gke-hipaa01
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest1-prod-gke01
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 9 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest1-prod-gke02
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uswest1-prod-gke03
      hpa: istio-ingressgateway
      ignoreQuota: true
      matching: .min = 8 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a1
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a10
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 28 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a11
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a12
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a13
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a14
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a15
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a16
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a17
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a19
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a2
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a20
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a21
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a23
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a24
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a25
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a26
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a27
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a28
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a29
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a30
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a31
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a32
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a33
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a34
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a35
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a36
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a37
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a38
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 74 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a39
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a4
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a40
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 40 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a41
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a42
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a43
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a44
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a45
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a46
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a47
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a48
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a49
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a5
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a50
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a51
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a52
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a53
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 22 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a54
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a55
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a56
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a58
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a59
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a6
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a60
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a61
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a62
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a63
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a64
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a65
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a66
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a67
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a69
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a7
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a70
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a71
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a72
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a76
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a78
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a79
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a8
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a80
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a85
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a86
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a88
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a9
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a91
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a92
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a93
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a94
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a95
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-ahipaa01
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-ahipaa02
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-awcnpsvcs01
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05