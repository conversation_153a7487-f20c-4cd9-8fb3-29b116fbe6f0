import yaml
import re, math
from jinja2 import Template


def read_yaml(file_name_with_path):
    # Load the YAML file
    with open(file_name_with_path, 'r') as file:
        return yaml.safe_load(file)


def extract_scaler_data(file_name_with_path):
    """
    file_name_with_path: Yaml path
    """
    data = read_yaml(file_name_with_path)

    # Now `data` is a dictionary
    new_list = []
    for step in data.get("steps"):
        for service in step.get("services"):
            for scalat in service.get("wcnp-scaler"):
                new_list.append(scalat)
    return new_list


def build_template_dict():
    pass


def filter_and_normalize_required_clusters(required_clusters: list, total_clusters: list, pct_increase=20):
    filter_list = list()
    for cluster in required_clusters:
        for usa_cluster_data in total_clusters:
            if cluster == usa_cluster_data["clusters"][0]["cluster"]:
                num = extract_num(usa_cluster_data['matching'])
                num = (num * pct_increase / 100) + num
                num = math.ceil(num)
                matching = f" .min = {num} | .scaled = true"
                filter_list.append({"namespace": usa_cluster_data['namespace'],
                                    "threshold": usa_cluster_data['threshold'],
                                    "matching": matching,
                                    "cluster_id": cluster
                                    })
    return filter_list


def create_yaml_from_template(data):
    template_content = """
    name: Istio Scaler Steps - 45k
    security:
      allowedGroups:
      - intl-sre
    steps:
      - name: step-1
        services:
          - name: ISTO Scale
            wcnp-scaler:
            {% for item in apps %}
             - namespace: "{{ item.namespace }}"
               hpa: istio-ingressgateway-customhpa
               ignoreQuota: true
               matching-type: jq
               threshold: {{ item.threshold }}
               matching: {{ item.matching }}
               clusters:
                 - cluster: {{ item.cluster_id }}
            {%- endfor %}
    """
    # Create a Jinja2 Template object
    template = Template(template_content)

    # Render the template with data
    rendered_content = template.render(apps=data)

    # Save the rendered content to a YAML file
    with open("/Users/<USER>/git/juno/wcnp/Istio_Scaler_Steps.yaml", "w") as yaml_file:
        yaml_file.write(rendered_content)

    print("YAML file generated successfully.")


def extract_num(text):
    # Regular expression to match a number after ".min = "
    match = re.search(r"\.min\s*=\s*(\d+)", text)

    if match:
        # Extract the matched number
        number = int(match.group(1))
        return number
    else:
        print("Unable to extract data from " + text)
        exit(1)


def create_yaml_using_usa_playbook(us_playbook, required_clusters: list):
    """
    required_clusters: used this clusters to extract scalar data from us_playbook

    Example:
        required_clusters: ["scus-prod-a61", "eus2-prod-a40"]
    """
    scalar_data = extract_scaler_data(us_playbook)
    data = filter_and_normalize_required_clusters(required_clusters, scalar_data)
    create_yaml_from_template(data)


if __name__ == "__main__":
    from libs.oneOps import OneOps

    """
    """
    mx = ["scus-prod-a61"
        , "eus2-prod-a40"
        , "scus-prod-a91"
        , "eus2-prod-a9"
        , "useast-prod-az-320"
        , "scus-prod-a84"
        , "useast-prod-az-335"
        , "eus2-prod-a11"
        , "uscentral-prod-az-026"
        , "eus2-prod-a60"
        , "scus-prod-a45"
        , "eus2-prod-a28"
        , "scus-prod-a72"
        , "eus2-prod-a47"
        , "eus2-prod-a34"
        , "uscentral-prod-az-008"
        , "eus2-prod-a20"
        , "eus2-prod-a17"
        , "scus-prod-a77"
        , "eus2-prod-a36"
        , "scus-prod-a60"
        , "wus-prod-a72"
        , "eus2-prod-a43"
        , "scus-prod-a100"
        , "wus-prod-a6"
        , "scus-prod-a75"
        , "useast-prod-az-021"
        , "eus2-prod-a14"
        , "uscentral-prod-az-315"
        , "scus-prod-a51"
        , "useast-prod-az-013"
        , "uscentral-prod-az-335"
        , "uscentral-prod-az-336"
        , "scus-prod-a93"
        , "useast-prod-az-308"
        , "scus-prod-a74"
        , "useast-prod-az-310"
        , "uscentral-prod-az-001"
        , "useast-prod-az-020"
        , "scus-prod-a50"
        , "eus2-prod-a8"
        , "eus2-prod-a15"
        , "uscentral-prod-az-003"
        , "scus-prod-a2"
        , "wus-prod-a26"
        , "useast-prod-az-309"
        , "scus-prod-a38"
        , "eus2-prod-a41"
        , "uscentral-prod-az-005"
        , "scus-prod-a67"
        , "scus-prod-a97"
        , "uscentral-prod-az-025"
        , "uscentral-prod-az-313"
        , "eus2-prod-aspcl09"
        , "uscentral-prod-az-318"
        , "uscentral-prod-az-013"
        , "useast-prod-az-006"
        , "uscentral-prod-az-024"
        , "eus2-prod-a63"
        , "scus-prod-a25"
        , "uscentral-prod-az-308"
        , "uscentral-prod-az-036"
        , "useast-prod-az-322"
        , "uscentral-prod-az-341"
        , "uscentral-prod-az-337"
        , "useast-prod-az-328"
        , "scus-prod-a73"
        , "useast-prod-az-323"
        , "scus-prod-a101"
        , "eus2-prod-a57"
        , "scus-prod-a87"
        , "useast-prod-az-001"
        , "useast-prod-az-331"
        , "scus-prod-a23"
        , "uscentral-prod-az-343"
        , "scus-prod-a92"
        , "useast-prod-az-318"
        , "scus-prod-a22"
        , "uscentral-prod-az-320"
        , "useast-prod-az-332"
        , "uscentral-prod-az-327"
        , "scus-prod-a48"
        , "useast-prod-az-326"
        , "scus-prod-a47"
        , "uscentral-prod-az-009"
        , "scus-prod-a6"
        , "scus-prod-a32"
        , "uscentral-prod-az-023"
        , "uswest-prod-az-335"
        , "scus-prod-a39"]
    # create_yaml_using_usa_playbook("/Users/<USER>/git/juno/libs/isto_scaleup_scripts/usa.yaml",mx)
    yaml_data = extract_scaler_data(
        "/Users/<USER>/git/juno/libs/isto_scaleup_scripts/ca-stress-test-istio-scaleup.yaml")

# Define the Jinja2 template
