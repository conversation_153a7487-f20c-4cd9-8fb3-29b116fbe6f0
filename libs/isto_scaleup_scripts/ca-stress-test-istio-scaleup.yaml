name: CA Stress Test | Istio Scaler Scale up
security:
  allowedGroups:
  - IntlSRE
steps:
- name: step-1
  services:
  - name: step-1
    wcnp-scaler:
    - clusters:
      - cluster: eus2-prod-a12
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a13
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a21
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a27
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 14 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a38
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a43
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a47
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a5
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 36 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a51
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a59
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a60
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a61
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a65
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 34 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a66
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 48 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a7
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 6 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-a8
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 57 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: eus2-prod-aspcl08
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 30 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a100
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 29 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a101
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a20
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 10 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a23
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a25
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a38
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 18 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a45
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 45 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a47
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a55
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a82
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a84
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 196 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a95
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 30 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a97
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 17 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-a99
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: scus-prod-aspcl08
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 30 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
- name: step-2
  services:
  - name: step-2
    wcnp-scaler:
    - clusters:
      - cluster: uscentral-prod-az-005
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-009
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 12 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-013
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-021
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-022
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 22 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-026
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-030
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-035
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-308
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 87 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-313
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 64 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-320
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 47 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-323
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-324
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 16 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: uscentral-prod-az-328
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 28 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-003
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 62 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-006
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-008
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 72 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-013
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 29 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-016
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 138 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-017
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 5 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-021
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 64 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-321
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 48 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-328
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 7 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: useast-prod-az-331
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 18 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
- name: step-3
  services:
  - name: step-3
    wcnp-scaler:
    - clusters:
      - cluster: wus-prod-a16
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
    - clusters:
      - cluster: wus-prod-a50
      hpa: istio-ingressgateway-customhpa
      ignoreQuota: true
      matching: .min = 4 | .scaled = true
      matching-type: jq
      namespace: istio-system
      threshold: 0.05
