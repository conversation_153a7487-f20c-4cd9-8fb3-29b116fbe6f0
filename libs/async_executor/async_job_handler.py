from libs.shell import bash
import logging, requests, os, json
from settings import TEMPLATE_OUTPUT_DIR, ASYNC

logger = logging.getLogger(__name__)
SSH_SERVER = ASYNC.get("SSH_SERVER")
SSH_SERVER_BASE_FOLDER = ASYNC.get("SSH_SERVER_BASE_FOLDER")
UPLOAD_FILE_NAME = ASYNC.get("UPLOAD_FILE_NAME")

LOCATIONS = {
    "input": "{}/input".format(SSH_SERVER_BASE_FOLDER),  # input files
    "meta": "{}/meta".format(SSH_SERVER_BASE_FOLDER),  # job metadata, banner , other than upc/items
    "in_progress": "{}/in_progress".format(SSH_SERVER_BASE_FOLDER),  # while processing will keep here
    "completed": "{}/completed".format(SSH_SERVER_BASE_FOLDER),  # job completed , job moved here
    "config": "{}/config.json".format(SSH_SERVER_BASE_FOLDER),  # job config
    "data": "{}/data".format(SSH_SERVER_BASE_FOLDER)  # output of the job, this is critical job
}


def get_configs(job_id):
    src_data_file = os.path.join(TEMPLATE_OUTPUT_DIR, UPLOAD_FILE_NAME.format_map({"job_id": job_id}))
    src_metadata_file = os.path.join(TEMPLATE_OUTPUT_DIR,f"{src_data_file.strip().rstrip()}.json")
    return {**LOCATIONS, "source_data_file": src_data_file, "source_meta_data_file": src_metadata_file}


def _upload(src_upload_file, dst_location):
    ssh_response = bash(f"scp {src_upload_file} {SSH_SERVER}:/{dst_location}/.")
    logger.info(f"ssh output is {ssh_response}")
    if ssh_response.return_code == 0:
        return True
    return False


def upload_data(job_id):
    config = get_configs(job_id)
    return _upload(config.get("source_data_file"), config.get("input"))


def upload_metadata(job_id):
    config = get_configs(job_id)
    return _upload(config.get("source_meta_data_file"), config.get("meta"))


def download_results(job_id):
    _file_name = UPLOAD_FILE_NAME.format_map({"job_id": job_id})
    src_upload_file = f"{LOCATIONS.get('data')}/{_file_name}.json"
    ssh_response = bash(f"scp app@{SSH_SERVER}:{src_upload_file} {TEMPLATE_OUTPUT_DIR}/.")
    logger.info(f"ssh output is {ssh_response}")
    if ssh_response.return_code == 0:
        return True
    return False


def get_response(job_id):
    config = get_configs(job_id)
    download_status = download_results(job_id)
    if not download_status:
        return None
    with open(config.get("source_meta_data_file")) as f:
        d = json.load(f)
        return d


def is_job_completed(job_id):
    command = f"find {SSH_SERVER_BASE_FOLDER} -type f -name '*_{job_id}'"
    ssh_response = bash(f"ssh {SSH_SERVER} {command}", read_lines=True)
    if ssh_response.return_code == 0:
        for _file in ssh_response.stdout:
            if f"completed" in _file and f"{job_id}" in _file:
                return True
    return False


def execute_async_task(job_id):
    command = f"ssh {SSH_SERVER} '''python {SSH_SERVER_BASE_FOLDER}/task.py  execute -j {job_id}'''"
    bash(command, back_ground=True)
