import logging, subprocess, os, time
from collections import namedtuple
import requests, json, argparse

logger = logging.getLogger(__name__)
agent_location = "/app/juno"
# agent_location ="/Users/<USER>/git/juno/libs/async_executor"
file_name = "file_{job_id}"  # DON'T CHANGE THIS VALUE, aligned to juno/settings.UPLOAD_FILE_NAME
metadata_file_name = "{}.json".format(file_name)  # DON'T CHANGE THIS VALUE, aligned to juno/settings.UPLOAD_FILE_NAME
data_file_name = "{}.json".format(file_name)  # DON'T CHANGE THIS VALUE, aligned to juno/settings.UPLOAD_FILE_NAME
LOCATIONS = {
    "input": "{}/input".format(agent_location),  # input files
    "meta": "{}/meta".format(agent_location),  # job metadata, banner , other than upc/items, remain should be here
    "in_progress": "{}/in_progress".format(agent_location),  # while processing will keep here
    "completed": "{}/completed".format(agent_location),  # job completed , job moved here
    # job config
    "data": "{}/data".format(agent_location)  # output of the job, this is critical job
}
config = "{}/config.json".format(agent_location)
JUNO_BASE_URL = "https://juno.api.stg.walmart.com"
# JUNO_BASE_URL = "http://localhost:9099"
JSON_OUTPUT_FILE = "{job_id}.json"
AKAMAI_THRESHOLD_BODY_SIZE = 50000
REPROCESS_CACHE_FLUSH_TIME_INTERVAL = 15
REPROCESS_CACHE_FLUSH_ELIGIBILITY_COUNT = 2
SSHResult = namedtuple('SSHResult',
                       ["command",
                        "return_code",
                        "pid",
                        "stdout",
                        "stderr",
                        "is_bg_process"
                        ])


def _sub_process(command):
    process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    return process


def bash(command, read_lines=False):
    """
     Executes Bash command in Local machine or localhost
    Args:
        command: Executes the command on local system
        read_lines:

    Returns:

    """
    try:
        shell_results = _sub_process(command)
        (stdout, stderr) = shell_results.communicate()
        if read_lines:
            response = _result(command, shell_results.returncode, shell_results.pid,
                               stdout.decode("utf-8").splitlines(),
                               stderr, is_bg_process=False)
        else:
            response = _result(command, shell_results.returncode, shell_results.pid, stdout.decode("utf-8"),
                               stderr, is_bg_process=False)

        return response
    except Exception as e:
        logger.error('Unable to run the command in with command %s and exception is %s' % (command, e))
        logger.exception(e)


def _result(command, return_code, pid, stdout, stderr, is_bg_process=False):
    result = None
    try:
        result = SSHResult(command, return_code, pid, stdout, stderr, is_bg_process)
    except Exception as e:
        logger.error('Unable to run the command in with command %s and exception is %s' % (command, e))
    return result


class JobExecutor:
    """
    It handles image files to flush the cache and post the image files to i5 image optimize queue.
    """

    def __init__(self, job_id):
        # self.market = market
        self.job_id = job_id
        self.files_map = dict()
        self.json_files_map = dict()

    def __call__(self, *args, **kwargs):
        JobExecutor.task_logger("Starting Rsync Client Process")
        JobExecutor.create_mandatory_locations()
        JobExecutor.command_logger("Reading config file")
        logger.info("config details are {}".format(config))

    @staticmethod
    def create_mandatory_locations():
        logger.info("Creating necessary locations")
        for name, loc in LOCATIONS.items():
            logger.info("Creating location {}".format(name))
            bash("mkdir -p {}".format(loc))

    @staticmethod
    def get_config():
        logger.info("Config location is {}".format(config))
        with open(config) as f:
            return json.load(f)

    def _get_meta_data(self):
        logger.info("Meta location is {}".format(LOCATIONS.get("meta")))
        config_file = LOCATIONS.get("meta")
        file_path = "{}/{}".format(config_file, metadata_file_name.format(job_id=self.job_id))
        if not JobExecutor.check_file_or_dir_exists(file_name=file_path):
            logger.error("Meta data file not found")
            exit(1)
        with open(file_path) as f:
            return json.loads(json.load(f))

    def _get_data_file(self):
        data = list()
        file_in_input = False
        file_is_in_progress = False
        logger.info("Data location is {}".format(LOCATIONS.get("input")))
        _file_name = file_name.format(job_id=self.job_id)
        source_file = "{}/{}".format(LOCATIONS.get("input"), _file_name)
        dst_loc = "{}/".format(LOCATIONS.get("in_progress"))
        dest_file = "{}/{}".format(LOCATIONS.get("in_progress"), _file_name)
        if JobExecutor.check_file_or_dir_exists(file_name=source_file):
            logger.info("Files exits {}".format(source_file))
            file_in_input = True
            bash("mv {} {}".format(source_file, dst_loc))
            in_progress_file = "{}/{}".format(LOCATIONS.get("in_progress"), _file_name)
            data = JobExecutor.get_file_content(in_progress_file)
        elif JobExecutor.check_file_or_dir_exists(file_name=dest_file):
            file_is_in_progress = True
            data = JobExecutor.get_file_content(dest_file)
        if not (file_in_input or file_is_in_progress):
            exit(1)
        return data

    @staticmethod
    def get_csv_text_file_content(file_with_location):
        return JobExecutor.get_file_content(file_with_location)

    @staticmethod
    def transform_data(data, meta_data):
        formatted_data = list()
        if "api/sre/itemOffer/anomalies" in meta_data.get("api"):
            for _data in data:
                formatted_data.append({"offer_id": _data})
            return formatted_data
        else:
            return data

    def process_data_file(self):
        data = self._get_data_file()
        metadata = self._get_meta_data()
        data = JobExecutor.transform_data(data, metadata)
        response = list()
        _chunks = JobExecutor.chunks(data, 20)
        for _data_chunk in _chunks:
            try:
                _res = self.make_juno_api_call(_data_chunk, metadata)
                response.extend(_res)
                time.sleep(6)
            except Exception as e:
                logger.error("Unable to process _data_chunk {} {}".format(_data_chunk, e))
        _file_name = file_name.format(job_id=self.job_id)
        file_path = "{}/{}".format(LOCATIONS.get("data"), data_file_name.format(job_id=self.job_id))
        self.write_json_file(file_path, response)
        source_file = "{}/{}".format(LOCATIONS.get("in_progress"), _file_name)
        dst_file = "{}/{}".format(LOCATIONS.get("completed"), _file_name)
        bash("mv {} {}".format(source_file, dst_file))

    @staticmethod
    def make_juno_api_call(_data, metadata):
        all_api_calls_failed = list()
        status = False
        _res = dict()
        for i in range(4):
            logger.info("Calling rest api, for iteration {}".format(i))
            try:
                status, _res = JobExecutor.call_rest_api(_data, metadata)
            except requests.exceptions.ConnectionError as e:
                logger.warning("Unable to connect to API", e)
            except Exception as e:
                logger.exception(e)
            all_api_calls_failed.append(status)
            if status:
                return _res.json().get("body")
        if len(all_api_calls_failed) == 4:
            logger.warning("All API calls are failing, existing the execution. Will resume in 10 minutes")
            return list()
        return list()

    @staticmethod
    def call_rest_api(body_data, config):
        """
        """
        logger.info("Making a call to Juno API {}".format(config.get("api")))
        config["data"] = body_data
        headers = {'Content-type': 'application/json', 'User-Agent': 'WMTPerformance'}

        logger.info("Rest API, post data {}".format(config))
        host_with_url = "{}/{}".format(JUNO_BASE_URL, config.get("api"))
        resp = requests.post(host_with_url, json=config, headers=headers, verify=False)
        if resp.ok:
            logger.info("Successfully got the response from Juno, processed images")
            logger.info("Response body {}".format(resp.json()))
            return True, resp
        else:
            logger.error("Failed get the 200 rc from Juno, failed processed images")
            logger.info("Response body {}".format(resp.json()))
            return False, resp

    @staticmethod
    def chunks(lst, n):
        """Yield successive n-sized chunks from lst."""
        for i in range(0, len(lst), n):
            yield lst[i:i + n]

    def handle_empty_file(self, _image_files, file_name):
        if len(_image_files) == 0:
            logger.info("File is empty {}, so moving to completed folder ".format(file_name))
            dst_file = "{}/{}".format(LOCATIONS.get("completed"), self.files_map.get(file_name))
            # move input/file to completed/file
            logger.info("move {} to {}".format(file_name, dst_file))
            bash("mv {} {}".format(file_name, dst_file))
            return True
        else:
            logger.debug("Image files count is non zero: {}".format(file_name))
            return False

    @staticmethod
    def format_json_name(_file):
        return JSON_OUTPUT_FILE.format(inventory_json=LOCATIONS.get("inventory"),
                                       file_name=_file)

    @staticmethod
    def read_json_file(file_name):
        try:
            with open(file_name) as f:
                return json.load(f)
        except Exception as e:
            logger.warning("unable to read json_file {}".format(file_name))
            return list()

    @staticmethod
    def write_json_file(file_name, data):
        """
        write akamai_urls to inventory/file_name json file.
        Args:
            file_name:
            data:

        Returns:

        """
        try:
            logger.info("Write, api response to inventory file {}".format(file_name))
            logger.debug("Data which is writing is {}".format(data))
            status = JobExecutor.check_file_or_dir_exists(file_name=file_name)
            if status:
                bash("rm -rf {}".format(file_name))
            with open(file_name, 'a') as f:
                json.dump(data, f)
            logger.info("Finished Writing, api response to inventory/{}".format(file_name))
        except Exception as e:
            logger.exception(e)

    @staticmethod
    def get_inventory_data(file_with_location):
        f = open(file_with_location, "r")
        data = f.readlines()
        f.close()
        return data

    @staticmethod
    def get_file_content(file_with_path):
        logger.info("Reading file of {}".format(file_with_path))
        cleaned_content = list()
        f = open(file_with_path, "r")
        _files = list()
        for line in f.readlines():
            token = line.replace('\r', '').replace('\n', '').replace(' ', '')
            logger.debug("UPC/Item before cleaning {}, after {}".format(line, token))
            cleaned_content.append(token)
        return cleaned_content

    @staticmethod
    def check_file_or_dir_exists(file_name=None, dir_name=None):
        """
        Checks weather dir or file exits
        """
        if dir_name:
            command = "[ -d {} ] && echo 'true' || echo 'false'".format(dir_name)
        else:
            command = "[ -f {} ] && echo 'true' || echo 'false'".format(file_name)
        status = bash(command=command)
        # Dir might be soft link, to handle that
        if status.stdout.strip("\n") == "false" and dir_name:
            command = " find -L {} -type l".format(dir_name)
            status = bash(command=command)
            if status.return_code == 0:
                return True
        if status.stdout.strip() == "true":
            return True
        else:
            return False

    @staticmethod
    def task_logger(message):
        logger.info("===========================================================================")
        logger.info(message)
        logger.info("===========================================================================")

    @staticmethod
    def command_logger(message):
        logger.info("****************** {} ******************".format(message))


def do_not_process_twice(name):
    cmd = "ps -ef|grep '{}'|grep python|grep -v grep|wc -l".format(name)
    res = bash(command=cmd)
    if res.return_code == 0:
        if int(res.stdout.strip().rstrip()) == 2:
            logger.warning("Process is already running {}".format(name))
            exit()


def check_arg():
    parser = argparse.ArgumentParser()
    subparsers = parser.add_subparsers(help='commands')

    args_start = subparsers.add_parser('execute', help='Execute given job ')
    args_start.set_defaults(which='execute')

    args_start.add_argument('-j', '--job-id', metavar='\b', required=True, help='Job ID ')
    parser.add_argument('-d', '--debug', action='store_true', help='Verbose mode')

    return parser.parse_args()


if __name__ == '__main__':
    import sys, os, datetime

    args = check_arg()
    if args.debug:
        log_level = logging.DEBUG
    else:
        log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(log_level)
    # log_format = '%(asctime)s  %(lineno)4d %(levelname)10s: %(message)s'
    log_format = '%(asctime)s %(filename)25s %(lineno)3d %(levelname)10s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setLevel(level=logging.DEBUG)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)

    filename = "{}-{}-{}.log".format(args.which, os.path.basename(__file__).replace('.py', ''),
                                     datetime.datetime.now().strftime('%Y-%m-%d'))
    _path = os.path.join(agent_location, "logs")
    bash("mkdir -p {}".format(_path))
    fh = logging.FileHandler(filename=os.path.join(_path, filename))
    fh.setLevel(log_level)
    fh.setFormatter(fmt=formatter)
    logger.addHandler(fh)

    # Additional configuration for error logging
    error_filename = "rsync_error.log".format(datetime.datetime.now().strftime('%Y-%m-%d'))
    error_fh = logging.FileHandler(filename=os.path.join(_path, error_filename))
    error_fh.setLevel(logging.ERROR)
    error_fh.setFormatter(fmt=formatter)
    logger.addHandler(error_fh)

    rsync = JobExecutor(job_id=args.job_id.strip().rstrip())
    if args.which == 'execute':
        rsync()
        rsync.process_data_file()
