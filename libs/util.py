# from template_engine import Engine
#
# template = Engine()
# template("cpu.yml", "/Users/<USER>/Downloads/example.yml")
# print(template.templates)
# print(template.template_variables)
# template.create()
import shutil, os
import ntpath, logging
from libs.shell import bash
import bisect
import pandas as pd
logger = logging.getLogger(__name__)


def bytes_to_mb(bytes, to='m', bsize=1024):
    """convert bytes to megabytes, etc.
               sample code:
                   print('mb= ' + str(bytesto(314575262000000, 'm')))
               sample output:
                   mb= 300002347.946
    """

    a = {'k': 1, 'm': 2, 'g': 3, 't': 4, 'p': 5, 'e': 6}
    r = float(bytes)
    return bytes / (bsize ** a[to])


def working_dir_usage(location):
    # total, used, free
    return shutil.disk_usage(location)


def path_leaf(path):
    head, tail = ntpath.split(path)
    return tail or ntpath.basename(head)


def extract_filenames(files):
    return [path_leaf(_file) for _file in files]


def check_file_or_dir_exists(file_name=None, dir_name=None):
    if dir_name:
        command = "[ -d {} ] && echo 'true' || echo 'false'".format(dir_name)
    else:
        command = "[ -f {} ] && echo 'true' || echo 'false'".format(file_name)
    status = bash(command=command)
    # Dir might be soft link, to handle that
    if status.stdout.strip() == "false" and dir_name:
        command = " find -L {} -type l".format(dir_name)
        status = bash(command=command)
        if status.return_code == 0:
            return True
    if status.stdout.strip() == "true":
        return True
    else:
        return False


def pull_data_for_variables(compiled_data):
    values = []
    try:
        for entry in compiled_data.get("sub_folder"):
            values.append(compiled_data.get(entry))
        return values
    except Exception as e:
        return []


def get_sub_folder_name(compiled_data):
    sub_folder = pull_data_for_variables(compiled_data)
    return '-'.join(sub_folder)


def add_path_to_existing_git_path(compiled_data, base_git_path, **kwargs):
    """
    it adds extra sub folder. It looks for two parameter in the payload
    1. is_sub_folder_required : Boolean
    2. sub_folder: [ ]

    Example:
        path = "/app"
        sub_folder: [ "sort_domain_name","name" ]
        is_sub_folder_required: true
        compiled_data: {"sort_domain_name": "mx", "name":"iro", ....}

        output: /app/mx-iro
    """
    _path = get_sub_folder_name(compiled_data)
    if compiled_data.get("is_sub_folder_required", False) and _path:
        base_git_path = os.path.join(base_git_path, _path)
    return base_git_path


def get_processing_time(start, end_time):
    total_time = end_time - start
    hours = total_time // 3600
    total_time %= 3600
    minutes = total_time // 60
    seconds = total_time % 60
    return "Total time: %d:%02d:%02d" % (hours, minutes, seconds)


from datetime import datetime, timedelta, time, timezone

from datetime import datetime, timedelta, time, timezone
import pytz

# Map of country → (peak_start, peak_end, offpeak_start, offpeak_end) in 24h local time
COUNTRY_HOURS = {
    "mexico": ((10, 0), (20, 0), (23, 0), (6, 0)),
    "canada": ((9, 0), (18, 0), (22, 0), (5, 0)),
    "chile": ((11, 0), (21, 0), (0, 0), (7, 0)),
    "massmart": ((8, 0), (17, 0), (21, 0), (5, 0)),
}

# Country → timezone mapping
COUNTRY_TIMEZONES = {
    "mexico": "America/Mexico_City",
    "canada": "America/Toronto",
    "chile": "America/Santiago",
    "massmart": "Africa/Johannesburg"
}


def get_peak_and_offpeak_range(days_ago: int, country: str):
    country = country.lower()

    if country not in COUNTRY_HOURS or country not in COUNTRY_TIMEZONES:
        raise ValueError(f"Unsupported country: {country}")

    peak_start_hr, peak_end_hr, offpeak_start_hr, offpeak_end_hr = COUNTRY_HOURS[country]
    tz = pytz.timezone(COUNTRY_TIMEZONES[country])

    now = datetime.now(tz)
    target_day = (now - timedelta(days=days_ago)).date()
    prev_day = target_day - timedelta(days=1)

    # Peak time
    peak_start = tz.localize(datetime.combine(target_day, time(*peak_start_hr)))
    peak_end = tz.localize(datetime.combine(target_day, time(*peak_end_hr)))

    # Off-peak time (spans previous day into current day)
    offpeak_start = tz.localize(datetime.combine(prev_day, time(*offpeak_start_hr)))
    offpeak_end = tz.localize(datetime.combine(target_day, time(*offpeak_end_hr)))

    return {
        "peak": (int(peak_start.timestamp()), int(peak_end.timestamp())),
        "offpeak": (int(offpeak_start.timestamp()), int(offpeak_end.timestamp()))
    }


from bisect import bisect_left, bisect_right


def extract_series_within_range(promql_response, start_time, end_time):
    """
    Extracts time series within a given time range from PromQL range response.

    :param promql_response: The raw parsed JSON from Prometheus range query
    :param start_time: Epoch start time (int)
    :param end_time: Epoch end time (int)
    :return: List of dicts with 'metric' and 'values' in the time range
    """

    results = []

    for series in promql_response:
        timestamps = [int(t[0]) for t in series['values']]
        start_idx = bisect_left(timestamps, start_time)
        end_idx = bisect_right(timestamps, end_time)
        sliced_values = series['values'][start_idx:end_idx]

        results.append({
            "metric": series["metric"],
            "values": sliced_values
        })

    return results


def extract_series_within_range_V2(series_data, start_time, end_time, filter_fn=None, **filter_kwargs):
    """
    Extracts series data between start_time and end_time.

    If filter_fn is provided, applies filter only after slicing values.
    """

    results = []
    for series in series_data:
        metric = series.get('metric', {})
        timestamps = [int(t[0]) for t in series.get('values', [])]
        start_idx = bisect_left(timestamps, start_time)
        end_idx = bisect_right(timestamps, end_time)
        sliced_values = series['values'][start_idx:end_idx]

        results.append({
            "metric": metric,
            "values": sliced_values
        })

    # 🔥 Now filter results if a filter_fn is given
    if filter_fn and filter_kwargs:
        results = filter_fn(results, **filter_kwargs)

    return results


def check_range_in_bounds(peak_offpeak: dict, full_range: tuple) -> bool:
    """
    Returns True only if both peak and off-peak ranges are fully within the given range.

    :param peak_offpeak: dict with 'peak' and 'offpeak' as (start_epoch, end_epoch)
    :param full_range: tuple (start_epoch, end_epoch)
    :return: True if both ranges are within bounds, False otherwise
    """
    full_start, full_end = full_range

    def is_within(sub_range):
        sub_start, sub_end = sub_range
        logger.info(f"full_start {full_start}, full_end {full_end} ")
        logger.info(f"sub_start {sub_start}, sub_end {sub_end} ")
        return full_start <= sub_start and sub_end <= full_end

    # Check if both peak and off-peak ranges are within the full range
    logger.info("================= Start ===========================")
    logger.info("---- Peak ----")
    _peak = is_within(peak_offpeak["peak"])
    logger.info("---- off Peak ----")
    _offpeak = is_within(peak_offpeak["offpeak"])
    logger.info(f"_peak {_peak} offpeak {_offpeak}")
    logger.info("================= End ===========================")

    # return is_within(peak_offpeak["peak"]) and is_within(peak_offpeak["offpeak"])
    return _peak and _offpeak

def log_and_drop_na(df: pd.DataFrame, context: str = "Data Cleanup") -> pd.DataFrame:
    """
    Logs how many rows contain NaN values, shows sample rows, and drops them.

    Args:
        df (pd.DataFrame): Input DataFrame
        context (str): Context name to prefix in logs

    Returns:
        pd.DataFrame: Cleaned DataFrame with all NaN rows removed
    """
    nan_rows = df[df.isna().any(axis=1)]
    num_nans = len(nan_rows)

    logger.info(f"[{context}] 🚨 Dropping {num_nans} rows with NaN values.")

    if num_nans >= 10:
        logger.info(f"[{context}] 🧪 Sample NaN rows:\n" + nan_rows.sample(10).to_string(index=False))
    elif num_nans > 0:
        logger.info(f"[{context}] 🧪 Sample NaN rows:\n" + nan_rows.to_string(index=False))

    return df.dropna()

if __name__ == "__main__":
    import logging

    log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    log_format = '%(asctime)s %(filename)18s %(funcName)25s %(lineno)3d %(levelname)10s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(log_level)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)
    print(get_peak_and_offpeak_range(2))
