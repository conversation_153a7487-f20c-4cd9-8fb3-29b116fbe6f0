import requests
from settings import MX_ITEM_SERVICE


class ItemService:
    def __init__(self):
        pass

    def get_item_details(self, banner: str, upcs: list):
        proxies = {
            "http": "http://sysproxy.wal-mart.com:8080",
            "https": "http://sysproxy.wal-mart.com:8080",
        }
        url = f"{MX_ITEM_SERVICE['url']}/{MX_ITEM_SERVICE['fullItems']}"
        payload = MX_ITEM_SERVICE['header']

        if "ea" in banner:
            payload.update({"wm_vertical": "ea"})
        elif "od" in banner:
            payload.update({"wm_vertical": "od"})
        else:
            payload.update({"wm_vertical": "ea"})

        pay_load = {"upcs": upcs}
        response = requests.post(url, json=pay_load, headers=payload, verify=False,
                                 proxies=proxies)
        response_data = list()
        if response.ok:
            data = response.json()
            for item_data in data.get("data", {}).get("items"):
                active = False
                if "active" == item_data.get("status"):
                    active = True

                response_data.append({"status": active, "itemId": item_data.get("itemId"),
                                      "upc": item_data.get("correlationIds").get("upc")})
        failed_upcs = list()
        for upc in upcs:
            for processed_data in response_data:
                if upc == processed_data["upc"]:
                    break
            else:
                failed_upcs.append({"status": False, "itemId": None,
                                    "upc": upc})
        response_data.extend(failed_upcs)
        return response_data


if __name__ == "__main__":
    item = ItemService()
    item.get_item_details("mx-ea", ["00840268907235", "00064578933300"])
