import time

from libs.prometheus_client import Prometheus
from libs.util import check_file_or_dir_exists
from libs.heml import W<PERSON><PERSON>elm
from collections import OrderedDict, defaultdict
import logging
import json, re
from settings import CONFIG_DIR
from libs.prometheus_client import Prometheus
from common.teap_handler.teap_data_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from concurrent.futures import ThreadPoolExecutor
from common.alert.alertsPostDataBuilder import AlertsPostDataBuilder

prometheus_handler = Prometheus()
logger = logging.getLogger(__name__)


def get_pods_count(namespace, app_id, cluster_id):
    results = prometheus_handler.get_pods_state(namespace=namespace,
                                                app_id=app_id,
                                                cluster_id=cluster_id)
    for metric in results.get("data").get("result"):
        _metric = metric.get("metric")
        _count = 0
        if _metric.get("phase") == "Running":
            _count = int(metric.get("values")[-1][1])
            return _count


def provide_wcnp_metrics(namespace, app_id=None, strict=False):
    results = OrderedDict()
    app = None
    data = None
    try:
        # Todo: Refactor this code to use is_app_exists
        pro = Prometheus()
        data = pro.get_namespace_details(namespace)
        logger.info(f"Namespace details are for given name is {data}")
        for app, app_data in data.items():
            if strict and app_id:
                # skipp processing for app, to ignore dev,stg ...
                skipp_processing = False
                app_id = app_id.strip().rstrip()
                regx_res = re.search(r"{app}(-prod-primary|-prod|-primary|-prod-*)".format(app=app_id), app)
                if not regx_res:
                    logger.info(f"Not matching regx for -prod-primary|-prod|-primary|-prod-*, app {app}")
                    skipp_processing = True
                # sometimes app teams never have proper app_id, generally should tail with prod-primary|-prod|-primary
                # so, app team might not have standards, so app and app_id might me same
                if app == app_id:
                    skipp_processing = False

                if skipp_processing:
                    continue
            logger.info(f"Matching regx for -prod-primary|-prod|-primary|-prod-*, app {app}")
            results[app] = OrderedDict()
            results[app]["clusters"] = list()
            _prev_cluster_info = None
            for cluster in app_data.get('clusters'):
                try:
                    logger.info(f"Detailed processing for  namespace: {namespace} cluster_id {cluster}")
                    _cluster = OrderedDict()
                    _cluster["pods"] = get_pods_count(namespace, app, cluster)
                    _cluster["mm_name"] = cluster
                    _cluster["namespace"] = namespace
                    # wmt = WMTwcnp(namespace, cluster)
                    wmt = WMTHelm(namespace, cluster, app)
                    cluster_data = wmt()
                    if len(cluster_data) == 0:
                        msg = f"Unable to get Helm data for namespace {namespace} app:{app} app_data:{app_data},skipping"
                        logger.error(msg)
                        results[app]["clusters"].append(_cluster)
                        results[app]["error"] = msg
                        continue

                    _cluster_info = cluster_data[-1]
                    logger.info(
                        f"Detailed processing for  namespace: {namespace} cluster_id {cluster} details are {cluster_data}")
                    _cluster.update(**_cluster_info)
                    if not _prev_cluster_info:
                        _prev_cluster_info = _cluster_info
                    else:
                        if _prev_cluster_info == _cluster_info:
                            logger.info("Equal")
                        else:
                            logger.info("not equal")
                    results[app]["clusters"].append(_cluster)
                except Exception as e:
                    logger.exception("Exception occurred while processing helm details")
                    logger.exception(e)
        if not results:
            if not data:
                logger.warning(
                    f"Not found any data in Production monitoring, should exits. Namespace {namespace} app {app_id}")
            logger.warning(f"Failed to get the details from helm for (namespace/app) ({namespace}/{app_id})")
            results[app_id] = {"namespace": namespace, "status": False}

        return results
    except Exception as e:
        logger.exception(f"Exception occurred while processing helm details (namespace/app) ({namespace}/{app_id})")
        logger.exception(e)
        results[app_id] = {"namespace": namespace, "status": False}
    return results


def get_hpa_difference(data):
    try:
        hpa = data.get('scaling').get('horizontal').get('scaling')
        if not hpa:
            return -1
        min = hpa.get("min")
        max = hpa.get("max")

        return float(max) / float(min)
    except Exception as e:
        logger.info(e)
        return -1


def is_app_exists(namespace, app_id):
    pro = Prometheus()
    data = pro.get_namespace_details(namespace)
    logger.info(f"Namespace details are for given name is {data}")
    for app, app_data in data.items():
        # skipp processing for app, to ignore dev,stg ...
        skipp_processing = False
        app_id = app_id.strip().rstrip()
        regx_res = re.search(r"{app}(-prod-primary|-prod|-primary|-prod-*)".format(app=app_id), app)

        # sometimes app teams never have proper app_id, generally should tail with prod-primary|-prod|-primary
        # so, app team might not have standards, so app and app_id might me same
        if app == app_id:
            skipp_processing = False

        if skipp_processing:
            return False
    return True


def is_vertical_scaling_enabled(data):
    try:
        return data.get('scaling').get('vertical').get('is_enabled')
    except Exception as e:
        logger.info(e)
        return -1


def is_canary_enabled(data):
    try:
        return data.get('canary').get('enabled')
    except Exception as e:
        logger.info(e)
        return -1


def current_pods_are_less_than_min_pods(data):
    try:
        hpa = data.get('scaling').get('horizontal').get('scaling')
        if not hpa:
            return -1
        max = hpa.get("max")
        pods = data.get('pods')
        return float(pods) / float(max)
    except Exception as e:
        logger.info(e)
        return -1


def current_pods_are_less_than_max_pods(data):
    try:
        hpa = data.get('scaling').get('horizontal').get('scaling')
        if not hpa:
            return -1
        min = hpa.get("min")
        pods = data.get('pods')
        return float(pods) / float(min)
    except Exception as e:
        logger.info(e)
        return -1


def _analyze(data):
    results = list()
    for _app in data:
        for app_id, cluster in _app.items():
            try:
                for cluster_info in cluster.get('clusters'):
                    res = dict()
                    hpa = get_hpa_difference(cluster_info)
                    vertical = is_vertical_scaling_enabled(cluster_info)
                    canary = is_canary_enabled(cluster_info)
                    _current_pods_min_hpa_weight_ratio = current_pods_are_less_than_min_pods(cluster_info)
                    _current_pods_are_less_than_max_pods = current_pods_are_less_than_max_pods(cluster_info)
                    res["hpa_ratio"] = hpa
                    res["is_canary_enabled"] = canary
                    res["is_vertical_scaling_enabled"] = vertical
                    res["current_pods"] = cluster_info.get('pods')
                    res["current_pods_min_hpa_weight_ratio"] = _current_pods_min_hpa_weight_ratio
                    res["current_pods_max_hpa_weight_ratio"] = _current_pods_are_less_than_max_pods
                    res["cluster_id"] = cluster_info.get('mm_name')
                    res["namespace"] = cluster_info.get('namespace')
                    res["app"] = app_id
                    results.append(res)
            except Exception as e:
                logger.exception(e)
    return results


def write_results_to_json(data, tier):
    results_file = f"{CONFIG_DIR}/wcnp_{tier}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=4)


def read_results_to_json(tier):
    results_file = f"{CONFIG_DIR}/wcnp_{tier}.json"
    status = check_file_or_dir_exists(file_name=results_file)
    if not status:
        return False, dict()
    with open(results_file) as user_file:
        parsed_json = json.load(user_file)
    return True, parsed_json


def modify_write_data_and_save(data, tier):
    final_data = dict()
    final_data["timestamp"] = time.time()
    final_data["data"] = data
    write_results_to_json(final_data, tier)


def eligible_to_get_new_wcnp_changes(tier):
    status, data = read_results_to_json(tier)
    if not status:
        return True
    last_processed_data_time = data["timestamp"]
    diff = time.time() - last_processed_data_time
    # pull required every one hour
    if diff / (60 * 60) >= 1:
        return True
    return False


def get_wcnp_hpa_data(tier, ignore_disabled_alerts=True):
    """
    Gets WCNP HPA data
    """
    data = get_wcnp_helm_data_from_config_store(tier, ignore_disabled_alerts)
    _res = _analyze(data)
    return _res


def get_wcnp_helm_data_from_config_store(tier, ignore_disabled_alerts=True):
    if eligible_to_get_new_wcnp_changes(tier):
        res = get_inventory_and_pull_wcnp_helm_data(tier, ignore_disabled_alerts=ignore_disabled_alerts)
        modify_write_data_and_save(res, tier)
    status, _data = read_results_to_json(tier)
    return _data.get("data")


def get_wcnp_not_followed_standards_apps(tier, hpa_threshold_rate=2.5, validate_slas=False):
    results = get_wcnp_hpa_data(tier, ignore_disabled_alerts=True)

    if not validate_slas:
        return results

    breached_apps = list()
    not_breached_apps = list()
    for _data in results:
        if len(_data) == 0:
            continue

        _sla_breached = False
        if _data.get("hpa_ratio") < hpa_threshold_rate:
            _sla_breached = True
        if not _data.get("is_canary_enabled"):
            _sla_breached = True
        if _data.get("is_vertical_scaling_enabled"):
            _sla_breached = True

        if _sla_breached:
            breached_apps.append(_data)
        else:
            not_breached_apps.append(_data)

    return {"breached_apps": breached_apps, "not_breached": not_breached_apps}


def hpa_breached(tier, hpa_threshold_rate=2.5):
    results = get_wcnp_hpa_data(tier, ignore_disabled_alerts=True)

    breached_apps = list()
    not_breached_apps = list()
    for _data in results:
        if len(_data) == 0:
            continue

        _sla_breached = False
        if _data.get("hpa_ratio") < hpa_threshold_rate:
            _sla_breached = True

        if _sla_breached:
            breached_apps.append(_data)
        else:
            not_breached_apps.append(_data)

    return {"breached_apps": breached_apps, "not_breached": not_breached_apps}


def prob_urls(tier):
    data = get_wcnp_helm_data_from_config_store(tier)
    _res = defaultdict(lambda: defaultdict(dict))
    for _data in data:
        for _app, clusters_info in _data.items():
            if "clusters" not in clusters_info:
                continue
            for cluster in clusters_info.get("clusters"):
                try:
                    live_prob = cluster.get("prob", dict()).get("liveness_probs", dict())
                    ready_prob = cluster.get("prob", dict()).get("ready_probs", dict())
                    try:
                        lbs = cluster.get("lbs").get("gslb")
                    except Exception as e:
                        logger.exception(e)
                    _res[cluster["namespace"]][_app]["liveness_probs"] = process_probe(live_prob, lbs)
                    _res[cluster["namespace"]][_app]["readiness_probs"] = process_probe(ready_prob, lbs)

                except Exception as e:
                    logger.exception(e)
                break
    return _res


def process_probe(data, gslb):
    _uri = data.get("uri")
    probe_interval = data.get("probe_interval")
    wait = data.get("wait")
    time_out = data.get("time_out")
    return {"uri": _uri, "probe_interval": probe_interval,
            "wait": wait, "time_out": time_out, "health_check": f"http://{gslb}{_uri}"}


def canary_breached(tier):
    results = get_wcnp_hpa_data(tier, ignore_disabled_alerts=True)

    breached_apps = list()
    not_breached_apps = list()
    for _data in results:
        if len(_data) == 0:
            continue

        _sla_breached = False
        if not _data.get("is_canary_enabled"):
            _sla_breached = True

        if _sla_breached:
            breached_apps.append(_data)
        else:
            not_breached_apps.append(_data)

    return {"breached_apps": breached_apps, "not_breached": not_breached_apps}


def vertical_scaling_breached(tier):
    results = get_wcnp_hpa_data(tier, ignore_disabled_alerts=True)

    breached_apps = list()
    not_breached_apps = list()
    for _data in results:
        if len(_data) == 0:
            continue

        _sla_breached = False
        if _data.get("is_vertical_scaling_enabled"):
            _sla_breached = True

        if _sla_breached:
            breached_apps.append(_data)
        else:
            not_breached_apps.append(_data)

    return {"breached_apps": breached_apps, "not_breached": not_breached_apps}


def club_all_clusters_into_one_namespace_app(data):
    res = defaultdict(lambda: defaultdict(lambda: 0))
    for _data in data:
        key = f"{_data.get('app')}-{_data.get('namespace')}"
        res[key][_data.get("cluster_id")] = _data
    return res


def get_inventory_and_pull_wcnp_helm_data(tier, ignore_disabled_alerts=True):
    template = "wcnp_alerts.yaml"
    is_sre_sla = True
    persist_data = False
    sre_override_xmatters_and_slack = True
    alert_data = AlertsPostDataBuilder(template, is_sre_sla=is_sre_sla, tier=tier,
                                       custom_inventory_file=None)
    data = alert_data.build_post_body(is_sre_sla=is_sre_sla, persist_data=persist_data,
                                      sre_override_xmatters_and_slack=sre_override_xmatters_and_slack)
    # _data = get_wcnp_hpa_data(data.get("apps_meta_data"))
    if ignore_disabled_alerts:
        data = [_data for _data in data.get("apps_meta_data") if _data.get("status", True)]
    wcnp_details = pull_wcnp_details(data)

    return wcnp_details


def pull_wcnp_details(data, pall_calls_size=20):
    wcnp_details = list()

    # for app in data:
    #     # if app.get("namespace") == 'mx-item-services':
    #     #     logger.info("check")
    #     wcnp_details.append(provide_wcnp_metrics(app.get("namespace"), app.get("app_name"), strict=True))

    data_per_threads = [data[i:i + pall_calls_size] for i in range(0, len(data), pall_calls_size)]
    for i, slice_data in enumerate(data_per_threads):
        logger.info("Processing slice {}/{}".format(i, len(data_per_threads)))
        futures = list()
        pool = ThreadPoolExecutor(len(slice_data))
        for app in slice_data:
            futures.append(pool.submit(provide_wcnp_metrics, app.get("namespace"), app.get("app_name"), strict=True))
        for future in futures:
            try:
                result = future.result()
                for k, v in result.items():
                    v["status"] = v.get("status", True)
                wcnp_details.append(result)
            except Exception as e:
                logger.exception(e)
    return wcnp_details


if __name__ == "__main__":
    # provide_wcnp_metrics(namespace="mx-glass")
    '''
    from libs.teap import Teap, SUPPORTED_MARKETS, SUPPORTED_TIERS
    from concurrent.futures import ThreadPoolExecutor

    logger = logging.getLogger(__name__)
    teap = Teap()
    apps = teap.get_apps_by_platform_and_tier_and_market(platform="wcnp", tier="zero", market="MX")
    # for app in apps:
    #     data = provide_wcnp_metrics(app['wcnp'].get('namespaceOrAssemblyOrDB'),app['wcnp'].get('app'))
    futures = []
    results = list()
    for app in apps:
        try:
            logger.info(f"Processing {app['wcnp'].get('namespaceOrAssemblyOrDB')}")
            pool = ThreadPoolExecutor(25)
            futures.append(pool.submit(provide_wcnp_metrics, app["wcnp"].get('namespaceOrAssemblyOrDB')))
        except Exception as e:
            logger.exception(e)
    for future in futures:
        try:
            _results = future.result()
            results.append(_results)
        except Exception as e:
            logger.exception(e)

    print(results)
    '''
    # provide_wcnp_metrics("mxsearch","rerank-service-od-primary")
    # data = provide_wcnp_metrics("mx-glass", app_id="mexico-ea-journey-prod-primary")
    # from common.teap_handler.teap_data_handler import TeapDataHandler
    #
    # teap = TeapDataHandler().teap_data
    # data = teap.get_apps_by_platform_and_tier_and_market(platform="wcnp", tier="zero", market="MX")
    # # data = analyze("mx-glass", app_id="mexico-ea-journey-prod-primary")
    # # print(data)
    # res = list()
    # for app in data:
    #     wcnp = app.get('wcnp')
    #     _data = analyze(wcnp.get('namespaceOrAssemblyOrDB'), app_id=wcnp.get('app'))
    #     res.append(_data)
    #     print()
    # print(res)
    # teap = TeapDataHandler().teap_data
    # data = teap.get_apps_by_platform_and_tier_and_market(platform="wcnp", tier="zero", market="MX")
    from logging import handlers
    import os

    log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    log_format = '%(asctime)s %(filename)28s %(funcName)33s %(lineno)5d %(levelname)10s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(log_level)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)

    file_handler = handlers.RotatingFileHandler(
        filename=os.path.join(os.path.basename(__file__).replace('py', 'log')),
        maxBytes=1048576,
        backupCount=5)
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(fmt=formatter)
    logger.addHandler(file_handler)
    # from common.alert.alertsPostDataBuilder import AlertsPostDataBuilder
    # from template_engine.template_handler import get_latest_templates
    # from common.alert.alert_modifier import AlertModifier
    #
    # template = "wcnp_alerts.yaml"
    # is_sre_sla = True
    # tier = "zero"
    # custom_inventory_file = None
    # persist_data = False
    # sre_override_xmatters_and_slack = True
    # alert_data = AlertsPostDataBuilder(template, is_sre_sla=is_sre_sla, tier=tier,
    #                                    custom_inventory_file=None)
    # data = alert_data.build_post_body(is_sre_sla=is_sre_sla, persist_data=persist_data,
    #                                   sre_override_xmatters_and_slack=sre_override_xmatters_and_slack)
    # _data = get_wcnp_hpa_data(data.get("apps_meta_data"))
    # _data = provide_wcnp_metrics("ca-pricing-engine", 'ca-pricing-engine-services', strict=True)
    # _data = get_wcnp_not_followed_standards_apps("one", hpa_threshold_rate=2.5, validate_slas=True)
    import pprint

    #
    # pprint.pprint(_data)
    # provide_wcnp_metrics("seller-settlement","seller-settlement",strict = True)
    # get_wcnp_hpa_data("zero")
    # get_wcnp_hpa_data("one")
    prob_urls("zero")
