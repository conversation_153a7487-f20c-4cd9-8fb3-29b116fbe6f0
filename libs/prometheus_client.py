__author__ = '<PERSON><PERSON> cheepati'
__version__ = 1.0

import requests, urllib3
import logging, os, time
from logging import handlers
from settings import PROMETHEUS_SERVER, PROMETHEUS_NON_PROD_SERVER
from collections import defaultdict, OrderedDict
from libs.date_time_helper import WMTDateTime
from datetime import datetime, timedelta
from libs.util import get_peak_and_offpeak_range, check_range_in_bounds,log_and_drop_na
from concurrent.futures import ThreadPoolExecutor
from libs.analysis import analyze_utilization_insights, analyze_peak_and_offpeak_per_cluster, \
    clean_and_merge_metric_dataframes_generic,calculate_final_recommended_pods,calculate_min_pods_peak_off_peak
from libs.util import extract_series_within_range, extract_series_within_range_V2
from concurrent.futures import as_completed
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any, Union
from ml_models.main_pipeline_v2 import prepare_training_inputs_from_prometheus_data, orchestrate_by_deployment
from settings import TEMPLATE_OUTPUT_DIR

logger = logging.getLogger(__name__)

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

IGNORE_APPS = ["wmt-vault-secrets-manager"]
IGNORE_PATTERN = ["preprod"]


class Prometheus(object):
    def __init__(self, **kwargs):
        self.lb = kwargs.get("lb", PROMETHEUS_SERVER)

    def set_environment(self, prod=True):
        """
        Set the Prometheus server environment

        Args:
            prod: If True, use production server, otherwise use non-prod
        """
        if prod:
            self.lb = PROMETHEUS_SERVER  # Use the default production server
        else:
            self.lb = PROMETHEUS_NON_PROD_SERVER

    def get_query_range_data(self, query, start_time, end_time, steps):
        """
        For a given query range, gets the data from Prometheus
        Args:
            query: range query to pull the data
            start_time: start epoch time
            end_end: end epoch time
            steps:

        Returns:

        """
        try:
            response = requests.get(f'{self.lb}/api/v1/query_range', verify=False,
                                    params={'query': query, 'start': start_time, 'end': end_time, 'step': steps},
                                    )
            data = response.json()
            if response.ok and response.status_code == 200:
                if data.get("status") == "success":
                    return data
            elif response.ok and response.status_code == 400:
                return dict()
            logger.exception(f"Exception occurred while processing PromQL {query}")
            return dict()
        except Exception as e:
            logger.exception("Exception occurred while processing query range data")
        return None

    def get_query_range_data_v2(self, query, start_time, end_time, steps, enrich_metrics_fn=None, enrich_config=None):
        """
        For a given query range, gets the data from Prometheus
        Args:
            query: range query to pull the data
            start_time (int, optional): Start time epoch (UTC seconds)
            end_time (int, optional): End time epoch (UTC seconds)
            steps (int, optional): Step size in seconds between samples (default 60s)
            enrich_metrics_fn (callable, optional): Callback function to enrich results (e.g., enrich_metrics_with_mapping).
            enrich_config (list of dict, optional): enrich config



        Returns:

        """
        try:
            response = requests.get(f'{self.lb}/api/v1/query_range', verify=False,
                                    params={'query': query, 'start': start_time, 'end': end_time, 'step': steps},
                                    )
            data = response.json()
            if response.ok and response.status_code == 200:
                if data.get("status") == "success":
                    result_series = data['data']['result']
                    # 🔥 Post-process each metric if needed
                    if enrich_metrics_fn and enrich_metrics_fn:
                        logger.info(f"Applying post-processing enrichment with {enrich_metrics_fn.__name__}")
                        results = enrich_metrics_fn(
                            result_series,
                            mapping_list=enrich_config.get("mapping_list", ()),
                            match_keys=enrich_config.get("match_keys", ()),
                            infer_mapping=enrich_config.get("infer_mapping", {}),
                            target_mapping=enrich_config.get("target_mapping", ()),
                            add_fields=enrich_config.get("add_fields", ()),
                            extra_mapping=enrich_config.get("extra_mapping", {}),
                            compress_categories=enrich_config.get("compress_categories", False),
                            category_fields=enrich_config.get("category_fields", []),
                            return_dataframe=enrich_config.get("return_dataframe", False),
                        )
                        return results
                    return result_series
            elif response.ok and response.status_code == 400:
                return dict()
            logger.exception(f"Exception occurred while processing PromQL {query}")
            return dict()
        except ConnectionError as e:
            logger.exception("Unable to connect to Prometheus server, please check connectivity")
        except Exception as e:
            logger.exception("Exception occurred while processing query range data")
        return None

    def get_query_range_data_with_meta(self, query, start_time, end_time, steps):
        """
        For a given query range, gets the data from Prometheus
        Args:
            query: range query to pull the data
            start_time: start epoch time
            end_end: end epoch time
            steps:

        Returns:

        """
        try:
            url = f'{self.lb}/api/v1/query_range'
            response = requests.get(url, verify=False,
                                    params={'query': query, 'start': start_time, 'end': end_time, 'step': steps})
            data = response.json()
            if response.ok and response.status_code == 200:
                if data.get("status") == "success":
                    # status, tooManyDataPoints,results
                    return True, False, data
            elif response.ok and response.status_code == 400:
                # status, tooManyDataPoints,results
                return False, True, dict()
            return False, False, dict()
        except Exception as e:
            logger.exception("Exception occurred while processing query range data")
        return False, False, dict()

    def make_series_call(self, query, start_time=None, end_time=None):
        """
        returns the list of time series that match a certain label
        Args:
            query:
            start_time:
            end_time:

        Returns:

        """
        try:
            if not start_time and end_time:
                response = requests.get(f'{self.lb}/api/v1/series', verify=False, params={'match[]': query})
            else:
                response = requests.get(f'{self.lb}/api/v1/series', verify=False,
                                        params={'match[]': query, 'start': start_time, 'end': end_time})
            return response.json()
        except Exception as e:
            logger.exception(f"Error occurred while accessing the query {query}, exception is {e}")
            return None

    def get_tps_by_all_rc_codes(self, namespace, app_id, steps=60, cluster_id=None, total=False, start_time=None,
                                end_time=None):
        """
        For given namespace, app_id, gets the TPS by response codes
        It gives you thee options
            1. For all regions by default
            2. By region
            3. by total
        Args:
            namespace:
            app_id:
            cluster_id:
            steps:
            total:
            start_time:
            end_time:
        Returns:

        """
        if not end_time or not start_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(3)
        logger.info(f"Pulling app response codes from {WMTDateTime.epoch_to_pst_datetime(start_time)} to "
                    f"{WMTDateTime.epoch_to_pst_datetime(end_time)}")

        if cluster_id:
            query = 'round(sum(envoy_cluster_upstream_rq:sum_rate2m{{cluster_id=~"{cluster_id}",' \
                    'cluster_name=~"outbound.*{app_id}.{namespace}.svc.cluster.local", ' \
                    'response_code_class=~".*"}}) by (response_code_class,app), 0.001)' \
                    ''.format_map({"app_id": app_id, "namespace": namespace, "cluster_id": cluster_id})
        elif total:
            query = 'round(sum(envoy_cluster_upstream_rq:sum_rate2m{{' \
                    'cluster_name=~"outbound.*{app_id}.{namespace}.svc.cluster.local", ' \
                    'response_code_class=~".*"}}) by (response_code_class,app), 0.001)' \
                    ''.format_map({"app_id": app_id, "namespace": namespace})
        else:
            query = 'round(sum(envoy_cluster_upstream_rq:sum_rate2m{{' \
                    'cluster_name=~"outbound.*{app_id}.{namespace}.svc.cluster.local", ' \
                    'response_code_class=~".*",cluster_id=~".*"}}) by (response_code_class,app,cluster_id), 0.001)' \
                    ''.format_map({"app_id": app_id, "namespace": namespace})

        res = self.get_query_range_data(query=query, start_time=start_time, end_time=end_time, steps=steps)
        return res

    def get_latencies(self, namespace, app_id, steps=60, cluster_id=None, total=False, percentile=0.95, start_time=None,
                      end_time=None):
        """
        For given namespace, app_id, gets the the latency
        It gives you thee options
            1. For all regions by default
            2. By region
            3. by total
        Args:
            namespace:
            app_id:
            cluster_id:
            steps:
            total:
            percentile:
            start_time:
            end_time:
        Returns:

        """
        if not end_time or not start_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(3)

        logger.info(f"Pulling app {namespace}/{app_id} latencies ({percentile}) "
                    f"from {WMTDateTime.epoch_to_pst_datetime(start_time)} "
                    f"to {WMTDateTime.epoch_to_pst_datetime(end_time)}")

        if cluster_id:
            query = 'histogram_quantile({percentile}, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{{' \
                    'cluster_name=~"outbound.*{app_id}.{namespace}.svc.cluster.local",cluster_id=~"{cluster_id}"}}) ' \
                    'by (app, namespace, le))' \
                    ''.format_map({"app_id": app_id, "namespace": namespace, 'percentile': percentile,
                                   'cluster_id': cluster_id})

        elif total:
            query = 'histogram_quantile({percentile}, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{{' \
                    'cluster_name=~"outbound.*{app_id}.{namespace}.svc.cluster.local"}}) ' \
                    'by (app, namespace, le))' \
                    ''.format_map({"app_id": app_id, "namespace": namespace, 'percentile': percentile})
        else:
            query = 'histogram_quantile({percentile}, sum(envoy_cluster_upstream_rq_time_bucket:sum_rate2m{{' \
                    'cluster_name=~"outbound.*{app_id}.{namespace}.svc.cluster.local", cluster_id=~".*"}}) ' \
                    'by (app, namespace, le, cluster_id))' \
                    ''.format_map({"app_id": app_id, "namespace": namespace, 'percentile': percentile})

        res = self.get_query_range_data(query=query, start_time=start_time, end_time=end_time, steps=steps)
        return res

    def process_gslb_ingress_traffic(self, lb):
        query = 'sum(dnsd_queryDetailed{{origin=~"{lb}",' \
                'client_data_center=~"(azure-canada-central|azure-canada-east|azure-china-east-2|azure-china-north-2|' \
                'azure-china-north-3|azure-east-japan|azure-east-us-2|azure-japan|azure-south-central-us|azure-uk|' \
                'azure-uk-south|azure-uk-west|azure-west-japan|azure-west-us|azure-west-us-2|cdc|dal|dc15|dc5|dfw|' \
                'edc|gcp-central-us-1|gcp-east-us-4|gcp-south-us-1|gcp-west-us-1|mcc-wm|mcw-wm|ndc|unknown)"}}) ' \
                'by (origin,client_data_center)'.format_map({"lb": lb})
        current_epoch, ago = Prometheus.get_last_n_minutes_epoch_times(10)
        res = self.get_query_range_data(query=query, start_time=ago, end_time=current_epoch, steps=60)
        return lb, res

    def process_gslb_egress_traffic(self, lb):
        query = 'sum(dnsd_queryDetailed{{origin=~"{lb}",' \
                'client_data_center=~"(azure-canada-central|azure-canada-east|azure-china-east-2|azure-china-north-2' \
                '|azure-china-north-3|azure-east-japan|azure-east-us-2|azure-japan|azure-south-central-us|azure-uk' \
                '|azure-uk-south|azure-uk-west|azure-west-japan|azure-west-us|azure-west-us-2|cdc|dal|dc15|dc5|dfw|' \
                'edc|gcp-central-us-1|gcp-east-us-4|gcp-south-us-1|gcp-west-us-1|mcc-wm|mcw-wm|ndc|unknown)"}}) ' \
                'by (target_data_center)'.format_map({"lb": lb})
        current_epoch, ago = Prometheus.get_last_n_minutes_epoch_times(10)
        res = self.get_query_range_data(query=query, start_time=ago, end_time=current_epoch, steps=60)
        return lb, res

    def get_all_assemblies_platforms_env(self, org):
        query = 'avg(n_cpus{{oot="{org}"}}) by (ooa,oop,ooe)'.format_map({"org": org})
        current_epoch, ago = Prometheus.get_last_n_minutes_epoch_times(1)
        data = self.get_query_range_data(query, ago, current_epoch, 15)
        if not data:
            return None
        if data.get("status") == "success":
            results = dict()
            for element in data.get("data").get("result"):
                ooa = element["metric"]["ooa"]
                oop = element["metric"]["oop"]
                if element.get("metric"):
                    if ooa in results:
                        results[ooa][oop] = [element["metric"]["ooe"]]
                    else:
                        results[ooa] = dict()
                        results[ooa][oop] = [element["metric"]["ooe"]]
            return results

    def get_all_app_names_and_cluster_ids(self, namespace, app_id=None):
        """
        Gets all app names and respective cluster_id for a given name space
        Args:
            namespace:
            app_id:

        Returns:

        """
        if app_id:
            query = 'kube_service_labels{{namespace="{namespace}", service="{app_id}"}}' \
                    ''.format_map({"namespace": namespace, "app_id": app_id})
        else:
            query = 'kube_pod_labels{{mms_source="wcnp", namespace="{namespace}",' \
                    'label_app!="helm",label_app!="highlander",label_app!="prometheus",' \
                    'label_app!~".*-inta-.*|.*-intb-.*|.*canary.*|.*default-tester|' \
                    '.*-inta|.*-intb|.*-nsf-.*"}}'.format_map({"namespace": namespace})
        current_epoch, ago = Prometheus.get_last_n_minutes_epoch_times(3)
        data = self.make_series_call(query, ago, current_epoch)
        if not data:
            return None
        if data.get("status") == "success":
            results = defaultdict(set)
            for element in data.get("data"):
                if element.get("label_app"):
                    results[element["label_app"]].add(element.get('cluster_id'))
            return results

    def get_wcnp_all_cluster_ids(self, namespace, app_id) -> list:
        """
        For given details return clusters as set of culter names
        """
        results = self.get_pods_state(namespace=namespace, app_id=app_id)
        response = set()
        for metric in results.get("data").get("result"):
            _metric = metric.get("metric")
            response.add(_metric.get("cluster_id"))
        return list(response)

    def get_wcnp_running_pods(self, namespace, app_id) -> list:
        results = self.get_pods_state(namespace=namespace, app_id=app_id)
        response = list()
        for metric in results.get("data").get("result"):
            _metric = metric.get("metric")
            cluster_id = _metric.get("cluster_id")
            _count = 0
            if _metric.get("phase") == "Running":
                _count = int(metric.get("values")[-1][1])
                response.append({"cluster": cluster_id, "count": _count})
        return response

    def get_namespace_details(self, namespace, app_id=None):
        if app_id:
            query = f'sum(kube_service_labels{{namespace="{namespace}", service="{app_id}"}}) \
            by (cluster_id,instance,label_app,label_mls_index,label_mls_cluster)'
        else:
            query = f'sum(kube_pod_labels{{mms_source="wcnp", namespace="{namespace}",label_app!="helm",' \
                    f'label_app!="highlander",label_app!="prometheus",label_app!~".*-inta-.*|.*-intb-.*|' \
                    f'.*canary.*|.*default-tester|.*-inta|.*-intb|.*-nsf-.*"}}) ' \
                    f'by (cluster_id,instance,label_app,label_mls_index,label_mls_cluster)'
        current_epoch, ago = Prometheus.get_last_n_minutes_epoch_times(3)
        status, too_much_dat_error, data = self.get_query_range_data_with_meta(query, ago, current_epoch, steps=60)
        if not data:
            return None
        if data.get("status") == "success":
            results = defaultdict(lambda: defaultdict(dict))
            for element in data.get("data").get("result"):
                val = 0
                if element.get("values"):
                    val = element.get("values")[-1][-1]
                _app = element.get("metric").get('label_app')
                if element.get("metric") and _app:
                    if _app in IGNORE_APPS:
                        continue
                    pattern_matched = False
                    for pattern in IGNORE_PATTERN:
                        if pattern in _app:
                            pattern_matched = True
                            break
                    if pattern_matched:
                        continue
                    results[_app]["clusters"].update({element.get("metric").get('cluster_id'): val})
                    results[_app]["mls_cluster"] = element.get("metric").get('label_mls_cluster')
                    results[_app]["label_mls_index"] = element.get("metric").get('label_mls_index')

            return results

    def get_remote_current_write_limit_rate(self, namespace):

        query = f'mms_ingestion_operator_whitelist_sample_rate{{mms_source="wcnp",  ' \
                f'prometheusNamespace=~"{namespace}", ' \
                f'prometheus=~"prometheus",storageType!="lts"}}'

        current_epoch, ago = Prometheus.get_last_n_minutes_epoch_times(3)
        status, too_much_dat_error, data = self.get_query_range_data_with_meta(query, ago, current_epoch, steps=60)
        if not data:
            return None
        if data.get("status") == "success":
            results = defaultdict(lambda: defaultdict(dict))
            for element in data.get("data").get("result"):
                val = 0
                if element.get("values"):
                    val = element.get("values")[-1][-1]
                _app = element.get("metric").get('whitelist')
                cluster = element.get("metric").get('cluster_id')
                results[_app][cluster].update({"value": val})

            return results

    def get_remote_write_limit(self, namespace):

        query = f'mms_ingestion_operator_whitelist_limit{{mms_source="wcnp",' \
                f'prometheusNamespace=~"{namespace}", ' \
                f'prometheus=~"prometheus",storageType!="lts"}}'

        current_epoch, ago = Prometheus.get_last_n_minutes_epoch_times(3)
        status, too_much_dat_error, data = self.get_query_range_data_with_meta(query, ago, current_epoch, steps=60)
        if not data:
            return None
        if data.get("status") == "success":
            results = defaultdict(lambda: defaultdict(dict))
            for element in data.get("data").get("result"):
                val = 0
                if element.get("values"):
                    val = element.get("values")[-1][-1]
                _app = element.get("metric").get('whitelist')
                cluster = element.get("metric").get('cluster_id')
                results[_app][cluster].update({"value": val})

            return results

    def get_throughput(self, namespace, app_id, steps=60, cluster_id=None, total=False, start_time=None,
                       end_time=None):
        """
        For given namespace, app_id, gets the TPS by response codes
        It gives you thee options
            1. For all regions by default
            2. By region
            3. by total
        Args:
            namespace:
            app_id:
            cluster_id:
            steps:
            total:
            start_time:
            end_time:

        Returns:

        """
        if not end_time or not start_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(3)
        logger.info(f"Pulling app {namespace}/{app_id} throughput from {WMTDateTime.epoch_to_pst_datetime(start_time)} "
                    f"to {WMTDateTime.epoch_to_pst_datetime(end_time)}")
        if cluster_id:
            query = 'round(sum(envoy_cluster_upstream_rq:sum_rate2m{{cluster_id=~"{cluster_id}",' \
                    'cluster_name=~"outbound.*{app_id}.{namespace}.svc.cluster.local"}}), 0.001)' \
                    ''.format_map({"namespace": namespace, "app_id": app_id, "cluster_id": cluster_id})
        elif total:
            query = 'round(sum(envoy_cluster_upstream_rq:sum_rate2m{{' \
                    'cluster_name=~"outbound.*{app_id}.{namespace}.svc.cluster.local"}}), 0.001)' \
                    ''.format_map({"namespace": namespace, "app_id": app_id})

        else:
            query = 'round(sum(envoy_cluster_upstream_rq:sum_rate2m{{' \
                    'cluster_name=~"outbound.*{app_id}.{namespace}.svc.cluster.local"}}) by (cluster_id), 0.001)' \
                    ''.format_map({"namespace": namespace, "app_id": app_id})

        res = self.get_query_range_data(query=query, start_time=start_time, end_time=end_time, steps=steps)
        return res

    def get_pods_state(self, namespace, app_id, steps=60, cluster_id=None, total=False, start_time=None,
                       end_time=None, **kwargs):
        """
        For given namespace, app_id, gets the TPS by response codes
        It gives you thee options
            1. For all regions by default
            2. By region
            3. by total
        Args:
            namespace:
            app_id:
            cluster_id:
            steps:
            total:
            start_time:
            end_time:


        Returns:

        """
        if not end_time or not start_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(3)
        logger.info(f"Pulling app {namespace}/{app_id} pods states from {WMTDateTime.epoch_to_pst_datetime(start_time)}"
                    f" to {WMTDateTime.epoch_to_pst_datetime(end_time)}")
        common_tags = 'namespace=~"{namespace}", pod=~"{app_id}-.*"'.format_map({"namespace": namespace,
                                                                                 "app_id": app_id})
        if cluster_id:
            query = 'sum(kube_pod_status_phase{{{common_tags},cluster_id=~"{cluster_id}"}} ' \
                    'and on (pod) (increase(kube_pod_container_status_restarts_total{' \
                    '{{common_tags},cluster_id=~"{cluster_id}"}}[2m]) == 0)) by (phase)' \
                    ''.format_map({"cluster_id": cluster_id, "common_tags": common_tags})
        elif total:
            query = 'sum(kube_pod_status_phase{{{common_tags}}} ' \
                    'and on (pod) (increase(kube_pod_container_status_restarts_total{{' \
                    '{common_tags}}[2m]) == 0)) by (phase)'.format_map({"common_tags": common_tags})

        else:
            query = 'sum(kube_pod_status_phase{{{common_tags},cluster_id=~".*"}} ' \
                    'and on (pod) (increase(kube_pod_container_status_restarts_total{{' \
                    '{common_tags}}}[2m]) == 0)) by (phase,cluster_id)' \
                    ''.format_map({"common_tags": common_tags})

        res = self.get_query_range_data(query=query, start_time=start_time, end_time=end_time, steps=steps)
        return res

    def get_pods_state_v2(self, namespace, app_id, steps=60, cluster_id=None, total=False, start_time=None,
                          end_time=None, extract_cluster_id=False, **kwargs):
        """
        For given namespace, app_id, gets the TPS by response codes
        It gives you thee options
            1. For all regions by default
            2. By region
            3. by total
        Args:
            namespace:
            app_id:
            cluster_id:
            steps:
            total:
            start_time:
            end_time:


        Returns:

        """
        if not end_time or not start_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(3)
        logger.info(f"Pulling app {namespace}/{app_id} pods states from {WMTDateTime.epoch_to_pst_datetime(start_time)}"
                    f" to {WMTDateTime.epoch_to_pst_datetime(end_time)}")
        ns_filter = Prometheus.build_promql_label_filter("namespace", namespace)

        app_filter = Prometheus.build_promql_regex_value(app_id)
        common_tags = f'{ns_filter}, pod=~"{app_filter}"'
        if cluster_id:
            _cluster_id = Prometheus.build_promql_label_filter("cluster_id", cluster_id)
            query = f'''sum(kube_pod_status_phase{{{common_tags},cluster_id=~"{cluster_id}"}} and on 
            (pod) (increase(kube_pod_container_status_restarts_total{ {{common_tags}, {_cluster_id}} }[2m]) == 0)) by (phase)'''
        elif total:
            query = f'''sum(kube_pod_status_phase{{{common_tags}}} and on (pod) 
            (increase(kube_pod_container_status_restarts_total{{{common_tags}}}[2m]) == 0)) by (phase)'''

        else:
            query = f'''sum(kube_pod_status_phase{{{common_tags},cluster_id=~".*"}} and on (pod) 
            (increase(kube_pod_container_status_restarts_total{{{common_tags}}}[2m]) == 0)) by (phase,cluster_id)'''

        res = self.get_query_range_data(query=query, start_time=start_time, end_time=end_time, steps=steps)
        if extract_cluster_id:
            def extract_cluster_ids(prom_response):
                results = prom_response.get("data", {}).get("result", [])
                return sorted(set(item["metric"]["cluster_id"] for item in results if "cluster_id" in item["metric"]))

            return extract_cluster_ids(res)
        return res

    def get_pods_memory_utilization(self, namespace: str, app_id: str, steps=60, cluster_id=None, total=False,
                                    threshold: float = None, start_time=None, end_time=None, ):
        """
        For given namespace, app_id, gets the TPS by response codes
        It gives you thee options
            1. For all regions by default
            2. By region
            3. by total
        Args:
            namespace:
            app_id:
            cluster_id:
            steps:
            total:
            threshold:
            start_time:
            end_time:

        Returns:

        """
        if not end_time or not start_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(3)
        logger.info(f"Pulling app {namespace}/{app_id} pods memory utilization from "
                    f"{WMTDateTime.epoch_to_pst_datetime(start_time)} to {WMTDateTime.epoch_to_pst_datetime(end_time)}")
        common_tags = 'namespace=~"{namespace}", pod=~"{app_id}-.*"'.format_map({"namespace": namespace,
                                                                                 "app_id": app_id})
        if cluster_id:
            query = 'max(container_memory_working_set_bytes{{{common_tags}, ' \
                    'cluster_id=~"{cluster_id}", container!=""}}) by (namespace, pod, container' \
                    ') / min(kube_pod_container_resource_limits_memory_bytes{{{common_tags}, ' \
                    'cluster_id=~"{cluster_id}", container!=""}}) by (namespace, pod, container)' \
                    ''.format_map({"common_tags": common_tags, "cluster_id": cluster_id})
        elif total:
            query = 'max(container_memory_working_set_bytes{{{common_tags}, ' \
                    'container!=""}}) by (namespace, pod, container)' \
                    ' / min(kube_pod_container_resource_limits_memory_bytes{{{common_tags}, container!=""}}) ' \
                    'by (namespace, pod, container)'.format_map({"common_tags": common_tags})

        else:
            query = 'max(container_memory_working_set_bytes{{{common_tags}, ' \
                    'cluster_id=~".*", container!=""}}) by (namespace, pod, container,' \
                    'cluster_id) / min(kube_pod_container_resource_limits_memory_bytes{{' \
                    '{common_tags}, cluster_id=~".*", container!=""}}) ' \
                    'by (namespace, pod, container,cluster_id)'.format_map({"common_tags": common_tags})
        if threshold:
            query = "({})*100>{}".format(query, float(threshold))
        res = self.get_query_range_data(query=query, start_time=start_time, end_time=end_time, steps=steps)
        return res

    def get_pods_cpu_utilization(self, namespace: str, app_id: str, steps=60, cluster_id=None, total=False,
                                 threshold: float = None, start_time=None, end_time=None):
        """
        For given namespace, app_id, gets the CPU utilization
        It gives you thee options
            1. For all regions by default
            2. By region
            3. by total
        Args:
            namespace:
            app_id:
            cluster_id:
            steps:
            total:
            threshold:
            start_time:
            end_time:

        Returns:

        """
        if not end_time or not start_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(3)
        logger.info(f"Pulling app {namespace}/{app_id} pods CPU utilization from "
                    f"{WMTDateTime.epoch_to_pst_datetime(start_time)} to "
                    f"{WMTDateTime.epoch_to_pst_datetime(end_time)}")
        common_tags = 'namespace=~"{namespace}", pod=~"{app_id}-.*"'.format_map({"namespace": namespace,
                                                                                 "app_id": app_id})
        if cluster_id:
            query = 'max(rate(container_cpu_usage_seconds_total{{{common_tags}, ' \
                    'container!="", container!="POD",cluster_id=~"{cluster_id}"}}[2m])) by ' \
                    '(namespace, pod, container) / min(kube_pod_container_resource_limits_cpu_cores{{' \
                    '{common_tags},container!="", container!="POD",' \
                    'cluster_id=~"{cluster_id}"}}) by (namespace, pod, container)' \
                    ''.format_map({"common_tags": common_tags, "cluster_id": cluster_id})
        elif total:
            query = 'max(rate(container_cpu_usage_seconds_total{{{common_tags}, ' \
                    'container!="", container!="POD"}}[2m])) by (namespace, pod, container) / min(' \
                    'kube_pod_container_resource_limits_cpu_cores{{{common_tags},container!="", container!="POD"}}) ' \
                    'by (namespace, pod, container)'.format_map({"common_tags": common_tags})

        else:
            query = 'max(rate(container_cpu_usage_seconds_total{{{common_tags}, ' \
                    'container!="", container!="POD"}}[2m])) by (namespace, pod, container,cluster_id) / min(' \
                    'kube_pod_container_resource_limits_cpu_cores{{{common_tags},container!="", container!="POD"}}) ' \
                    'by (namespace, pod, container,cluster_id)'.format_map({"common_tags": common_tags})
        if threshold:
            query = "({})*100>{}".format(query, float(threshold))
        res = self.get_query_range_data(query=query, start_time=start_time, end_time=end_time, steps=steps)
        return res

    def get_all_wcnp_alerts(self, namespace=None, steps=60, market=None, tier=None, n_minutes_data=5, tool="juno"):
        """
        Get's last n minutes alerts data
        """
        end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(n_minutes_data)

        if namespace:
            logger.info(f"Pulling app {namespace} pods CPU utilization from "
                        f"{WMTDateTime.epoch_to_pst_datetime(start_time)} to "
                        f"{WMTDateTime.epoch_to_pst_datetime(end_time)}")
            common_tags = 'namespace=~"{namespace}", tool=~"{tool}"'.format_map({"namespace": namespace, "tool": tool})
        else:
            common_tags = 'tool=~"{tool}"'.format_map({"tool": tool})
        if market and tier:
            query = 'ALERTS{{{common_tags}, ' \
                    'container!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|' \
                    '.*default-tester", job_name!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|' \
                    'secrets-.*|.*default-tester", alert_team!~"ALL|af_wcnp_containers",' \
                    'market=~"{market}",tier=~"{tier}"}}' \
                    ''.format_map({"common_tags": common_tags, "market": market, "tier": tier})
        elif market:
            query = 'ALERTS{{{common_tags}, ' \
                    'container!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|' \
                    '.*default-tester", job_name!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|' \
                    'secrets-.*|.*default-tester", alert_team!~"ALL|af_wcnp_containers",' \
                    'market=~"{market}"}}' \
                    ''.format_map({"common_tags": common_tags, "market": market})
        elif tier:
            query = 'ALERTS{{{common_tags}, ' \
                    'container!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|' \
                    '.*default-tester", job_name!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|' \
                    'secrets-.*|.*default-tester", alert_team!~"ALL|af_wcnp_containers",' \
                    'tier=~"{tier}"}}' \
                    ''.format_map({"common_tags": common_tags, "tier": tier})
        else:
            query = 'ALERTS{{{common_tags}, ' \
                    'container!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|' \
                    '.*default-tester", job_name!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|' \
                    'secrets-.*|.*default-tester", alert_team!~"ALL|af_wcnp_containers"}}' \
                    ''.format_map({"common_tags": common_tags})

        res = self.get_query_range_data(query=query, start_time=start_time, end_time=end_time, steps=steps)
        return res

    def get_all_wcnp_alerts_group_by(self, steps=60, n_minutes_data=5, tool="juno"):
        """
        Get's last n minutes alerts data
        """
        end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(n_minutes_data)

        common_tags = 'tool=~"{tool}"'.format_map({"tool": tool})
        query = 'sum(ALERTS{{{common_tags}, ' \
                'container!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|' \
                '.*default-tester", job_name!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|' \
                'secrets-.*|.*default-tester", alert_team!~"ALL|af_wcnp_containers",alert_type=~"wcnp"}}) by ' \
                '(namespace,app_name,alertstate,market,tier)'.format_map({"common_tags": common_tags})

        res = self.get_query_range_data(query=query, start_time=start_time, end_time=end_time, steps=steps)
        return res

    def get_all_alerts_group_by(self, steps=60, n_minutes_data=5, tool="juno"):
        """
        Get's last n minutes alerts data
        """
        end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(n_minutes_data)

        common_tags = 'tool=~"{tool}"'.format_map({"tool": tool})
        query = 'sum(ALERTS{{{common_tags}, ' \
                'container!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|' \
                '.*default-tester", job_name!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|' \
                'secrets-.*|.*default-tester", alert_team!~"ALL|af_wcnp_containers"}}) by ' \
                '(alert_type,alertstate,market,tier)'.format_map({"common_tags": common_tags})

        res = self.get_query_range_data(query=query, start_time=start_time, end_time=end_time, steps=steps)
        return res

    def get_pods_restart(self, namespace: str, app_id: str, steps=60, cluster_id=None, total=False,
                         threshold: float = None, start_time=None, end_time=None):
        """
        For given namespace, app_id, gets the TPS by response codes
        It gives you thee options
            1. For all regions by default
            2. By region
            3. by total
        Args:
            namespace:
            app_id:
            cluster_id:
            steps:
            total:
            threshold:
            start_time:
            end_time:


        Returns:

        """
        if not end_time or not start_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(3)
        logger.info(f"Pulling app {namespace}/{app_id} pods restarts from "
                    f"{WMTDateTime.epoch_to_pst_datetime(start_time)} to "
                    f"{WMTDateTime.epoch_to_pst_datetime(end_time)}")
        common_tags = 'namespace=~"{namespace}", pod=~"{app_id}-.*"'.format_map({"namespace": namespace,
                                                                                 "app_id": app_id})
        if cluster_id:
            query = 'sum(increase(kube_pod_container_status_restarts_total{{{common_tags},container!="", ' \
                    'container!="POD",cluster_id=~"{cluster_id}"}}[2m])) ' \
                    'by (cluster_id,container)'.format_map({"common_tags": common_tags, "cluster_id": cluster_id})
        elif total:
            query = 'sum(increase(kube_pod_container_status_restarts_total{{{common_tags},' \
                    'container!="", container!="POD"}}[2m])) by (container)'.format_map({"common_tags": common_tags})

        else:
            query = 'sum(increase(kube_pod_container_status_restarts_total{{{common_tags},container!="", ' \
                    'container!="POD"}}[2m])) by (cluster_id,container)'.format_map({"common_tags": common_tags})
        if threshold:
            query = "({})*100>{}".format(query, float(threshold))
        res = self.get_query_range_data(query=query, start_time=start_time, end_time=end_time, steps=steps)
        return res

    def get_tps_by_rc_code(self, namespace, app_id, response_code, steps=60, cluster_id=None, start_time=None,
                           end_time=None):
        """
        For given namespace, app_id, gets the TPS by response codes
        Args:
            namespace:
            app_id:
            response_code:
            cluster_id:
            steps:
            start_time:
            end_time:

        Returns:

        """
        if response_code not in ["5xx", "4xx", "3xx", "2xx"]:
            return None
        if not end_time or not start_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(3)
        cluster_name = self.get_envoy_rc_code_cluster_name(namespace, app_id, start_time, end_time)
        if cluster_id:
            query = 'round(sum(envoy_cluster_upstream_rq:sum_rate2m{{cluster_name="{cluster_name}", ' \
                    'cluster_id=~"{cluster_id}", response_code_class=~"{response_code}"}}) by (response_code_class, ' \
                    'cluster_name, namespace,cluster_id), 0.001)'.format_map({"cluster_id": cluster_id,
                                                                              "cluster_name": cluster_name,
                                                                              "response_code": response_code})
        else:
            query = 'round(sum(envoy_cluster_upstream_rq:sum_rate2m{{cluster_name="{cluster_name}", ' \
                    'response_code_class=~"{response_code}"}}) by (response_code_class, cluster_name, ' \
                    'namespace), 0.001)'.format_map({"cluster_name": cluster_name, "response_code": response_code})

        res = self.get_query_range_data(query=query, start_time=start_time, end_time=end_time, steps=steps)
        return res

    def get_pods_stats(self, namespace: str, app_id: str, steps=60, cluster_id=None, total=False,
                       threshold: float = None, start_time=None, end_time=None):
        """
        Gets pods data
        Args:
            namespace:
            app_id:
            steps:
            cluster_id:
            total:
            threshold:
            start_time:
            end_time:

        Returns:

        """
        results = OrderedDict()
        if not end_time or not start_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(3)
        logger.info(f"Pulling app {namespace}/{app_id} stats from {WMTDateTime.epoch_to_pst_datetime(start_time)} to "
                    f"{WMTDateTime.epoch_to_pst_datetime(end_time)}")
        p_state = self.get_pods_state(namespace, app_id, steps=steps, cluster_id=cluster_id, total=total,
                                      threshold=threshold, start_time=start_time, end_time=end_time)
        p_restart = self.get_pods_restart(namespace, app_id, steps=steps, cluster_id=cluster_id, total=total,
                                          threshold=threshold, start_time=start_time, end_time=end_time)
        p_cpu_util = self.get_pods_cpu_utilization(namespace, app_id, steps=steps, cluster_id=cluster_id, total=total,
                                                   threshold=threshold, start_time=start_time, end_time=end_time)
        p_mem_util = self.get_pods_memory_utilization(namespace, app_id, steps=steps, cluster_id=cluster_id,
                                                      total=total, threshold=threshold, start_time=start_time,
                                                      end_time=end_time)
        results["state"] = p_state.get("data") if p_state.get("status") == "success" else {}
        results["restart"] = p_restart.get("data") if p_restart.get("status") == "success" else {}
        results["cpu_util"] = p_cpu_util.get("data") if p_cpu_util.get("status") == "success" else {}
        results["mem_util"] = p_mem_util.get("data") if p_mem_util.get("status") == "success" else {}
        return results

    def get_app_stats(self, namespace, app_id, steps=60, cluster_id=None, total=False, start_time=None, end_time=None):
        """
        Get all app stats
        Args:
            namespace:
            app_id:
            steps:
            cluster_id:
            total:
            start_time:
            end_time:

        Returns:

        """
        try:
            results = OrderedDict()
            results["latencies"] = OrderedDict()
            if not end_time or not start_time:
                end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(3)
            logger.info(
                f"Pulling app {namespace}/{app_id} stats from {WMTDateTime.epoch_to_pst_datetime(start_time)} to "
                f"{WMTDateTime.epoch_to_pst_datetime(end_time)}")
            p_50 = self.get_latencies(namespace, app_id, steps=steps, cluster_id=cluster_id, total=total,
                                      percentile=0.50,
                                      start_time=start_time, end_time=end_time)
            p_95 = self.get_latencies(namespace, app_id, steps=steps, cluster_id=cluster_id, total=total,
                                      percentile=0.95,
                                      start_time=start_time, end_time=end_time)
            p_99 = self.get_latencies(namespace, app_id, steps=steps, cluster_id=cluster_id, total=total,
                                      percentile=0.99,
                                      start_time=start_time, end_time=end_time)
            throughput = self.get_throughput(namespace, app_id, steps=steps, cluster_id=cluster_id, total=total,
                                             start_time=start_time, end_time=end_time)
            rc_tps = self.get_tps_by_all_rc_codes(namespace, app_id, steps=steps, cluster_id=cluster_id, total=total,
                                                  start_time=start_time, end_time=end_time)
            results["latencies"]["p95"] = p_95.get("data") if p_95.get("status") == "success" else {}
            results["latencies"]["p99"] = p_99.get("data") if p_99.get("status") == "success" else {}
            results["latencies"]["p50"] = p_50.get("data") if p_50.get("status") == "success" else {}
            results["throughput"] = throughput.get("data") if throughput.get("status") == "success" else {}
            results["rc_tps"] = rc_tps.get("data") if rc_tps.get("status") == "success" else {}
            return results
        except Exception as e:
            return str(e)

    def pull_data(self, namespace, app_id, start_time, end_time, steps=60, cluster_id=None, total=False,
                  threshold=None):
        app_stats = self.get_app_stats(namespace, app_id, steps=steps, cluster_id=cluster_id, total=total,
                                       start_time=start_time, end_time=end_time)
        pod_stats = self.get_pods_stats(namespace, app_id, steps=steps, cluster_id=cluster_id, total=total,
                                        threshold=threshold, start_time=start_time, end_time=end_time)
        return {"app_stats": app_stats, "pod_stats": pod_stats, "namespace": namespace, "app_id": app_id}

    def parallel_render(self):
        pass

    @staticmethod
    def get_last_n_minutes_epoch_times(n_minutes: int):
        current_epoch = time.time()
        ago = current_epoch - (n_minutes * 60)
        return current_epoch, ago

    def get_envoy_rc_code_cluster_name(self, namespace, app_id, stat_time, end_time):
        """
        Get Envoy cluster response codes of cluster_name. For given namespace and cluster returns cluster_name
        shown below
        outbound|4000||aroundme-prod.ce-around-me-gql.svc.cluster.local
        Args:
            namespace: wcnp namespace name
            app_id: wcnp app name
            stat_time: epoch start time
            end_time: epoch end time

        Returns:

        """
        query = 'envoy_cluster_upstream_rq:sum_rate2m{{cluster_name=~".*{app_id}.{namespace}.svc.cluster.local"}}' \
                ''.format_map({"app_id": app_id, "namespace": namespace})
        response = self.make_series_call(query, stat_time, end_time)
        if not response:
            return None
        if response.get("status") == "success":
            for element in response.get("data"):
                # All cluster_id would be same, get first one
                return element['cluster_name']

    @staticmethod
    def _process_results_by_dc(self, response):

        results = defaultdict(list)
        if not response:
            return None
        if response.get("status") == "success":
            for element in response.get("data"):
                results['cluster_id'] = element['cluster_id']
                results['cluster_id'].append()

    def get_all_oneops_dashboards(self, org, assembly):
        dasboard = "https://grafana.mms.walmart.net/d/mex5zyDOLNGz/mexico-platform-metrics?orgId=1&" \
                   "var-datasource=production&var-oot={org}&var-ooa={assembly}&" \
                   "var-ooe={env}&var-oop={platform}&var-lbdc=All&var-oo_dc=All"
        try:
            results = OrderedDict()
            for _assembly, platforms in self.get_all_assemblies_platforms_env(org).items():
                results[_assembly] = dict()
                if assembly == _assembly:
                    results[_assembly][assembly] = dict()
                    for _platform, envs in platforms.items():
                        results[_assembly][assembly][_platform] = dict()
                        for env in envs:
                            results[_assembly][assembly][_platform][env] = dasboard.format_map({"org": org,
                                                                                                "assembly": assembly,
                                                                                                "env": env,
                                                                                                "platform": _platform})
                    break

            return results
        except Exception as e:
            return str(e)

    def get_all_pods_cpu_cycles(self, namespace, app_id, cluster_id=None, end_time=None, start_time=None):
        if not end_time or not start_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(3)
        logger.info(f"Pulling app {namespace}/{app_id} pods CPU utilization from "
                    f"{WMTDateTime.epoch_to_pst_datetime(start_time)} to "
                    f"{WMTDateTime.epoch_to_pst_datetime(end_time)}")
        common_tags = 'namespace=~"{namespace}", pod=~"{app_id}-.*"'.format_map({"namespace": namespace,
                                                                                 "app_id": app_id})
        if cluster_id:
            query = 'container_cpu_usage_seconds_total{{ {common_tags},mms_source="wcnp",' \
                    'container!="POD",cluster_id={cluster_id}}}'.format_map({"common_tags": common_tags})

        else:
            query = 'container_cpu_usage_seconds_total{{{common_tags},mms_source="wcnp",' \
                    'container!="POD"}}'.format_map({"common_tags": common_tags})

        res = self.make_series_call(query=query, start_time=start_time, end_time=end_time)
        return res

    def get_containers(self, namespace, app_id=None):
        results = defaultdict(dict)
        for app_id, cluster_ids in self.get_all_app_names_and_cluster_ids(namespace, app_id).items():
            # for cluster_id in cluster_ids:
            containers = set()
            res = self.get_all_pods_cpu_cycles(namespace, app_id)
            if res.get("status") == "success" and len(res.get("data")) > 0:
                for entry in res.get("data"):
                    if entry.get('container'):
                        if entry.get('container') not in ["highlander", "vault-agent-init", "install-oneagent",
                                                          "istio-proxy", ""]:
                            containers.add(entry.get('container'))
            results[app_id]["clusters"] = cluster_ids
            results[app_id]["containers"] = containers
            results[app_id]["namespace"] = namespace
        return results

    def get_all_dashboards(self, namespace, app_id=None):
        try:
            results = OrderedDict()
            for app_id, cluster_ids in self.get_all_app_names_and_cluster_ids(namespace, app_id).items():
                results[app_id] = OrderedDict()
                if "ops" not in results[app_id]:
                    results[app_id]["ops"] = OrderedDict()
                if "monitoring" not in results[app_id]:
                    results[app_id]["monitoring"] = OrderedDict()

                if "k8" not in results[app_id]["ops"]:
                    results[app_id]["ops"]["k8"] = OrderedDict()
                    results[app_id]["ops"]["k8"]["console"] = Prometheus.build_wcnp_console_dashboard(namespace)
                results[app_id]["monitoring"]["isto"] = Prometheus.build_isto_dashboard(namespace, app_id)
                results[app_id]["monitoring"]["sre"] = Prometheus.build_intl_sre_dashboard(namespace, app_id)
                #   results[app_id]["monitoring"]["splunk"] = Prometheus.build_intl_sre_dashboard(namespace, app_id)
                if "pods_robes" not in results[app_id]["monitoring"]:
                    results[app_id]["monitoring"]["pods_robes"] = OrderedDict()
                results[app_id]["monitoring"]["pods_robes"]["all"] = Prometheus.build_pods_dashboard(namespace, app_id)
                for cluster_id in cluster_ids:
                    results[app_id]["monitoring"]["pods_robes"][cluster_id] = Prometheus.build_pods_probes_dashboard(
                        namespace,
                        cluster_id)
                    results[app_id]["ops"]["k8"][cluster_id] = Prometheus.build_k8_cluster_url(namespace,
                                                                                               cluster_id)

            return results
        except Exception as e:
            return str(e)

    @staticmethod
    def build_sre_dashboard(namespace, app_id):
        url = f"http://grafana360.sre.prod.walmart.net/d/E-OHGTOMk/sre-wcnp-onedash?orgId=1&from=now-25m&to=now&" \
              f"refresh=30s&var-namespace={namespace}&var-app={app_id}&var-clusterId=All&var-istioDS=All&" \
              f"var-nodeDS=All&var-pod=All&var-long_interval=$__auto_interval_long_interval&" \
              f"var-interval=$__auto_interval_interval&var-cluster_name=All".format_map({"namespace": namespace,
                                                                                         "app_id": app_id})
        logger.info(f"SRE link for {namespace}/{app_id} : {url}")
        return url

    @staticmethod
    def build_pods_probes_dashboard(namespace, cluster_id):
        url = f"http://grafana.{cluster_id}.cluster.k8s.us.walmart.net/d/S7GP5kqik/pods?orgId=1&from=now-10m&" \
              f"to=now&var-datasource=default&var-namespace={namespace}&var-pod=All&var-interval=" \
              f"$__auto_interval_interval&var-long_interval=$__auto_interval_long_interval"
        logger.info(f"PODS probs link for {namespace}/{cluster_id} : {url}")
        return url

    @staticmethod
    def build_gslb_graph(lb):
        url = f"https://grafana.mms.walmart.net/d/000000098/gslb-by-domain?orgId=1&refresh=1m&" \
              f"var-datasource=production&var-origin={lb}&var-client_dc=All"
        return url

    @staticmethod
    def build_k8_cluster_url(namespace, cluster_id):
        url = f"https://k8s-dashboard.kube-system.{cluster_id}.cluster.k8s.us.walmart.net/#/" \
              f"overview?namespace={namespace}"
        logger.info(f"K8 cluster link for {namespace}/{cluster_id} : {url}")
        return url

    @staticmethod
    def build_isto_dashboard(namespace, app_id):
        url = f"https://grafana.mms.walmart.net/d/V0WeEf0Wk/istio-service-dashboard-mms?orgId=1&" \
              f"var-datasource=production&var-namespace={namespace}&var-clusterId=All&var-app={app_id}&" \
              f"var-percentile=95&var-destination=All&" \
              f"from=now-1h&to=now&refresh=5m".format_map({"namespace": namespace, "app_id": app_id})
        logger.info(f"ISTO link for {namespace}/{app_id} : {url}")
        return url

    @staticmethod
    def build_pods_dashboard(namespace, app_id):
        url = f"https://grafana.mms.walmart.net/d/7Wushy_mk/apps?orgId=1&from=now-30m&to=now&" \
              f"var-datasource=production&var-namespace={namespace}&var-cluster_id=All&var-app={app_id}&" \
              f"var-interval=$__auto_interval_interval&var-long_interval=$__auto_interval_long_interval"
        logger.info(f"PODS link for {namespace}/{app_id} : {url}")
        return url

    @staticmethod
    def build_wcnp_console_dashboard(namespace):
        url = f"https://console.dx.walmart.com/wcnp/namespaces/{namespace}?active_tab=details"
        logger.info(f"PODS link for {namespace} : {url}")
        return url

    @staticmethod
    def build_intl_sre_dashboard(namespace, app_id):
        url = f"https://grafana.mms.walmart.net/d/9VUSEPSRETwcnpGzINTL/intl-sre-wcnp-platform-metrics?orgId=1&" \
              f"refresh=5m&var-datasource=production&var-namespace={namespace}&var-cluster_id=All&var-app={app_id}"
        logger.info(f"PODS link for {namespace} : {url}")
        return url

    @staticmethod
    def get_splunk_link(mls_cluster, index):
        if mls_cluster == "mlsIntl":
            url = f"https://mls-intl-us.prod.us.walmart.net/en-US/app/search/search?q=search%20index%3D%22{index}%22"
            return url
        return None

    @staticmethod
    def get_kitt_location():
        return None

    def get_topped_alert(self, k=10, namespace=None, market=None, tier=None, duration=1440):
        import re
        # check market and tier
        market_list = ['mx', 'ca']
        tier_pattern = re.compile("tier-[012]")

        if market and not any(element in market for element in market_list):
            return "invalid market"

        if tier and re.match(tier_pattern, tier) is None:
            return "invalid tier"

        response = self.get_all_alerts(namespace=namespace, market=market, tier=tier, n_minutes_data=duration)
        top_result = dict()
        alert_category = dict()
        # re-organize result
        data_block = response['data']['result']
        for bloc in data_block:
            key = bloc['metric']['alert_component']
            alert_id = bloc['metric']['alert_id']
            alert_sla_name = bloc['metric']['alert_sla_name']
            count = len(bloc['values'])
            if key not in alert_category.keys():
                alert_category[key] = dict()
            if alert_id not in alert_category[key].keys():
                alert_category[key][alert_id] = dict()
            if alert_sla_name not in alert_category[key][alert_id].keys():
                alert_category[key][alert_id][alert_sla_name] = count
            else:
                alert_category[key][alert_id][alert_sla_name] += count

            if 'sum' in alert_category[key][alert_id].keys():
                alert_category[key][alert_id]['sum'] += count
            else:
                alert_category[key][alert_id]['sum'] = count

        for key, val in alert_category.items():
            alert_type_list = list()
            sorted_result = sorted(val, key=lambda x: (val[x]['sum']), reverse=True)
            for each_piece in sorted_result:
                if len(alert_type_list) < k:
                    each_alert = dict()
                    each_alert.update(alert_category[key][each_piece])
                    each_alert['alert_id'] = each_piece
                    alert_type_list.append(each_alert)
            top_result[key] = alert_type_list
        return top_result

    def get_alert_sla_name(self, k=10, namespace=None, market=None, tier=None, duration=1440):
        response = self.get_topped_alert(k, namespace=namespace, market=market, tier=tier, duration=duration)
        ret = dict(list())
        summary = ['alert_id', 'sum']

        for key, val in response.items():
            piece = dict()
            for val_piece in val:
                alert_sla_names = [val_key for val_key in val_piece.keys() if val_key not in summary]
                for alert_sla in alert_sla_names:
                    if alert_sla not in piece.keys():
                        piece[alert_sla] = val_piece[alert_sla]
                    else:
                        piece[alert_sla] += val_piece[alert_sla]
            piece = dict(sorted(piece.items(), key=lambda item: item[1], reverse=True))
            ret[key] = piece

        return ret

    def get_cosmos_subscription(self):
        subscription_details = dict()
        end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(3)
        query = 'sum({__name__=~"availablestorage_bytes_total"}) by(subscription_name)'
        res = self.get_query_range_data(query=query, start_time=start_time, end_time=end_time, steps=3)
        for entry in res.get("data").get("result"):
            sub = entry.get("metric").get('subscription_name')
            subscription_details[sub] = self.get_resource_groups(sub)
        return subscription_details

    def get_resource_groups(self, subscription_name):
        resource_groups = list()
        query = f'sum(availablestorage_bytes_total{{subscription_name="{subscription_name}"}}) by(resource_group)'
        end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(3)
        res = self.get_query_range_data(query=query, start_time=start_time, end_time=end_time, steps=3)
        for entry in res.get("data").get("result"):
            resource_groups.append(entry.get("metric").get('resource_group'))
        return resource_groups

    @staticmethod
    def build_promql_label_filter(label, value):
        """
        Constructs a PromQL label filter string for a single or multiple values.
        - For list: uses regex match (label=~"a|b|c")
        - For string: uses exact match (label="val")
        - For None: returns empty string
        """
        if isinstance(value, list):
            return f'{label}=~"{"|".join(value)}"'
        elif value:
            return f'{label}="{value}"'
        return ""

    @staticmethod
    def build_promql_container_filter(app_id):
        """
        Builds PromQL container regex filter based on one or more app_ids
        """
        if isinstance(app_id, list):
            return f'container=~".*({"|".join(app_id)}).*"'
        elif app_id:
            return f'container=~".*{app_id}.*"'
        return ""

    @staticmethod
    def build_promql_label_regex_filter(label, value):
        """
         Constructs a PromQL label filter string for a single or multiple values.
         - For list: uses regex match (label=~"a|b|c")
         - For string: uses exact match (label="val")
         - For None: returns empty string
         """
        if isinstance(value, list):
            return f'{label}=~".*({"|".join(value)}).*"'
        elif value:
            return f'{label}=~".*{value}.*"'
        return ""

    @staticmethod
    def build_promql_regex_value(value):
        """
        Builds a PromQL-safe regex value string for 'label=~"..."'
        - list → '(.*val1.*|.*val2.*)'
        - str  → '.*val.*'
        """
        if isinstance(value, list):
            return f'({"|".join(f".*{v}.*" for v in value)})'
        elif value:
            return f'.*{value}.*'
        return ""

    @staticmethod
    def build_promql_value(value):
        """
        Builds a plain or regex OR-separated PromQL value string.
        - list → 'val1|val2|val3'
        - str  → 'val'
        Useful for label=~"..." expressions.
        """
        if isinstance(value, list):
            return "|".join(value)
        elif value:
            return value
        return ""

    def get_all_alerts(self, namespace=None, steps=60, market=None, tier=None, n_minutes_data=5, tool="juno"):
        """
        Get's last n minutes alerts data
        """
        end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(n_minutes_data)

        if namespace:
            logger.info(f"Pulling app {namespace} pods CPU utilization from "
                        f"{WMTDateTime.epoch_to_pst_datetime(start_time)} to "
                        f"{WMTDateTime.epoch_to_pst_datetime(end_time)}")
            common_tags = 'namespace=~"{namespace}", tool=~"{tool}"'.format_map({"namespace": namespace, "tool": tool})
        else:
            common_tags = 'tool=~"{tool}"'.format_map({"tool": tool})
        if market and tier:
            query = 'ALERTS{{{common_tags}, ' \
                    'container!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|' \
                    '.*default-tester", job_name!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|' \
                    'secrets-.*|.*default-tester", alert_team!~"ALL|af_wcnp_containers",' \
                    'market=~".*{market}.*",tier=~"{tier}"}}' \
                    ''.format_map({"common_tags": common_tags, "market": market, "tier": tier})
        elif market:
            query = 'ALERTS{{{common_tags}, ' \
                    'container!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|' \
                    '.*default-tester", job_name!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|' \
                    'secrets-.*|.*default-tester", alert_team!~"ALL|af_wcnp_containers",' \
                    'market=~".*{market}.*"}}' \
                    ''.format_map({"common_tags": common_tags, "market": market})
        elif tier:
            query = 'ALERTS{{{common_tags}, ' \
                    'container!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|' \
                    '.*default-tester", job_name!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|' \
                    'secrets-.*|.*default-tester", alert_team!~"ALL|af_wcnp_containers",' \
                    'tier=~"{tier}"}}' \
                    ''.format_map({"common_tags": common_tags, "tier": tier})
        else:
            query = 'ALERTS{{{common_tags}, ' \
                    'container!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|' \
                    '.*default-tester", job_name!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|' \
                    'secrets-.*|.*default-tester", alert_team!~"ALL|af_wcnp_containers"}}' \
                    ''.format_map({"common_tags": common_tags})

        res = self.get_query_range_data(query=query, start_time=start_time, end_time=end_time, steps=steps)
        return res

    def get_all_alerts_improved(
            self,
            namespace=None,
            steps=60,
            n_minutes_data=5,
            tool="juno",
            start_epoch=None,
            end_epoch=None,
            **kwargs
    ):
        """
        Retrieves the last n minutes of alerts data, supporting multiple values for specific fields.

        Parameters:
            namespace (str or list, optional): Namespace(s) to filter alerts.
            steps (int): Number of steps for the query range.
            n_minutes_data (int): Number of minutes of data to retrieve.
            tool (str or list): Tool(s) to filter alerts.
            **kwargs: Additional label filters, where values can be str or list.

        Returns:
            res: The result of the Prometheus query.
        """

        if start_epoch and end_epoch:
            start_time = int(start_epoch)
            end_time = int(end_epoch)
        else:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(n_minutes_data)

        # Handle namespaces and tools, supporting multiple values
        if namespace:
            namespaces = namespace if isinstance(namespace, list) else [namespace]
            namespace_regex = '|'.join(namespaces)
            logger.info(
                f"Pulling app namespaces {namespace_regex} pods CPU utilization from "
                f"{WMTDateTime.epoch_to_pst_datetime(start_time)} to "
                f"{WMTDateTime.epoch_to_pst_datetime(end_time)}"
            )
            common_tags = f'namespace=~"({namespace_regex})", tool=~"({tool})"'
        else:
            common_tags = f'tool=~"({tool})"'

        # Add additional tags from kwargs, supporting multiple values
        additional_tags = []
        for key, value in kwargs.items():
            if isinstance(value, list):
                value_regex = '|'.join(value)
                additional_tags.append(f'{key}=~"({value_regex})"')
            else:
                additional_tags.append(f'{key}=~"{value}"')

        if additional_tags:
            tags = f'{common_tags}, ' + ', '.join(additional_tags)
        else:
            tags = common_tags

        query = (
            f'ALERTS{{{tags}, '
            f'container!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|.*default-tester", '
            f'job_name!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|.*default-tester", '
            f'alert_team!~"ALL|af_wcnp_containers"}}'
        )

        res = self.get_query_range_data(
            query=query,
            start_time=start_time,
            end_time=end_time,
            steps=steps
        )

        return res

    # def get_all_alerts_bycomponent(self,alert_component,n_minutes_data=5,tier=None,market=None):
    #     """
    #             Get all app stats
    #             Args:
    #                 alert_component: eg:oneops,wcnp..
    #                 tier: app tier
    #                 market: market (mx,ca...)
    #
    #             Returns:
    #             return list of alerts(dict) based on component
    #             """
    #     alerts_data = self.get_all_alerts( tier=tier,n_minutes_data=n_minutes_data, market=market, tool="juno")
    #     if not alerts_data:
    #         return []
    #     alerts = alerts_data["data"]["result"]
    #     final = [i['metric'] for i in alerts if i['metric']['alertstate'] == 'firing']
    #     #return final
    #     final = list(i for i in final if 'alert_type' in i.keys())
    #
    #     return [i for i in final if alert_component.lower() in i['alert_type'].lower()]

    def get_cluster_cpu_utilization(self, namespace=None, app_id=None, steps=60,
                                    threshold=None, start_time=None, end_time=None, enrich_metrics_fn=None,
                                    enrich_config=None):
        """
        Gets CPU utilization aggregated by cluster_id using the specified query pattern
        """
        if not end_time or not start_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(3)

        ns_filter = Prometheus.build_promql_label_filter("namespace", namespace)
        app_filter = Prometheus.build_promql_container_filter(app_id)

        query = f'''
            (max(rate(container_cpu_usage_seconds_total{{
                mms_source="wcnp", 
                {ns_filter},
                {app_filter},
                container!="", 
                container!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|.*default-tester|.*delta"
            }}[10m])) by (namespace, container, cluster_id) 
            / 
            min(kube_pod_container_resource_limits{{
                resource="cpu", 
                mms_source="wcnp", 
                 {ns_filter},
                  {app_filter},
            }}) by (namespace, container, cluster_id) * 100)
        '''

        if threshold:
            query = f"({query})>{float(threshold)}"

        result_series = self.get_query_range_data_v2(query=query, start_time=start_time, end_time=end_time,
                                                     steps=steps,
                                                     enrich_metrics_fn=enrich_metrics_fn, enrich_config=enrich_config)
        if len(result_series) == 0:
            return {}, {}
        return result_series, query

    def get_cluster_memory_utilization(self, namespace=None, app_id=None, steps=60,
                                       threshold=None, start_time=None, end_time=None,
                                       enrich_metrics_fn=None, enrich_config=None):
        """
        Gets memory utilization aggregated by cluster_id using the specified query pattern
        """
        if not end_time or not start_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(3)
        ns_filter = Prometheus.build_promql_label_filter("namespace", namespace)
        app_filter = Prometheus.build_promql_container_filter(app_id)

        query = f'''
        (max(container_memory_working_set_bytes{{
            mms_source="wcnp", 
             {ns_filter},
            {app_filter},
            container!="", 
            container!~"prometheus.*|tiller.*|vault.*|istio-proxy|akeyless-sidecar|secrets-.*|.*default-tester|.*delta"
        }}) by (namespace, container, cluster_id) 
        / 
        min(kube_pod_container_resource_limits{{
            resource="memory", 
            mms_source="wcnp", 
             {ns_filter},
            {app_filter},
        }}) by (namespace, container, cluster_id) * 100)
        '''

        if threshold:
            query = f"({query})>{float(threshold)}"

        # Use a very small step size - just get a few data points
        result_series = self.get_query_range_data_v2(query=query, start_time=start_time, end_time=end_time,
                                                     steps=steps,
                                                     enrich_metrics_fn=enrich_metrics_fn, enrich_config=enrich_config)
        if len(result_series) == 0:
            return {}, {}
        return result_series, query

    def get_cluster_traffic(self, namespace=None, app_id=None, steps=60,
                            start_time=None, end_time=None, enrich_metrics_fn=None, enrich_config=None):
        """
        Gets traffic metrics aggregated by cluster_id using the WCNP query pattern
        """
        logger.info(f"Pulling app {namespace}/{app_id} pods traffic details from "
                    f"{WMTDateTime.epoch_to_pst_datetime(start_time)}"
                    f" to {WMTDateTime.epoch_to_pst_datetime(end_time)}")
        if not end_time or not start_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(3)

        def build_promql_capture_regex(app_ids, namespaces):
            app_part = "|".join(app_ids) if isinstance(app_ids, list) else app_ids
            ns_part = "|".join(namespaces) if isinstance(namespaces, list) else namespaces
            return f"outbound.*({app_part}).*({ns_part}).svc.cluster.local"

        pattern = build_promql_capture_regex(app_ids=app_id, namespaces=namespace)
        # Use the wcnp_metric_handler query pattern for traffic
        query = f'''
            sum by (cluster_id, app, namespace) (
                label_replace(
                    label_replace(
                        envoy_cluster_upstream_rq:sum_rate2m{{cluster_name=~"{pattern}",cluster_name!~".*canry.*|.*cnry.*|.*canary.*"}} * 60,
                        "app", "$1", "cluster_name", "{pattern}"
                    ),
                    "namespace", "$2", "cluster_name", "{pattern}"
                )
             )
            '''

        result_series = self.get_query_range_data_v2(query=query, start_time=start_time, end_time=end_time,
                                                     steps=steps,
                                                     enrich_metrics_fn=enrich_metrics_fn, enrich_config=enrich_config)
        if len(result_series) == 0:
            return {}, {}
        return result_series, query

    def analyze_capacity(self, namespace, app_id, county, days, duration_minutes=6,
                         baseline_threshold_cpu=None,
                         baseline_threshold_mem=None,
                         cpu_deviation_threshold=30,
                         mem_deviation_threshold=30):
        results = list()  # Ensure this remains a list
        metric_results = list()
        futures = list()
        for day in days:
            logger.info(f"Async processing get_metrics started for day_{day}")
            # day_results = self.get_metrics_by_cluster(namespace, app_id, day, county)
            pool = ThreadPoolExecutor(len(days))
            futures.append(pool.submit(self.get_metrics_by_cluster, namespace, app_id, day, county))
        for future in futures:
            try:
                day_results = future.result()
                day = f"day_{day_results['day']}"
                logger.info(f"Processing finished for {day}")
                metric_results.append({"values": day_results, "day": day})
            except Exception as e:
                pass
            # metric_results.append({"values": day_results, "day": f"day_{day}"})

        for _data in metric_results:
            self.convert_data_to_pd(_data)
            analysis_data, day = Prometheus.analyze_and_get_min_pods(_data)
            insights = analyze_utilization_insights(_data["values"]["peak"]["cpu_data"],
                                                    memory_data=_data["values"]["peak"]["memory_data"],
                                                    duration_minutes=duration_minutes,
                                                    baseline_threshold_cpu=baseline_threshold_cpu,
                                                    baseline_threshold_mem=baseline_threshold_mem,
                                                    cpu_deviation_threshold=cpu_deviation_threshold,
                                                    mem_deviation_threshold=mem_deviation_threshold)
            # Append the dictionary to the results list
            results.append({"day": day, "analysis_data": analysis_data, "insights": insights})

        return results

    @staticmethod
    def analyze_and_get_min_pods(data):
        raw_data = data.get("values")
        analysis_data = analyze_peak_and_offpeak_per_cluster(
            cpu_peak_data=raw_data["peak"]["cpu_data"], memory_peak_data=raw_data["peak"]["memory_data"],
            tps_peak_data=raw_data["peak"]["traffic_data"], pod_peak_data=raw_data["peak"]["pods_data"],
            cpu_off_data=raw_data["offpeak"]["cpu_data"], memory_off_data=raw_data["offpeak"]["memory_data"],
            tps_off_data=raw_data["offpeak"]["traffic_data"], pod_off_data=raw_data["offpeak"]["pods_data"]
        )
        return analysis_data, data["day"]

    def convert_data_to_pd(self, data):
        raw_data = data.get("values")
        results = defaultdict(dict)
        extra_peak_metadata = {"day": data["day"],
                               # "traffic": "peak"
                               }
        extra_off_peak_metadata = {"day": data["day"],
                                   # "traffic": "offpeak"
                                   }
        results["peak"]["cpu_data"] = self.prometheus_json_to_dataframe(raw_data["peak"]["cpu_data"], metric_name="cpu",
                                                                        extra_metadata=extra_peak_metadata)
        results["peak"]["memory_data"] = self.prometheus_json_to_dataframe(raw_data["peak"]["memory_data"],
                                                                           metric_name="memory",
                                                                           extra_metadata=extra_peak_metadata)
        results["peak"]["traffic_data"] = self.prometheus_json_to_dataframe(raw_data["peak"]["traffic_data"],
                                                                            metric_name="traffic",
                                                                            extra_metadata=extra_peak_metadata)
        results["peak"]["pods_data"] = self.prometheus_json_to_dataframe(raw_data["peak"]["pods_data"],
                                                                         metric_name="pods",
                                                                         extra_metadata=extra_peak_metadata)
        results["offpeak"]["cpu_data"] = self.prometheus_json_to_dataframe(raw_data["offpeak"]["cpu_data"],
                                                                           metric_name="cpu",
                                                                           extra_metadata=extra_off_peak_metadata)
        results["offpeak"]["memory_data"] = self.prometheus_json_to_dataframe(raw_data["offpeak"]["memory_data"],
                                                                              metric_name="memory",
                                                                              extra_metadata=extra_off_peak_metadata)
        results["offpeak"]["traffic_data"] = self.prometheus_json_to_dataframe(raw_data["offpeak"]["traffic_data"],
                                                                               metric_name="traffic",
                                                                               extra_metadata=extra_off_peak_metadata)
        results["offpeak"]["pods_data"] = self.prometheus_json_to_dataframe(raw_data["offpeak"]["pods_data"],
                                                                            metric_name="pods",
                                                                            extra_metadata=extra_off_peak_metadata)

        return results

    def get_metrics_by_cluster(self, namespace, app_id, day, county):
        time_ranges = get_peak_and_offpeak_range(day, county)
        results = {}
        futures = list()
        pool = ThreadPoolExecutor(2)
        futures.append(pool.submit(self._get_metrics, namespace=namespace, app_id=app_id,
                                   start_time=time_ranges["peak"][0], end_time=time_ranges["peak"][1],
                                   traffic_type="peak"))
        futures.append(pool.submit(self._get_metrics, namespace=namespace, app_id=app_id,
                                   start_time=time_ranges["offpeak"][0], end_time=time_ranges["offpeak"][1],
                                   traffic_type="offpeak"))
        for future in futures:
            try:
                day_results = future.result()
                traffic_type = day_results["traffic_type"]
                results.update({traffic_type: day_results})
            except Exception as e:
                pass

        results.update({"day": day})
        return results

    def get_all_metrics(self, namespace, app_id, start_time, end_time):
        results, cpu_query = self.get_cluster_cpu_utilization(
            namespace=namespace,
            app_id=app_id,
            steps=60,
            threshold=None,
            start_time=start_time,
            end_time=end_time
        )

        response2 = extract_series_within_range(results, 1745449049, 1745450505)

        return response

    def summarize_runner_pods(self, namespace, app_id, start_time, end_time):
        pods_raw_data = self.get_running_pods_count_recent(
            namespace=namespace,
            app_label=app_id,
            start_time=start_time,
            end_time=end_time
        )
        return {"pods_data": pods_raw_data, "namespace": namespace, "app": app_id}

    def _get_metrics(self, namespace, app_id, start_time, end_time, traffic_type):
        """
        Gets all metrics by cluster_id with proper time ranges

        Args:
            namespace: Kubernetes namespace
            app_id: Application ID
            days: Number of days of history to include (1, 7, 14 etc)
            nightly_only: If True, only include night hours (10PM-6AM)
        """

        insights = {}
        # Now get metrics with proper time range
        cpu_raw_data = self.get_cluster_cpu_utilization(
            namespace=namespace,
            app_id=app_id,
            start_time=int(start_time),
            end_time=int(end_time)
        )
        memory_raw_data = self.get_cluster_memory_utilization(
            namespace=namespace,
            app_id=app_id,
            start_time=start_time,
            end_time=end_time
        )
        traffic_raw_data = self.get_cluster_traffic(
            namespace=namespace,
            app_id=app_id,
            start_time=start_time,
            end_time=end_time
        )

        pods_raw_data = self.get_running_pods_count(
            namespace=namespace,
            app_id=app_id,
            start_time=start_time,
            end_time=end_time
        )
        # pods, pods_raw_data = 0, []
        return {
            "cpu_data": cpu_raw_data,
            "memory_data": memory_raw_data,
            "pods_data": pods_raw_data,
            "traffic_type": traffic_type,
            "traffic_data": traffic_raw_data,
            "start_time": start_time,
            "end_time": end_time
        }

    def get_traffic_utilization(self, namespace=None, app_id=None, steps=60,
                                start_time=None, end_time=None):
        """
        Gets average traffic metrics grouped by app name, namespace and cluster_id
        """
        # Get the raw data
        results, query = self.get_cluster_traffic(
            namespace=namespace,
            app_id=app_id,
            steps=steps,
            start_time=start_time,
            end_time=end_time
        )
        logger.debug(f"traffic_utilization query {query} for namespace {namespace}")
        if not results or 'data' not in results or 'result' not in results['data']:
            return {}

        # Process the raw data to get averages
        # results['data']['result']
        return results

    def get_cpu_utilization(self, namespace=None, app_id=None, steps=60,
                            threshold=None, start_time=None, end_time=None):
        """
        Gets average CPU utilization grouped by app name, namespace and cluster_id
        """
        logger.info(f"Pulling app {namespace}/{app_id} pods CPU utilization from "
                    f"{WMTDateTime.epoch_to_pst_datetime(start_time)}"
                    f" to {WMTDateTime.epoch_to_pst_datetime(end_time)}")
        # Get the raw data
        results, cpu_query = self.get_cluster_cpu_utilization(
            namespace=namespace,
            app_id=app_id,
            steps=steps,
            threshold=threshold,
            start_time=start_time,
            end_time=end_time
        )
        logger.debug(f"cpu_query {cpu_query} for namespace {namespace}")

        if not results or 'data' not in results or 'result' not in results['data']:
            return {}

        # Process the raw data to get averages by multiple dimensions
        # results['data']['result']
        return results

    def get_memory_utilization(self, namespace=None, app_id=None, steps=60,
                               threshold=None, start_time=None, end_time=None):
        """
        Gets average memory utilization grouped by app name, namespace and cluster_id
        """
        logger.info(f"Pulling app {namespace}/{app_id} pods memory utilization from "
                    f"{WMTDateTime.epoch_to_pst_datetime(start_time)}"
                    f" to {WMTDateTime.epoch_to_pst_datetime(end_time)}")
        # Get the raw data
        results, query = self.get_cluster_memory_utilization(
            namespace=namespace,
            app_id=app_id,
            steps=steps,
            threshold=threshold,
            start_time=start_time,
            end_time=end_time
        )
        logger.debug(f"memory_query {query} for namespace {namespace}")
        if not results or 'data' not in results or 'result' not in results['data']:
            return {}

        # Process the raw data to get averages by multiple dimensions
        # results['data']['result']
        return results

    def enrich_metrics_with_mapping(self, series_list: List[dict],
                                    mapping_list: Optional[List[dict]] = None,
                                    match_keys: Tuple[str, ...] = ("namespace",),
                                    infer_mapping: Optional[Dict[str, str]] = None,
                                    target_mapping: Optional[Dict[str, str]] = None,
                                    add_fields: Optional[Tuple[str, ...]] = None,
                                    extra_mapping: Optional[Dict[str, Any]] = None,
                                    compress_categories: bool = True,
                                    category_fields: Optional[List[str]] = None,
                                    return_dataframe: bool = False,
                                    ) -> Union[pd.DataFrame, List[dict]]:
        """
        Normalize Prometheus-style JSON data into a clean, enriched Pandas DataFrame.

            This utility transforms Prometheus `range_query` responses into a flat structure
            suitable for machine learning or analytics. It supports mapping, enrichment, field renaming,
            category compression, and static metadata injection.

            Parameters
            ----------
            series_list : List[dict]
                List of Prometheus series results. Each item must have a "metric" dict and a "values" list.
                Example:
                    [
                        {
                            "metric": {"namespace": "ns1", "container": "checkout"},
                            "values": [[1680000000, "0.5"], [1680000060, "0.6"]]
                        },
                        ...
                    ]

            mapping_list : List[dict], optional
                Optional enrichment mapping to add metadata per row (e.g., map namespace to business unit).
                Example:
                    [{"namespace": "ns1", "app_id": "checkout"}, {"namespace": "ns2", "app_id": "orders"}]

            match_keys : Tuple[str], default ("namespace",)
                Keys used to match Prometheus metric labels to entries in `mapping_list`.

            infer_mapping : Dict[str, str], optional
                Auto-fill missing fields by copying existing ones. Format: {source: target}.
                Example: {"container": "app_id"}

            target_mapping : Dict[str, str], optional
                Rename fields after enrichment. Example: {"app_id": "app"}

            add_fields : Tuple[str], optional
                Fields to explicitly pull from mapping_list and inject into metric (if not already present).

            extra_mapping : Dict[str, Any], optional
                Static metadata to be added to each row. Example: {"source": "prometheus", "region": "US"}

            compress_categories : bool, default True
                If True, compress all string columns using Pandas 'category' dtype.

            category_fields : List[str], optional
                If provided, only these columns will be compressed (e.g., ["namespace", "app"]).
            return_dataframe :  true returns dataframe else series_list

            Returns:
            --------
            Union[pd.DataFrame, List[dict]]


            Example Usage
            -------------

            raw_series = [
                {"metric": {"namespace": "sales", "container": "SalesApp"}, "values": [[1680000000, "12.5"]]},
                {"metric": {"namespace": "finance", "container": "FinanceApp"}, "values": [[1680000000, "9.1"]]}
            ]

            mapping_list = [
                {"namespace": "sales", "app_id": "SalesApp"},
                {"namespace": "finance", "app_id": "FinanceApp"}
            ]

            df = normalize_series_for_dataframe(
                series_list=raw_series,
                mapping_list=mapping_list,
                match_keys=("namespace",),
                infer_mapping={"container": "app_id"},
                target_mapping={"app_id": "app"},
                add_fields=("app_id",),
                extra_mapping={"region": "us-east"},
                compress_categories=True
            )

            Output:
            -------
               timestamp  value namespace       app    region
            0 1680000000   12.5     sales   SalesApp  us-east
            1 1680000000    9.1   finance  FinanceApp  us-east

            Example 2: add_fields only
            ===================

            # Inputs:
            series = [{'metric': {'namespace': 'sales', 'container': 'sales-app'}, 'values': [[1710000000, '12.3']]}]
            mapping = [{'namespace': 'sales', 'app_id': 'sales-app', 'business_unit': 'Retail'}]
            add_fields = ('app_id', 'business_unit')

            #Output
                {
                    'namespace': 'sales',
                    'container': 'sales-app',
                    'app_id': 'sales-app',
                    'business_unit': 'Retail'
                }

            Example 2: add_fields + target_mapping
            ===================

            # Inputs:
            series = [{'metric': {'namespace': 'sales', 'container': 'sales-app'}, 'values': [[1710000000, '9.9']]}]
            mapping = [{'namespace': 'sales', 'app_id': 'sales-app'}]
            target_mapping = {'app_id': 'app'}

            #Output
                {
                    'namespace': 'sales',
                    'container': 'sales-app',
                    'app': 'sales-app'  # app_id was renamed to app
                }
            Example 3: add_fields + extra_mapping
            ====================================

            # Inputs:
            series = [{'metric': {'namespace': 'finance'}, 'values': [[1710000000, '15.5']]}]
            mapping = [{'namespace': 'finance', 'app_id': 'finance-app'}]
            extra_mapping = {'env': 'prod', 'region': 'us-east'}

            #Output
                {
                    'namespace': 'finance',
                    'app_id': 'finance-app',
                    'env': 'prod',
                    'region': 'us-east'
                }

            Example 5: infer_mapping, target_mapping, and mapping_list
            ===========================================================

            # Inputs:
            series = [{'metric': {'namespace': 'sales','container': 'sales-app'}, 'values': [[1710000000, '15.5']]}]
            mapping = [{'namespace': 'sales','app_id': 'sales-app'}]

            df = normalize_series_for_dataframe(
                series_list=series,
                mapping_list=mapping_list,
                match_keys=("namespace",),
                infer_mapping={"app_id": "container"},
                target_mapping={"app_id": "app"},
                compress_categories=False,
                return_dataframe=False  # ← to see enriched `series_list`
            )

            #Output
                {'namespace': 'sales', 'container': 'sales-app', 'app': 'sales-app'}

            Example 6: compress_categories
            ==================================

            normalize_series_for_dataframe(
                series_list=series,
                mapping_list=mapping,
                match_keys=("namespace",),
                category_fields=["namespace", "app"]
            )

            Example 7: using add_fields target_mapping extra_mapping
            ===================
            series = [{'metric': {'namespace': 'sales'}, 'values': [[1710000000, '15.5']]}]
            mapping = [{'namespace': 'sales','app_id': 'sales-app'}]


            df = normalize_series_for_dataframe(
                series_list=series,
                mapping_list=mapping_list,
                match_keys=("namespace",),
                add_fields=("app_id",),  # Extract 'app_id' from the mapping
                target_mapping={"app_id": "app"},  # Rename it to 'app'
                extra_mapping={"container": "app"},  # Copy app to container
                return_dataframe=False  # If you want enriched series instead of DF
            )
            #Output
                {'namespace': 'sales', 'container': 'sales-app', 'app': 'sales-app'}

            """

        if not series_list:
            return pd.DataFrame() if return_dataframe else []

        mapping_lookup = {}
        if mapping_list:
            for entry in mapping_list:
                key = tuple(entry.get(k) for k in match_keys)
                mapping_lookup[key] = entry

        enriched_series = []
        enriched_rows = []

        for series in series_list:
            metric = series.get('metric', {})
            values = series.get('values', [])

            # Step 1: Infer fields if mapping given
            if infer_mapping:
                for src_field, tgt_field in infer_mapping.items():
                    if src_field in metric and tgt_field not in metric:
                        metric[tgt_field] = metric[src_field]

            # Step 2: Match and inject from mapping
            key = tuple(metric.get(k) for k in match_keys)
            matched = mapping_lookup.get(key)
            if matched:
                if add_fields:
                    for f in add_fields:
                        if f not in metric and f in matched:
                            metric[f] = matched[f]
                for k, v in matched.items():
                    metric.setdefault(k, v)

            # Step 3: Rename fields
            if target_mapping:
                for old, new in target_mapping.items():
                    if old in metric:
                        metric[new] = metric.pop(old)

            # Step 4: Add static metadata
            if extra_mapping:
                metric.update(extra_mapping)

            enriched_series.append({"metric": metric, "values": values})

            if return_dataframe:
                for ts, val in values:
                    row = {**metric, "timestamp": int(ts), "value": float(val)}
                    enriched_rows.append(row)

        if not return_dataframe:
            return enriched_series

        df = pd.DataFrame(enriched_rows)
        if compress_categories:
            cols_to_compress = category_fields or df.select_dtypes(include="object").columns
            for col in cols_to_compress:
                if col in df.columns:
                    df[col] = df[col].astype("category")

        return df

    def get_running_pods_count(self, namespace, app_id, cluster_ids=None, start_time=None, end_time=None,
                               steps=60, enrich_metrics_fn=None, enrich_config=None):
        """
        Gets running pods count using regex pattern for app labels
        """
        # Get last 3 minutes only
        if not end_time or not start_time:
            end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(3)

        logger.info(f"Pulling app {namespace}/{app_id} pods counts from "
                    f"{WMTDateTime.epoch_to_pst_datetime(start_time)}"
                    f" to {WMTDateTime.epoch_to_pst_datetime(end_time)}")
        # Use the existing method to get all cluster IDs if not provided
        if not cluster_ids:
            # cluster_ids = self.get_wcnp_all_cluster_ids(namespace, app_id)
            cluster_ids = self.get_pods_state_v2(namespace, app_id, extract_cluster_id=True)

        ns_filter = Prometheus.build_promql_label_filter("namespace", namespace)
        cluster_ids_filter = Prometheus.build_promql_label_filter("cluster_id", cluster_ids)
        pod_filter = Prometheus.build_promql_label_regex_filter("pod", app_id)
        query = f'''
            sum(kube_pod_status_phase{{
               mms_source="wcnp",{cluster_ids_filter}, 
               {ns_filter},{pod_filter},
               phase=~"Running"
            }}
            and on(pod) kube_pod_labels{{
               mms_source="wcnp",{cluster_ids_filter}, 
               {pod_filter},{ns_filter}
            }}
            and on (pod) (increase(kube_pod_container_status_restarts_total{{
               mms_source="wcnp",{cluster_ids_filter}, 
               {pod_filter}, {ns_filter}
            }}[2m]) == 0)) by (cluster_id,namespace)
            '''

        try:
            # Use a very small step size - just get a few data points
            result_series = self.get_query_range_data_v2(query=query, start_time=start_time, end_time=end_time,
                                                         steps=steps,
                                                         enrich_metrics_fn=enrich_metrics_fn,
                                                         enrich_config=enrich_config)
            if len(result_series) == 0:
                return {}, {}
            return result_series, query
        except Exception as e:
            logger.error(f"Error fetching pod counts: {str(e)}")
            return {}, {}

    def filter_metrics_by_wcnp_labels(self, series_data, namespace, app=None, container=None, **kwargs):
        """
        Filters Prometheus time series by exact label matches.

        :param series_data: Output from extract_series_within_range (list of dicts)
        :param namespace: Optional namespace to match
        :param app: Optional app name to match
        :param container: Optional container name to match
        :return: Filtered list of time series dicts
        """

        def matches(metric):
            return (
                    (namespace is None or metric.get("namespace") == namespace) and
                    (app is None or metric.get("app") == app) and
                    (container is None or metric.get("container") == container)
            )

        return [series for series in series_data if matches(series["metric"])]

    def filter_metrics_by_labels(self, series_data, namespace=None, app=None, container=None):
        """
        Filters Prometheus time series by exact label matches.

        :param series_data: Output from extract_series_within_range (list of dicts)
        :param namespace: Optional namespace to match
        :param app: Optional app name to match
        :param container: Optional container name to match
        :return: Filtered list of time series dicts
        """

        def matches(metric):
            return (
                    (namespace is None or metric.get("namespace") == namespace) and
                    (app is None or metric.get("app") == app) and
                    (container is None or metric.get("container") == container)
            )

        return [series for series in series_data if matches(series["metric"])]

    @staticmethod
    def prometheus_json_to_dataframe(prometheus_results, metric_name=None, metric_mapping=None,
                                     compress_categories=True, extra_metadata=None):
        """
        Converts Prometheus range query JSON results into a pandas DataFrame with optional metadata enrichment.

        Args:
            prometheus_results (list): List of result dicts from Prometheus response.
            metric_name (str, optional): Metric name to rename 'value' column to. E.g., 'cpu', 'memory', 'traffic'.
            metric_mapping (dict, optional): Mapping like {'container': 'app_id'} to remap metric fields.
            compress_categories (bool): Whether to convert string columns to 'category' dtype.
            extra_metadata (dict, optional): Extra static metadata key-values to add to every row.

        Returns:
            pd.DataFrame: A DataFrame with merged metadata and proper metric columns.
        """
        rows = []

        for result in prometheus_results:
            metric_info = result.get('metric', {})
            values = result.get('values', [])

            # Base metadata extraction
            cluster_id = metric_info.get('cluster_id')
            namespace = metric_info.get('namespace')
            container = metric_info.get('container') or metric_info.get('pod')  # sometimes pod/container
            app = metric_info.get('app', container)  # Prefer 'app', fallback to 'container'

            # Apply metric mapping if provided
            if metric_mapping:
                for original_key, new_key in metric_mapping.items():
                    if original_key in metric_info:
                        locals()[new_key] = metric_info[original_key]

            for timestamp, value in values:
                row = {
                    'timestamp': int(timestamp),
                    'cluster_id': cluster_id,
                    'namespace': namespace,
                    'app_id': app,
                    (metric_name if metric_name else 'value'): float(value)
                }

                if extra_metadata:
                    row.update(extra_metadata)

                rows.append(row)

        df = pd.DataFrame(rows)

        if compress_categories:
            for col in ['cluster_id', 'namespace', 'app_id']:
                if col in df.columns:
                    df[col] = df[col].astype('category')

        return df


class Analysis(Prometheus):
    def __init__(self, apps: list, days: int, country: str, **kwargs):
        """
        Initializes the Analysis class with namespace, app_id, and time range.

        Args:
            apps (list): Kubernetes namespace.
            days (int): Number of days to analyze.
            country (str): Country for peak/off-peak range.
            **kwargs: Additional arguments for the Prometheus parent class.
        """
        super().__init__(**kwargs)
        logger.info("Initializing Analysis class")
        self.end_time = int(time.time())
        buffer_hours = 17 * 60 * 60  # To utilize 2 days promQL response adding more buffer
        self.start_time = self.end_time - ((days * 86400) + buffer_hours)
        self.apps = apps
        self.namespace = [app.get("namespace") for app in apps]
        self.app_id = [app.get("app") for app in apps]
        self.days_meta = dict()
        logger.info(f"self.start_time {self.start_time}")
        logger.info(f"self.end_time {self.end_time}")
        for num in range(1, days + 1):
            ranges = get_peak_and_offpeak_range(num, country=country)
            logger.info(f"For day {num} time ranges {ranges}")
            logger.info(f"Total self.start_time {self.start_time} self.end_time {self.end_time}")
            if check_range_in_bounds(ranges, (self.start_time, self.end_time)):
                self.days_meta.update({num: ranges})

        logger.info("Analysis class initialized with days_meta: %s", self.days_meta)
        self.cpu_results: dict = {}
        self.memory_results: dict = {}
        self.traffic_results: dict = {}
        self.pods_results: dict = {}
        self.is_fetch_finished = False

    def fetch(self):
        """
        Fetches CPU, memory, traffic, and pod metrics for the specified namespace and app_id.
        """
        if len(self.days_meta) == 0:
            return
        logger.info("Fetching metrics for namespaces: %s and app_ids: %s", self.namespace, self.app_id)
        logger.info("Fetching metrics for namespaces: %s and app_ids: %s", self.namespace, self.app_id)

        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = {
                "cpu": executor.submit(
                    self.get_cluster_cpu_utilization,
                    namespace=self.namespace,
                    app_id=self.app_id,
                    steps=60,
                    threshold=None,
                    start_time=self.start_time,
                    end_time=self.end_time,
                    enrich_metrics_fn=self.enrich_metrics_with_mapping,
                    enrich_config={
                        "mapping_list": self.apps,
                        "infer_mapping": {'container': 'app_id'},
                        "target_mapping": {'app_id': 'app'},
                        "match_keys": ("namespace", "container",),
                        "return_dataframe": False,
                        "compress_categories": False
                    }
                ),
                "memory": executor.submit(
                    self.get_cluster_memory_utilization,
                    namespace=self.namespace,
                    app_id=self.app_id,
                    steps=60,
                    threshold=None,
                    start_time=self.start_time,
                    end_time=self.end_time,
                    enrich_metrics_fn=self.enrich_metrics_with_mapping,
                    enrich_config={
                        "mapping_list": self.apps,
                        "infer_mapping": {'container': 'app_id'},
                        "target_mapping": {'app_id': 'app'},
                        "match_keys": ("namespace", "container",),
                        "return_dataframe": False,
                        "compress_categories": False
                    }
                ),
                "traffic": executor.submit(
                    self.get_cluster_traffic,
                    namespace=self.namespace,
                    app_id=self.app_id,
                    steps=60,
                    start_time=self.start_time,
                    end_time=self.end_time

                ),
                "pods": executor.submit(
                    self.get_running_pods_count,
                    namespace=self.namespace,
                    app_id=self.app_id,
                    steps=60,
                    start_time=self.start_time,
                    end_time=self.end_time,
                    enrich_metrics_fn=self.enrich_metrics_with_mapping,
                    enrich_config={
                        "mapping_list": self.apps,
                        "add_fields": ("app_id",),
                        "target_mapping": {'app_id': 'app'},
                        "extra_mapping": {"container": "app"},
                        "match_keys": ("namespace",),
                        "return_dataframe": False,
                        "compress_categories": False
                    }
                )
            }

        self.cpu_results, cpu_query = futures["cpu"].result()
        logger.info("Fetched CPU metrics with query: %s", cpu_query)

        self.memory_results, memory_query = futures["memory"].result()
        logger.info("Fetched memory metrics with query: %s", memory_query)

        self.traffic_results, traffic_query = futures["traffic"].result()
        logger.info("Fetched traffic metrics with query: %s", traffic_query)

        self.pods_results, pods_query = futures["pods"].result()
        logger.info("Fetched pods metrics with query: %s", pods_query)
        self.is_fetch_finished = True

    def testing_timeragera(self, namespace, app, day):
        logger.info("Testing timerange for namespace: %s, app: %s, day: %d", namespace, app, day)
        return {"day": day}

    def analyze(self, duration_minutes=6, baseline_threshold_cpu=None, baseline_threshold_mem=None,
                cpu_deviation_threshold=30, mem_deviation_threshold=30):

        if not self.is_fetch_finished:
            logger.error("Metrics fetching not finished. Please call fetch() before analyze().")
            return []

        logger.info("Starting analysis with duration_minutes: %d", duration_minutes)
        results = []
        metric_results = []
        with ThreadPoolExecutor(len(self.days_meta)) as pool:

            futures = []
            for day in range(1, len(self.days_meta) + 1):
                logger.info(f"Fetching day {day} metrics")
                for app in self.apps:
                    if app.get("namespace") and app.get("app"):
                        logger.info(f"Fetching day {day} metrics for {app}")
                        future = pool.submit(self.filter_metrics, app.get("namespace"), app.get("app"), day)
                        futures.append(future)

            for future in as_completed(futures):
                try:
                    day_results = future.result()
                    day = f"day_{day_results['day']}"
                    logger.info("Processing finished for %s", day)
                    metric_results.append({"values": day_results, "day": day})
                except Exception as e:
                    logger.error("Error processing metrics: %s", e)
                    logger.exception(e)
        for _data in metric_results:
            analysis_data, day = Prometheus.analyze_and_get_min_pods(_data)
            dat = self.convert_data_to_pd(_data)
            merged_df,final_peak_df,offpeak_df, current_pods_df = prepare_training_inputs_from_prometheus_data(dat)
            merged_df["final_recommended_min_pods"] = merged_df.apply(calculate_final_recommended_pods, axis=1)
            ml_results = orchestrate_by_deployment(merged_df,model_dir=os.path.join(TEMPLATE_OUTPUT_DIR,"models"))

            insights = analyze_utilization_insights(
                _data["values"]["peak"]["cpu_data"],
                memory_data=_data["values"]["peak"]["memory_data"],
                duration_minutes=duration_minutes,
                baseline_threshold_cpu=baseline_threshold_cpu,
                baseline_threshold_mem=baseline_threshold_mem,
                cpu_deviation_threshold=cpu_deviation_threshold,
                mem_deviation_threshold=mem_deviation_threshold,
            )
            _namespace = _data.get("values", {}).get("peak", {}).get("namespace")
            _app_id = _data.get("values", {}).get("peak", {}).get("app_id")
            results.append({"day": day, "analysis_data": analysis_data, "insights": insights,
                            "namespace": _namespace,
                            "app_id": _app_id})
            for result in results:
                for cluster,cluster_data in result.get("analysis_data",{}).items():
                    key =f"{_namespace}||{_app_id}||{cluster}"
                    cluster_data["ml_suggested_min_hpa"] = ml_results.get(key, {}).get('suggested_min_hpa')
        logger.info("Analysis completed with results: %s", results)
        return results

    def filter_metrics(self, namespace, app_id, day):
        logger.info("Filtering metrics for namespace: %s, app_id: %s, day: %d", namespace, app_id, day)
        time_ranges = self.days_meta.get(day)
        logger.info(f"Day {day} Time rages are {time_ranges} ")
        logger.info(f"self.days_meta {self.days_meta} ")
        results = {}
        futures = list()
        pool = ThreadPoolExecutor(2)
        futures.append(pool.submit(self.filter_traffic_type_metrics, namespace=namespace, app_id=app_id,
                                   start_time=time_ranges["peak"][0], end_time=time_ranges["peak"][1],
                                   traffic_type="peak"))
        futures.append(pool.submit(self.filter_traffic_type_metrics, namespace=namespace, app_id=app_id,
                                   start_time=time_ranges["offpeak"][0], end_time=time_ranges["offpeak"][1],
                                   traffic_type="offpeak"))
        for future in futures:
            try:
                day_results = future.result()
                traffic_type = day_results["traffic_type"]
                results.update({traffic_type: day_results})
            except Exception as e:
                logger.exception("Error filtering metrics: %s", e)

        results.update({"day": day})
        logger.info("Filtered metrics for day %d:", day)
        return results

    def filter_traffic_type_metrics(self, namespace, app_id, start_time, end_time, traffic_type):
        """
        Filters metrics for a specific namespace and app_id within a given time range.

        Args:
            namespace: Kubernetes namespace
            app_id: Application ID
            start_time: Start time in epoch seconds
            end_time: End time in epoch seconds
            traffic_type: Type of traffic (peak or offpeak)
        """
        logger.info("Filtering %s metrics for namespace: %s, app_id: %s, start_time: %d, end_time: %d",
                    traffic_type, namespace, app_id, start_time, end_time)

        # _cpu_chunk_data = extract_series_within_range(self.cpu_results, start_time, end_time)
        # cpu_raw_data = self.filter_metrics_by_labels(_cpu_chunk_data, namespace, container=app_id)

        cpu_raw_data = extract_series_within_range_V2(series_data=self.cpu_results, start_time=start_time,
                                                      end_time=end_time,
                                                      filter_fn=self.filter_metrics_by_wcnp_labels,
                                                      namespace=namespace, container=app_id)
        memory_raw_data = extract_series_within_range_V2(series_data=self.memory_results, start_time=start_time,
                                                         end_time=end_time,
                                                         filter_fn=self.filter_metrics_by_wcnp_labels,
                                                         namespace=namespace, container=app_id)
        traffic_raw_data = extract_series_within_range_V2(series_data=self.traffic_results, start_time=start_time,
                                                          end_time=end_time,
                                                          filter_fn=self.filter_metrics_by_wcnp_labels,
                                                          namespace=namespace, app=app_id)
        pods_raw_data = extract_series_within_range_V2(series_data=self.pods_results, start_time=start_time,
                                                       end_time=end_time,
                                                       filter_fn=self.filter_metrics_by_wcnp_labels,
                                                       namespace=namespace)

        logger.info("Filtered %s metrics for namespace: %s, app_id: %s", traffic_type, namespace, app_id)
        return {
            "cpu_data": cpu_raw_data,
            "memory_data": memory_raw_data,
            "pods_data": pods_raw_data,
            "traffic_type": traffic_type,
            "traffic_data": traffic_raw_data,
            "start_time": start_time,
            "end_time": end_time,
            "namespace": namespace,
            "app_id": app_id
        }


if __name__ == "__main__":
    log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    log_format = '%(asctime)s %(filename)18s %(funcName)25s %(lineno)3d %(levelname)10s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(log_level)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)
    p = Prometheus()
    # print(p.get_cluster_avg_cpu_utilization(namespace="ca-around-me",app_id="aroundme"))
    # print(p.get_cluster_avg_memory_utilization(namespace="ca-around-me",app_id="aroundme"))

    # print(p.get_running_pods_count_recent(namespace="ca-content-layout-service",app_label="content-layout-service-prod-primary"
    # print(p.get_cluster_avg_cpu_utilization(namespace="ca-content-layout-service",app_id="content-layout-service-prod-primary"))
    # print(p.get_metrics_by_cluster(namespace="mx-glass", app_id="mexico-ea-journey-prod", day=1))
    # p.analyze_capacity(namespace="mx-glass", app_id="mexico-ea-journey-prod", county="mexico")
    days = [1, 2, 7, 14, 19]
    # p.analyze_capacity(namespace="sct-rap", app_id="rap-orchestration-prod-tg2-async", county="mexico", days=days)
    # p.get_all_alerts(n_minutes_data=1400)
    # time_ranges = get_peak_and_offpeak_range(1, "mexico")
    # start = 1745451505
    # end = 1745448049
    # p.get_all_metrics(namespace="sct-rap", app_id="rap-orchestration-prod-tg2-async",
    #                   start_time=end, end_time=start)
    apps = [#{"namespace": "sct-rap", "app": "rap-orchestration-prod-tg2-async"},
            {"namespace": "mx-glass", "app": "mexico-ea-journey-prod"}]
    analyze = Analysis(apps=apps, days=2, country="mexico")
    analyze.fetch()
    analyze.analyze()

    # print(p.get_metrics_by_cluster(namespace="ca-content-layout-service",app_id="content-layout-service-prod-primary",days=7,nightly_only=True))
    # query = '''sum(
    # rate(container_network_receive_bytes_total{mms_source="wcnp", cluster_id=~"scus-prod-a50", namespace=~"rollups-read"}[2m])
    # and on(pod) kube_pod_labels{mms_source="wcnp", cluster_id=~"scus-prod-a50", label_app=~"unified-rollups-read-prod-primary", namespace=~"rollups-read"}
    # ) by (namespace, pod)'''
    # query = '''round(sum(envoy_cluster_upstream_rq:sum_rate2m{cluster_name="outbound|4000||aroundme-prod.ce-around-me-gql.svc.cluster.local", response_code_class=~".*"}) by (response_code_class, cluster_name, namespace), 0.001)'''
    # query = '''round(sum(envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~"outbound.*limo-read-nsf-prod-cnry.csis-limo-oneapp-read.svc.cluster.local"}) by (cluster_id), 0.001)'''
    # query = '''round(sum(rate(envoy_cluster_upstream_rq{cluster_name=~"outbound.*limo-read-nsf-prod-cnry.csis-limo-oneapp-read.svc.cluster.local", response_code_class=~".*", cluster_id=~".*"}[2m])) by (response_code_class, app, namespace, cluster_id), 0.001)'''
    # response = requests.get('http://prometheus-query.prod.mms.walmart.net/api/v1/query_range',
    #                             params={'query': query, 'start': 1631647470, 'end': 1631647770, 'step': 15})
    # data = response.json()
    # print(data)
    #
    # query = '''envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~".*aroundme-prod.ce-around-me-gql.svc.cluster.local"}'''
    # response = requests.get('http://prometheus-query.prod.mms.walmart.net/api/v1/series',
    #                         params={'match[]': query, 'start': 1631555895, 'end': 1631556195})
    # data = response.json()
    # print(data)
    import pprint

    # p = Prometheus()
    # data = p.get_remote_write_limit(namespace="mx-glass")
    # print(data)
    # query = '''(
    #         (
    #                   sum(
    #                         avg_over_time(
    #                                     envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~"outbound.*ewallet-service-prod.*.ewallet-service.svc.cluster.local", cluster_id=~".*"}[10m]
    #                                     )
    #                        )
    #
    #             /
    #
    #                     sum(
    #                         avg_over_time(
    #                                     envoy_cluster_upstream_rq:sum_rate2m{cluster_name=~"outbound.*ewallet-service-prod.*.ewallet-service.svc.cluster.local",  cluster_id=~".*"} [10m] offset 1w
    #                                     )
    #                         )
    #
    #         ) - 1
    #
    #     ) *100 >100'''
    # # end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(21000)
    # auery = ""
    # res = p.get_query_range_data(query=query, start_time=start_time, end_time=end_time, steps=1)
    # import pprint
    # pprint.pprint(res)
    # p.get_pods_cpu_utilization('locationservice-app','locationservice-app',steps=60)
    # p.get_tps_by_rc_codes("","",True)
    # p.finding_series_by_label_matchers(query, stat_time=1631555895, end_time=1631556195)
    # p.get_envoy_rc_code_cluster_name("rollups-read", "unified-rollups-read-prod-primary", 1631555895, 1631556195)

    # end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(1)
    # print(f"start_time {start_time},end_time {end_time}")
    # # results = p.get_app_stats("perceive-gm", "perceive-server-prod", start_time=start_time,
    # #                           end_time=end_time)
    # # p_results = p.get_pods_stats("perceive-gm", "perceive-server-prod", start_time=start_time,
    # #                              end_time=end_time)
    # p_results = p.get_pods_cpu_utilization("perceive-gm", "perceive-server-prod", start_time=start_time, end_time=end_time)
    #
    #
    # #pprint.pprint(results)
    # pprint.pprint(p_results)Manjul
    #
    # # p.get_pods_restart("perceive-gm", "perceive-server-prod")
    # # p.get_all_app_names_and_cluster_ids("csis-limo-oneapp-read", "limo-read-sf-txn-prod-primary")
    # # p.get_throughput("csis-limo-oneapp-read", "limo-read-sf-txn-prod-primary", cluster_id="eus2-prod-a21")
    # # logger.info(p.get_all_dashboards("polaris-gm","polaris-usgm-prod-primary"))
    # # logger.info(p.get_all_app_names_and_cluster_ids("item-service"))
    # data1 = p.get_all_app_names_and_cluster_ids(namespace="item-service-mx-gql")
    # data = p.get_namespace_details(namespace="mx-glass")
    # print(data)
    # print(len(data))
    # d = p.process_gslb_ingress_traffic("iro-prod.item-assembler.k8s.glb.us.walmart.net")
    # d2 = p.process_gslb_egress_traffic("iro-prod.item-assembler.k8s.glb.us.walmart.net")
    # data = p.create_sre_cosmos_db_alert("gamora-ias-prod-azure-cosmosdb")
    # print(data)
    # #p.get_resource_groups("sp-managed-cosmos-prod-02")
    # print()
    # d2 = p.finding_series_by_label_matchers(query='n_cpus{oot="mexicoecomm"}', start_time=1697839208,
    #                                         end_time=1697842808)
    # d = p.get_query_range_data(query='avg(n_cpus{oot="mexicoecomm"}) by (ooa,oop,ooe,dc)', start_time=1697839208,
    #                            end_time=1697842808, steps=2)
    # d = p.get_all_assemblies_platforms_env("mexicoecomm")
    # d = p.get_all_oneops_dashboards("mexicoecomm", "ATSupport")
    # print(d)
    # data = p.get_all_alerts(namespace="sct-vulcan-ca", tier="one")
    # print(data)
    '''
    sum(kube_pod_status_phase{mms_source="wcnp", namespace="mx-pno",label_app!="helm",label_app!="highlander",
    label_app!="prometheus",label_app!~".*-inta-.*|.*-intb-.*|.*canary.*|.*default-tester|.*-inta|.*-intb|.*-nsf-.*"}) 
    by (phase,cluster_id,instance,label_app,label_mls_index,label_mls_cluste)
    '''

    # def test():
    #     p = Prometheus()
    #
    #     wcnp1 = p.wcnp_get_wcnp_5xx(tier="one",market="CA")
    #     alerts_data = p.get_all_wcnp_alerts(namespace=".*",tier="one",market="CA",tool="juno")
    #     alerts = alerts_data["data"]["result"]
    #     print(alerts[0])
    #     final  = [i for i in alerts if i['metric']['alertstate'] == 'firing']
    #     oneops = [i for i in final if "oneops" in i['metric']['alert_component']]
    #     wcnp = [i for i in final if "wcnp" in i['metric']['alert_component']]
    #     #print(len(final))
    #
    #     print(len(wcnp))
    #
    #     for i in wcnp:
    #         #print(i['metric'].items())
    #         print(i['metric']['alertname'])
    #         print(i['metric']['alert_component'])
    #         print(i['metric']['namespace'])
    #         print(i['metric']['app_name'])
    #
    #     for i in wcnp1:
    #         #print(i['metric'].items())
    #         print(i)
