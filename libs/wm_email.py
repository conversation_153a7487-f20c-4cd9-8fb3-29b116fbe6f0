#! /usr/bin/env python

"""
To send email, use this script. Before running the script , host should have smtp server running.
This script works in any walmart servers.

If you want to send email from your local, start smtp server (by default python has it), below is the command

python -m smtpd -c DebuggingServer -n localhost:1025
"""
__author__ = 'Madhu Cheepati'

import logging
import mimetypes
import os
import queue
import smtplib
import threading
import time
from threading import RLock
from email import encoders
from email.mime.audio import MIMEAudio
from email.mime.base import MIMEBase
from email.mime.image import MIMEImage
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

logger = logging.getLogger(__name__)
SENDER = 'SRE Alert Report <<EMAIL>>'
MAILSERVER = "smtp.wal-mart.com"


class EmailSender(object):
    lock = RLock()

    class ServiceTask:
        def __init__(self, recipient, subject, body, attachment=None, directory=None):
            self.recipient = recipient
            self.subject = subject
            self.body = body
            self.attachment = attachment
            self.directory = directory

    def __new__(cls):
        cls.lock.acquire()
        if not hasattr(cls, 'instance'):
            cls.instance = super(EmailSender, cls).__new__(cls)
            cls.task_queue = queue.Queue()
            cls.working_thread = threading.Thread(target=cls.__working_thread, daemon=True).start()
        cls.lock.release()
        return cls.instance

    @classmethod
    def __working_thread(cls):
        smtp_sender = smtplib.SMTP(MAILSERVER, 25)

        while True:
            try:
                item = cls.task_queue.get()
                if isinstance(item, cls.ServiceTask):
                    cls.__send_email(item.recipient, item.subject, item.body, item.attachment, item.directory,
                                     smtp_obj=smtp_sender)

            except Exception as e:
                print("send email errors: " + str(e))

    def submit(self, recipient, subject, body, attachment=None, directory=None):
        service_task = self.ServiceTask(recipient, subject, body, attachment, directory)
        self.task_queue.put(service_task)

    @classmethod
    def __send_email(cls, recipients, subject, body, attachment, directory, sender=SENDER, email_format='plain',
                     smtp_obj=None):
        try:
            if isinstance(recipients, str):
                recipients = recipients.split(',')

            # Create message container - the correct MIME type is multipart/alternative.
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = sender
            msg['To'] = ','.join(recipients)

            # Create the body of the message (a plain-text and an HTML version).
            if email_format == 'html':
                message = MIMEText(body, 'html')
            else:
                message = MIMEText(body, 'plain')

            msg.attach(message)

            if attachment or directory:
                part_of_message = cls.__add_attachments(directory=directory, attachments=attachment)
                msg.attach(part_of_message)

                smtp_obj.sendmail(sender, recipients, msg.as_string())
                logger.debug("Successfully sent email")
        except Exception as e:
            print(str(e))
            logger.error(str(e))

    @classmethod
    def __add_attachments(cls, directory=None, attachments=None):
        # Create the enclosing (outer) message
        outer = MIMEMultipart()
        files_list = []
        # need check type of argument
        if directory is not None and not isinstance(directory, str):
            raise ValueError("Directory need to be string")
        if attachments is not None and not isinstance(attachments, list):
            raise ValueError("Attachment need to be list")

        if directory:
            for filename in os.listdir(directory):
                path = os.path.join(directory, filename)
                if not os.path.isfile(path):
                    continue
                files_list.append(path)
        elif attachments:
            for filename in attachments:
                if not os.path.isfile(filename):
                    continue
                files_list.append(filename)

        for filename in files_list:
            # Guess the content type based on the file's extension.  Encoding
            # will be ignored, although we should check for simple things like
            # gzip'd or compressed files.
            ctype, encoding = mimetypes.guess_type(filename)
            if ctype is None or encoding is not None:
                # No guess could be made, or the file is encoded (compressed), so
                # use a generic bag-of-bits type.
                ctype = 'application/octet-stream'
            maintype, subtype = ctype.split('/', 1)
            if maintype == 'text':
                fp = open(filename)
                # Note: we should handle calculating the charset
                msg = MIMEText(fp.read(), _subtype=subtype)
                fp.close()
            elif maintype == 'image':
                fp = open(filename, 'rb')
                msg = MIMEImage(fp.read(), _subtype=subtype)
                fp.close()
            elif maintype == 'audio':
                fp = open(filename, 'rb')
                msg = MIMEAudio(fp.read(), _subtype=subtype)
                fp.close()
            else:
                fp = open(filename, 'rb')
                msg = MIMEBase(maintype, subtype)
                msg.set_payload(fp.read())
                fp.close()
                # Encode the payload using Base64
                encoders.encode_base64(msg)
            # Set the filename parameter
            msg.add_header('Content-Disposition', 'attachment', filename=os.path.basename(filename))
            outer.attach(msg)
        # Now send or store the message
        # composed = outer.as_string()
        return outer


if __name__ == "__main__":
    e1 = EmailSender()
    e2 = EmailSender()
    print(e1 is e2)
    recipient_list = ["<EMAIL>"]
    email_title = "Test mail"
    email_body = "test result"
    email_attachment = ["/Users/<USER>/Desktop/test_attachment.rtf"]
    email_directory = "/Users/<USER>/Desktop/untitled folder 2"
    email_sender = EmailSender()
    email_sender.submit(recipient_list, email_title, email_body, email_attachment, email_directory)
    time.sleep(10)
    email_sender.submit(recipient_list, email_title, email_body, email_attachment)
    time.sleep(100000)
