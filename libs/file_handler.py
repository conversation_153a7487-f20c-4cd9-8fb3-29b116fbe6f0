from django.http import HttpResponse
import csv, os, time


def download_alert_file(file_path, file_name):
    # file_path = '/Users/<USER>/git/juno/output/1681331099029755000/cart-page-ca-cart-page.yaml'
    FilePointer = open(file_path, "r")
    response = HttpResponse(FilePointer, content_type='text/yaml')
    response['Content-Disposition'] = f'attachment; filename={file_name}'
    return response


def download_cav_file(file_path, file_name):
    FilePointer = open(file_path, "r")
    response = HttpResponse(FilePointer, content_type='text/cav')
    response['Content-Disposition'] = f'attachment; filename={file_name}'
    return response


def download_alert_content(content: dict, file_name):
    import json
    # file_path = '/Users/<USER>/git/juno/output/1681331099029755000/cart-page-ca-cart-page.yaml'
    response = HttpResponse(json.dumps(content), content_type='text/yaml')
    response['Content-Disposition'] = f'attachment; filename={file_name}'
    return response


def create_hpa_data(data):
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    FILE_DIR = os.path.join(BASE_DIR, 'output')
    _file_name = f"hpa_data_{int(time.time() * 1000)}.csv"
    file_name = os.path.join(FILE_DIR, _file_name)
    with open(file_name, 'w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(
            ["NameSpace", "app", "Cluster", "Current_Pods", "hpa_min", "hpa_max", "hpa_enabled", "hpa_criteria",
             "hpa_target", "prob_live_probe_interval", "prob_live_wait", "prob_live_time_out",
             "prob_ready_probe_interval", "prob_ready_wait", "prob_ready_time_out"

             ])
        for _data in data:
            writer.writerow([_data.get("namespace"), _data.get("app"), _data.get("current_cluster"),
                             _data.get("current_pods"),
                             _data.get("hap_min"), _data.get("hap_max"), _data.get("hap_enabled"),
                             _data.get("hap_criteria"), _data.get("hap_target"),
                             _data.get("prob_live_probe_interval"),
                             _data.get("prob_live_wait"),
                             _data.get("prob_live_time_out"),
                             _data.get("prob_ready_probe_interval"),
                             _data.get("prob_ready_wait"),
                             _data.get("prob_ready_time_out"),
                             ])
    return download_cav_file(file_name, _file_name)
