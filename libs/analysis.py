from statistics import mean, stdev
from scipy.stats import zscore
from functools import reduce
import pandas as pd
import logging
from collections import defaultdict
from math import ceil
import numpy as np
logger = logging.getLogger(__name__)
def compute_z_score_tag(values, label, upper_z=2.0, lower_z=-1.5):
    if not values:
        return {"avg": 0, "z_score": 0, "tag": "no-data", "label": label}

    μ = mean(values)
    σ = stdev(values) if len(values) > 1 else 0.0001
    z = (values[-1] - μ) / σ

    if z > upper_z:
        tag = "spike"
    elif z < lower_z:
        tag = "underutilized"
    else:
        tag = "normal"

    return {
        "avg": round(μ, 2),
        "latest": round(values[-1], 2),
        "z_score": round(z, 2),
        "tag": tag,
        "label": label
    }


def detect_sustained_spikes(timestamps, values, threshold_pct=30, duration_minutes=30, baseline_threshold=None):
    """
    Detects sustained spike windows based on inferred resolution from timestamps.
    Flags values above max(avg + threshold_pct, baseline_threshold) for a sustained duration (e.g., 30 minutes).

    Args:
        timestamps (List[int]): List of epoch timestamps.
        values (List[float]): List of metric values (e.g., CPU utilization).
        threshold_pct (float): % above average to consider spike.
        duration_minutes (int): Minimum duration to qualify as a sustained spike.
        baseline_threshold (float or None): Optional fixed minimum baseline threshold (e.g., 80.0).
    """
    if len(timestamps) < 2:
        return {
            "avg": 0,
            "threshold": 0,
            "resolution_seconds": 0,
            "sustained_spike_windows": []
        }

    avg = mean(values)
    dynamic_threshold = avg * (1 + (float(threshold_pct) / 100))
    final_threshold = max(dynamic_threshold,
                          baseline_threshold) if baseline_threshold is not None else dynamic_threshold
    resolution_seconds = timestamps[1] - timestamps[0]
    required_points = duration_minutes * 60 // resolution_seconds

    spike_windows = []
    current_window = []

    for ts, val in zip(timestamps, values):
        if val > final_threshold:
            current_window.append((ts, val))
        else:
            if len(current_window) >= required_points:
                severity = classify_spike_severity(current_window, resolution_seconds)
                spike_windows.append({
                    "start": current_window[0][0],
                    "end": current_window[-1][0],
                    "duration_minutes": len(current_window) * resolution_seconds // 60,
                    "severity": severity,
                    "data_points": current_window
                })
            current_window = []

    if len(current_window) >= required_points:
        severity = classify_spike_severity(current_window, resolution_seconds)
        spike_windows.append({
            "start": current_window[0][0],
            "end": current_window[-1][0],
            "duration_minutes": len(current_window) * resolution_seconds // 60,
            "severity": severity,
            "data_points": current_window
        })

    return {
        "avg": round(avg, 2),
        "threshold": round(final_threshold, 2),
        "resolution_seconds": resolution_seconds,
        "sustained_spike_windows": spike_windows
    }


def classify_spike_severity(spike_window, resolution_seconds):
    """
    Classifies severity based on duration.
    """
    duration_min = len(spike_window) * resolution_seconds // 60
    if duration_min >= 60:
        return "critical_spike"
    elif duration_min >= 30:
        return "sustained_spike"
    elif duration_min >= 15:
        return "mild_spike"
    else:
        return "short_spike"


def extract_data(data):
    """
    Extracts timestamps and values from the input CPU data.

    Parameters:
        cpu_data: List of dictionaries with 'metric' and 'values' keys.

    Returns:
        A tuple of (timestamps, values) ready for analysis.
    """
    if not data or 'values' not in data[0]:
        raise ValueError("Invalid data format. Expected a list of dictionaries with a 'values' key.")

    # Extract the 'values' key from the first dictionary
    raw_values = data[0]['values']

    # Unpack timestamps and values
    timestamps, values = zip(*raw_values)

    # Convert values to floats
    values = list(map(float, values))

    return list(timestamps), values


def analyze_utilization_insights(cpu_data, memory_data, duration_minutes=6,
                                 baseline_threshold_cpu=None,
                                 baseline_threshold_mem=None,
                                 cpu_deviation_threshold=30,
                                 mem_deviation_threshold=30
                                 ):
    """
    Analyzes CPU and memory utilization for:
      - Z-score classification
      - Sustained spike detection with auto-resolved sampling rate

    Parameters:
        cpu_data, memory_data: [timestamps, values]
        duration_minutes: how long a spike must persist to be reported
        baseline_threshold_cpu: % above cpu average to consider as spike
        baseline_threshold_mem: % above memory average to consider as spike
        cpu_deviation_threshold: % CPU utilization must exceed the moving average to be considered a spike
        mem_deviation_threshold: % Memory utilization must exceed the moving average to be considered a spike

    Returns:
        Dictionary of cpu and memory insight reports.
    """
    results = dict()
    for _item in cpu_data:
        cpu_ts, cpu_vals = extract_data(cpu_data)
        mem_ts, mem_vals = extract_data(memory_data)

        res = {
            "cpu_insight": detect_sustained_spikes(cpu_ts, cpu_vals, cpu_deviation_threshold, duration_minutes,
                                                   baseline_threshold_cpu),
            "memory_insight": detect_sustained_spikes(mem_ts, mem_vals, mem_deviation_threshold, duration_minutes,
                                                      baseline_threshold_mem)
        }
        results.update({_item["metric"]["cluster_id"]: res})
    return results


def parse_by_cluster(promql_data):
    """
    Parses a Prometheus-style result list (with multiple cluster responses)
    where each value is already in percent (e.g., CPU utilization %).
    Returns: {cluster_id: [percent values over time]}
    """
    cluster_data = defaultdict(list)
    for entry in promql_data:
        cluster = entry["metric"].get("cluster_id", "default")
        for _, val in entry["values"]:
            cluster_data[cluster].append(float(val))  # already in percent
    return cluster_data


def analyze_peak_and_offpeak_per_cluster(
        cpu_peak_data, memory_peak_data, tps_peak_data, pod_peak_data,
        cpu_off_data, memory_off_data, tps_off_data, pod_off_data,
        buffer=1.2, max_cpu_per_pod=0.7
):
    """
    Performs per-cluster capacity analysis based on peak and off-peak PromQL results.
    Each dataset is expected to be a Prometheus-style response: list of dicts with `metric` and `values`
    """

    tps_peak = parse_by_cluster(tps_peak_data)
    pod_peak = parse_by_cluster(pod_peak_data)
    tps_off = parse_by_cluster(tps_off_data)
    pod_off = parse_by_cluster(pod_off_data)
    cpu_util_percent_off = parse_by_cluster(cpu_off_data)
    cpu_util_percent_peak = parse_by_cluster(cpu_peak_data)
    mem_percent_peak = parse_by_cluster(memory_peak_data)
    mem_percent_off = parse_by_cluster(memory_off_data)

    results = {}

    for cluster in tps_peak:
        if (cluster in pod_peak and cluster in tps_off and cluster in pod_off and cluster in cpu_util_percent_off and
                cluster in cpu_util_percent_peak):
            # Extract peak TPS values for the current cluster
            tps_vals_peak = tps_peak[cluster]
            # Extract peak pod count values for the current cluster
            pod_vals_peak = pod_peak[cluster]
            # Extract off-peak TPS values for the current cluster
            tps_vals_off = tps_off[cluster]
            # Extract off-peak pod count values for the current cluster

            pod_vals_off = pod_off[cluster]
            # Extract off-peak CPU utilization values (already in %) for the current cluster
            cpu_vals_percent_off = cpu_util_percent_off[cluster]

            # Extract peak CPU utilization values (already in %) for the current cluster
            cpu_vals_percent_peak = cpu_util_percent_peak[cluster]

            # Extract peak Memory utilization values (already in %) for the current cluster
            mem_vals_percent_peak = mem_percent_peak[cluster]

            # Extract off-peak Memory utilization values (already in %) for the current cluster
            mem_vals_percent_off = mem_percent_off[cluster]

            avg_tps_peak = mean(tps_vals_peak)
            avg_pods_peak = mean(pod_vals_peak)
            tps_per_pod_peak = avg_tps_peak / avg_pods_peak if avg_pods_peak else 1

            avg_tps_off = mean(tps_vals_off)
            min_pods_by_tps = ceil(avg_tps_off / tps_per_pod_peak) if tps_per_pod_peak else 1

            avg_cpu_util_off = mean(cpu_vals_percent_off)
            max_cpu_percent = max_cpu_per_pod * 100
            min_pods_by_cpu = ceil(avg_cpu_util_off / max_cpu_percent) if max_cpu_percent > 0 else 1

            recommended = ceil(max(min_pods_by_tps, min_pods_by_cpu) * buffer)

            results[cluster] = {
                "avg_tps_peak": round(avg_tps_peak, 2),
                "avg_pods_peak": round(avg_pods_peak, 2),
                "tps_per_pod_peak": round(tps_per_pod_peak, 2),
                "avg_tps_off": round(avg_tps_off, 2),
                "avg_cpu_util_off": ceil(avg_cpu_util_off),
                "avg_cpu_util_peak": ceil(mean(cpu_vals_percent_peak)),
                "avg_memory_util_peak": ceil(mean(mem_vals_percent_peak)),
                "avg_memory_util_off": ceil(mean(mem_vals_percent_off)),
                "avg_pods_off": ceil(mean(pod_vals_off)),
                "recommended_min_pods_by_tps": min_pods_by_tps,
                "recommended_min_pods_by_cpu": min_pods_by_cpu,
                "recommended_min_replicas": recommended
            }

    # return pd.DataFrame.from_dict(results, orient="index").reset_index().rename(columns={"index": "cluster_id"})
    return results


def infer_merge_keys(frames):
    """
    Infers best merge_keys based on common columns across all frames.

    Args:
        frames (list[pd.DataFrame]): List of pandas DataFrames.

    Returns:
        list[str]: List of common columns across all frames.
    """
    if not frames:
        return []

    all_keys = [set(df.columns) for df in frames]
    common_keys = set.intersection(*all_keys)

    return sorted(list(common_keys))  # Sort for consistency


def clean_and_merge_metric_dataframes_generic(
        metric_data: dict,
        user_drop_list: dict = None,
        merge_keys: list = None,
        fallback_merge_keys: list = None,
):
    """
    Cleans and merges multiple metric DataFrames in a dynamic, user-extensible way.

        Parameters
        ----------
        metric_data : dict
            Dictionary mapping metric names to their respective pandas DataFrames.
            Example:
                {
                    "cpu_data": pd.DataFrame(...),
                    "memory_data": pd.DataFrame(...),
                    "traffic_data": pd.DataFrame(...)
                }

        user_drop_list : dict, optional
            Dictionary specifying columns to be dropped from each individual DataFrame **before** merging.
            - Key: metric name (must match a key in metric_data)
            - Value: List of columns to drop from that DataFrame.
            If not provided, no columns are dropped.

            Example:
                {
                    "cpu_data": ["namespace", "day"],
                    "memory_data": ["namespace"],
                    "pods_data": ["traffic_type"]
                }

        merge_keys : list, optional
            List of columns to merge all DataFrames on.
            If not provided, the function **automatically infers** common columns across all DataFrames.

            Example:
                ["timestamp", "cluster_id", "namespace", "app_id"]

        fallback_merge_keys : list, optional
            If `merge_keys` is not explicitly provided and **auto-inference fails** (i.e., no common columns across frames),
            this fallback list will be used instead.

            Example:
                ["timestamp"]

        Returns
        -------
        pd.DataFrame
            A single merged DataFrame with cleaned columns based on the provided parameters.

        Behavior and Notes
        ------------------
        - If `user_drop_list` is provided, only specified columns are dropped (non-crashing even if column is missing).
        - If `merge_keys` is not provided, common columns across all frames are **auto-detected**.
        - If auto-detection fails, and `fallback_merge_keys` is also not given, the function will raise ValueError.
        - Merging is done using `outer` join — meaning no data loss across time series.

        Example Usage
        -------------
          merged_df = clean_and_merge_metric_dataframes_generic(
                metric_data=metrics_dict,
                user_drop_list={
                    "cpu_data": ["namespace", "day"],
                    "pods_data": ["traffic_type"]
                },
                merge_keys=["timestamp", "cluster_id", "app_id"],
            )

        Example: Automatic Merge Keys
        ------------------------------
        If you do NOT pass `merge_keys`, and the DataFrames have these columns:
            cpu_data    : timestamp, cluster_id, app_id, value
            memory_data : timestamp, cluster_id, app_id, value
            pods_data   : timestamp, cluster_id, app_id, value

        Then the inferred `merge_keys` will automatically be:
            ["timestamp", "cluster_id", "app_id"]

    """
    frames = []

    for metric_name, df in metric_data.items():
        df = df.copy()

        if user_drop_list and metric_name in user_drop_list:
            drop_cols = user_drop_list[metric_name]
            df = df.drop(columns=[col for col in drop_cols if col in df.columns], errors="ignore")

        frames.append(df)

    if not frames:
        return pd.DataFrame()

    # 🔥 Infer merge_keys if not provided
    if merge_keys is None:
        inferred_keys = infer_merge_keys(frames)
        if inferred_keys:
            merge_keys = inferred_keys
            print(f"[Info] Auto-inferred merge_keys: {merge_keys}")
        elif fallback_merge_keys:
            merge_keys = fallback_merge_keys
            print(f"[Warning] Auto-infer failed. Falling back to provided merge_keys: {merge_keys}")
        else:
            raise ValueError(
                "Could not infer merge_keys, and no fallback_merge_keys were provided."
            )

    merged_df = reduce(
        lambda left, right: pd.merge(left, right, on=merge_keys, how="outer"),
        frames
    )

    return merged_df


def analyze_throttling(df, namespace_col, app_col, cluster_col="cluster_id",
                       max_allowed_throttle_pct=5.0, throttle_penalty_unit=5):
    """
    Calculates extra pod recommendations for apps with sustained throttling spikes.

    Args:
        df (pd.DataFrame): Input throttling data with 'throttle_pct' and 'timestamp'.
        namespace_col (str): Column name for namespace grouping.
        app_col (str): Column name for application ID.
        cluster_col (str): Column for cluster ID (default 'cluster_id').
        max_allowed_throttle_pct (float): Threshold above which throttling is penalized.
        throttle_penalty_unit (float): Throttling increment per additional pod.

    Returns:
        Dict[Tuple[str, str, str], int]: Map of (namespace, app, cluster) → extra pods.

    Example:
        analyze_throttling(df, "namespace", "app_id", max_allowed_throttle_pct=10)
    """
    df = detect_spikes(df, metric_col="throttle_pct")

    extra_pods = {}
    for (ns, app, cluster), group in df[df["is_spike"]].groupby([namespace_col, app_col, cluster_col]):
        avg_throttle = group["throttle_pct"].mean()
        if avg_throttle > max_allowed_throttle_pct:
            extra_pods[(ns, app, cluster)] = ceil((avg_throttle - max_allowed_throttle_pct) / throttle_penalty_unit)
    return extra_pods


def analyze_latency(df, namespace_col, app_col, cluster_col="cluster_id",
                    max_latency_ms=500, latency_penalty_unit=100):
    """
    Calculates extra pods based on average sustained latency spikes per app.

    Args:
        df (pd.DataFrame): Latency time series data with 'latency_ms'.
        namespace_col (str): Grouping column for namespace.
        app_col (str): Grouping column for app.
        cluster_col (str): Column for cluster grouping (default 'cluster_id').
        max_latency_ms (int): Latency threshold in milliseconds.
        latency_penalty_unit (int): Pod penalty step per latency overage (ms).

    Returns:
        Dict[Tuple[str, str, str], int]: Extra pods required due to high latency.

    Example:
        analyze_latency(latency_df, "namespace", "app_id", max_latency_ms=400)
    """
    df = detect_spikes(df, metric_col="latency_ms")

    extra_pods = {}
    for (ns, app, cluster), group in df[df["is_spike"]].groupby([namespace_col, app_col, cluster_col]):
        avg_latency = group["latency_ms"].mean()
        if avg_latency > max_latency_ms:
            extra_pods[(ns, app, cluster)] = ceil((avg_latency - max_latency_ms) / latency_penalty_unit)
    return extra_pods


def analyze_error_rate(df, namespace_col, app_col, cluster_col="cluster_id",
                       max_error_pct=1.0, error_penalty_unit=1):
    """
    Recommends additional pods based on sustained high error rate spikes.

    Args:
        df (pd.DataFrame): Error percentage time series with 'error_pct'.
        namespace_col (str): Namespace grouping column.
        app_col (str): App ID grouping column.
        cluster_col (str): Cluster grouping column (default 'cluster_id').
        max_error_pct (float): Max allowable error % before penalizing.
        error_penalty_unit (float): Penalty divisor for overage %.

    Returns:
        Dict[Tuple[str, str, str], int]: Map of app deployments → extra pod count.

    Example:
        analyze_error_rate(errors_df, "namespace", "app_id", max_error_pct=2.5)
    """
    df = detect_spikes(df, metric_col="error_pct")

    extra_pods = {}
    for (ns, app, cluster), group in df[df["is_spike"]].groupby([namespace_col, app_col, cluster_col]):
        avg_error = group["error_pct"].mean()
        if avg_error > max_error_pct:
            extra_pods[(ns, app, cluster)] = ceil((avg_error - max_error_pct) / error_penalty_unit)
    return extra_pods


def apply_penalty_rules(
        penalty_rules,
        group_keys=("namespace", "app_id", "cluster_id"),
):
    """
    Applies a set of penalty functions to associated DataFrames and aggregates the average adjustment per group.

    Parameters:
    -----------
    penalty_rules : List[Dict]
        Each dict must have:
            - 'df': DataFrame to analyze
            - 'fn': penalty function
            - 'kwargs': arguments to pass to the function
    group_keys : Tuple[str]
        The grouping keys (e.g., namespace, app_id, cluster_id)

    Returns:
    --------
    Dict[Tuple, int] : Map of (namespace, app_id, cluster_id) → avg additional pods
    """
    from collections import defaultdict

    all_adjustments = defaultdict(list)

    for rule in penalty_rules:
        df = rule.get("df")
        fn = rule.get("fn")
        kwargs = rule.get("kwargs", {})

        if df is not None and not df.empty:
            penalties = fn(df, *group_keys, **kwargs)
            for key, val in penalties.items():
                all_adjustments[key].append(val)

    final_adjustments = {
        key: ceil(sum(vals) / len(vals)) for key, vals in all_adjustments.items()
    }

    return final_adjustments


def calculate_min_pods_full(
        peak_df,
        offpeak_df,
        throttling_df=None,
        latency_df=None,
        error_rate_df=None,
        buffer=1.2,
        max_cpu_per_pod=0.7,
        namespace_col="namespace",
        app_col="app_id"
):
    """
    Computes the recommended minimum number of Kubernetes pods required per application per cluster,
    using traffic-per-pod, CPU utilization, and optional penalty adjustments based on throttling,
    latency, and error rate.

    Parameters:
    -----------
    peak_df : pd.DataFrame
        DataFrame containing CPU, traffic, and pod usage during peak hours.
        Must include columns: ['timestamp', namespace_col, app_col, cluster_col, 'cpu', 'traffic', 'pods'].

    offpeak_df : pd.DataFrame
        DataFrame containing CPU, traffic, and pod usage during off-peak hours.
        Must include columns: ['timestamp', namespace_col, app_col, cluster_col, 'cpu', 'traffic', 'pods'].

    throttling_df : pd.DataFrame, optional
        Optional DataFrame with 'throttle_pct' for each timestamp. Used to adjust recommendations
        based on throttling spikes. Must include same grouping columns and 'timestamp'.

    latency_df : pd.DataFrame, optional
        Optional DataFrame with 'latency_ms' per timestamp. Penalizes apps breaching latency thresholds.

    error_rate_df : pd.DataFrame, optional
        Optional DataFrame with 'error_pct'. Adds pods for services with high sustained error rates.

    buffer : float, default=1.2
        Safety buffer multiplier on the final recommended pods (e.g., 20% over-provisioning).

    max_cpu_per_pod : float, default=0.7
        Maximum safe CPU utilization per pod. If a pod exceeds this % utilization, more pods will be recommended.

    namespace_col : str, default="namespace"
        Column name representing namespace in all DataFrames.

    app_col : str, default="app_id"
        Column name representing application or workload ID.

    cluster_col : str, default="cluster_id"
        Column name for Kubernetes cluster ID (used for grouping capacity decisions per cluster).

    Returns:
    --------
    pd.DataFrame
        Final DataFrame with one row per (namespace, app, cluster) triple, including:
        - avg_cpu_peak / off
        - avg_traffic_peak / off
        - avg_pods_peak / off
        - tps_per_pod_peak
        - min_pods_by_tps
        - min_pods_by_cpu
        - base_recommendation
        - extra_adjustments (from penalties)
        - final_recommended_min_pods

    Example:
    --------
    df = calculate_min_pods_full(
        peak_df=peak_usage_df,
        offpeak_df=offpeak_usage_df,
        throttling_df=throttle_df,
        latency_df=latency_df,
        error_rate_df=error_df,
        buffer=1.2,
        max_cpu_per_pod=0.7
    )
    """

    # 1. Aggregate peak and off-peak metrics
    peak_agg = peak_df.groupby([namespace_col, app_col]).agg({
        'cpu': 'mean', 'traffic': 'mean', 'pods': 'mean'
    }).rename(columns={
        'cpu': 'avg_cpu_peak', 'traffic': 'avg_traffic_peak', 'pods': 'avg_pods_peak'
    })

    off_agg = offpeak_df.groupby([namespace_col, app_col]).agg({
        'cpu': 'mean', 'traffic': 'mean', 'pods': 'mean'
    }).rename(columns={
        'cpu': 'avg_cpu_off', 'traffic': 'avg_traffic_off', 'pods': 'avg_pods_off'
    })

    merged = peak_agg.join(off_agg, how='inner')

    # 2. Compute baseline minPods
    merged['tps_per_pod_peak'] = merged['avg_traffic_peak'] / merged['avg_pods_peak']
    merged['min_pods_by_tps'] = (merged['avg_traffic_off'] / merged['tps_per_pod_peak']).apply(
        lambda x: max(ceil(x), 1))
    merged['min_pods_by_cpu'] = (merged['avg_cpu_off'] / (max_cpu_per_pod * 100)).apply(lambda x: max(ceil(x), 1))

    merged['base_recommendation'] = merged.apply(
        lambda row: ceil(max(row['min_pods_by_tps'], row['min_pods_by_cpu']) * buffer),
        axis=1
    )

    # 3. Penalty rule engine
    penalty_rules = []

    if throttling_df is not None and not throttling_df.empty:
        penalty_rules.append({
            "df": throttling_df,
            "fn": analyze_throttling,
            "kwargs": {"max_allowed_throttle_pct": 5.0, "throttle_penalty_unit": 5}
        })

    if latency_df is not None and not latency_df.empty:
        penalty_rules.append({
            "df": latency_df,
            "fn": analyze_latency,
            "kwargs": {"max_latency_ms": 500, "latency_penalty_unit": 100}
        })

    if error_rate_df is not None and not error_rate_df.empty:
        penalty_rules.append({
            "df": error_rate_df,
            "fn": analyze_error_rate,
            "kwargs": {"max_error_pct": 1.0, "error_penalty_unit": 1}
        })

    adjustments = apply_penalty_rules(penalty_rules, namespace_col, app_col)

    # 4. Apply adjustments
    merged['extra_adjustments'] = merged.index.map(lambda idx: adjustments.get(idx, 0))
    merged['final_recommended_min_pods'] = merged['base_recommendation'] + merged['extra_adjustments']

    return merged.reset_index()


def detect_spikes(df, metric_col, timestamp_col="timestamp", z_thresh=1.5, min_duration_secs=60):
    """
    Detects sustained spikes in a given metric using z-score and time-window filtering.

    Args:
        df (pd.DataFrame): Input DataFrame with time series data.
        metric_col (str): Column name for the metric to detect spikes on (e.g., 'cpu').
        timestamp_col (str): Timestamp column (epoch seconds). Default is 'timestamp'.
        z_thresh (float): Z-score threshold to classify a value as a spike. Default is 1.5.
        min_duration_secs (int): Minimum duration in seconds for a spike to be considered significant.

    Returns:
        pd.DataFrame: Original DataFrame with an added boolean column 'is_spike'.

    Example:
        df = detect_spikes(cpu_df, metric_col="cpu", z_thresh=2.0, min_duration_secs=90)
    """
    df = df.sort_values(timestamp_col).reset_index(drop=True).copy()

    if metric_col not in df.columns or df[metric_col].isnull().all():
        df["is_spike"] = False
        return df

    df["zscore"] = zscore(df[metric_col].ffill().fillna(0))
    df["is_spike"] = df["zscore"].abs() > z_thresh
    df["spike_group"] = (df["is_spike"] != df["is_spike"].shift()).cumsum()

    durations = df[df["is_spike"]].groupby("spike_group")[timestamp_col].agg(["min", "max"])
    durations["duration"] = durations["max"] - durations["min"]
    valid_groups = durations[durations["duration"] >= min_duration_secs].index

    df["is_spike"] = df["spike_group"].isin(valid_groups) & df["is_spike"]
    return df.drop(columns=["zscore", "spike_group"])


def correlate_spikes_across_metrics(metric_dfs: dict, timestamp_col="timestamp", time_tolerance_secs=180):
    spike_timestamps = []

    for name, df in metric_dfs.items():
        spikes = df[df['is_spike']][timestamp_col].tolist()
        spike_timestamps.extend(spikes)

    if not spike_timestamps:
        return pd.DataFrame(columns=[timestamp_col, "correlated_spike"])

    ts_series = pd.Series(sorted(spike_timestamps))
    spike_counts = ts_series.value_counts().sort_index()

    spike_df = pd.DataFrame({timestamp_col: spike_counts.index, "count": spike_counts.values})
    spike_df = spike_df.sort_values(timestamp_col)

    spike_df['group'] = ((spike_df[timestamp_col] - spike_df[timestamp_col].shift()) > time_tolerance_secs).cumsum()
    result = spike_df.groupby("group")[timestamp_col].agg(['min', 'max'])
    result['correlated_spike'] = True

    return result.reset_index(drop=True).rename(columns={"min": timestamp_col})[[timestamp_col, 'correlated_spike']]

from math import ceil, isnan, isinf
# Fallback to 1 if invalid or undefined
def safe_ceil_div(numerator, denominator):
    try:
        if denominator == 0:
            return 1
        return max(ceil(numerator / denominator), 1)
    except ValueError:
        msg = f"Error occurred numerator {numerator} denominator {denominator}"
        logger.error(msg)
        raise Exception(msg)

def calculate_min_pods_peak_off_peak(
        peak_df,
        offpeak_df,
        throttling_df=None,
        latency_df=None,
        error_rate_df=None,
        buffer=1.2,
        max_cpu_per_pod=0.7,
        namespace_col="namespace",
        app_col="app_id"
):
    """
    Calculates the recommended minimum number of Kubernetes pods per application
    during off-peak periods based on CPU utilization, latency, throttling, error rate,
    and CPU burst detection.

    The goal is to reduce cost by identifying safe lower bounds for minPods, while
    preserving reliability (SLA-compliant latency, no throttling, etc.).

    Parameters
    ----------
    peak_df : pd.DataFrame
        DataFrame containing CPU, traffic, and pods data during peak periods.
        Required columns: ['traffic', 'pods'], grouped by [namespace_col, app_col].
        Used only to estimate traffic-per-pod (TPS per pod).

    offpeak_df : pd.DataFrame
        DataFrame containing CPU, traffic, and pods data during off-peak periods.
        Required columns: ['cpu', 'traffic', 'pods'], grouped by [namespace_col, app_col].
        Used to estimate CPU-based minPods and perform SLA analysis.

    throttling_df : pd.DataFrame, optional
        DataFrame with average throttling percentage (0-100%) during off-peak.
        Expected column: 'throttle_pct'. Penalizes under-provisioning if high.

    latency_df : pd.DataFrame, optional
        DataFrame with latency observations in milliseconds during off-peak.
        Expected column: 'latency_ms'. Penalizes if P90 latency exceeds 500ms.

    error_rate_df : pd.DataFrame, optional
        DataFrame with application-level error rate during off-peak.
        Expected column: 'error_pct'. Penalizes if error rate is consistently high.

    buffer : float, default=1.2
        Multiplier to apply to the base minPods calculation for safety margin.
        For example, 1.2 means 20% over-provisioning on top of calculated need.

    max_cpu_per_pod : float, default=0.7
        Maximum safe CPU utilization per pod (e.g., 0.7 = 70%).
        Used to calculate CPU-based minPods like: (avg_cpu_off / 70%)

    namespace_col : str, default='namespace'
        Column name in the DataFrames representing Kubernetes namespace.

    app_col : str, default='app_id'
        Column name in the DataFrames representing application identifier.

    Returns
    -------
    pd.DataFrame
        DataFrame with one row per (namespace, app_id) containing:
            - avg_cpu_off
            - avg_traffic_off
            - tps_per_pod_peak
            - min_pods_by_cpu
            - min_pods_by_tps
            - base_recommendation
            - p90_latency
            - avg_throttle_pct
            - avg_error_pct
            - cpu_max
            - cpu_spike_ratio
            - latency_penalty_pods
            - throttle_penalty_pods
            - error_penalty_pods
            - burst_penalty_pods
            - penalty_adjustment
            - final_recommended_min_pods

        These columns allow comparing baseline vs adjusted minPods, along with
        reasoning based on SLA and burst behavior.
    """

    # 1. Aggregate PEAK only for TPS per pod
    peak_agg = peak_df.groupby([namespace_col, app_col],observed=True).agg({
        'traffic': 'mean',
        'pods': 'mean'
    }).rename(columns={
        'traffic': 'avg_traffic_peak',
        'pods': 'avg_pods_peak'
    })

    # 2. Aggregate OFFPEAK for all other stats
    off_agg = offpeak_df.groupby([namespace_col, app_col],observed=True).agg({
        'cpu': 'mean',
        'traffic': 'mean',
        'pods': 'mean'
    }).rename(columns={
        'cpu': 'avg_cpu_off',
        'traffic': 'avg_traffic_off',
        'pods': 'avg_pods_off'
    })

    merged = peak_agg.join(off_agg, how='inner')

    # 3. TPS per pod (from peak) → use it to calculate off-peak TPS-based min pods
    merged['tps_per_pod_peak'] = merged['avg_traffic_peak'] / merged['avg_pods_peak']
    # merged['min_pods_by_tps'] = (merged['avg_traffic_off'] / merged['tps_per_pod_peak']).apply(
    #     lambda x: max(ceil(x), 1))
    merged['min_pods_by_tps'] = (merged['avg_traffic_off'] / merged['tps_per_pod_peak']).apply(safe_ceil_div)

    # 4. CPU-based min pods (from off-peak)
    # merged['min_pods_by_cpu'] = (merged['avg_cpu_off'] / (max_cpu_per_pod * 100)).apply(
    #     lambda x: max(ceil(x), 1))
    merged['min_pods_by_cpu'] = (merged['avg_cpu_off'] / (max_cpu_per_pod * 100)).apply(safe_ceil_div)

    # 5. Base recommendation: take the more conservative (higher) of the two
    merged['base_recommendation'] = merged[['min_pods_by_cpu', 'min_pods_by_tps']].max(axis=1)

    # 6. Add latency penalty (from off-peak)
    if latency_df is not None:
        latency_agg = latency_df.groupby([namespace_col, app_col],observed=True).agg({'latency_ms': lambda x: np.percentile(x, 90)})
        latency_agg = latency_agg.rename(columns={'latency_ms': 'p90_latency'})
        merged = merged.join(latency_agg, how='left')
        merged["latency_penalty_pods"] = merged["p90_latency"].apply(lambda x: 1 if x > 500 else 0)
    else:
        merged["p90_latency"] = np.nan
        merged["latency_penalty_pods"] = 0

    # 7. Add throttling penalty (off-peak)
    if throttling_df is not None:
        throttle_agg = throttling_df.groupby([namespace_col, app_col],observed=True).agg({"throttle_pct": "mean"})
        merged = merged.join(throttle_agg, how='left')
        merged["throttle_penalty_pods"] = merged["throttle_pct"].apply(lambda x: 1 if x > 5 else 0)
    else:
        merged["throttle_penalty_pods"] = 0

    # 8. Add error rate penalty (off-peak)
    if error_rate_df is not None:
        error_agg = error_rate_df.groupby([namespace_col, app_col],observed=True).agg({"error_pct": "mean"})
        merged = merged.join(error_agg, how='left')
        merged["error_penalty_pods"] = merged["error_pct"].apply(lambda x: 1 if x > 5 else 0)
    else:
        merged["error_penalty_pods"] = 0

    # 9. Detect burst using off-peak CPU data (max vs avg)
    cpu_max_df = offpeak_df.groupby([namespace_col, app_col],observed=True)["cpu"].max().reset_index()
    cpu_max_df.rename(columns={"cpu": "cpu_max"}, inplace=True)
    merged = merged.reset_index().merge(cpu_max_df, on=[namespace_col, app_col], how="left")
    merged.set_index([namespace_col, app_col], inplace=True)

    merged["cpu_spike_ratio"] = merged["cpu_max"] / (merged["avg_cpu_off"] + 1e-5)
    merged["burst_penalty_pods"] = np.where(
        (merged["cpu_spike_ratio"] > 2) & (merged["p90_latency"] > 500),
        1,
        0
    )

    # 10. Total penalties and final pod calculation
    penalty_cols = ["latency_penalty_pods", "throttle_penalty_pods", "error_penalty_pods", "burst_penalty_pods"]
    merged["penalty_adjustment"] = merged[penalty_cols].sum(axis=1)

    merged["final_recommended_min_pods"] = (
            merged["base_recommendation"] * buffer + merged["penalty_adjustment"]
    ).apply(lambda x: max(ceil(x), 1))

    return merged.reset_index()


def calculate_min_pods_from_single_df(
        df,
        namespace_col="namespace",
        app_col="app_id",
        cpu_col="cpu",
        latency_col="latency_ms",
        throttle_col="throttle_pct",
        error_col="error_pct",
        buffer=1.2,
        max_cpu_per_pod=0.7
):
    """
    Estimate minimum required pods using a single combined dataset for N days.
    No TPS-based logic. Only CPU, latency, burst, throttling, and error rate are used.

    Args:
        df (pd.DataFrame): Input data with time-series metrics
        namespace_col (str): Column representing namespace
        app_col (str): Column representing app_id
        cpu_col (str): Column for CPU utilization (%)
        latency_col (str): Column for latency (ms)
        throttle_col (str): Column for throttling (%)
        error_col (str): Column for error rate (%)
        buffer (float): Safety multiplier on base pod count
        max_cpu_per_pod (float): Max safe CPU per pod (e.g., 0.7 = 70%)

    Returns:
        pd.DataFrame: One row per (namespace, app) with final min_pods and rationale columns
    """

    group_cols = [namespace_col, app_col]

    # 1. Aggregate core metrics
    agg = df.groupby(group_cols).agg({
        cpu_col: ['mean', 'max'],
        latency_col: lambda x: np.percentile(x, 90) if len(x) else np.nan,
        throttle_col: 'mean',
        error_col: 'mean'
    })

    # Flatten column names
    agg.columns = [
        'avg_cpu', 'cpu_max',
        'p90_latency',
        'avg_throttle_pct', 'avg_error_pct'
    ]
    agg = agg.reset_index()

    # 2. CPU-based min pods
    agg["min_pods_by_cpu"] = (agg["avg_cpu"] / (max_cpu_per_pod * 100)).apply(lambda x: max(ceil(x), 1))

    # 3. Burst detection
    agg["cpu_spike_ratio"] = agg["cpu_max"] / (agg["avg_cpu"] + 1e-5)
    agg["burst_penalty_pods"] = np.where(
        (agg["cpu_spike_ratio"] > 2) & (agg["p90_latency"] > 500),
        1, 0
    )

    # 4. Latency penalty
    agg["latency_penalty_pods"] = agg["p90_latency"].apply(lambda x: 1 if x > 500 else 0)

    # 5. Throttling penalty
    agg["throttle_penalty_pods"] = agg["avg_throttle_pct"].apply(lambda x: 1 if x > 5 else 0)

    # 6. Error rate penalty
    agg["error_penalty_pods"] = agg["avg_error_pct"].apply(lambda x: 1 if x > 5 else 0)

    # 7. Sum penalties
    penalty_cols = ["latency_penalty_pods", "throttle_penalty_pods", "error_penalty_pods", "burst_penalty_pods"]
    agg["penalty_adjustment"] = agg[penalty_cols].sum(axis=1)

    # 8. Final recommendation
    agg["final_recommended_min_pods"] = (
            agg["min_pods_by_cpu"] * buffer + agg["penalty_adjustment"]
    ).apply(lambda x: max(ceil(x), 1))

    return agg


if __name__ == "__main__":
    import logging

    log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    log_format = '%(asctime)s %(filename)18s %(funcName)25s %(lineno)3d %(levelname)10s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(log_level)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)
    # ==================================================
    # Example 5-min interval data for 2 hours
    import time
    from random import uniform

    start = int(time.time()) - 2 * 3600
    timestamps = [start + i * 300 for i in range(24)]

    cpu_values = [uniform(0.3, 0.5) for _ in range(15)] + [0.9] * 6 + [uniform(0.3, 0.4) for _ in range(3)]
    mem_values = [uniform(0.3, 0.5) for _ in range(24)]

    cpu_data = [timestamps, cpu_values]
    mem_data = [timestamps, mem_values]

    # result = analyze_utilization_insights([cpu_data], [mem_data])
    # Example dummy test (replace later with real metric data)
    example_df = pd.DataFrame({
        "timestamp": np.arange(0, 600, 60),  # every 1 minute
        "cpu": [10, 12, 11, 15, 80, 85, 13, 11, 10, 9]  # simulate CPU spike
    })

from math import ceil

def calculate_final_recommended_pods(row, max_cpu_per_pod=0.7):
    """
    Rule-based pod recommendation using normalized CPU percentage and TPS.

    Args:
        row (pd.Series): Row with 'cpu_pct', 'traffic', and 'pods'
        max_cpu_per_pod (float): Max safe CPU threshold per pod (e.g., 0.7 = 70%)

    Returns:
        int: Recommended pod count
    """
    cpu_pct = row.get("cpu", 0.0)
    tps = row.get("traffic", 0.0)
    pods = max(row.get("pods", 1), 1)

    # Observed TPS handled per pod
    tps_per_pod = tps / pods if pods > 0 else 1

    # If CPU is 0 (likely underutilized or missing data), return observed pods
    if cpu_pct == 0:
        return pods

    # Extrapolate TPS capacity if CPU scaled to max threshold
    tps_capacity = tps_per_pod / (cpu_pct / 100) * max_cpu_per_pod

    return max(ceil(tps / tps_capacity), 1)
