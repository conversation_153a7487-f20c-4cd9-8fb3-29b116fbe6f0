import requests
import logging, os, settings
from libs.prometheus_client import Prometheus
from importlib.machinery import SourceFileLoader
from libs.util import check_file_or_dir_exists
from pre_processor.validator import Validator

logger = logging.getLogger(__name__)


class Loader:
    def __init__(self, template, is_pre_processors=True):
        self.template = template
        self.is_pre_processors = is_pre_processors

    def get_executor_file(self):
        placeholders_tokens = os.path.splitext(self.template)
        if len(placeholders_tokens) != 2:
            return None
        template_without_postfix = placeholders_tokens[0]
        return f"{template_without_postfix}.py"

    def load_dynamic_executor_file(self):
        try:
            executor_file = self.get_executor_file()
            if executor_file is None:
                return None

            exec_dir = self.get_exec_dir()
            full_file_path = os.path.join(exec_dir, executor_file)
            if not check_file_or_dir_exists(full_file_path):
                return None

            return self._load_module_from_file(executor_file, exec_dir)
        except (FileNotFoundError, Exception) as e:
            logger.exception(e)
            return None

    def get_exec_dir(self):
        if self.is_pre_processors:
            _dir = "pre_processors"
        else:
            _dir = "post_processors"
        return os.path.join(settings.TEMPLATE_BASE_DIR, 'sre-alert-templates', _dir)

    def _load_module_from_file(self, executor_file, exec_dir):
        template_without_postfix = os.path.splitext(executor_file)[0]
        module = SourceFileLoader(template_without_postfix, f"{exec_dir}/{executor_file}").load_module()
        return module.Processor


if __name__ == "__main__":
    # from common.teap_handler.teap_data_handler import TeapDataHandler
    # from libs.oneOps import OneOps
    #
    # teap = TeapDataHandler().teap_data
    # data = teap.get_apps_by_platform_and_tier(platform="cosmosdb", tier="one")
    # _data = data[2]
    from template_engine.template_handler import get_latest_templates

    get_latest_templates(force_pull_required=True)
    post_data = {
        "global_data_vars": {
            "min": 20,
            "max": 50,
            "namespace": "mx-single-profile"

        },
        "create_pull_request": True,
        "apps_meta_data": [
            {
                "app_id": "mx-colony-service-prod-primary"
            }

        ]
    }
    connector = Loader("cron_scale_wcnp.yaml", post_data)
    pre_processor = connector.load_dynamic_executor_file()
    methods = dir(pre_processor)
    print(methods)
    data = pre_processor.execute()
    print(data)
    for _method in methods:
        if "validate_" in _method:
            is_annotated_with_property = isinstance(getattr(type(pre_processor), _method, None), property)
            if not is_annotated_with_property:
                m = getattr(pre_processor, _method)
                print(m())
