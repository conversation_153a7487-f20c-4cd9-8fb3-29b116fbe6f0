import requests

URL = "https://dl-read-api.prod.mase.glb.us.walmart.net/api/distribution-lists/{user}/subscribed"


def does_user_belongs_to_ad_group(user, provided_ad_groups):
    ad_groups = list()
    url = URL.format_map({"user": user})
    response = requests.get(url, verify=False)
    if response.ok:
        data = response.json()
        if len(data) > 0:
            ad_groups = [_data.get("alias") for _data in data]
    for ad_group in provided_ad_groups:
        if ad_group in ad_groups:
            return True
    return False


if __name__ == "__main__":
    print(does_user_belongs_to_ad_group("t0o03on", ["CAPrivacyOps"]))
