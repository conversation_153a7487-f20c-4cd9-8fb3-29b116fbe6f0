import requests
import logging
from collections import defaultdict

logger = logging.getLogger(__name__)
BASE_URL = "http://sre.api.prod.walmart.com/api/v1/datalake/active-taeps"
TAEP_BASE_URL = "http://sre.api.prod.walmart.com/api/v1/datalake/taep"
HEADERS = {'Content-Type': 'application/json'}


def get_one_ops_results(uri, base_url=None):
    # logger.debug('#Slack: Posting messagebody: {}'.format(message_body))
    try:
        base = base_url if base_url else BASE_URL
        r = requests.get(f"{base}/{uri}", headers=HEADERS, verify=False, timeout=10)
        logger.info("{}/{}".format(base, uri))
        return r.json()

    except Exception as e:
        return {'ok': False, 'error': "Exception: %s" % e}


def orgs():
    return get_one_ops_results("tenants")


def assemblies(org):
    uri = f"tenant/{org}/assemblies"
    return get_one_ops_results(uri)


def environments(org, assembly):
    uri = f"tenant/{org}/assembly/{assembly}/environments"
    return get_one_ops_results(uri)


def platforms(org, assembly, environment):
    uri = f"tenant/{org}/assembly/{assembly}/environment/{environment}/platforms"
    return get_one_ops_results(uri)


def computes(org, assembly, environment, platform):
    uri = f"tenant/{org}/assembly/{assembly}/environment/{environment}/platform/{platform}/computes"
    return get_one_ops_results(uri, base_url=TAEP_BASE_URL)


def ips(org, assembly, environment, platform):
    uri = f"tenant/{org}/assembly/{assembly}/environment/{environment}/platform/{platform}/computes"
    results = get_one_ops_results(uri, base_url=TAEP_BASE_URL)
    if results.get("http-status") == 200:
        _results = list()
        for compute in results["body"]["result"]:
            _results.append(compute["ipaddress"])
        return _results

    return get_one_ops_results(uri, base_url=TAEP_BASE_URL)


def cores(org, assembly, environment, platform):
    uri = f"tenant/{org}/assembly/{assembly}/environment/{environment}/platform/{platform}/computes"
    results = get_one_ops_results(uri, base_url=TAEP_BASE_URL)
    if results.get("http-status") == 200:
        all_dcs = {}
        d = defaultdict(list)
        for compute in results["body"]["result"]:
            d[compute["datacenter"]].append(compute)
        for k, v in d.items():
            if len(v) > 0:
                all_dcs[k] = {"total_core": len(v) * int(v[0]["cores"]), "computes": len(v),
                              "per_compute_cores": v[0]["cores"],
                              "per_compute_ram": v[0]["ram"]}
        return all_dcs

    return get_one_ops_results(uri, base_url=TAEP_BASE_URL)


def get_host_names(org, assembly, environment, platform):
    uri = f"tenant/{org}/assembly/{assembly}/environment/{environment}/platform/{platform}/fqdns"
    results = get_one_ops_results(uri, base_url=TAEP_BASE_URL)
    if results.get("http-status") == 200:
        _results = list()
        for compute in results["body"]["result"]:
            if len(compute.get('gslb_map')) != 0:
                continue
            _compute = compute["dns_names"][0]
            _results.append(_compute.get("name"))
        return _results

    return get_one_ops_results(uri, base_url=TAEP_BASE_URL)


if __name__ == "__main__":
    import os

    # log_level = logging.DEBUG
    log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(log_level)
    log_format = '%(asctime)s %(filename)18s %(lineno)3d %(levelname)10s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(log_level)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)

    import pprint

    # pprint.pprint(ips("gieo-perf","steller-api","proda","app"))
    pprint.pprint(get_host_names("mexicoecomm", "samsestore", "qa", "restapp"))
    # pprint.pprint(ips("mexicoecomm", "samsestore", "prod", "restapp"))
