import requests
from libs.wm_email import Em<PERSON><PERSON><PERSON>
import datetime
from settings import XMATTERS_API_KEY, XMATTERS_API_SECRET
from datetime import timezone
from dateutil import parser

# Set up the API endpoint and authentication details
base_url = "https://walmart.xmatters.com"
api_url = "https://walmart.xmatters.com/api/xm/1/events"
api_key = XMATTERS_API_KEY
api_secret = XMATTERS_API_SECRET
# Set up the request headers with authentication details
headers = {
    "Content-Type": "application/json"
}


def get_xmatters_events(group='intl-sre-p1', n_hours=24, start_epoch=None, end_epoch=None, **kwargs):
    # Calculate the date range for the last 'days' days
    end_date = datetime.datetime.utcnow()
    start_date = end_date - datetime.timedelta(hours=n_hours)

    if start_epoch:
        start_date = datetime.datetime.fromtimestamp(start_epoch)
    if end_epoch:
        end_date = datetime.datetime.fromtimestamp(end_epoch)

    # Format the date range in the required format for the API
    start_date_str = start_date.strftime("%Y-%m-%dT%H:%M:%Sz")
    end_date_str = end_date.strftime("%Y-%m-%dT%H:%M:%Sz")

    # Set up the request parameters with the date range and group
    params = {
        "from": start_date_str,
        "to": end_date_str,
        "offset": 0,
        'limit': 1000,
        'targetedRecipients': group
    }

    # Send the GET request to fetch the events
    response = requests.get(api_url, headers=headers, params=params, auth=(api_key, api_secret))

    result = []

    # Check if the request was successful
    if response.status_code == 200:
        events = response.json()
        for event in events['data']:
            if event['name'].rstrip('\n') not in result:
                # Convert to epoch using dateutil.parser
                timestamp = parser.isoparse(event['created'])
                result.append({
                    'name': event['name'].rstrip('\n'),
                    'timestamp': event['created'],
                    'epoch': int(timestamp.timestamp())
                })
    else:
        raise Exception(f"Failed to fetch events. Status code: {response.status_code}")

    return result


def generate_report(days=7, group='intl-sre-p1',num=10, **kwargs):
    # Calculate the date range for the last 'days' days
    end_date = datetime.datetime.utcnow()
    start_date = end_date - datetime.timedelta(days=days)

    # Format the date range in the required format for the API
    start_date_str = start_date.strftime("%Y-%m-%dT%H:%M:%Sz")
    end_date_str = end_date.strftime("%Y-%m-%dT%H:%M:%Sz")

    # Set up the request parameters with the date range and group
    params = {
        "from": start_date_str,
        "to": end_date_str,
        "offset": 0,
        'limit': 1000,
        'targetedRecipients': group
    }

    # Send the GET request to fetch the events
    response = requests.get(api_url, headers=headers, params=params, auth=(api_key, api_secret))

    # Check if the request was successful
    if response.status_code == 200:
        events = response.json()
        # Process the events as needed
        print(len(events['data']))
        # create a list of each containg tuple alert name and number of times triggered
        alert_dict = {}
        for alert in events['data']:
            alert_dict[alert['name'].rstrip('\n')] = alert_dict.get(alert['name'].rstrip('\n'), 0) + 1

        #  sort the list by number of times triggered
        sorted_alerts = sorted(alert_dict.items(), key=lambda x: x[1], reverse=True)

        # import csv

        # with open('xmatters_analysis.csv', 'w', newline='') as f:
        #     writer = csv.writer(f)
        #     writer.writerows(sorted_alerts)

        return sorted_alerts[:num]
    else:
        print("Failed to fetch events. Status code:", response.status_code)


def invoke_xmatters(subject, message):
    url = "https://walmart.xmatters.com/api/integration/1/functions/fc510f4e-4384-4f58-8448-f0c84e850bae/" \
          "triggers?apiKey=dd535793-bdf0-435d-a62a-fa61b155e454"
    # url = "https://walmart.xmatters.com/api/integration/1/functions/ce900564-553c-4415-9efc-7cd487c57fc1/triggers?apiKey=03542958-5d67-4a76-8e60-65585a1ee602"
    data = {
        "properties": {
            "subject": subject,
            "message": message
        }
    }

    data = requests.post(url, json=data, verify=False)
    return data


def email_report(recipients,days=7, group='intl-sre-p1', num=7,  **kwargs):
    try:
        alerts = generate_report(days, group,num)
        message = ""
        es = EmailSender()
        for alert in alerts:
            message += f"{alert[0]}: {alert[1]}\n"
        if not recipients:
            recipients = ['<EMAIL>']

        email_title = f"Alert Report for last {days} days"
        email_body = message
        es.submit(recipients, email_title, email_body,None,None)
        return message
    except Exception as e:
        print("Error in sending email", e)



# invoke_xmatters("namespace does not have enough hpa ratio data", "namespace does not have enough hpa ratio data")

if __name__ == "__main__":

    get_xmatters_events()
