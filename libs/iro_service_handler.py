import time

from settings import CACHE_FLUSH_META_DATA, CACHE_FLUSH_URL, ITEM_VISIBILITY, ITEM_DETAILS
import requests
import logging

logger = logging.getLogger(__name__)


class MeghaCacheHandler:
    def __init__(self):
        pass

    @staticmethod
    def get_headers():
        return {
            "Content-Type": "application/json",
            "correlationId": "dev-test"
        }

    @staticmethod
    def is_item_id(item_id):
        if len(item_id) > 20:
            # UPC
            return True
        # item_id
        return False

    @staticmethod
    def delete_item_cache(banner, item_id, ignore_details_from_payload=True):
        is_item_id = MeghaCacheHandler.is_item_id(item_id)
        payload = MeghaCacheHandler._get_item_cache_payload(banner, item_id, is_item_id, ignore_details_from_payload)
        res = requests.delete(CACHE_FLUSH_URL.get("delete"), json=payload,
                              headers=MeghaCacheHandler.get_headers(), verify=False)
        if res.ok:
            return {"response": res.text, "status": True, "item_id": item_id}
        return {"response": res.text, "status": False, "item_id": item_id}

    @staticmethod
    def delete_pno_cache(banner, item_id, store_id, ignore_details_from_payload=True):
        is_item_id = MeghaCacheHandler.is_item_id(item_id)
        payload = MeghaCacheHandler._get_pno_cache_body(banner, item_id, store_id, is_item_id,
                                                        ignore_details_from_payload)
        res = requests.delete(CACHE_FLUSH_URL.get("delete"), json=payload,
                              headers=MeghaCacheHandler.get_headers(), verify=False)
        if res.ok:
            return {"response": res.text, "status": True, "item_id": item_id}
        return {"response": res.text, "status": False, "item_id": item_id}

    @staticmethod
    def is_items_sellable(banner, items_with_store_id):
        """
        items_with_store_id = [{"item_or_upc":"","store_id":""}]
        """
        proxies = {
            "http": "http://sysproxy.wal-mart.com:8080",
            "https": "http://sysproxy.wal-mart.com:8080",
        }
        response_pay_load = list()
        pay_load = MeghaCacheHandler._get_is_item_sellable_payload(items_with_store_id)
        headers = MeghaCacheHandler.get_item_sellable_header(banner)
        response = requests.post(ITEM_VISIBILITY.get("url"), json=pay_load, headers=headers, verify=False,
                                 proxies=proxies)
        if response.ok:

            data = response.json()
            for item in data.get("items"):
                response_pay_load.append({"item_id": item.get("requestedItemId").get("value"),
                                          "store_id": item.get("requestedOffer").get("storeNumber"),
                                          "is_sellable": item.get("isSellable")})
        return response_pay_load

    @staticmethod
    def get_item_details(banner, item_or_upc):
        """
        """
        proxies = {
            "http": "http://sysproxy.wal-mart.com:8080",
            "https": "http://sysproxy.wal-mart.com:8080",
        }
        pay_load = MeghaCacheHandler.get_payload_for_item_sellable(item_or_upc)
        response_pay_load = list()
        headers = MeghaCacheHandler.get_item_sellable_header(banner)
        
        try:
            response = requests.post(ITEM_DETAILS.get("url"), json={"item": pay_load}, headers=headers, verify=False,
                                   proxies=proxies)
            
            if response.ok:
                data = response.json()
                item_data = data.get("item", {})
                ranked_offers = item_data.get("rankedOffers", [])
                
                for ranked_offer in ranked_offers:
                    # Extract WFS fields from availabilityInfo with safe gets
                    availability_info = ranked_offer.get("availabilityInfo", {})
                    
                    # Get raw values without defaults to allow null propagation for calculation
                    available_qty = availability_info.get("availableQty")
                    available_wfs_qty = availability_info.get("availableWfsQty")
                    wfs_global_inventory = availability_info.get("wfsGlobalInventory")
                    
                    # Calculate sellerAvailableQty with null propagation
                    seller_available_qty = None
                    if available_qty is not None and available_wfs_qty is not None and wfs_global_inventory is not None:
                        seller_available_qty = available_qty - available_wfs_qty - wfs_global_inventory
                    
                    _processed_dat = {
                        "upcId": item_data.get("requestedItemId", {}).get("value"),
                        "upstreamOfferId": ranked_offer.get("correlationIds", {}).get("upstreamOfferId"),
                        "sellerId": ranked_offer.get("sellerId"),
                        "sellerType": ranked_offer.get("sellerType"),
                        "rank": ranked_offer.get("rank"),
                        # Use raw values to allow nulls
                        "availableQty": available_qty,
                        "isStaleData": ranked_offer.get("isStaleData"),
                        # Add WFS fields without defaults
                        "wfsEnabled": availability_info.get("wfsEnabled"),
                        "availableWfsQty": available_wfs_qty,
                        "wfsGlobalEnabled": availability_info.get("wfsGlobalEnabled"),
                        "anyWfsEnabled": availability_info.get("anyWfsEnabled"),
                        "wfsGlobalInventory": wfs_global_inventory,
                        # Add the calculated field
                        "sellerAvailableQty": seller_available_qty
                    }
                    response_pay_load.append(_processed_dat)
            else:
                logger.error(f"Failed to get item details for {item_or_upc}. Status: {response.status_code}, Response: {response.text}")
        except Exception as e:
            logger.exception(f"Exception in get_item_details for {item_or_upc}: {str(e)}")
            
        return response_pay_load

    @staticmethod
    def get_item_sellable_header(banner):
        headers = dict()
        common_headers = ITEM_VISIBILITY.get("include_headers")
        banner_headers = ITEM_VISIBILITY.get("header").get(banner)
        headers.update({**banner_headers, **common_headers, "WM_QOS.CORRELATION_ID": str(int(time.time() * 1000)),
                        'User-Agent': 'WMTPerformance'})

        return headers

    @staticmethod
    def _get_item_cache_payload(banner, item_id, is_item_id, ignore_details_from_payload=False):
        payload = MeghaCacheHandler._get_item_cache_body(banner, item_id, is_item_id)
        if not ignore_details_from_payload:
            for item in payload.get("cacheKeys"):
                item["view"] = "detail"
        return payload

    @staticmethod
    def get_item_cache_data(banner, item_id, is_item_id=True, ignore_details_from_payload=False):
        data = MeghaCacheHandler._get_item_cache_payload(banner, item_id, is_item_id, ignore_details_from_payload)
        res = requests.post(CACHE_FLUSH_URL.get("get"), json=data,
                            headers=MeghaCacheHandler.get_headers(), verify=False)
        data = res.json()
        return data

    @staticmethod
    def get_pno_cache_data(banner, item_id, store_id, is_item_id=True):
        payload = MeghaCacheHandler._get_pno_cache_body(banner, item_id, store_id, is_item_id)
        res = requests.post(CACHE_FLUSH_URL.get("get"), json=payload,
                            headers=MeghaCacheHandler.get_headers(), verify=False)
        data = res.json()
        return data

    @staticmethod
    def _get_id_type(is_item_id):
        if is_item_id:
            return "I_"
        return "U_"

    @staticmethod
    def _get_item_cache_body(banner, item_id, is_item_id):

        return {
            "serviceName": "item",
            "idType": MeghaCacheHandler._get_id_type(is_item_id),
            "cacheKeys": [
                {
                    "itemId": item_id
                }
            ],
            "tenantId": CACHE_FLUSH_META_DATA.get(banner).get("tenant_id"),
            "bannerId": CACHE_FLUSH_META_DATA.get(banner).get("banner_id"),
            "mart": "walmart_mexico",
            "bu": "walmart_mexico",
            "vertical": CACHE_FLUSH_META_DATA.get(banner).get("vertical"),
            "version": CACHE_FLUSH_META_DATA.get(banner).get("version")
        }

    @staticmethod
    def _get_pno_cache_body(banner, item_id, store_id, is_item_id, ignore_details_from_payload=False):
        return {
            "cacheKeys": [
                {
                    "itemId": item_id,
                    "storeId": str(store_id)
                }
            ],
            "serviceName": "pno",
            "idType": MeghaCacheHandler._get_id_type(is_item_id),
            "tenantId": CACHE_FLUSH_META_DATA.get(banner).get("tenant_id"),
            "bannerId": CACHE_FLUSH_META_DATA.get(banner).get("banner_id"),
            "version": CACHE_FLUSH_META_DATA.get(banner).get("version"),
            "mart": "walmart_mexico",
            "bu": "walmart_mexico",
            "vertical": CACHE_FLUSH_META_DATA.get(banner).get("vertical")
        }

    @staticmethod
    def _get_is_item_sellable_payload(items_with_store_id):
        items = list()
        for item_with_store in items_with_store_id:
            items.append(MeghaCacheHandler.get_payload_for_item_sellable(item_with_store.get("item_or_upc"),
                                                                         item_with_store.get("store_id")))

        return {
            "items": items
        }

    @staticmethod
    def _get_is_item_details_payload(items_with_store_id):
        items = list()
        for item_with_store in items_with_store_id:
            items.append(MeghaCacheHandler.get_payload_for_item_sellable(item_with_store))

        return items

    @staticmethod
    def get_payload_for_item_sellable(item_id, store_id=None):
        status = MeghaCacheHandler.is_item_id(item_id)
        _data = "itemId" if status else "upc"
        data = {
            "itemId": {
                "value": item_id,
                "type": _data
            }
        }

        if store_id:
            data.update({"preferredStore": store_id})

        return data


if __name__ == "__main__":
    delete_cache = MeghaCacheHandler()
    # MeghaCacheHandler.get_item_cache("ea", "00750955284589", is_upc=True)
    delete_cache.get_pno_cache_data("ea", "00750043515073", "11220")
    # data = delete_cache.delete_item_cache("od", "d946c19e-16f1-418f-a580-c74f319d740e", is_item_id=False)
    # DeleteCache.delete_item_cache("ea", "f0fde411-7d0b-4c77-87d4-c5773d51b2e1")
    # DeleteCache.delete_pno_cache("od", "d946c19e-16f1-418f-a580-c74f319d740e","0000009999")
    # DeleteCache.delete_pno_cache("ea", "f0fde411-7d0b-4c77-87d4-c5773d51b2e1","0000005468")
    # data = MeghaCacheHandler.is_items_sellable("mx-ea",
    #                                            [{"item_or_upc": "8fa69915-6959-4bbd-9503-29bd198bee5b",
    #                                              "store_id": "0000005468"}])
    # 00750228783989
    # data2 = MeghaCacheHandler.get_item_details("mx-ea", "8fa69915-6959-4bbd-9503-29bd198bee5b")
    #data2 = MeghaCacheHandler.is_items_sellable("mx-ea", [{"item_or_upc": "00750639282788"}])
    #data2 = MeghaCacheHandler.get_item_details("mx-ea","00750043515073")
    data2=""
    print(data2)
