
import cx_Oracle
import sys, os
import cx_Oracle
import cx_Oracle
import sys
import os

try:
    if sys.platform.startswith("darwin"):
        lib_dir = os.path.join(os.environ.get("HOME"), "Downloads",
                               "instantclient_19_8")
        cx_Oracle.init_oracle_client(lib_dir=lib_dir)
    elif sys.platform.startswith("win32"):
        lib_dir=r"C:\oracle\instantclient_19_9"
        cx_Oracle.init_oracle_client(lib_dir=lib_dir)
except Exception as err:
    print("Whoops!")
    print(err);
    sys.exit(1);

connection = cx_Oracle.connect(user="READONLYOMS", password="READONLYOMS",
                               dsn="edc-ep14-scan.prod.walmart.com/mxomsp1_mxomspdbp1_ro.gecwalmart.com",
                               encoding="UTF-8")

cursor = connection.cursor()
cursor.execute('select * from database.table') # use triple quotes if you want to spread your query across multiple lines
for row in cursor:
    print(row)