import requests
import time
from concurrent.futures import ThreadPoolExecutor


class CarrierMethodService:
    """Service for retrieving carrier method information for a distributor/seller"""

    BU_MART_MAPPING = {
        "Walmart MX": {"BU_ID": "7", "MART_ID": "7"},
        "Walmart Sams": {"BU_ID": "208", "MART_ID": "208"},
        "mx": {"BU_ID": "7", "MART_ID": "7"},
        "sams": {"BU_ID": "208", "MART_ID": "208"}
    }

    DEFAULT_BU = "mx"

    def __init__(self):
        self.base_url = "http://dc-square.prod.cp.prod.walmart.com/dc-square-app/services/distributors"
        self.mcse_base_url = "https://mx-validar.prod.walmart.com/validate/distributors"
        self.max_threads = 5 # Maximum number of threads for parallel processing

    def get_headers(self, bu_id, mart_id):
        """Generate headers for the API request"""
        return {
            "WM_SVC.VERSION": "0.0.1",
            "WM_SVC.ENV": "prod",
            "WM_SVC.NAME": "dc-square-app",
            "WM_CONSUMER.SOURCE_ID": "OMS",
            "WM_QOS.CORRELATION_ID": str(int(time.time() * 1000)),
            "WM_CONSUMER.ID": "074f0c9b-bda4-4d25-bccf-4e9fbef9e681",
            "Content-Type": "application/json",
            "WM_BU.ID": bu_id,
            "WM_MART.ID": mart_id,
            "ENABLE_BU_MART_FILTER": "true"
        }

    def get_mcse_headers(self, bu_id):
        """Generate headers for the MCSE API request"""
        return {
            "accept": "application/json",
            "BU_ID": bu_id,
            "Content-Type": "application/json"
        }

    def get_distributor_details(self, distributor_id, business_unit="mx"):
        """
        Retrieves distributor details for a given distributor ID
        
        Args:
            distributor_id: Seller/partner ID (e.g., ***********)
            business_unit: Business unit name (e.g., "mx" or "sams")
            
        Returns:
            Dictionary containing distributor details
        """
        try:
            # Get BU/MART IDs for the specified business unit
            bu_mart = self.BU_MART_MAPPING.get(business_unit, self.BU_MART_MAPPING[self.DEFAULT_BU])
            bu_id = bu_mart["BU_ID"]
            mart_id = bu_mart["MART_ID"]
            
            # Prepare API request
            url = f"{self.base_url}/{distributor_id}"
            headers = self.get_headers(bu_id, mart_id)
            
            # Make API call
            proxies = {
                "http": "http://sysproxy.wal-mart.com:8080",
                "https": "http://sysproxy.wal-mart.com:8080",
            }
            
            response = requests.get(url, headers=headers, proxies=proxies, verify=False)
            
            if not response.ok:
                return {
                    "status": "error",
                    "message": f"API call failed with status code {response.status_code}"
                }
            
            # Process response data
            data = response.json()
            
            if data.get("status") == "OK" and data.get("payload") and data.get("payload").get("distributorCore"):
                distributor_core = data.get("payload").get("distributorCore")
                return {
                    "status": "success",
                    "data": {
                        "gecOrgId": distributor_core.get("gecOrgId"),
                        "distributorName": distributor_core.get("distributorName"),
                        "active": distributor_core.get("active"),
                        "distributorSupportedServices": distributor_core.get("distributorSupportedServices", [])
                    }
                }
            
            return {
                "status": "error",
                "message": "Failed to extract distributor details from response"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }

    def get_mcse_carrier_methods(self, distributor_id, business_unit="mx"):
        """
        Retrieves carrier methods from MCSE API for a given distributor ID
        
        Args:
            distributor_id: Seller/partner ID (e.g., ***********)
            business_unit: Business unit name (e.g., "mx" or "sams")
            
        Returns:
            Dictionary containing MCSE carrier methods
        """
        try:
            # Get BU ID for the specified business unit
            bu_mart = self.BU_MART_MAPPING.get(business_unit, self.BU_MART_MAPPING[self.DEFAULT_BU])
            bu_id = bu_mart["BU_ID"]
            
            # Prepare API request
            url = self.mcse_base_url
            headers = self.get_mcse_headers(bu_id)
            payload = [distributor_id]
            
            # Make API call
            proxies = {
                "http": "http://sysproxy.wal-mart.com:8080",
                "https": "http://sysproxy.wal-mart.com:8080",
            }
            
            response = requests.post(url, headers=headers, json=payload, proxies=proxies, verify=False)
            
            if not response.ok:
                return {
                    "status": "error",
                    "message": f"MCSE API call failed with status code {response.status_code}"
                }
            
            # Process response data
            data = response.json()
            mcse_carrier_methods = []
            
            if data and isinstance(data, list) and len(data) > 0:
                distributor_data = data[0]
                
                # Extract carrier methods data even if overall status is FAIL
                if distributor_data.get("carrierMethods"):
                    for item in distributor_data.get("carrierMethods", []):
                        mcse_carrier_methods.append({
                            "carrierMethodId": item.get("id"),
                            "itemSizes": item.get("itemSizes", []),
                            "pickupCalendar": item.get("pickupCalendar", []),
                            "transitDays": item.get("transitDays", {})
                        })
                    
                    return {
                        "status": "success",
                        "data": {
                            "sellerId": distributor_id,
                            "mcseStatus": distributor_data.get("status"),  # Include original MCSE status
                            "businessUnit": distributor_data.get("distributor", {}).get("businessUnit"),
                            "dcCalendar": distributor_data.get("dcCalendar", []),
                            "carrierMethods": mcse_carrier_methods
                        }
                    }
            
            return {
                "status": "error",
                "message": "Failed to extract MCSE carrier methods from response"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }

    def get_single_distributor_carrier_methods(self, distributor_id, business_unit="mx"):
        """
        Retrieves carrier methods for a single distributor ID
        
        Args:
            distributor_id: Seller/partner ID (e.g., ***********)
            business_unit: Business unit name (e.g., "mx" or "sams")
            
        Returns:
            Dictionary containing distributor details and carrier methods
        """
        try:
            # Get distributor details
            distributor_result = self.get_distributor_details(distributor_id, business_unit)
            if distributor_result.get("status") == "error":
                return distributor_result
            
            distributor_details = distributor_result.get("data")
            
            # Get BU/MART IDs for the specified business unit
            bu_mart = self.BU_MART_MAPPING.get(business_unit, self.BU_MART_MAPPING[self.DEFAULT_BU])
            bu_id = bu_mart["BU_ID"]
            mart_id = bu_mart["MART_ID"]
            
            # Prepare API request for carrier methods
            url = f"{self.base_url}/{distributor_id}/preferredcms"
            headers = self.get_headers(bu_id, mart_id)
            
            # Make API call
            proxies = {
                "http": "http://sysproxy.wal-mart.com:8080",
                "https": "http://sysproxy.wal-mart.com:8080",
            }
            
            response = requests.get(url, headers=headers, proxies=proxies, verify=False)
            
            if not response.ok:
                return {
                    "status": "error",
                    "message": f"API call failed with status code {response.status_code}"
                }
            
            # Process response data
            data = response.json()
            carrier_methods = []
            
            if data.get("status") == "OK" and data.get("payload"):
                for item in data.get("payload", []):
                    carrier_methods.append({
                        "carrierMethodId": item.get("carrierMethodId"),
                        "carrierMethodName": item.get("carrierMethodName"),
                        "shippingMethod": item.get("shippingMethod"),
                        "createdBy": item.get("createdBy"),
                        "modifiedBy": item.get("modifiedBy"),
                        "isActive": item.get("isActive")
                    })
            
            return {
                "status": "success",
                "data": {
                    "sellerId": distributor_id,
                    "distributor": distributor_details,
                    "carrierMethods": carrier_methods
                }
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }
    
    def get_single_distributor_cross_validation(self, distributor_id, business_unit="mx"):
        """
        Retrieves and cross-validates carrier methods between DCC and MCSE for a single distributor ID
        
        Args:
            distributor_id: Seller/partner ID (e.g., ***********)
            business_unit: Business unit name (e.g., "mx" or "sams")
            
        Returns:
            Dictionary containing distributor details and cross-validated carrier methods
        """
        try:
            # Get distributor details
            distributor_result = self.get_distributor_details(distributor_id, business_unit)
            if distributor_result.get("status") == "error":
                return distributor_result
            
            distributor_details = distributor_result.get("data")
            
            # Get BU/MART IDs for the specified business unit
            bu_mart = self.BU_MART_MAPPING.get(business_unit, self.BU_MART_MAPPING[self.DEFAULT_BU])
            bu_id = bu_mart["BU_ID"]
            mart_id = bu_mart["MART_ID"]
            
            # Prepare API request for carrier methods
            url = f"{self.base_url}/{distributor_id}/preferredcms"
            headers = self.get_headers(bu_id, mart_id)
            
            # Make API call
            proxies = {
                "http": "http://sysproxy.wal-mart.com:8080",
                "https": "http://sysproxy.wal-mart.com:8080",
            }
            
            response = requests.get(url, headers=headers, proxies=proxies, verify=False)
            
            if not response.ok:
                return {
                    "status": "error",
                    "message": f"API call failed with status code {response.status_code}"
                }
            
            # Process response data
            data = response.json()
            carrier_methods = []
            
            if data.get("status") == "OK" and data.get("payload"):
                for item in data.get("payload", []):
                    carrier_methods.append({
                        "carrierMethodId": item.get("carrierMethodId"),
                        "carrierMethodName": item.get("carrierMethodName"),
                        "shippingMethod": item.get("shippingMethod"),
                        "createdBy": item.get("createdBy"),
                        "modifiedBy": item.get("modifiedBy"),
                        "isActive": item.get("isActive")
                    })
            
            # Get MCSE carrier methods
            mcse_result = self.get_mcse_carrier_methods(distributor_id, business_unit)
            mcse_methods = {}
            
            if mcse_result.get("status") == "success":
                mcse_data = mcse_result.get("data", {})
                # Create map of carrier method ID to details for easier lookup
                for cm in mcse_data.get("carrierMethods", []):
                    mcse_methods[cm.get("carrierMethodId")] = cm
            
            # Combine DCC and MCSE carrier methods
            combined_methods = []
            
            # Add all methods from DCC first
            for cm in carrier_methods:
                cm_id = cm.get("carrierMethodId")
                combined_methods.append({
                    **cm,
                    "is_dcc": True,
                    "is_mcse": cm_id in mcse_methods,
                    "mcse_details": mcse_methods.get(cm_id, {}) if cm_id in mcse_methods else None
                })
            
            # Add methods from MCSE that aren't in DCC
            for cm_id, cm_details in mcse_methods.items():
                if not any(m.get("carrierMethodId") == cm_id for m in carrier_methods):
                    combined_methods.append({
                        "carrierMethodId": cm_id,
                        "carrierMethodName": None,
                        "shippingMethod": None,
                        "createdBy": None,
                        "modifiedBy": None,
                        "isActive": None,
                        "is_dcc": False,
                        "is_mcse": True,
                        "mcse_details": cm_details
                    })
            
            return {
                "status": "success",
                "data": {
                    "sellerId": distributor_id,
                    "distributor": distributor_details,
                    "carrierMethods": combined_methods
                }
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }
    
    def get_carrier_methods_cross_validation(self, distributor_ids, business_unit="mx"):
        """
        Retrieves and cross-validates carrier methods between DCC and MCSE for multiple distributor IDs
        
        Args:
            distributor_ids: Single Seller/partner ID or list of IDs (e.g., "***********" or ["***********", "***********"])
            business_unit: Business unit name (e.g., "mx" or "sams"), applied to all distributor IDs
            
        Returns:
            Dictionary containing:
            - distributors: List of distributor details and their cross-validated carrier methods
        """
        try:
            # Convert single ID to list if necessary
            if not isinstance(distributor_ids, list):
                distributor_ids = [distributor_ids]
                
            # Limit to maximum 15 IDs per request
            if len(distributor_ids) > 15:
                distributor_ids = distributor_ids[:15]
                
            results = []
            
            # Process distributor IDs in parallel using ThreadPoolExecutor
            with ThreadPoolExecutor(min(self.max_threads, len(distributor_ids))) as executor:
                # Submit tasks to the executor
                future_to_id = {
                    executor.submit(self.get_single_distributor_cross_validation, distributor_id, business_unit): distributor_id
                    for distributor_id in distributor_ids
                }
                
                # Process results as they complete
                for future in future_to_id:
                    result = future.result()
                    if result.get("status") == "success":
                        results.append(result.get("data"))
            
            return {
                "status": "success",
                "data": {
                    "distributors": results
                }
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }

    def get_carrier_methods(self, distributor_ids, business_unit="mx"):
        """
        Retrieves carrier methods and distributor details for multiple distributor IDs
        
        Args:
            distributor_ids: Single Seller/partner ID or list of IDs (e.g., "***********" or ["***********", "***********"])
            business_unit: Business unit name (e.g., "mx" or "sams"), applied to all distributor IDs
            
        Returns:
            Dictionary containing:
            - distributors: List of distributor details and their carrier methods
        """
        try:
            # Convert single ID to list if necessary
            if not isinstance(distributor_ids, list):
                distributor_ids = [distributor_ids]
                
            # Limit to maximum 15 IDs per request
            if len(distributor_ids) > 15:
                distributor_ids = distributor_ids[:15]
                
            results = []
            
            # Process distributor IDs in parallel using ThreadPoolExecutor
            with ThreadPoolExecutor(min(self.max_threads, len(distributor_ids))) as executor:
                # Submit tasks to the executor
                future_to_id = {
                    executor.submit(self.get_single_distributor_carrier_methods, distributor_id, business_unit): distributor_id
                    for distributor_id in distributor_ids
                }
                
                # Process results as they complete
                for future in future_to_id:
                    result = future.result()
                    if result.get("status") == "success":
                        results.append(result.get("data"))
            
            return {
                "status": "success",
                "data": {
                    "distributors": results
                }
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            } 