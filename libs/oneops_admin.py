#!/usr/bin/env python
"""
Description: This script can be used to do some common oneops related stuff. E.g. run commands on computes, retrieve
list of computes, hostnames, vips etc.

Usage:

1. See help:
    $ ./oneops.py --help

2. Run command
    $ ./oneops.py -a sunnyvale -e jenkins-proda -p base --cmd 'cat /etc/labs_tags' -u polaris

3. View Compute/IP list

    $ ./oneops.py -a sunnyvale -e jenkins-proda -p base --computes
    $ ./oneops.py -a Preso -e prod-a -p app-preso --computes

4. View hostnames

    $ ./oneops.py -a Preso -e prod-a -p app-preso --hostnames

5. View VIPs

    $ ./oneops.py -a Preso -e prod-a -p app-preso --vips

6. Run a command, in batches, with some wait time between batches

    $ ./oneops.py -a Preso -e prod-a -p app-preso --cmd 'sudo service tomcat7 restart' --wait 120 --threads 5

7. Mark a cloud as Secondary

    $ ./oneops.py -a Preso -e prod-a -p app-preso --secondary prod-dal1

8. Mark a cloud as Primary

    $ ./oneops.py -a Preso -e prod-a -p app-preso --primary prod-dal1

9. Mark a cloud as Ignored

    $ ./oneops.py -a Preso -e prod-a -p app-preso --ignored prod-dal1

10. Mark a cloud as Active

    $ ./oneops.py -a Preso -e prod-a -p app-preso --active prod-dal1

11. Mark a cloud as Secondary and trigger a deployment

    $ ./oneops.py -a Preso -e prod-a -p app-preso --secondary prod-dal1 --deploy

12. Update a variable and trigger a deployment

    $ ./oneops.py -a Preso -e prod-a -p app-preso --varname appVersion --varvalue 0.2.9 --deploy

13. Run a command only against certain clouds

    $ ./oneops.py -a Preso -e prod-a -p app-preso -c prod-dal1,prod-dal2 --cmd 'uptime'

14. Pull design for an environment and Deploy simultaneously

    $ ./oneops.py -a electrode-search -e stage --pull --deploy

15. Add a new cloud level variable

    $ ./oneops.py --clouds prod-cdc7,prod-cdc8 --cvar hello --cval 12345689

15. Add a new Encrypted cloud level variable

    $ ./oneops.py --clouds prod-cdc7 --cvar mypass --cval 123lkjs2 --cencrypt

16. Update a Cloud level variable with new value

    $ ./oneops.py --clouds prod-cdc7,prod-cdc8 --cvar hello --cval world --ucvar

17. Delete a Cloud level variable

    $ ./oneops.py --clouds prod-cdc7,prod-cdc8 --cvar hello --dcvar

18. Cancel any pending failed deployments

    $ ./oneops.py -a Preso -e prod-b --cancel-failed-deploy

19. Pull Design to ALL Environments

    $ ./oneops.py -a Preso -p app-preso --pull-all

20. Fetch VARIABLE value for ALL Environments of a given Assembly, for a given Variable

    $ ./oneops.py -a Preso -p app-preso --get-vars --varname appVersion

21. Toggle the Default Relay for all Environments of a given assembly

    $ ./oneops.py -a Preso -p app-preso --toggle-all-relays

22. Restart tomcat-daemon component on all computes with a step percentage of 20

    $ ./oneops.py -a Preso -e stage -p app-preso --mode operations --component tomcat-daemon --action restart --step 20

23. Restart tomcat-daemon component on specific clouds with max 3 retries on failure

    $ ./oneops.py -a Preso -e stage -p app-preso -c prod-dal1,prod-dfw1 --mode operations --component tomcat-daemon --action restart --step 50 --retry-action 3

24. Extract Design for a given Assembly to a JSON file (file will be written to in the directory from where the command
    is executed, not the directory where oneops.py exists)

    $ ./oneops.py -a Preso --extract

25. List all Assemblies for a given Org

    $ ./oneops.py --assemblies
"""

import os
import sys
import json
import time
import base64
import urllib3
import logging
import warnings
import requests
import itertools
import subprocess
from functools import partial
from math import ceil
from multiprocessing import Pool
from optparse import OptionParser
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

try:
    import progressbar
    from progressbar.widgets import Widget
    from progressbar.widgets import Percentage
    from progressbar.widgets import AnimatedMarker

    NO_PROGRESSBAR = False
except ImportError:
    NO_PROGRESSBAR = True
try:
    # For python2 backward compatibility
    input = raw_input
except NameError:
    pass

__author__ = 'Parth Santpurkar'
__python3_compatible__ = True

# Disable Insecure URL warnings...
urllib3.disable_warnings()
from requests.packages.urllib3.exceptions import InsecureRequestWarning

requests.packages.urllib3.disable_warnings(InsecureRequestWarning)
# turn down requests log verbosity
logging.getLogger('requests').setLevel(logging.CRITICAL)

DEFAULT_API_TOKEN = 'a1G2uX66BiqJGsKKLqzj'
DEFAULT_ONEOPS_ORG = 'polaris'
DEFAULT_NUM_PROCESSES = 20
COMMAND = 'uptime'
USER = 'app'
NO_VERBOSE = False
PROTECTED_COMMANDS = ['stop', 'start', 'restart']
PROTECTED_COMMANDS_MIN_WAIT_TIME = 60
SPINNER = itertools.cycle(['-', '/', '|', '\\'])

#####################################
# Cronus API to fetch CRQ Defaults  #
#####################################
CRONUS_HOST = 'http://cronus.walmart.com'
CRONUS_REDIS_API = "/tools/api/redis/?op=get&access_key=52430167-3d2d-4115-b93a-42ee22009955&key="
CHANGE_REQUEST_REDIS_KEY = "holiday-focus-blanket-crq"
NEW_SESSION = requests.Session()
RETRIES = Retry(total=5, backoff_factor=1, status_forcelist=[502, 503, 504, 521])
NEW_SESSION.mount('http://', HTTPAdapter(max_retries=RETRIES))
DEFAULT_COMMIT_MESSAGE = "Change commited by oneops.py"

#################################
#     ONEOPS REST API URLS      #
#################################
ONEOPS_BASE_API_URL = 'https://oneops.prod.walmart.com'
ONEOPS_HERMES_METRICS_URL = 'http://$HOSTNAME:6969/publish/'
ORGANIZATION_LIST_API_URL = ONEOPS_BASE_API_URL + '/account/organizations.json'
CLOUD_LIST_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/clouds.json'
ASSEMBLY_LIST_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies.json'
ASSEMBLY_EXTRACT_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/design/extract.json'
ENVIRONMENT_LIST_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/operations/environments/'
ENVIRONMENT_GET_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/operations/environments/' \
                                                '$ENVIRONMENT.json'
PLATFORM_LIST_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/design/platforms/'
PLATFORM_GET_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/design/platforms/$PLATFORM.json'
COMPUTE_LIST_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/operations/environments/' \
                                             '$ENVIRONMENT/platforms/$PLATFORM/components/compute/' \
                                             'instances.json?instances_state=all'
COMPONENT_DETAIL_LIST_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/operations/environments/' \
                                                      '$ENVIRONMENT/platforms/$PLATFORM/components/$COMPONENT/' \
                                                      'instances.json?instances_state=all'
VARIABLE_LIST_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/transition/environments/' \
                                              '$ENVIRONMENT/platforms/$PLATFORM/variables.json'
CLOUD_PRIORITY_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/transition/environments/' \
                                               '$ENVIRONMENT/platforms/$PLATFORM/cloud_priority'
CLOUD_CONFIG_STATUS_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/transition/environments/' \
                                                    '$ENVIRONMENT/platforms/$PLATFORM/cloud_configuration'
ORG_QUOTA_REPORT_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/reports/compute?format=json'
COMMIT_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/transition/environments/$ENVIRONMENT/commit'
RELEASE_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/transition/environments/$ENVIRONMENT/releases/bom'
NEW_DEPLOYMENT_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/transition/environments/' \
                                               '$ENVIRONMENT/deployments'
DEPLOYMENT_STATUS_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/transition/environments/' \
                                                  '$ENVIRONMENT/deployments/$DEPLOYMENT_ID/status'
DEPLOYMENT_STATE_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/transition/environments/' \
                                                 '$ENVIRONMENT/deployments/latest'
DEPLOYMENT_PROGRESS_STATUS_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/transition/environments/' \
                                                           '$ENVIRONMENT/deployments/$DEPLOYMENT_ID/status.json'
TRANSITION_STATUS_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/transition/environments/$ENVIRONMENT.json'
TRANSITION_DESIGN_PULL_URL = ONEOPS_BASE_API_URL + "/$ORGANIZATION/assemblies/$ASSEMBLY/transition/environments/$ENVIRONMENT/pull?async=true"
TRANSITION_DESIGN_PULL_STATUS_URL = ONEOPS_BASE_API_URL + "/$ORGANIZATION/assemblies/$ASSEMBLY/transition/environments/$ENVIRONMENT/pull_status.json"
TRANSITION_VARIABLE_VERSION_UPDATE_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/transition/' \
                                                               'environments/$ENVIRONMENT/platforms/$PLATFORM/variables/$VARNAME'
TRANSITION_ENVIRONMENT_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/transition/' \
                                                       'environments/$ENVIRONMENT/platforms/$PLATFORM.json'
COMPONENT_TOUCH_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/transition/' \
                                                'environments/$ENVIRONMENT/platforms/$PLATFORM/' \
                                                'components/$COMPONENT/touch.json'
CLOUD_VARIABLE_ADD_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/clouds/$CLOUD/variables'
CLOUD_VARIABLE_EDIT_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/clouds/$CLOUD/variables/$VARNAME'
LAST_DEPLOYMENT_STATUS_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/transition/' \
                                                   'environments/$ENVIRONMENT/deployments/latest.json'
CANCEL_FAILED_DEPLOYMENT_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/transition/' \
                                                     'environments/$ENVIRONMENT/deployments/$LAST_DEPLOYMENT_ID.json'
DEFAULT_RELAY_TOGGLE_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/transition/environments/' \
                                                     '$ENVIRONMENT/relays/default/toggle'

OPERATIONS_COMPONENT_ID_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/operations/environments/' \
                                                    '$ENVIRONMENT/platforms/$PLATFORM/components/$COMPONENT.json'
OPERATIONS_COMPONENT_ACTIONS_API_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/assemblies/$ASSEMBLY/operations/environments/' \
                                                             '$ENVIRONMENT/platforms/$PLATFORM/components/$COMPONENT/actions.json'
OPERATIONS_EXECUTE_ACTION_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/operations/procedures'
OPERATIONS_STATUS_URL = ONEOPS_BASE_API_URL + '/$ORGANIZATION/operations/procedures/$PROCEDURE_ID.json'

TRANSITION_VARIABLE_PAYLOAD = {
    'cms_dj_ci': {
        'ciAttributes': {
            'value': '',
            'encrypted_value': ''
        }
    }
}

CLOUD_VARIABLE_PAYLOAD = {
    "cms_ci": {
        "ciClassName": "account.Cloudvar",
        "nsPath": "/polaris/_clouds/$CLOUD",
        "ciName": "$VARNAME",
        "ciAttributes": {
            "secure": False,
            "value": "",
            "encrypted_value": ""
        }
    }
}

EXECUTE_ACTION_PAYLOAD = {
    "cms_procedure": {
        "procedureCiId": "0",
        "procedureName": "$ACTION_NAME",
        "definition": {
            "flow": [],
            "name": "$ACTION_NAME"
        },
        "ciId": "$COMPONENT_ID",
        "procedureState": "active"
    }
}

EXECUTE_ACTION_FLOW_PAYLOAD = {
    "targetIds": ["$INSTANCE_ID"],
    "relationName": "base.RealizedAs",
    "direction": "from",
    "actions": [{
        "actionName": "$ACTION_NAME",
        "stepNumber": 1,
        "extraInfo": 0,
        "isCritical": True
    }]
}


class Oneops:
    """
    Models all Oneops functionalities and APIs
    """
    VALID_CLOUD_PRIORITIES = [1, 2]
    VALID_CLOUD_STATUSES = ['active', 'inactive']
    DEPLOYMENT_DONE_STATES = ['failed', 'canceled', 'complete']
    DEPLOYMENT_NOT_SUCCESSFUL_STATES = ['failed', 'canceled']

    def __init__(self, api_token, org, assembly=None, platform=None, environment=None, component=None, **kwargs):
        """
        Args:
            api_token (str): A Oneops API Token
            org: Oneops organization
            assembly: Oneops assembly name
            platform: Oneops platform name
            environment: Oneops environment name
            kwargs:
                disable_progressbar (bool): Disable drawing a progressbar for deployments
                cloud_activity (bool): Whether actions are being taken directly at cloud level, and not directed towards
                                        any assembly/environment (helps skip platform check)
        """
        self.api_token = base64.b64encode(api_token.encode('utf-8'))
        self.header = {
            'Authorization': 'Basic {}'.format(self.api_token.decode('utf-8')),
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
        self.org = org
        self.assembly = assembly
        self.platform = platform
        self.component = component
        self.environment = environment
        self.last_deployment_id = None
        self.deployment_id = None
        self.disable_progressbar = kwargs.get('disable_progressbar', False)
        self.performing_cloud_activity = kwargs.get('cloud_activity', False)
        self.performing_org_activity = kwargs.get('org_activity', False)
        self.performing_assembly_activity = kwargs.get('assembly_activity', False)
        self.commit_message = kwargs.get('commit_message')
        # Updated by method: _update_deployment_progress_status()
        self._deployment_progress_status = {}

        if not (self.performing_cloud_activity or self.performing_org_activity or self.performing_assembly_activity) \
                and self.platform is None:
            platforms = self.get_all_platforms()
            if len(platforms) == 1:
                self.platform = platforms[0]
                print_pretty('Using platform: {}'.format(self.platform))
            else:
                raise RuntimeError('Could not determine platform to use. Please specify one of these Available '
                                   'Platforms => {}'.format(platforms))

        print_pretty('ORG: {} | ASSEMBLY: {} | ENV: {} | PLATFORM: {}'.format(self.org, self.assembly, self.environment,
                                                                              self.platform))

    def get_all_orgs(self):
        """
        Returns the list of Organizations associated with the token
        """
        data = []
        _response = self._get_oneops_response(ORGANIZATION_LIST_API_URL)
        for org in _response:
            data.append(org.get('name'))
        return data

    def get_all_assemblies(self, org_name=None):
        """
        Returns a list of Assemblies for a given Org
        Args:
            org_name (str):
        """
        data = []
        _url = ASSEMBLY_LIST_API_URL.replace('$ORGANIZATION', org_name if org_name else self.org)
        _response = self._get_oneops_response(_url)
        for _assembly in _response:
            data.append(_assembly.get('ciName'))
        return data

    def get_all_environments(self, org_name=None, assembly_name=None, raw=False):
        """
        Given an Org and an Assembly, returns the list of Environments
        Args:
            org_name (str): Name of the org
            assembly_name (str): Name of the assembly
            raw (bool): Return the raw response
        """
        _response = self._retrieve_env_list(
            org_name if org_name else self.org,
            assembly_name if assembly_name else self.assembly
        )
        if raw:
            return _response
        return [env.get('ciName') for env in _response]

    def get_all_clouds(self, org_name=None):
        """
        Given an Org, retrieves info about all Clouds
        Args:
            org_name (str): Name of the org
        """
        org_name = org_name if org_name else self.org
        _url = CLOUD_LIST_API_URL.replace('$ORGANIZATION', org_name)
        _response = self._get_oneops_response(_url)
        return _response

    def get_all_cloud_ids(self, org_name):
        """
        Given an Org, return a dict containing each cloud and its ID
        Args:
            org_name (str): Name of the org
        Returns:
            A dict containing cloud-name and its id
        """
        cloud_data = self.get_all_clouds(org_name)
        response = {}
        for c in cloud_data:
            response[c.get('ciName')] = c.get('ciId')
        return response

    def get_all_computes(self, org_name, assembly_name, env_name, platform_name=None, clouds=None):
        """
        Returns the list of IP Addresses of all computes for a given Env
        """
        compute_list = self._retrieve_component_list(org_name, assembly_name, env_name, 'compute', platform_name)
        if clouds:
            print('Filtering computes by cloud(s): {}'.format(clouds))
            compute_list = [compute for compute in compute_list if
                            compute.get('cloud', {}).get('toCi', {}).get('ciName', {}) in clouds]
        return [compute.get('ciAttributes', {}).get('public_ip') for compute in compute_list]

    def get_all_hostnames(self, org_name, assembly_name, env_name, platform_name=None):
        """
        Returns the list of IP Addresses of all computes for a given Env
        """
        _response = self._retrieve_component_list(org_name, assembly_name, env_name, 'compute', platform_name)
        return ['{}.{}.{}.{}.<domain>'.format(compute.get('ciAttributes', {}).get('hostname'), env_name, assembly_name,
                                              org_name) for compute in _response]

    def get_all_computes_per_cloud(self, org_name=None, assembly_name=None, env_name=None, platform_name=None,
                                   exclude_secondary_clouds=False):
        """
        Returns list of computes categorized per cloud
        """
        _response = self._retrieve_component_list(org_name if org_name else self.org,
                                                  assembly_name if assembly_name else self.assembly,
                                                  env_name if env_name else self.environment,
                                                  'compute', platform_name if platform_name else self.platform)
        data = {}
        for c in _response:
            cloud = c.get('deployedTo', {}).get('ciName')
            ip = c.get('ciAttributes', {}).get('public_ip')
            if exclude_secondary_clouds and c.get('cloud', {}).get('relationAttributes', {}).get('priority',
                                                                                                 '1') != '1':
                continue
            if cloud in data:
                data[cloud].append(ip)
            else:
                data[cloud] = [ip]
        return data

    def get_env_vip(self, org_name, assembly_name, env_name, platform_name=None):
        """
        Returns the main GLB VIP for a given Org|Assembly|Env|Platform
        """
        _response = self._retrieve_component_list(org_name, assembly_name, env_name, 'fqdn', platform_name)
        _vips = []
        if _response:
            _response = _response[0]
            gslb_names = json.loads(_response.get('ciAttributes', {}).get('gslb_vnames', "{}"))
            if gslb_names:
                vip = list(gslb_names.values())[0]
                _vips.append(vip)

            full_aliases = json.loads(_response.get('ciAttributes', {}).get('full_aliases', "[]") or "[]")
            if full_aliases:
                _vips += full_aliases

            gslb_map = json.loads(_response.get('ciAttributes', "{}").get('gslb_map', "{}")).get('glb')
            if gslb_map:
                _vips.append(gslb_map)
        return _vips

    def get_variable(self, variable, environment=None):
        """
        Fetch a variable
        :param environment:
        :return:
        """
        if environment:
            self.environment = environment
        url = self._format_url('{}.json'.format(TRANSITION_VARIABLE_VERSION_UPDATE_URL.replace('$VARNAME', variable)))
        result = self.invoke_api(url, method='get')
        if result['success']:
            return result['response']['ciAttributes']['value']
        return result['response']

    def get_all_variables(self, org_name, assembly_name, env_name):
        """
        Returns the list of variables as key-value pairs for a given Org|Assembly|Env
        Returns:
            A dict of the form
            {

                'platform1': {
                    k1: v1
                    k2: v2
                },
                'platform2': {
                    k1: v1
                    k2: v2
                },
            }
        """
        data = {}
        _platforms = self.get_all_platforms()
        for platform in _platforms:
            _url = VARIABLE_LIST_API_URL.replace('$ORGANIZATION', org_name).replace('$ASSEMBLY', assembly_name). \
                replace('$ENVIRONMENT', env_name).replace('$PLATFORM', platform)
            _response = self._get_oneops_response(_url)
            _vars = {}
            for var in _response:
                _vars[var['ciName']] = var.get('ciAttributes', {}).get('value')
            data[platform] = _vars
        return data

    def get_all_platforms(self):
        _url = PLATFORM_LIST_API_URL.replace('$ORGANIZATION', self.org).replace('$ASSEMBLY', self.assembly)
        _response = self._get_oneops_response(_url)
        return [x.get('ciName') for x in _response]

    def get_org_reports(self, org_name):
        """
        Generate Summary Report for a given Org
        """
        _url = ORG_QUOTA_REPORT_API_URL.format('$ORGANIZATION', org_name)
        _response = self._get_oneops_response(_url)
        return _response

    def get_all_clouds_for_environment(self):
        """
        For a given environment fetch all the clouds
        """
        url = TRANSITION_ENVIRONMENT_API_URL.replace('$ORGANIZATION', self.org).replace('$ASSEMBLY', self.assembly). \
            replace('$ENVIRONMENT', self.environment).replace('$PLATFORM', self.platform)
        data = self.invoke_api(url)['response']
        return [x['toCi']['ciName'] for x in data['consumes']]

    def get_operations_component_details(self):
        """
        :return
            {
                'actions': List of supported actions
                'id': ID of the component
            }
        """
        id_url = self._format_url(OPERATIONS_COMPONENT_ID_URL)
        actions_url = self._format_url(OPERATIONS_COMPONENT_ACTIONS_API_URL)
        id_data = self._get_oneops_response(id_url)
        actions_data = self._get_oneops_response(actions_url)
        _component = {}
        if isinstance(actions_data, list) and 'ciId' in id_data:
            _component['actions'] = [
                {'name': i['description'], 'id': i['actionId'], 'apiName': i['actionName'], 'isCustom': True}
                if i['actionName'] == 'user-custom-attachment'
                else {'name': i['actionName'], 'id': i['actionId'], 'apiName': i['actionName'], 'isCustom': False}
                for i in actions_data]
            _component['id'] = id_data['ciId']
            _component['status'] = True
        else:
            _component['status'] = False
        return _component

    def get_operations_instance_ids(self, clouds=False):
        """
        :param
            clouds     : Optional list of clouds to run on
        :return
            List of instance IDs
        """
        url = self._format_url(COMPONENT_DETAIL_LIST_API_URL)
        data = self._get_oneops_response(url)
        if isinstance(data, list):
            if clouds:
                return [str(i['ciId']) for i in data if i['deployedTo']['ciName'] in clouds]
            else:
                return [str(i['ciId']) for i in data]

    def wait_for_operations(self, id):
        """
        :param
            id: ID of the procedure triggered
        :return:
            Status of the operation of type string
        """
        url = self._format_url(OPERATIONS_STATUS_URL).replace('$PROCEDURE_ID', str(id))
        _status = {
            "state": "active"
        }
        while _status['state'] == "active":
            for i in range(5):
                data = self._get_oneops_response(url)
                if data:
                    break
                time.sleep(5)
            if not data:
                raise RuntimeError("Failed to get status for operation ID {} from OneOps".format(id))
            _status['state'] = data['procedureState']
            _status['num_instances'] = len(data['actions'])
            _status['inprogress'] = len([i for i in data['actions'] if i['actionState'] == 'inprogress'])
            _status['complete'] = len([i for i in data['actions'] if i['actionState'] == 'complete'])
            _status['pending'] = len([i for i in data['actions'] if i['actionState'] == 'pending'])
            _status['failed'] = len([i for i in data['actions'] if i['actionState'] == 'failed'])
            sys.stdout.write(
                "(OPERATION) [ IN-PROGRESS: {1}/{0} ] [ PENDING: {2}/{0} ] [ DONE: {3}/{0} ] [ FAILED: {4}/{0} ] [ PERCENT DONE: {5} % ]\n".format(
                    _status['num_instances'], _status['inprogress'], _status['pending'], _status['complete'],
                    _status['failed'],
                    int(_status['complete'] / float(_status['num_instances']) * 100)))
            sys.stdout.flush()
            time.sleep(10)
        print()
        if _status['state'] == 'complete':
            return {
                'status': True,
            }
        else:
            return {
                'status': False,
                'failed': [i['ciId'] for i in data['actions'] if i['actionState'] == 'failed']
            }

    def execute_action_on_component(self, component_id, instances, action, action_id, step, is_custom):
        """
        :param
            component_id    : Id of the component
            instances       : List of instance IDs to run action on
            action          : Action to execute
            action_id       : Id of the action
            step            : The roll percentage to execute the action
            is_custom       : Whether the action is user defined
        :return:
            Returns procedureId of type string
        """
        url = self._format_url(OPERATIONS_EXECUTE_ACTION_URL)
        _payload = dict(EXECUTE_ACTION_PAYLOAD)
        _payload['cms_procedure']['procedureName'] = action
        _payload['cms_procedure']['definition']['name'] = action
        _payload['cms_procedure']['ciId'] = str(component_id)
        _split_data = self.get_operations_split_data(instances, step)
        for i in _split_data['instances']:
            _flow = dict(EXECUTE_ACTION_FLOW_PAYLOAD)
            _flow['targetIds'] = i
            _flow['actions'][0]['actionName'] = action
            if is_custom:
                _flow['actions'][0]['extraInfo'] = action_id
            else:
                _flow['actions'][0].pop('extraInfo', None)
            _payload['cms_procedure']['definition']['flow'].append(_flow)
        _payload['cms_procedure']['definition'] = str(_payload['cms_procedure']['definition']).replace("'", "\"")
        data = self.invoke_api(url, payload=_payload, method='post')
        if data['success']:
            return data['response']['procedureId']

    def retry_or_cancel_operation(self, id, op='cancel'):
        """
        :param
            id: Procedure ID to retry or cancel
            op: Either cancel or retry
        :return:
            True or False
        """
        url = self._format_url(OPERATIONS_STATUS_URL).replace('$PROCEDURE_ID', str(id))
        _payload = dict(EXECUTE_ACTION_PAYLOAD)
        if op == 'cancel':
            _payload['cms_procedure']['procedureState'] = 'canceled'
        else:
            _payload['cms_procedure']['procedureState'] = 'active'
        _payload['cms_procedure']['definition'] = "null"
        _payload['cms_procedure']['procedureName'] = "null"
        _payload['cms_procedure']['ciId'] = "null"
        for i in range(5):
            data = self.invoke_api(url, _payload, method='put', pass_as_data=True)
            if data['success']:
                return True
            time.sleep(5)

    def get_operations_split_data(self, instances, step):
        """
        :param
            instances   : List of instances to run operation on
            step        : Step percentage to execute
        :return:
            Split size and instance split of type dict
        """
        _split_size = int(step / float(100) * len(instances))
        return {
            'split_size': _split_size,
            'instances': [instances[i:i + _split_size] for i in range(0, len(instances), _split_size)]
        }

    def mark_cloud_primary(self, org_name, assembly_name, env_name, platform_name, cloud_name):
        """
        Mark a given cloud as primary (Online, taking traffic)
        """
        return self._change_cloud_priority(org_name, assembly_name, env_name, platform_name, cloud_name, priority=1)

    def mark_cloud_secondary(self, org_name, assembly_name, env_name, platform_name, cloud_name):
        """
        Mark a given cloud as secondary (Offline, no traffic)
        """
        return self._change_cloud_priority(org_name, assembly_name, env_name, platform_name, cloud_name, priority=2)

    def mark_cloud_active(self, org_name, assembly_name, env_name, platform_name, cloud_name):
        """
        Mark a given cloud as active (Any pending changes in transition will go here, when deployment is triggered)
        """
        return self._change_cloud_config_status(org_name, assembly_name, env_name, platform_name, cloud_name,
                                                status='active')

    def mark_cloud_inactive(self, org_name, assembly_name, env_name, platform_name, cloud_name):
        """
        Mark a given cloud as inactive (Any pending changes in transition will will not be deployed to this cloud, when deployment is triggered)
        """
        return self._change_cloud_config_status(org_name, assembly_name, env_name, platform_name, cloud_name,
                                                status='inactive')

    def run_deployment(self):
        """
        Commits changes and runs the deployment
        """
        if self.is_deployment_running():
            raise RuntimeError('Another Deployment is already in progress...Exiting')
        print_pretty('Committing Env changes...')
        self.commit_environment_changes()
        self.wait_for_deployment_plan_generation()
        time.sleep(15)
        self.deployment_id = self.create_new_deployment()
        if self.deployment_id:
            self.wait_for_deployment_to_finish()

    def commit_environment_changes(self):
        """
        Commit all pending changes for the Env
        """
        url = COMMIT_API_URL.replace('$ORGANIZATION', self.org).replace('$ASSEMBLY', self.assembly). \
            replace('$ENVIRONMENT', self.environment).replace('$PLATFORM', self.platform)
        payload = {
            "commit": "true",
            "desc": "{}".format(self.commit_message)
        }
        result = self.invoke_api(url, payload, 'post')
        if not result['success']:
            raise RuntimeError('Error committing changes: {}'.format(result['response']))

    def update_variable(self, var_name, var_value, commit):
        """
        Update a given variable name, with new value
        """
        url = TRANSITION_VARIABLE_VERSION_UPDATE_URL.replace('$ORGANIZATION', self.org).replace('$ASSEMBLY',
                                                                                                self.assembly). \
            replace('$ENVIRONMENT', self.environment).replace('$PLATFORM', self.platform).replace('$VARNAME', var_name)

        TRANSITION_VARIABLE_PAYLOAD['cms_dj_ci']['ciAttributes']['value'] = str(var_value)
        result = self.invoke_api(url, payload=TRANSITION_VARIABLE_PAYLOAD, method='put', pass_as_data=True)

        if result['success']:
            if commit:
                self.commit_environment_changes()
            return True
        else:
            print('Error Updating current variable payload: {}'.format(result['response']))
            return False

    def add_cloud_variable(self, cloud, var_name, var_value, encrypted=False):
        url = CLOUD_VARIABLE_ADD_API_URL.replace('$ORGANIZATION', self.org).replace('$CLOUD', cloud)
        CLOUD_VARIABLE_PAYLOAD['cms_ci']['ciName'] = var_name
        if encrypted:
            CLOUD_VARIABLE_PAYLOAD['cms_ci']['ciAttributes']['secure'] = True
            CLOUD_VARIABLE_PAYLOAD['cms_ci']['ciAttributes']['encrypted_value'] = var_value
        else:
            CLOUD_VARIABLE_PAYLOAD['cms_ci']['ciAttributes']['value'] = var_value
        result = self.invoke_api(url, payload=CLOUD_VARIABLE_PAYLOAD, method='post')
        return result

    def update_cloud_variable(self, cloud, var_name, var_value, encrypted=False):
        url = CLOUD_VARIABLE_EDIT_API_URL.replace('$ORGANIZATION', self.org).replace('$CLOUD', cloud). \
            replace('$VARNAME', var_name)
        CLOUD_VARIABLE_PAYLOAD['cms_ci']['ciName'] = var_name
        if encrypted:
            CLOUD_VARIABLE_PAYLOAD['cms_ci']['ciAttributes']['secure'] = True
            CLOUD_VARIABLE_PAYLOAD['cms_ci']['ciAttributes']['encrypted_value'] = var_value
        else:
            CLOUD_VARIABLE_PAYLOAD['cms_ci']['ciAttributes']['value'] = var_value
        result = self.invoke_api(url, payload=CLOUD_VARIABLE_PAYLOAD, method='put', pass_as_data=True)
        return result

    def delete_cloud_variable(self, cloud, var_name):
        url = CLOUD_VARIABLE_EDIT_API_URL.replace('$ORGANIZATION', self.org).replace('$CLOUD', cloud). \
            replace('$VARNAME', var_name)
        result = self.invoke_api(url, method='delete')
        return result

    def toggle_relay(self, environment=None):
        """
        Toggle the default Relay
        :return:
        """
        if environment:
            self.environment = environment
        url = self._format_url(DEFAULT_RELAY_TOGGLE_API_URL)
        response = self.invoke_api(url, payload={}, method='put')
        return response

    def touch_component(self, component_name):
        """
        Touch a Oneops component for a given Env & Platform
        """
        url = COMPONENT_TOUCH_API_URL.replace('$ORGANIZATION', self.org).replace('$ASSEMBLY', self.assembly). \
            replace('$ENVIRONMENT', self.environment).replace('$PLATFORM', self.platform). \
            replace('$COMPONENT', component_name)
        status = self.invoke_api(url, method='post')
        print_pretty('Touching component: {}'.format(component_name))
        if not status['success']:
            print('Error Touching Component: {}'.format(status['response']))
        return status['success']

    def is_deployment_running(self):
        """
        Check if there is any deployment currently running
        """
        url = DEPLOYMENT_STATE_API_URL.replace('$ORGANIZATION', self.org).replace('$ASSEMBLY', self.assembly). \
            replace('$ENVIRONMENT', self.environment).replace('$PLATFORM', self.platform)

        result = self.invoke_api(url, method='get')['response']
        return not (result.get('deploymentState') == 'complete' or result.get('deploymentState') == 'canceled')

    def get_release_state(self):
        """
        Fetch the release state
        """
        url = RELEASE_API_URL.replace('$ORGANIZATION', self.org).replace('$ASSEMBLY', self.assembly). \
            replace('$ENVIRONMENT', self.environment)
        return self.invoke_api(url)['response']

    def get_deployment_status(self):
        """
        Fetch the current deployment status active/cancelled etc..
        """
        for i in range(5):
            url = DEPLOYMENT_STATUS_API_URL.replace('$ORGANIZATION', self.org).replace('$ASSEMBLY', self.assembly). \
                replace('$ENVIRONMENT', self.environment).replace('$DEPLOYMENT_ID', str(self.deployment_id))
            data = self.invoke_api(url)
            if data['success']:
                return data['response']['deploymentState']
            time.sleep(2)
        return 'ERROR_COULD_NOT_FETCH_STATUS'

    def get_last_deployment_status(self):
        """
        Fetchs the last deployment ID and its status
        """
        url = LAST_DEPLOYMENT_STATUS_URL.replace('$ORGANIZATION', self.org).replace('$ASSEMBLY', self.assembly). \
            replace('$ENVIRONMENT', self.environment)
        _response = self.invoke_api(url)['response']
        return {
            "id": _response['deploymentId'],
            "status": _response['deploymentState']
        }

    def create_new_deployment(self):
        """
        Create a new deployment and returns the Deployment ID, if a deployment is created, else returns None
        """
        url = NEW_DEPLOYMENT_API_URL.replace('$ORGANIZATION', self.org).replace('$ASSEMBLY', self.assembly). \
            replace('$ENVIRONMENT', self.environment).replace('$PLATFORM', self.platform)
        release_state = self.get_release_state()
        # print('Release State: {}'.format(release_state))

        if release_state == '{"errors":["not found"]}':
            print_pretty('No Deployment necessary..')
            return None

        deployment_payload = {
            "cms_deployment": {
                "comments": "{}".format(self.commit_message),
                "nsPath": release_state.get('nsPath'),
                "releaseId": release_state.get('releaseId')
            }
        }

        result = self.invoke_api(url, payload=deployment_payload, method='post')
        if not result['success']:
            print('Error creating a new deployment => {}'.format(result['response']))
            return None
        _deployment_id = result['response']['deploymentId']
        print_pretty('Created new Deployment: {}'.format(_deployment_id))
        return _deployment_id

    def wait_for_deployment_plan_generation(self):
        """
        Waits until a deployment plan is generated
        """
        url = TRANSITION_STATUS_API_URL.replace('$ORGANIZATION', self.org).replace('$ASSEMBLY', self.assembly). \
            replace('$ENVIRONMENT', self.environment)
        state = 'locked'
        while state != 'default':
            state = self.invoke_api(url)['response'].get('ciState')
            print('Waiting for the deployment plan to be generated. CURRENT STATE == {}'.format(state))
            self._spinner()
        print_pretty('Deployment Plan generated...')

    def wait_for_deployment_to_finish(self):
        """
        Waits until the deployment is finished
        """
        state = 'active'
        print('Waiting for deployment {} to finish. STATE ==> {}'.format(self.deployment_id, state))

        if self.disable_progressbar:
            while state not in Oneops.DEPLOYMENT_DONE_STATES:
                state = self.get_deployment_status()
                self._log_deployment_progress(state)
                time.sleep(10)
        else:
            bar = self._get_deployment_progress_bar()

            while state not in Oneops.DEPLOYMENT_DONE_STATES:
                state = self.get_deployment_status()
                self._wait(bar)
            bar.finish()

        if state in Oneops.DEPLOYMENT_NOT_SUCCESSFUL_STATES:
            raise RuntimeError('Deployment NOT SUCCESSFUL')
        print_pretty('DEPLOYMENT SUCCESSFUL')

    def get_deployment_done_percent(self):
        return self._deployment_progress_status.get('completed_pcnt', 0.0)

    def get_deployment_in_progress_percent(self):
        return self._deployment_progress_status.get('in_progress_pcnt', 0.0)

    def get_deployment_failed_percent(self):
        return self._deployment_progress_status.get('failed_pcnt', 0.0)

    def get_deployment_stats_pending_stages(self):
        return '{}/{}'.format(self._deployment_progress_status.get('pending', 0.0),
                              self._deployment_progress_status.get('total', 0.0))

    def get_deployment_stats_in_progress_stages(self):
        return '{}/{}'.format(self._deployment_progress_status.get('in_progress', 0.0),
                              self._deployment_progress_status.get('total', 0.0))

    def get_deployment_stats_completed_stages(self):
        return '{}/{}'.format(self._deployment_progress_status.get('completed', 0.0),
                              self._deployment_progress_status.get('total', 0.0))

    def get_deployment_overall_progress(self):
        return self._deployment_progress_status.get('completed_pcnt', 0.0) + \
               self._deployment_progress_status.get('in_progress_pcnt', 0.0)

    def pull_design(self, environment=None):
        """
        Pulls the latest design for the specified environment
        """
        if environment:
            self.environment = environment
        url = self._format_url(TRANSITION_DESIGN_PULL_URL)
        self.invoke_api(url, method='post')
        self._wait_for_design_pull_to_finish()

    def cancel_failed_deployment(self):
        """
        Checks if there are any failed deployments and cancels it
        """
        _last_deployment = self.get_last_deployment_status()
        if _last_deployment['status'] == 'failed':
            self.last_deployment_id = _last_deployment['id']
            url = self._format_url(CANCEL_FAILED_DEPLOYMENT_URL)
            cancel_response = self.invoke_api(url, method='put',
                                              payload={"cms_deployment": {"deploymentState": "canceled"}},
                                              pass_as_data=True)
            if cancel_response.get('success'):
                return {
                    "success": True,
                    "id": self.last_deployment_id
                }
            else:
                raise RuntimeError("Failed to cancel pending deployment with ID {}".format(self.last_deployment_id))
        else:
            return {
                "success": False
            }

    def extract_design(self, assembly=None):
        """
        Extracts/exports the Design of any assembly in JSON format for ALL Platforms in the design.

        :param assembly: Name of the assembly
        :return: a dict of the form:
            {
                'success': True/False,
                'response' a JSON object representing the design for the assembly including all platforms
            }
        """
        if assembly:
            self.assembly = assembly
        url = self._format_url(ASSEMBLY_EXTRACT_API_URL)
        return self.invoke_api(url)

    #
    # Helper Methods
    #

    def _format_url(self, url):
        """
        Replace all variables in the URL with correct one's
        """
        return url.replace('$ORGANIZATION', self.org). \
            replace('$ASSEMBLY', str(self.assembly)). \
            replace('$ENVIRONMENT', str(self.environment)). \
            replace('$PLATFORM', str(self.platform)). \
            replace('$PLATFORM', str(self.platform)). \
            replace('$DEPLOYMENT_ID', str(self.deployment_id)). \
            replace('$LAST_DEPLOYMENT_ID', str(self.last_deployment_id)). \
            replace('$COMPONENT', str(self.component))

    def _retrieve_env_list(self, org_name, assembly_name):
        """
        Retrieve list of envs
        """
        _url = ENVIRONMENT_LIST_API_URL.replace('$ORGANIZATION', org_name).replace('$ASSEMBLY', assembly_name)
        return self._get_oneops_response(_url)

    def _retrieve_component_list(self, org_name, assembly_name, env_name, component_name, platform_name=None):
        """
        Retrieves the list of components for a given Org|Assembly|Env|Component
        E.g. list of all computes, list of all fqdns etc..
        """
        if platform_name:
            _platform = platform_name
        else:
            _platform = self._get_app_platform(org_name, assembly_name)

        _url = COMPONENT_DETAIL_LIST_API_URL.replace('$ORGANIZATION', org_name).replace('$ASSEMBLY', assembly_name). \
            replace('$ENVIRONMENT', env_name).replace('$PLATFORM', _platform).replace('$COMPONENT', component_name)
        result = self.invoke_api(_url)
        if result['success']:
            return result['response']
        else:
            print('Error retreiving component list for: {} => {}'.format(component_name, result['response']))
        return []

    def _get_oneops_response(self, url):
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            response = requests.get(url, headers=self.header, verify=False)
        if response.status_code == 200:
            return response.json()
        else:
            return []

    def invoke_api(self, url, payload=None, method='get', pass_as_data=False):
        error = ''
        for i in range(5):
            try:
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    if payload and method == 'put' and not pass_as_data:
                        response = requests.request(method, url, headers=self.header, verify=False, params=payload,
                                                    timeout=60)
                    elif payload and method == 'put' and pass_as_data:
                        response = requests.request(method, url, headers=self.header, verify=False, json=payload,
                                                    timeout=60)
                    elif payload and method == 'post':
                        response = requests.request(method, url, headers=self.header, verify=False, json=payload,
                                                    timeout=60)
                    else:
                        response = requests.request(method, url, headers=self.header, verify=False)

                if response.status_code == 200:
                    return {
                        'success': True,
                        'response': response.json()
                    }
                else:
                    return {
                        'success': False,
                        'response': response.text
                    }
            except Exception as e:
                print('Exception invoking API {}: URL => {} || {}'.format(method, url, e))
                error = str(e)
        return {
            'success': False,
            'response': error
        }

    def _put_oneops(self, url, params):
        print("Calling URL: {} || Params: {}".format(url, params))
        self.header.update({
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        })
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            response = requests.put(url, headers=self.header, verify=False, data=params, timeout=20)
        # print("PUT Response: {}".format(response.json()))
        if response.status_code == 200:
            return {
                'success': True,
                'response': response.json()
            }
        else:
            return {
                'success': False,
                'response': response.text
            }

    def _get_app_platform(self, org_name, assembly_name):
        """
        Returns list of platforms (with app in the name)
        """
        _url = PLATFORM_LIST_API_URL.replace('$ORGANIZATION', org_name).replace('$ASSEMBLY', assembly_name)
        _response = self._get_oneops_response(_url)
        if len(_response) > 1:
            # Implies, there are multiple platforms
            for p in _response:
                name = p.get('ciName')
                if 'app' in name:
                    return name
        return _response[0].get('ciName')

    def _change_cloud_priority(self, org_name, assembly_name, env_name, platform_name, cloud_name, priority):
        """
        Change a given cloud's priority (Primary = 1, Secondary = 2)
        """
        if priority not in self.VALID_CLOUD_PRIORITIES:
            raise RuntimeError('Invalid Cloud Priority: {}. Valid Priorities: {}'.format(priority,
                                                                                         self.VALID_CLOUD_PRIORITIES))
        _url = CLOUD_PRIORITY_API_URL.replace('$ORGANIZATION', org_name).replace('$ASSEMBLY', assembly_name). \
            replace('$ENVIRONMENT', env_name).replace('$PLATFORM', platform_name)

        _params = {
            'cloud_id': self.get_all_cloud_ids(org_name)[cloud_name],
            'priority': priority
        }

        result = self.invoke_api(_url, _params, 'put')
        if int(result['success'] and result['response']['relationAttributes']['priority']) == priority:
            print_pretty("Successfully changed Cloud priority for {} to {}".format(cloud_name, priority))
            return True
        else:
            print("Error changing Priority for Cloud {} to {} => {}".format(cloud_name, priority, result['response']))
            return False

    def _change_cloud_config_status(self, org_name, assembly_name, env_name, platform_name, cloud_name, status):
        """
        Change a cloud's config status (Active vs Inactive)
        """
        if status not in self.VALID_CLOUD_STATUSES:
            raise RuntimeError('Invalid Cloud Config status: {}. Valid Statuses: {}'.format(status,
                                                                                            self.VALID_CLOUD_STATUSES))

        _url = CLOUD_CONFIG_STATUS_API_URL.replace('$ORGANIZATION', org_name).replace('$ASSEMBLY', assembly_name). \
            replace('$ENVIRONMENT', env_name).replace('$PLATFORM', platform_name)

        _cloud_id = self.get_all_cloud_ids(org_name)[cloud_name]

        _params = {
            'cloud_id': _cloud_id,
            'attributes[adminstatus]': status
        }

        result = self.invoke_api(_url, _params, 'put')
        if result['success'] and result['response']['relationAttributes']['adminstatus'] == status:
            print_pretty("Successfully changed Cloud Config Status for {} to {}".format(cloud_name, status))
            return True
        else:
            print("Error changing Config status for Cloud: {} to: {} => {}".format(cloud_name, status,
                                                                                   result['response']))
            return False

    def _update_deployment_progress_status(self):
        """
        Return stats related to progress of deployment
        """
        _url = DEPLOYMENT_PROGRESS_STATUS_API_URL.replace('$ORGANIZATION', self.org).replace('$ASSEMBLY',
                                                                                             self.assembly). \
            replace('$ENVIRONMENT', self.environment).replace('$DEPLOYMENT_ID', str(self.deployment_id))
        result = self.invoke_api(_url)
        if result['success']:
            stats = result['response']['rfc_info']
            total = 0
            in_progress = 0
            pending = 0
            complete = 0
            failed = 0

            for recordId, status in stats.items():
                total += 1
                current_state = status['state']
                if current_state == 'pending':
                    pending += 1
                elif current_state == 'complete':
                    complete += 1
                elif current_state == 'inprogress':
                    in_progress += 1
                elif current_state == 'failed':
                    failed += 1

            if total > 0:
                self._deployment_progress_status = {
                    'ok': True,
                    'total': total,
                    'completed': complete,
                    'completed_pcnt': int((float(complete) / float(total)) * 100),
                    'failed': failed,
                    'failed_pcnt': int((float(failed) / float(total)) * 100),
                    'in_progress': in_progress,
                    'in_progress_pcnt': int((float(in_progress) / float(total)) * 100),
                    'pending': pending,
                    'pending_pcnt': int((float(pending) / float(total)) * 100),
                }

    def _get_deployment_progress_bar(self):
        """Returns a Progressbar which shows status of deployment"""

        return progressbar.ProgressBar(
            widgets=[' [',
                     progressbar.Timer(),
                     ']( STEPS ->>',
                     OneopsProgress('IN-PROGRESS', self.get_deployment_stats_in_progress_stages),
                     OneopsProgress('PENDING', self.get_deployment_stats_pending_stages),
                     OneopsProgress('DONE', self.get_deployment_stats_completed_stages),
                     ' )',
                     OneopsProgress('STATUS', self.get_deployment_status),
                     ' [',
                     Percentage(),
                     ' ]',
                     ' [',
                     AnimatedMarker(),
                     ']',
                     progressbar.Bar(fill='-'),
                     ]
        ).start()

    def _log_deployment_progress(self, state):
        """
        Log progress of a deployment.

        To be used when Progressbar is DISABLED
        """
        self._update_deployment_progress_status()
        sys.stdout.write(
            "(STEPS)[ IN-PROGRESS: {} ] [ PENDING: {} ] [ DONE: {} ] [ DEPLOYMENT STATUS: {} ] [ PERCENT DONE: {} % ]\n".
            format(self.get_deployment_stats_in_progress_stages(), self.get_deployment_stats_pending_stages(),
                   self.get_deployment_stats_completed_stages(), state, self.get_deployment_done_percent()))
        sys.stdout.flush()

    def _wait_for_design_pull_to_finish(self):
        """
        Wait for Design pull to finish
        """
        url = self._format_url(TRANSITION_DESIGN_PULL_STATUS_URL)
        state = 'manifest_locked'
        while state == 'manifest_locked':
            response = self.invoke_api(url=url, method="post")['response']
            state = response['ciState']
            print('Waiting for Design Pull to finish... [[ STATE == {} ]]'.format(state))
            time.sleep(3)
        print_pretty('Design pull complete.')

    def _wait(self, bar, time_secs=30):
        """
        Wait for a given period of time, while displaying a ProgressBar...
        """
        for i in range(time_secs / 2):
            self._update_deployment_progress_status()
            bar.update(self.get_deployment_overall_progress())
            time.sleep(5)

    def _spinner(self, time_secs=15):
        """
        Display Spinner while waiting
        """
        for i in range(time_secs * 2):
            sys.stdout.write(next(SPINNER))
            sys.stdout.flush()
            sys.stdout.write('\b')
            time.sleep(0.5)


try:
    class OneopsProgress(Widget):
        """Used to display progress bar for completion"""

        def __init__(self, title, func, suffix=''):
            self.title = title
            self.func = func
            self.suffix = suffix

        def update(self, pbar):
            """Call func, which should return the % of completion"""
            return ' [{}: {}{}]'.format(self.title, self.func(), self.suffix)
except NameError:
    pass


def main():
    opts, args = parse_args()
    start_time = int(time.time())
    global COMMAND
    global NO_PROGRESSBAR
    if NO_PROGRESSBAR or opts.disable_progressbar:
        disable_progressbar = True
    else:
        disable_progressbar = False

    global NO_VERBOSE
    NO_VERBOSE = opts.no_verbose

    oneops = Oneops(
        opts.api_token,
        opts.organization,
        opts.assembly,
        opts.platform,
        opts.environment,
        opts.component_name,
        **{
            'disable_progressbar': disable_progressbar,
            'cloud_activity': True if opts.cloud_var_name else False,
            'org_activity': True if opts.get_all_assemblies else False,
            'assembly_activity': True if opts.extract_design else False,
            'commit_message': opts.commit_message
        }
    )

    if opts.list_computes or opts.list_ips:
        print_pretty('Fetching IP Addresses for all Computes')
        computes = oneops.get_all_computes(opts.organization, opts.assembly, opts.environment, opts.platform)

        if opts.random:
            print_pretty('Picking one IP at random')
            import random
            computes = [random.choice(computes)]

        if opts.print_cssh_style:
            print('')
            print(" ".join(computes))
            print('')
        else:
            print("\n".join(computes))

    elif opts.extract_design:
        print_pretty('Extracting Design for Assembly: {}'.format(opts.assembly))
        response = oneops.extract_design()
        if response['success']:
            file_name = '{}.json'.format(opts.assembly)

            if os.path.isfile(file_name):
                print_pretty('Deleting already existing file...')
                os.remove(file_name)

            with open(file_name, 'w') as f:
                f.write(json.dumps(response['response']))
            print_pretty('Design written to file: {}'.format(file_name))
        else:
            print_pretty('Error extracting design for assembly: {}. Error = {}'.format(opts.assembly,
                                                                                       response['response']))

    elif opts.list_hostnames:
        print_pretty('Fetching Hostnames for all Computes')
        hostnames = oneops.get_all_computes(opts.organization, opts.assembly, opts.environment, opts.platform)
        #
        cus_command = "hostname -f"
        run_command_parallel(hostnames, opts.concurrency, opts.wait_time, cus_command)

    elif opts.list_vips:
        print_pretty('Fetching all VIPs')
        vips = oneops.get_env_vip(opts.organization, opts.assembly, opts.environment, opts.platform)
        if not vips:
            print_pretty("No VIPs were found")
            exit(1)
        for v in vips:
            print(v)

    elif opts.get_all_assemblies:
        print('=================\nAssemblies\n=================')
        for a in oneops.get_all_assemblies():
            print(a)
        print('=================')

    elif opts.get_all_envs:
        envs = oneops.get_all_environments()
        print('=================\nEnvironments\n=================')
        for e in envs:
            print(e)
        print('=================')

    elif opts.list_clouds:
        print_pretty('Fetching all Clouds')
        clouds = oneops.get_all_clouds_for_environment()
        for c in clouds:
            print(c)

    elif opts.command:
        print_pretty('COMMAND: "{}" \nUSER:\t"{}"\tForce Override: {}'.format(opts.command, opts.user,
                                                                              opts.force_override))
        if any([True if x in opts.command else False for x in PROTECTED_COMMANDS]) and not opts.force_override:
            if (not opts.wait_time) or (opts.wait_time < PROTECTED_COMMANDS_MIN_WAIT_TIME):
                print_pretty('ERROR: The command you are trying to execute has RESTRICTED words. '
                             'Please ensure to add a wait time of atleast {} seconds'.format(
                    PROTECTED_COMMANDS_MIN_WAIT_TIME), False)
                exit(1)

        if opts.force_override:
            opts.wait_time = 0

        if not opts.no_confirm:
            response = input('Proceed? ("y/yes" for yes, anything else will abort): ')
            if response.lower() not in ['y', 'yes']:
                print("USER_ABORTED. Exiting...")
                exit(0)

        global USER
        cus_command = opts.command
        USER = opts.user

        print_pretty('Fetching Compute IPs...')
        if opts.clouds:
            clouds = opts.clouds.strip().split(',')
            print_pretty('Fetching Compute IPs. Filtered by Clouds: {}'.format(clouds))
        else:
            clouds = []
        computes = oneops.get_all_computes(opts.organization, opts.assembly, opts.environment, opts.platform, clouds)
        print_pretty('Total Computes found: {}'.format(len(computes)))
        run_command_parallel(computes, opts.concurrency, opts.wait_time, cus_command)

    elif opts.cloud_var_name:
        if opts.delete_cloud_var:
            confirm_or_exit('Proceed with DELETING Cloud Variable: {} ?'.format(opts.cloud_var_name))
            for cloud in opts.clouds.strip().split(','):
                print_pretty('DELETING VAR: {} for cloud: {} '.format(opts.cloud_var_name, cloud))
                result = oneops.delete_cloud_variable(cloud, opts.cloud_var_name)
                if not result['success']:
                    print_pretty(
                        'Failed to delete Cloud Variable for {} ! \nError: {}'.format(cloud, result['response']))
        elif opts.update_cloud_var:
            confirm_or_exit('Proceed with UPDATING Cloud Variable: {} ?'.format(opts.cloud_var_name))
            for cloud in opts.clouds.strip().split(','):
                print_pretty(
                    'UPDATING VAR: {} for cloud: {} to: {}'.format(opts.cloud_var_name, cloud, opts.cloud_var_value))
                result = oneops.update_cloud_variable(cloud, opts.cloud_var_name, opts.cloud_var_value,
                                                      opts.cloud_var_encrypted)
                if not result['success']:
                    print_pretty(
                        'Failed to Update Cloud Variable for {} ! \nError: {}'.format(cloud, result['response']))
        else:
            for cloud in opts.clouds.strip().split(','):
                print_pretty('Setting VAR: {} with value: {} for cloud: {}  || Encrypted ? : {}'.format(
                    opts.cloud_var_name, opts.cloud_var_value, cloud, opts.cloud_var_encrypted))
                result = oneops.add_cloud_variable(cloud, opts.cloud_var_name, opts.cloud_var_value,
                                                   opts.cloud_var_encrypted)
                if not result['success']:
                    print_pretty('Failed to add new Cloud Variable ! \nError: {}'.format(result['response']))

    elif opts.toggle_relay or opts.toggle_all_relays:
        if opts.toggle_all_relays:
            print_pretty('Toggling Relays for ALL Envs')
            envs = oneops.get_all_environments(opts.organization, opts.assembly)
            for env in envs:
                oneops.toggle_relay(environment=env)
                print_pretty('Relay Toggled for Env: {}'.format(env))
        else:
            oneops.toggle_relay()
            print_pretty('Relay Toggled for Env: {}'.format(opts.environment))

    elif opts.get_all_vars:
        print_pretty('Fetching variable value for : {}'.format(opts.var_name))
        envs = oneops.get_all_environments(opts.organization, opts.assembly)
        print("==================================================================")
        print("{0:50} {1}".format('Environment', 'Variable Value'))
        print("==================================================================")
        for env in envs:
            print("{0:50} {1}".format(env, oneops.get_variable(opts.var_name, environment=env)))
        print("==================================================================")

    elif opts.oneops_mode == "operations":
        print_pretty('Fetching required data for {}'.format(opts.component_name))
        _components = oneops.get_operations_component_details()
        if not _components['status']:
            raise RuntimeError('Could not fetch any data for component {}'.format(opts.component_name))
        if not opts.component_action in [i['name'] for i in _components['actions']]:
            raise RuntimeError(
                'Please specify one of the following supported actions for {}: {}'.format(opts.component_name,
                                                                                          _components['actions']))
        _action_id = [i['id'] for i in _components['actions'] if i['name'] == opts.component_action][0]
        _action_name = [i['apiName'] for i in _components['actions'] if i['name'] == opts.component_action][0]
        _is_custom_action = [i['isCustom'] for i in _components['actions'] if i['name'] == opts.component_action][0]
        _clouds = opts.clouds if opts.clouds else False
        _instances = oneops.get_operations_instance_ids(_clouds)
        _min_step = int(ceil(1 / float(len(_instances)) * 100))
        _step = int(opts.action_step) if int(opts.action_step) > _min_step else _min_step
        _split_data = oneops.get_operations_split_data(_instances, _step)
        print_pretty(
            'Total instances executing {}: {} | Batch size: {}'.format(opts.component_action, len(_instances),
                                                                       len(_split_data['instances'][0])))
        if not opts.no_confirm:
            response = input('Proceed? ("y/yes" for yes, anything else will abort): ')
            if response.lower() not in ['y', 'yes']:
                print("User Aborted. Exiting...")
                exit(0)
        _procedure_id = oneops.execute_action_on_component(_components['id'], _instances, str(_action_name), _action_id,
                                                           _step, _is_custom_action)
        if not _procedure_id:
            raise RuntimeError("Failed to execute action!")
        print_pretty('Successfully submitted operation with ID {}'.format(_procedure_id))
        _status = dict(oneops.wait_for_operations(_procedure_id))
        if not _status['status']:
            if opts.action_retry > 0:
                for i in range(opts.action_retry):
                    print_pretty('Operation failed. Retry Attempt {} in progress..'.format(i + 1))
                    oneops.retry_or_cancel_operation(_procedure_id, op='retry')
                    _status = dict(oneops.wait_for_operations(_procedure_id))
                    if _status['status']:
                        break
        if _status['status']:
            print_pretty('Operation completed successfully!!')
        else:
            oneops.retry_or_cancel_operation(_procedure_id, op='cancel')
            raise RuntimeError('Operation failed on instance ID: {}'.format(str(_status['failed'])))

    elif any([opts.clouds_active, opts.clouds_ignored, opts.clouds_secondary, opts.clouds_primary, opts.var_name,
              opts.trigger_deployment, opts.touch_component, opts.pull_design, opts.pull_design_to_all_envs,
              opts.cancel_failed_deploy]):
        if opts.pull_design:
            print_pretty('Pulling latest design to Environment')
            oneops.pull_design()
        elif opts.pull_design_to_all_envs:
            print_pretty('Pulling Design to ALL Environments')
            envs = oneops.get_all_environments(opts.organization, opts.assembly)
            print_pretty('Found {} environments: {}'.format(len(envs), envs))
            for env in envs:
                print_pretty('Pulling Design to Env: {}'.format(env))
                oneops.pull_design(environment=env)

        if opts.clouds_primary or opts.clouds_secondary:
            if opts.clouds_primary:
                print_pretty('Marking Cloud(s) as Primary: {}'.format(opts.clouds_primary))
                for cloud in opts.clouds_primary.strip().split(','):
                    success = oneops.mark_cloud_primary(opts.organization, opts.assembly, opts.environment,
                                                        opts.platform,
                                                        cloud.strip())
                    if not success:
                        exit(1)
            if opts.clouds_secondary:
                print_pretty('Marking Cloud(s) as Secondary: {}'.format(opts.clouds_secondary))
                for cloud in opts.clouds_secondary.strip().split(','):
                    success = oneops.mark_cloud_secondary(opts.organization, opts.assembly, opts.environment,
                                                          opts.platform,
                                                          cloud.strip())
                    if not success:
                        exit(1)

        if opts.clouds_active or opts.clouds_ignored:
            if opts.clouds_active:
                print_pretty('Marking Cloud(s) as Active: {}'.format(opts.clouds_active))
                for cloud in opts.clouds_active.strip().split(','):
                    success = oneops.mark_cloud_active(opts.organization, opts.assembly, opts.environment,
                                                       opts.platform,
                                                       cloud.strip())
                    if not success:
                        exit(1)

            if opts.clouds_ignored:
                print_pretty('Marking Cloud(s) as Ignored: {}'.format(opts.clouds_ignored))
                for cloud in opts.clouds_ignored.strip().split(','):
                    success = oneops.mark_cloud_inactive(opts.organization, opts.assembly, opts.environment,
                                                         opts.platform,
                                                         cloud.strip())
                    if not success:
                        exit(1)

        if opts.var_name:
            print_pretty('Updating Variable => {} with new value => {}'.format(opts.var_name, opts.var_value))
            success = oneops.update_variable(opts.var_name, opts.var_value, opts.commit_changes)
            if not success:
                exit(1)

        if opts.touch_component:
            for component in opts.touch_component.strip().split(','):
                success = oneops.touch_component(component)
                if success:
                    continue
                else:
                    exit(1)

        if opts.cancel_failed_deploy:
            print_pretty('Checking for any failed deployments')
            _discard_status = oneops.cancel_failed_deployment()
            if _discard_status.get("success"):
                print_pretty("Failed deployment ID {} cancelled successfully".format(_discard_status.get("id")))
            else:
                print_pretty('No failed deployments found')

        if opts.trigger_deployment:
            if not opts.no_confirm:
                confirm_or_exit('TRIGGER A DEPLOYMENT ? ')
            oneops.run_deployment()

    print_done(start_time)


def confirm_or_exit(message):
    response = input('{}\n("y/yes" for yes, anything else will abort): '.format(message))
    if response.lower() not in ['y', 'yes']:
        print("USER_ABORTED. Exiting...")
        exit(0)
    return True


def batch(iterable, n=1):
    l = len(iterable)
    for ndx in range(0, l, n):
        yield iterable[ndx:min(ndx + n, l)]


def run_command_parallel(computes, concurrency, wait_time, cus_command):
    print_pretty('Running command parallely... [{}]'.format(cus_command))
    results = []
    counter = 1
    batches = ceil(float(len(computes)) / float(concurrency))
    for compute_list in batch(computes, int(concurrency)):
        _pool = Pool(processes=int(concurrency))
        _results = _pool.map(partial(run_command, cmd=cus_command), compute_list)
        results += _results
        if not NO_VERBOSE:
            print('Batch [{} of {}] done..Sleeping => {} secs\n'.format(counter, int(batches), wait_time))
        if wait_time:
            time.sleep(wait_time)
        counter += 1
        _pool.close()
    if NO_VERBOSE:
        for row in results:
            print(row[1])
    else:
        print("=-=" * 50)
        for row in results:
            print('{0:20} ==>\t{1}\n{2}'.format(row[0], row[1], '=' * 100))


def run_command(host, cmd):
    """Run a command via ssh on a remote server"""
    global USER
    cmd = cmd.replace('"', '\\"')
    cmd = cmd.replace('$', '\\$')
    sshcmd = 'ssh -o ServerAliveInterval=60 -o ConnectTimeout=10 -o BatchMode=yes ' \
             '-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o LogLevel=quiet %s@%s "%s"' \
             % (USER, host, cmd)
    try:
        ps = subprocess.Popen(sshcmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
        return host, ps.communicate()[0].strip().decode('utf-8')
    except Exception as e:
        return host, "Error: %s" % e


def print_pretty(message, no_verbose=False):
    global NO_VERBOSE
    if NO_VERBOSE:
        no_verbose = NO_VERBOSE
    if no_verbose:
        return
    else:
        print('=-=' * 50)
        print(message)
        print('=-=' * 50)
        print('')


def print_done(start_time):
    time_taken_secs = int(time.time()) - start_time
    if time_taken_secs > 60:
        time_taken = '{} mins {} s'.format(time_taken_secs / 60, time_taken_secs % 60)
    else:
        time_taken = '{} secs'.format(time_taken_secs)
    print_pretty("Done (in appx. {})".format(time_taken))


def parse_args():
    usage = 'Usage: %prog -a <assembly> -e <env> -p <platform> [--cmd <command | --computes | --hostnames | --vips | ' \
            '--primary/secondary/active/inactive | --deploy | --pull] '
    parser = OptionParser(usage=usage)
    parser.add_option('-o', '--org', dest='organization', default=DEFAULT_ONEOPS_ORG,
                      help='Oneops Org [Default: {}]'.format(DEFAULT_ONEOPS_ORG))

    parser.add_option('-a', '--assembly', dest='assembly',
                      help='Oneops Assembly')

    parser.add_option('-e', '--env', dest='environment',
                      help='Oneops Environment')

    parser.add_option('-p', '--platform', dest='platform',
                      help='Oneops Platform (OPTIONAL)')

    parser.add_option('-c', '--clouds', dest='clouds',
                      help='Oneops Clouds (comma delimited) (OPTIONAL)')

    parser.add_option('-t', '--threads', dest='concurrency', default=DEFAULT_NUM_PROCESSES,
                      help='Number of Processes [Default: {}]'.format(DEFAULT_NUM_PROCESSES))

    parser.add_option('-w', '--wait', dest='wait_time', default=None, type=float,
                      help='Time to wait/sleep, in seconds, between batches of running command [Default: None]')

    parser.add_option('-u', '--user', dest='user', default=USER,
                      help='User to run remote command as [Default: {}]'.format(USER))

    parser.add_option('--api-token', dest='api_token', default=DEFAULT_API_TOKEN,
                      help='Oneops API user token')

    parser.add_option('--no-confirm', dest='no_confirm', action='store_true', default=False,
                      help="Don't ask for User confirmation when running a command")

    parser.add_option('--no-verbose', dest='no_verbose', action='store_true', default=False,
                      help="Verbosity of output, when true, will not output in tabular format")

    parser.add_option('--random', dest='random', action='store_true', default=False,
                      help='Use with --ips to get one randomly picked IP')

    # Actions
    parser.add_option('--cmd', '--command', dest='command',
                      help='Command to run against the remote server [Default: {}]'.format(COMMAND))

    parser.add_option('--computes', dest='list_computes', action='store_true', default=False,
                      help='List Oneops Computes IP Addresses')

    parser.add_option('--ips', dest='list_ips', action='store_true', default=False,
                      help='List Oneops Computes IP Addresses (same as --computes)')

    parser.add_option('--hostnames', dest='list_hostnames', action='store_true', default=False,
                      help='List Oneops Computes Hostnames')

    parser.add_option('--toggle-relay', dest='toggle_relay', action='store_true', default=False,
                      help='Toggle the DEFAULT Relay')

    parser.add_option('--toggle-all-relays', dest='toggle_all_relays', action='store_true', default=False,
                      help='Toggle the DEFAULT Relay for ALL Envs of a given Assembly')

    parser.add_option('--get-vars', dest='get_all_vars', action='store_true', default=False,
                      help='Get Variable value for ALL Envs (specified by --vn or --varname)')

    parser.add_option('--vips', dest='list_vips', action='store_true', default=False,
                      help='List all VIPs')

    parser.add_option('--list-clouds', dest='list_clouds', action='store_true', default=False,
                      help='List all Clouds for the specified Env/Platform')

    parser.add_option('--pc', '--print', dest='print_cssh_style', action='store_true', default=False,
                      help='Print compute IPs Cssh style (space delimited)')

    parser.add_option('--primary', dest='clouds_primary',
                      help='Mark Clouds as Primary (Comma delimted cloud names)')

    parser.add_option('--secondary', dest='clouds_secondary',
                      help='Mark Cloud as Secondary (Comma delimted cloud names)')

    parser.add_option('--active', dest='clouds_active',
                      help='Mark Clouds as Primary (Comma delimted cloud names)')

    parser.add_option('--ignored', dest='clouds_ignored',
                      help='Mark Cloud as Secondary (Comma delimted cloud names)')

    parser.add_option('--cvar', dest='cloud_var_name',
                      help='Name of the Cloud Level Variable')

    parser.add_option('--cval', dest='cloud_var_value', default='',
                      help='Value of the Cloud Level Variable')

    parser.add_option('--cencrypt', dest='cloud_var_encrypted', action='store_true', default=False,
                      help='Store the Cloud Var as encrypted')

    parser.add_option('--dcvar', dest='delete_cloud_var', action='store_true', default=False,
                      help='Delete Cloud level Variable')

    parser.add_option('--ucvar', dest='update_cloud_var', action='store_true', default=False,
                      help='Update Cloud level Variable')

    parser.add_option('--vn', '--varname', dest='var_name',
                      help='Name of the platform variable')

    parser.add_option('--vv', '--varvalue', dest='var_value',
                      help='Name of the platform variable')

    parser.add_option('--touch', dest='touch_component',
                      help='Touch Component(s), comma separated list of components')

    parser.add_option('--commit', dest='commit_changes', action='store_true', default=False,
                      help='Commit any changes to the environment')

    parser.add_option('--deploy', dest='trigger_deployment', action='store_true', default=False,
                      help='Trigger a deployment')

    parser.add_option('--force', dest='force_override', action='store_true', default=False,
                      help='Force override the wait time incase of restricted commands. USE CAUTIOUSLY')

    parser.add_option('--pull', dest='pull_design', action='store_true', default=False,
                      help='Pull latest Design to an Environment')

    parser.add_option('--pull-all', dest='pull_design_to_all_envs', action='store_true', default=False,
                      help='Pull latest Design to ALL Environments')

    parser.add_option('--cancel-failed-deploy', dest='cancel_failed_deploy', action='store_true', default=False,
                      help='Cancel any failed deployments')

    parser.add_option('--nobar', dest='disable_progressbar', action='store_true', default=False,
                      help='Dont use progressbar in the output')

    parser.add_option('--assemblies', dest='get_all_assemblies', action='store_true', default=False,
                      help='Print the list of ALL Assemblies for a given Org')

    parser.add_option('--envs', dest='get_all_envs', action='store_true', default=False,
                      help='Print the list of ALL Environments for a given Assembly')

    parser.add_option('--mode', type='choice', dest='oneops_mode', action='store', choices=['transition', 'operations'],
                      default='transition',
                      help='Specify whether to run in transition or operations mode on OneOps [Default: transition]')

    parser.add_option('--component', dest='component_name',
                      help='Specify the component on which to run operations')

    parser.add_option('--action', dest='component_action',
                      help='Action to perform on the specified component in operation mode')

    parser.add_option('--step', dest='action_step', default=10, type='int',
                      help='Roll percentage when performing the specified action in operation mode')

    parser.add_option('--retry-action', dest='action_retry', default=0, type='int',
                      help='Number of retries before canceling the operation [Default: None]')

    parser.add_option('--extract', dest='extract_design', default=False, action='store_true',
                      help='Extract/export all platforms of a design as JSON file')

    parser.add_option('--commit-message', dest='commit_message', default=DEFAULT_COMMIT_MESSAGE,
                      help='Add a commit message')

    opts, args = parser.parse_args()

    if opts.oneops_mode == "operations" and (not opts.assembly or not opts.environment or not opts.platform):
        parser.error("Missing a mandatory param out of: -a -e -p")

    if opts.oneops_mode == "operations" and (not opts.component_name or not opts.component_action):
        parser.error("Missing a mandatory param out of: --component --action")

    if opts.extract_design and not opts.assembly:
        parser.error('Please provide a valid Assembly Name using -a')

    if opts.var_name:
        if (not opts.get_all_vars) and (not opts.var_value):
            parser.error('Missing parameter: --varvalue')

    return opts, args


if __name__ == '__main__':
    main()