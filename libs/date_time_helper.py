import requests, logging, re, time, random, datetime, pytz
from datetime import timedelta


class WMTDateTime:
    pst = pytz.timezone('America/Los_Angeles')
    date_time_format = '%m/%d/%Y:%H:%M:%S'
    usa_format = '%Y-%m-%d %H:%M:%S'

    @property
    def current_datetime_obj(self):
        return datetime.datetime.now(WMTDateTime.pst)

    @property
    def current_hour(self):
        return datetime.datetime.now(WMTDateTime.pst).strftime('%H')

    @property
    def datetime(self):
        return datetime.datetime.now(WMTDateTime.pst).strftime(WMTDateTime.date_time_format)

    @property
    def previous_hour_datetime(self):
        day_ago = datetime.datetime.now(WMTDateTime.pst) - timedelta(hours=1)
        return day_ago.strftime(WMTDateTime.date_time_format)

    @staticmethod
    def get_previous_hour_datetime(year, month, day, hour, minute, second, microsecond=000000):
        day_ago = datetime.datetime(year, month, day, hour, minute, second, microsecond)
        day_ago = day_ago - timedelta(hours=1)
        day_ago = day_ago.replace(tzinfo=WMTDateTime.pst)
        return day_ago.strftime(WMTDateTime.date_time_format)

    @staticmethod
    def datetime_obj_using_datetime_str(date_time, format):
        day_ago = datetime.datetime.strptime(date_time, format)
        # day_ago = day_ago - timedelta(hours=1)
        day_ago = day_ago.replace(tzinfo=WMTDateTime.pst)
        return day_ago

    @staticmethod
    def epoch_to_pst_datetime(epoch):
        d = datetime.datetime.utcfromtimestamp(epoch)
        d = d.replace(tzinfo=WMTDateTime.pst)
        return d.strftime(WMTDateTime.usa_format)

    @staticmethod
    def is_time_gap_passed(previous_date_time, gap, date_time_format=None):
        """

        Args:
            previous_date_time:
            gap: in minutes
            date_time_format:

        Returns:

        """
        if not date_time_format:
            date_time_format = WMTDateTime.date_time_format
        previous_date_time = WMTDateTime.datetime_obj_using_datetime_str(previous_date_time, date_time_format)
        current_date_time = datetime.datetime.now(WMTDateTime.pst)
        cur = current_date_time.timestamp()
        pre=previous_date_time.timestamp()
        print(current_date_time -cur)
        # time_delta = current_date_time - previous_date_time
        # delta_in_seconds = time_delta.total_seconds()
        # if delta_in_seconds < (gap * 60):
        #     return False
        # return True

    @staticmethod
    def get_last_24_hours_data(date_time_format=None):
        one_day_time_rages = list()
        _now = datetime.datetime.now(WMTDateTime.pst)
        day_ago = _now - timedelta(hours=24)
        previous_day = day_ago
        if not date_time_format:
            date_time_format = WMTDateTime.date_time_format
        for hour in range(24):
            previous_day_with_one_hour = day_ago + timedelta(hours=hour + 1)
            _prev = previous_day_with_one_hour.strftime(date_time_format)
            _prev_minus_one_hour = previous_day.strftime(date_time_format)
            one_day_time_rages.append((_prev, _prev_minus_one_hour))
            previous_day = previous_day_with_one_hour
        return one_day_time_rages


if __name__ == "__main__":
    import sys, os, datetime

    log_level = logging.INFO
    # log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(log_level)
    log_format = '%(asctime)s %(filename)28s  %(lineno)4d %(levelname)10s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setLevel(level=logging.DEBUG)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)

    filename = "{}-{}.log".format(os.path.basename(__file__).replace('.py', ''),
                                  datetime.datetime.now().strftime('%Y-%m-%d'))
    fh = logging.FileHandler(filename=os.path.join((os.path.dirname(os.path.abspath(__file__))), filename))
    fh.setLevel(log_level)
    fh.setFormatter(fmt=formatter)
    logger.addHandler(fh)
    _date = WMTDateTime()
    # date_time_format = '%m/%d/%Y:%H:%M:%S'
    # Splunk requires following format 2022-06-13T12:00:00.000
    # _date_time_format='%Y-%m-%dT%H:%M:%S.%f'
    # print(_date.get_last_24_hours_data(_date_time_format))
    # print(_date.current_datetime_obj)
    # print(_date.datetime)
    # print(_date.previous_hour_datetime)
    # print(_date.last_24_hours)
    # # print(_date.get_previous_hour_datetime("2021", "8", "1", "8", "38", "46"))
    # print(WMTDateTime.datetime_obj_using_datetime_str("08/01/2021:08:49:48", '%m/%d/%Y:%H:%M:%S'))
    WMTDateTime.is_time_gap_passed("2022-06-14T17:10:48", gap=30,date_time_format='%Y-%m-%dT%H:%M:%S')
