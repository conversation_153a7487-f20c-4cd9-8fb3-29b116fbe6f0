from concurrent.futures import ThreadPoolExecutor
from libs.prometheus_client import Prometheus
import logging, os, time

logger = logging.getLogger(__name__)
p = Prometheus()


def monitor_gslbs(lbs):
    futures = []
    results = list()
    length = len(lbs)
    for lb in lbs:
        pool = ThreadPoolExecutor(25)
        futures.append(pool.submit(p.process_gslb_ingress_traffic, lb))
    for future in futures:
        try:
            _results = future.result()
            results.append(_results)
        except Exception as e:
            logger.exception(e)
    return results
if __name__ == "__main__":
    from libs.oneClick import process_gslbs
    url = "https://gecgithub01.walmart.com/raw/SRE/production-playbooks/main/yaml-playbooks/international-tech/" \
              "intl-canada/canada-gtm-failover/disable-azure-scus.yml?token=GHSAT0AAAAAAAAC7RFQPCBTB3LFAPCHF2LGZCQ2BQA"
    lbs = process_gslbs(url)
    results = monitor_gslbs(lbs)
    print(results)