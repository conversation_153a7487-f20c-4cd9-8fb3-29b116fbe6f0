__author__ = '<PERSON><PERSON> cheepati'
__version__ = 1.0
import requests, json
import logging

logger = logging.getLogger(__name__)


class Slack(object):
    def __init__(self, **kwargs):
        super(Slack, self).__init__()
        self.cronus_host = kwargs.get('cronus_host', 'http://cronus.walmart.com')
        self.headers = {'content-type': 'application/json'}

    def post_message(self, message, **kwargs):

        """
        Parameters are :    title
                            title_link
                            channels
                            username

        """

        uri = '{}/tools/slack/?message={}'.format(self.cronus_host, message)
        items_array = [k + '=' + v for k, v in kwargs.items()]
        items_array_as_string = '&'.join(items_array)
        url = '{}&{}'.format(uri, items_array_as_string)
        response = requests.get(url, headers=self.headers, verify=False)
        if response.status_code==400:
            logger.warning("Message length is too big, not able to send")
            return 0
        if response.status_code != requests.codes.ok:
            logger.error("Response code {}".format(response.status_code))
            raise Exception("Request , response code is {}".format(response.status_code))


if __name__ == "__main__":
    slack = Slack()
    slack.post_message('13 server are not reachable and 12 server sre read only ', channels='steller',
                       title='OOR Pull request for review ', title_link='http://www.google.com', username='appmgt')
