import requests, logging
import re
import requests, yaml
from collections import OrderedDict
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)
WCNP_OPS = "https://wcnp-ops-tooling-api.wcnp-ops-tooling.prod.k8s.walmart.net"
HELM = "https://helm2-api.kitt.prod.k8s.walmart.net"
APIs = {
    "kitt_content_url": "{HELM}/getValues/{cluster_id}/{namespace}/{deployment}/{release_version}",
    # Gets latest release number and releaseName or app name for all apps in that namespaces
    "release_details": "{HELM}/ls/{cluster_id}/{namespace}",
    "release_versions": "{WCNP_OPS}/history/{cluster_id}/{namespace}/{deployment}"

}

EXCLUDED_PATTERN_RELEASE_NAMES = ["-canary", "-preprod"]
IGNORE_LB_ROUTINGS = ["dev", "stage", "pr", "teflon"]


class WMTHelm:
    def __init__(self, namespace, cluster_id, app=None):
        self.namespace = namespace
        self.cluster_id = cluster_id
        self.deployments = None
        self.app = app
        if app:
            if "-primary" in self.app:
                self.app = app.split("-primary")[0]
        self.get_release_details()

    def get_kitt_urls(self):
        """
        Given Namespace and Cluster, build's helm url, which contains kitt data
        """
        try:
            urls = list()
            for _deployment in self.deployments:
                logger.info(f"Deployment details are {_deployment}")
                _url = WMTHelm.build_helm2_content_url(self.namespace, self.cluster_id,
                                                       deployment=_deployment.get("name"),
                                                       release_version=_deployment.get("version"))
                url_map = _deployment
                url_map["url"] = _url
                urls.append(url_map)
            return urls
        except Exception as e:
            logger.exception(e)

    def __call__(self, *args, **kwargs):
        try:
            _results = list()
            futures = list()
            deployments = self.get_kitt_urls()
            # pool = ThreadPoolExecutor(len(deployments))
            # for dep in deployments:
            #     futures.append(pool.submit(self.process, dep))
            # for future in futures:
            #     try:
            #         _results.extend(future.result())
            #     except Exception as e:
            #         logger.exception(e)
            for dep in deployments:
                res = self.process(dep)
                _results.append(res)
                # _results.append(OrderedDict({**res, **dep}))
            return _results
        except Exception as e:
            logger.exception(e)

    def process(self, deployment):
        logger.info(f"Processing kitt file of {deployment.get('url')}")
        kitt_data = self.get_kitt_file(deployment.get("url"))
        logger.debug(f"kitt data is for {deployment.get('url')} is {kitt_data}")
        res = self.analyze_kitt(kitt_data)
        logger.info(f"kitt Analyze data for {deployment.get('url')} is {res}")
        return OrderedDict({**res})

    @staticmethod
    def get_kitt_file(url):
        try:
            resp = requests.get(url=url, verify=False)
            values = yaml.load(resp.content, Loader=yaml.FullLoader)
            return values
        except Exception as e:
            logger.exception(e)
            return {}

    def get_release_details(self):
        """
        Gets latest release number and releaseName or app name for all apps in that namespaces
        """
        try:
            url = APIs.get("release_details").format_map(
                {"HELM": HELM, "cluster_id": self.cluster_id, "namespace": self.namespace})
            logger.info(f"Helm release or deployment url is {url}")
            response = requests.get(url, verify=False)
            if response.ok:
                self.deployments = self._normalize_release_details(response.json())
            else:
                self.deployments = list()
        except Exception as e:
            logger.exception(e)

    def _normalize_release_details(self, results_data):
        releases = list()

        for release in results_data:
            logger.info(f"Helm release details are {release}")
            if WMTHelm.is_release_in_exclude_names(release.get('releaseName')):
                continue
            if self.app:
                if self.app != release.get('releaseName'):
                    continue

            _release = dict()
            _release["name"] = release.get('releaseName')
            _release["version"] = release.get('releaseRevision')
            releases.append(_release)
        return releases

    @staticmethod
    def is_release_in_exclude_names(name_to_check):
        for exclude_pattern in EXCLUDED_PATTERN_RELEASE_NAMES:
            if exclude_pattern in name_to_check:
                return True
        return False

    @staticmethod
    def build_helm2_content_url(namespace, cluster_id, deployment, release_version):
        url = APIs.get("kitt_content_url").format_map({"HELM": HELM, "cluster_id": cluster_id, "namespace": namespace,
                                                       "deployment": deployment, "release_version": release_version})
        return url

    def analyze_kitt(self, data):
        result = OrderedDict()
        result["lbs"] = self.get_gslab_link(data)
        result["canary"] = self.canary_analysis_data(data)
        result["scaling"] = dict()
        result["prob"] = dict()
        status, v_data = self.vertical_scaling_enabled(data)
        result["scaling"]["vertical"] = {"is_enabled": status, **v_data}
        status, h_data = self.get_hpa_details(data)
        result["scaling"]["horizontal"] = {"is_enabled": status, **h_data}
        result["prob"] = self.probs(data)
        result["git"] = WMTHelm.get_git_link(data)
        result["dynatrace_enabled"] = WMTHelm.get_dynatrace_options(data)
        if "JAVA_OPTS" in data.get("env"):
            result["jvm"] = dict()
            result["jvm"]["JAVA_OPTS"] = data.get("env").get("JAVA_OPTS")
            result["jvm"]["is_g1_gc"] = True if "UseG1GC" in data.get("env").get("JAVA_OPTS") else False

        return result

    def canary_analysis_data(self, data):
        try:
            logger.info(f"Canary analysis started for {self.namespace}")
            result = dict()
            result["enabled"] = False
            global_data = data.get("global")
            if global_data.get("flagger"):
                flagger = global_data.get("flagger")
                if 'canaryService' in flagger:
                    result['experiments'] = flagger.get('canaryService').get('metrics')
                    if flagger.get('enabled') or not flagger.get('skipAnalysis'):
                        if len(flagger.get('canaryService').get('metrics')) > 0:
                            result["enabled"] = True
                result['defaultMetrics'] = flagger.get('defaultMetrics')
            logger.info(f"Canary analysis Finished for {self.namespace} output {result}")
            return result
        except Exception as e:
            logger.exception(e)

    def probs(self, data):
        try:
            logger.info(f"Probs analysis started for {self.namespace}")
            result = dict()
            if "livenessProbe" in data:
                liveness_probs = self._analyze_probe(data.get('livenessProbe'))
                result["liveness_probs"] = liveness_probs
            if "readinessProbe" in data:
                ready_probs = self._analyze_probe(data.get('readinessProbe'))
                result["ready_probs"] = ready_probs
            return result
        except Exception as e:
            logger.exception(e)

    def _analyze_probe(self, probe_data):
        data = {}
        # data["enabled"] = probe_data.get('enabled')
        data['probe_interval'] = probe_data.get('probeInterval')
        data["wait"] = probe_data.get('wait')
        data['time_out'] = probe_data.get('timeout', 10)
        data['uri'] = probe_data.get('path', -1)
        return data

    def vertical_scaling_enabled(self, data):
        try:
            logger.info(f"vertical_scaling analysis started for {self.namespace}")
            status = True
            scoped_data_min = WMTHelm.get_scoped_data(data, "min")
            scoped_data_max = WMTHelm.get_scoped_data(data, "max")
            _int_max_cpu, raw_max_cpu = WMTHelm._extract_number(scoped_data_max.get("cpu"))
            _int_min_cpu, raw_min_cpu = WMTHelm._extract_number(scoped_data_min.get("cpu"))
            _int_max_mem, raw_max_mem = WMTHelm._extract_number(scoped_data_max.get("memory"))
            _int_min_mem, raw_min_mem = WMTHelm._extract_number(scoped_data_min.get("memory"))

            if (_int_min_cpu == _int_max_cpu) or (_int_max_mem == _int_min_mem):
                status = False
            logger.info(
                f"vertical_scaling analysis Finished for {self.namespace}, is vertical_scaling_enabled {status}")
            return status, {"min_cpu": raw_min_cpu, "max_cpu": raw_max_cpu, "min_memory": raw_min_mem,
                            "max_memory": raw_max_mem}
        except Exception as e:
            logger.exception(e)
            return True, {"message": f"Exception occurred {e}"}

    @staticmethod
    def _extract_number(data):
        if not data:
            return None, None
        _data = re.findall(r'\d*\.?\d*', str(data))
        da = [_d for _d in _data if _d][-1]
        if da.isdigit():
            return int(da), data
        else:
            return float(da), data

    @staticmethod
    def get_glb(self):
        pass

    @staticmethod
    def get_git_link(data):
        if "metadata" in data:
            return data.get("metadata").get('annotations').get('app.kubernetes.io/scm-url')

    def get_gslab_link(self, data):
        try:
            logger.info(f"Gslab analysis stated for {self.namespace}")
            gslb = None
            cname = None
            if data.get("global", dict()).get("currentRouting", dict()):
                if "glb" in data.get("global", dict()).get("currentRouting", dict()).get('glbName'):
                    gslb = data.get("global", dict()).get("currentRouting", dict()).get('glbName')
                if len(data.get("global", dict()).get("currentRouting", dict()).get('ingressHosts')) > 0:
                    cname = data.get("global", dict()).get("currentRouting", dict()).get('ingressHosts')[-1]
            logger.info(f"Gslab analysis Finished for {self.namespace}, output: gslb is {gslb},custom_gslb is {cname}")
            return {"gslb": gslb, "custom_gslb": cname}
        except Exception as e:
            logger.exception(e)

    @staticmethod
    def get_dynatrace_options(data):
        return data.get("env").get('DYNATRACE_ENABLED')

    @staticmethod
    def get_scoped_data(data: dict, parameter: str):
        """
        For a given parameter, checks parameter with Global parameter, if does not found. Uses Global else top level one
        """
        scope_data = None
        if parameter in data:
            return data.get(parameter)
        if parameter in data.get("global"):
            return data.get("global").get(parameter)

    def get_hpa_details(self, data):
        logger.info(f"HPA analysis started for {self.namespace}")
        results = dict()
        results["hpa"] = dict()
        hpa = WMTHelm.get_scoped_data(data, "scaling")
        is_enabled = hpa.get("enabled")
        if is_enabled:
            results["is_cpu_based_hpa"] = True
            results["scaling"] = dict()
            results["scaling"]["min"] = hpa.get("min")
            results["scaling"]["max"] = hpa.get("max")
        # Only deals CPU based HPA
        if 'cpuPercent' in hpa:
            results["hpa"]["criteria"] = "cpuPercent"
            results["hpa"]["target"] = hpa.get('cpuPercent')
        # Deals Custom HPA
        elif 'custom' in hpa:
            results["hpa"]["criteria"] = "custom"
            hpa_data = hpa.get('prometheusQueries')
            # todo: targetAverageValue may not get always
            for key, val in hpa_data.items():
                results["hpa"]["target"] = val.get('targetAverageValue')
                results["hpa"]["query"] = val.get('queryContent')
        else:
            results["hpa"]["criteria"] = None
            results["hpa"]["target"] = None
        logger.info(f"HPA analysis finished for {self.namespace}, is hpa enabled {is_enabled}, o/p {results}")
        return is_enabled, results


if __name__ == "__main__":
    url1 = "https://helm2-api.kitt.prod.k8s.walmart.net/getValues/scus-prod-a97/ce-orchestra/ce-sample-service-prod/2"
    url2 = "https://helm2-api.kitt.prod.k8s.walmart.net/getValues/scus-prod-a61/item-service-mx-gql/item-service-prod/66"
    url3 = "https://helm2-api.kitt.prod.k8s.walmart.net/getValues/scus-prod-a61/ce-cartxo-mx-gql/cartxo-prod/86"
    url4 = "https://helm2-api.kitt.prod.k8s.walmart.net/getValues/eus2-prod-aspcl08/ca-single-profile/ca-profile-app-prod-eus/77"
    data_1 = WMTHelm.get_kitt_file(url1)
    data_2 = WMTHelm.get_kitt_file(url2)
    data_3 = WMTHelm.get_kitt_file(url3)
    data_4 = WMTHelm.get_kitt_file(url4)
    print(WMTHelm.get_gslab_link(data_1))
    print(WMTHelm.get_gslab_link(data_2))
    print(WMTHelm.get_gslab_link(data_3))
    print(WMTHelm.get_gslab_link(data_4))
    # res = WMTwcnp.get_release_details("mx-glass", "scus-prod-a77")
    #
    # # print(WMTwcnp.horizontal_scaling_enabled(data_1))
    # #
    # # print(WMTwcnp.get_hpa_details(data_1))
    # # print(WMTwcnp.horizontal_scaling_enabled(data_2))
    # #
    # # print(WMTwcnp.get_hpa_details(data_2))
    wmt = WMTHelm("ca-single-profile", "eus2-prod-aspcl08")
    results = wmt()
    print(results)
    # wmt = WMTwcnp("ce-orchestra", "scus-prod-a97")
    # results = wmt()
    # print(results)
