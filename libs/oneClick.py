from git_service.git import GitApi
import yaml, re, logging
from libs.wcnp_handler import get_pods_count
from concurrent.futures import ThreadPoolExecutor
import urllib.parse as urlparse
import ntpath, os

exp = r"\.min\s*=\s*(.+?)\s*\|\s*\.max\s*=\s*(.+?)\s*\|"
min_max_compliel = re.compile(exp)

logger = logging.getLogger(__name__)


def process_nested_scalar_files(url):
    files_to_process = list()
    git_api = GitApi()
    data = git_api.get_file_content_using_url(url)
    values = yaml.load(data, Loader=yaml.FullLoader)
    for _file in values.get("references", list()):
        url_parts = list(urlparse.urlparse(url))
        head, tail = ntpath.split(url_parts[2])
        # query = dict(urlparse.parse_qsl(url_parts[4]))
        join_path = os.path.join(head, _file.get("file"))
        abspath_path = os.path.abspath(join_path)
        url_parts[2] = abspath_path
        final_path = urlparse.urlunparse(url_parts)
        # final_path = f"{url_parts[0]}://{url_parts[1]}{abspath_path}"
        files_to_process.append(final_path)
    return files_to_process


def process_gslbs(url):
    processed_lbs = list()
    git_api = GitApi()
    data = git_api.get_file_content_using_url(url)
    values = yaml.load(data, Loader=yaml.FullLoader)
    for step in values.get("steps", list()):
        for k, v in step.items():
            if "services" != k:
                continue
            for srv in v:
                for k, lbs in srv.items():
                    for _, _lbs in lbs.items():
                        for _lb in _lbs:
                            processed_lbs.append(_lb.get("domain"))
    return processed_lbs


def get_current_count(data):
    current_count = get_pods_count(data.get("namespace"), data.get("app"), data.get("cluster_id"))
    return data, current_count


def analyze_scalar_data(url, pall_calls_size=20):
    results = process_scalar(url, pall_calls_size=pall_calls_size)
    for data in results:
        try:
            data["min_threshold_pct"] = (data.get("current_count") / float(data.get("min_expected"))) * 100
        except Exception as e:
            data["min_threshold_pct"] = 0
            logger.info(e)
        try:
            data["max_threshold_pct"] = (data.get("current_count") / float(data.get("max_expected"))) * 100
        except Exception as e:
            data["max_threshold_pct"] = 0
            logger.info(e)

    return results


def process_scalar_file_and_filter_not_meeting_max_pods_apps(url, pall_calls_size=20, check_thresholds=True):
    not_meeting_apps = list()
    nmeeting_apps = list()
    results = process_scalar_file_and_data(url, pall_calls_size=pall_calls_size)
    for data in results:
        if data.get("current_count") >= data.get("min_expected"):
            logger.info(f"Min Pods are created {data}, successfully")
            nmeeting_apps.append(data)
        else:
            logger.warning(f"Min Pods are created {data}, Failed")
            not_meeting_apps.append(data)
    return {"success": nmeeting_apps, "failed": not_meeting_apps}


def process_scalar(url, pall_calls_size=20):
    git_api = GitApi()
    data = git_api.get_file_content_using_url(url)
    values = yaml.load(data, Loader=yaml.FullLoader)
    if "references" in values:
        consolidated_data = list()
        files = process_nested_scalar_files(url)
        for _file in files:
            _data = process_scalar_file_and_data(_file, pall_calls_size)
            consolidated_data.extend(_data)
        return consolidated_data
    else:
        return process_scalar_file_and_data(url, pall_calls_size)


def _has_nested_yaml(url):
    git_api = GitApi()
    data = git_api.get_file_content_using_url(url)
    values = yaml.load(data, Loader=yaml.FullLoader)
    if "references" in values:
        return True
    return False


def process_scalar_file_and_data(url, pall_calls_size=20):
    git_api = GitApi()
    data = git_api.get_file_content_using_url(url)
    values = yaml.load(data, Loader=yaml.FullLoader)
    data = values.get("services")
    results = list()
    for service in data:
        if 'wcnp-scaler' not in service:
            continue
        for scalr_data in service.get('wcnp-scaler', dict()):

            namespace = scalr_data.get("namespace")
            app_id = scalr_data.get("app")
            min_max_data = min_max_compliel.findall(scalr_data.get("matching"))
            min, max = 0, 0
            if min_max_data:
                min, max = min_max_data[0]
            clusters = scalr_data.get("clusters")
            for cluster in clusters:
                results.append(
                    {"namespace": namespace, "app": app_id, "min_expected": int(min), "max_expected": int(max),
                     "cluster_id": cluster.get("cluster")})
    # for app in resp:
    #     current_count = get_pods_count(app.get("namespace"), app.get("app"), app.get("cluster_id"))
    #     app["min"] = current_count

    data_per_threads = [results[i:i + pall_calls_size] for i in range(0, len(results), pall_calls_size)]
    for i, slice_data in enumerate(data_per_threads):
        logger.info("Processing slice {}/{}".format(i, len(data_per_threads)))
        futures = list()
        pool = ThreadPoolExecutor(len(slice_data))
        for app in slice_data:
            futures.append(pool.submit(get_current_count, app))
        for future in futures:
            try:
                data, current_count = future.result()
                data["current_count"] = current_count
            except Exception as e:
                logger.exception(e)
    return results


if __name__ == "__main__":
    # url = "https://gecgithub01.walmart.com/raw/SRE/production-playbooks/main/yaml-playbooks/international-tech/intl-canada/ca-wcnp-scaler/ca-stress-test-scale/ca-stress-test-scale-100pct.yaml?token=GHSAT0AAAAAAAADOAUD2GJ46BDOB6EER7V6ZJQSZ3Q"
    # post_stress_test = "https://gecgithub01.walmart.com/raw/SRE/production-playbooks/main/yaml-playbooks/international-tech/intl-canada/ca-wcnp-scaler/ca-stress-test-scale/ca-post-stress-test-normal-mode.yaml?token=GHSAT0AAAAAAAADOAUDBJNQ736WD4XBNYFWZJQVACQ"
    # analyze_scalar_data(post_stress_test)
    # process_gslbs(
    #     "https://gecgithub01.walmart.com/raw/SRE/production-playbooks/main/yaml-playbooks/international-tech/intl-mexico/mexico-gtm-failover/disable-azure-scus.yml?token=GHSAT0AAAAAAAAC7RFQEIS3D5LGTRBLLEFSZCQWAVA")
    process_scalar(
        "https://gecgithub01.walmart.com/raw/SRE/production-playbooks/main/yaml-playbooks/international-tech/intl-mexico/mx-scaler-stresstest/mx-wcnp-scale-down-All-post-stresstest.yaml?token=GHSAT0AAAAAAAADOAUDECJFWKDEBP5KAL3GZJSWLSQ")
