__author__ = '<PERSON><PERSON> cheepati'
__version__ = "0.0.1"
"""
This is Splunk client to execute splunk queries.
"""
import requests, logging, re, time, random, datetime, pytz
from lib.ssh import Parallel
from common.settings import BASE_DIR
from common.lib.core.indexers import nfs_indexers
from common.lib.utility import write_dict_to_file
from common import settings

import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

uname = "SVCGTPPerformance"
pwd = '''([\|WR[>Gef3h6=@'''
logger = logging.getLogger(__name__)
pst = pytz.timezone('America/Los_Angeles')


class SplunkRestClient:
    exporter_location = "/app/omniperf"
    conf_location = "/tmp"
    conf_file = ".splunk.conf"
    splunk_client = "splunk_client.py"

    def __init__(self, service, query, latest_time='now', earliest_time="-2h", sid=None):
        self.service = service
        self.query = query
        self.latest_time = latest_time
        self.earliest_time = earliest_time
        self.time_is = datetime.datetime.now(pst).strftime('%Y_%m_%d_%H_%M')
        self.sid = f"{service}_{self.time_is}"
        self.user_name = uname
        self.password = pwd
        self.url = f'https://api-ce-logsearch01.prod.walmart.com:8089/services/search/jobs'
        self.successfully_executed_query = False
        self._is_fetch_done = False
        self.output_mode = 'json'
        self.events = 0
        self.indexer = random.choice(nfs_indexers)
        self.exporter_input_location = f'{SplunkRestClient.exporter_location}/input'
        self._file = f"{self.sid}.json"
        self.dst_conf_file = None
        self.artifact_file = None
        self._output = '{}/output/{}.csv'.format(SplunkRestClient.exporter_location, self.sid)

    def build_post_body(self):
        body_data = {
            # 'max_count': self.max_count,
            'search': self.query,
            'earliest_time': self.earliest_time,
            'output_mode': self.output_mode,
            'latest_time': self.latest_time

        }

        return body_data

    def fetch(self, wait=False, retries=4, *args, **kwargs) -> None:
        """
        Executes the Query on Splunk.
        Args:
            query:
            wait:
            retries:
            *args:
            **kwargs:

        Returns:

        """
        logger.info("Initializing Splunk fetch call")
        for retry in range(retries):
            if retry != 0: logger.info(f"Retrying to execute query of {retry}/{retries} ")
            try:
                headers = {'Content-Type': 'application/json', 'Accept': 'application/json'}
                resp = requests.post(self.url, data=self.build_post_body(), verify=False,
                                     auth=(self.user_name, self.password), headers=headers)
                if resp.ok:
                    self.sid = resp.json().get("sid")
                    self.successfully_executed_query = True
                    logger.info(f"Initializing Splunk fetch call successful, sid is {self.sid}")
                    break
                logger.error("Initializing Splunk fetch call Failed")
            except Exception as e:
                logger.exception(e)
                continue
        if wait:
            self.fetch_wait()

    def fetch_wait(self, sleep=15) -> None:
        """
        It waits until query processing is done util job fetches all the required data.
        Args:
            sleep:

        Returns:

        """
        logger.info("Process waits until, Splunk fetch call finishes")
        try:
            while True:
                time.sleep(sleep)
                if not self.is_fetch_done: continue
                self._is_fetch_done = True
                logger.info("Splunk fetch call finished processing")
                break
        except Exception as e:
            pass

    def exporter_wait(self, sleep=15) -> None:
        """
        Waits until Splunk exporter exports results to output file
        Args:
            sleep:

        Returns:

        """
        logger.info("Process waits until, Splunk exporter call finishes")
        try:
            while True:
                time.sleep(sleep)
                if not self.is_splunk_exporter_execution_done: continue
                logger.info("Finished executing,Splunk exporter call")
                break
        except Exception as e:
            pass

    def _get_fetch_status(self, retries=3):
        logger.info("Checking Splunk Fetch call status")
        _status = False
        for retry in range(retries):
            try:
                url = f"{self.url}/{self.sid}"
                headers = {'Content-Type': 'application/json', 'Accept': 'application/json'}
                data = {'output_mode': self.output_mode}
                resp = requests.post(url, data, verify=False, auth=(self.user_name, self.password), headers=headers)
                if resp.ok:
                    _dispatchState = resp.json().get("entry")[0].get("content").get('dispatchState')
                    if _dispatchState == "DONE":
                        # eventCount
                        self.events = resp.json().get("entry")[0].get("content").get('eventCount')
                        logger.info("Splunk Fetch call finished successfully")
                        return True
                    progress = resp.json().get("entry")[0].get("content").get('doneProgress')
                    logger.info(f"Current Splunk Fetch call status is {_dispatchState}, "
                                f"progress status is {progress}/100")
                return _status
            except Exception as e:
                logger.exception(e)
        return _status

    def get_in_progress_jobs(self, retries=3):
        logger.info("Processing to get in-progress job")
        jobs = list()
        for retry in range(retries):
            try:
                url = f"{self.user_url}"
                headers = {'Content-Type': 'application/json', 'Accept': 'application/json'}
                data = {'output_mode': self.output_mode}
                resp = requests.post(url, data, verify=False, auth=(self.user_name, self.password), headers=headers)
                if resp.ok:
                    # jobs = [job for job in resp.json()["entry"] if job.get("author") == self.user_name]
                    for job in resp.json()["entry"]:
                        logger.info(job["author"])
                    logger.info(jobs)
                return jobs
            except Exception as e:
                logger.exception(e)
        return jobs

    @property
    def is_fetch_done(self) -> bool:
        """
        Is task is completed or not
        Returns:

        """
        _status = self._get_fetch_status()
        if _status:
            return True
        return False

    def _get_output(self, retries=3):
        for retry in range(retries):
            try:
                url = f"{self.url}/{self.sid}/results"
                headers = {'Content-Type': 'application/json', 'Accept': 'application/json'}
                data = {'output_mode': self.output_mode}
                resp = requests.post(url, data, verify=False, auth=(self.user_name, self.password), headers=headers)
                # if resp.ok:
                #     status = resp.json().get("entry")[0].get("content").get('dispatchState')
                #     if status == "DONE":
                #         return True
                # return status
            except Exception as e:
                logger.exception(e)

    def async_export(self) -> None:
        """
        Processing large splunk files
        Args:
            output:

        Returns:

        """
        logger.info("Initializing Splunk exporter call, takes few minutes ")
        conf_location = "/tmp"
        conf_file = ".splunk.conf"
        url = f"{self.url}/{self.sid}/results"
        export_command = f'''curl -k -u SVCGTPPerformance:$(cat /tmp/.splunk.conf) {url} --get -d output_mode=csv -o " \
                         f"{self._file} '''
        logger.info(f"Splunk exporter command is {export_command}")
        # copy splunk config
        scp_res = Parallel.SCP.scp_to_remote(from_location=f"{BASE_DIR}/config/splunk.conf", to_host=self.indexer,
                                             to_location=conf_location, to_file=conf_file)
        if scp_res.return_code == 0:
            logger.info("Splunk exporter config copied successfully")
        command_to_create_dir = "mkdir -p {}".format(self.exporter_location)
        # create necessary locations
        res = Parallel.ssh(self.indexer, command=command_to_create_dir)
        if res.return_code == 0:
            logger.info("Splunk exporter Necessary locations created successfully")
        logger.info("Splunk exporter Initialized")
        Parallel.ssh(self.indexer, command=export_command, bg_exec=True)
        Parallel.ssh(self.indexer, command=f'rm -f {conf_location}/{conf_file}')

    def results(self) -> (str, str):
        """
        Processing large splunk files
        Args:
            output:

        Returns:

        """
        logger.info(
            f"Initializing Splunk exporter call in {self.indexer}, takes few minutes. Output location is {self._file} ")
        conf_location = "/tmp"
        conf_file = ".splunk.conf"
        query_file = "query.conf"
        url = f"{self.url}/export"
        export_command = f'''curl -k -u SVCGTPPerformance:$(cat /tmp/.splunk.conf) {url} --data-urlencode \
        search=$(cat /tmp/query.conf) -d output_mode=csv -d earliest_time="-15m" -o {self._file} &'''
        logger.info(f"Splunk exporter command is {export_command}")
        # copy splunk config
        scp_res = Parallel.SCP.scp_to_remote(from_location=f"{BASE_DIR}/config/splunk.conf", to_host=self.indexer,
                                             to_location=conf_location, to_file=conf_file)
        if scp_res.return_code == 0:
            logger.info("Splunk exporter config copied successfully")
            # copy splunk config
        scp_res = Parallel.SCP.scp_to_remote(from_location=f"{BASE_DIR}/config/query.conf", to_host=self.indexer,
                                             to_location=conf_location, to_file=query_file)
        if scp_res.return_code == 0:
            logger.info("Splunk exporter config copied successfully")
        command_to_create_dir = "mkdir -p {}".format(self.exporter_location)
        # create necessary locations
        res = Parallel.ssh(self.indexer, command=command_to_create_dir)
        if res.return_code == 0:
            logger.info("Splunk exporter Necessary locations created successfully")
        logger.info("Splunk exporter Initialized")
        Parallel.ssh(self.indexer, command=export_command)
        # Parallel.ssh(self.indexer, command=f'rm -f {conf_location}/{conf_file}')

        return self.indexer, self._file

    def copy_config_client_files(self):

        logger.info("Copy Splunk exporter config file")
        # copy splunk config
        scp_res = Parallel.SCP.scp_to_remote(from_location=f"{BASE_DIR}/config/splunk.conf", to_host=self.indexer,
                                             to_location=SplunkRestClient.conf_location,
                                             to_file=SplunkRestClient.conf_file)
        if scp_res.return_code == 0:
            logger.info("Splunk exporter config copied successfully")

        logger.info("Copy Splunk exporter client file")

        self.dst_conf_file = f"{SplunkRestClient.exporter_location}/{SplunkRestClient.splunk_client}"
        from_location = f"{BASE_DIR}/service/steller/{SplunkRestClient.splunk_client}"

        logger.info(f"dst_conf_file is {self.dst_conf_file}")
        logger.info(f"from_location is {from_location}")
        scp_res = Parallel.SCP.scp_to_remote(from_location=from_location, to_host=self.indexer,
                                             to_location=SplunkRestClient.exporter_location,
                                             to_file=SplunkRestClient.splunk_client)
        if scp_res.return_code == 0:
            logger.info("Splunk exporter client copied successfully")

    def create_and_copy_artifact_file(self, data: dict):
        self.artifact_file = f"{settings.STELLER_OUTPUT_LOCATION}/{self._file}"
        logger.info(f"artifact_file {self.artifact_file}")
        logger.info(f"artifact  data {data}")
        write_dict_to_file(self.artifact_file, data)
        # copy splunk config
        scp_res = Parallel.SCP.scp_to_remote(from_location=self.artifact_file, to_host=self.indexer,
                                             to_location=f"{self.exporter_input_location}",
                                             to_file=self._file)
        if scp_res.return_code == 0:
            logger.info("Splunk exporter config copied successfully")

    def pre_process(self, data):
        "input output in_progress logs"
        logger.info("Preparing pre-process work, copying client, creating necessary files and copying query ...")
        create_working_locations = "mkdir -p"
        for _location in ["input", "output", "in_progress", "logs"]:
            create_working_locations = f"{create_working_locations} {SplunkRestClient.exporter_location}/{_location} "
        Parallel.ssh(self.indexer, create_working_locations)
        self.copy_config_client_files()
        self.create_and_copy_artifact_file(data)
        logger.info("Completed work")

    def export(self) -> (str, str):
        """
        Processing large splunk files
        Args:
            output:

        Returns:

        """
        logger.info(f"Initializing Splunk exporter call in {self.indexer}, takes few minutes. "
                    f"Output location is {self._output} ")
        self.pre_process(self.build_post_body())
        command = f"cd {SplunkRestClient.exporter_location} && nohup python {SplunkRestClient.splunk_client} execute " \
                  f"--sid {self.sid} >/dev/null 2>&1 & "
        logger.info(f"Exporter command is {command} and executing at {self.indexer}")
        logger.info("Splunk exporter Initialized, takes some time to finish")
        Parallel.ssh(self.indexer, command, bg_exec=True)

        return self.indexer, self._file

    @staticmethod
    def _normalize_ssh_output(results, read_lines=False):
        if results.return_code == 0:
            if isinstance(results.stdout, list):
                return True, results.stdout
            else:
                return True, results.stdout.rstrip("\n")

        return False, None

    @property
    def is_splunk_exporter_execution_done(self):
        logger.info("Checking Splunk exporter call status")
        success_command = f'ps -ef|grep curl|grep {self.sid}|grep -v "ps -ef"|wc -l'
        s_status, s_stdout = SplunkRestClient._normalize_ssh_output(Parallel.ssh(host=self.indexer,
                                                                                 command=success_command))
        if s_status:
            if int(s_stdout) >= 1:
                logger.info(f"Checking Splunk exporter call in-progress for {self.sid}")
                return False
        logger.info(f"Checking Splunk exporter call completed for {self.sid}")
        return True

    def wait(self) -> None:
        if not self._is_fetch_done:
            self.fetch_wait()
        self.exporter_wait()

    def _remove_job(self, retries=3):
        logger.info("Processing to get in-progress job")
        for retry in range(retries):
            try:
                url = f"{self.url}/{self.sid}"
                headers = {'Content-Type': 'application/json', 'Accept': 'application/json'}
                resp = requests.delete(url, verify=False, auth=(self.user_name, self.password), headers=headers)
                if resp.ok:
                    logger.info(f"Successfully delete the job {self.sid}")
                    break
                else:
                    logger.warning(f"Sid of {self.sid}, is already deleted from Splunk ")
                    break
            except Exception as e:
                logger.exception(e)

    @property
    def output(self) -> (str, str):
        """
        Gets the output of the executed query
        Returns:

        """
        self.export()
        self.exporter_wait()
        logger.info(f"Exporter Job is done, output is at {self.indexer}:{self._file}")
        logger.info("Cleaning necessary files files in Indexers")
        self.cleanup()
        return self.indexer, self._file, self._output

    def cleanup(self) -> (str, str):
        """
        Gets the output of the executed query
        Returns:

        """
        logger.info(f"Cleaning up artifact file from output location,{self.artifact_file}")
        Parallel.bash(f"rm -f {self.artifact_file}")

        logger.info("Cleaning Splunk config file")
        Parallel.ssh(self.indexer, command=f"rm -f {self.dst_conf_file}")

        logger.info(f"Deleting processed sid {self.sid}")
        self._remove_job()
        return self.artifact_file, self.dst_conf_file


if __name__ == "__main__":
    import sys, os, datetime

    log_level = logging.INFO
    # log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(log_level)
    log_format = '%(asctime)s %(filename)28s  %(lineno)4d %(levelname)10s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setLevel(level=logging.DEBUG)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)

    filename = "{}-{}.log".format(os.path.basename(__file__).replace('.py', ''),
                                  datetime.datetime.now().strftime('%Y-%m-%d'))
    fh = logging.FileHandler(filename=os.path.join((os.path.dirname(os.path.abspath(__file__))), filename))
    fh.setLevel(log_level)
    fh.setFormatter(fmt=formatter)
    logger.addHandler(fh)

    # _query = "search index=wcnp_walmart-web"
    _query_val = '''search index=wcnp_search-frontend   cluster_id="*prod*" log.request.path="*" log.event=NGINX_LOGGED|stats count  by log.request.path'''
    _query = '''index="wcnp_walmart-web" ("landingPageURL"="https://www.walmart.com/ip*" OR "landingPageURL"="https://www.walmart.com/product*") actionCateg="log" "pulseData.msg.metric.queryName"= "query ItemById"| stats count by "pulseData.msg.metric.headers.x-o-item-id"'''
    sp = SplunkRestClient("search", _query, earliest_time="08/01/2022:00:00:00", latest_time="08/02/2022:01:00:00")
    out = sp.output
    # %m/%d/%Y:%H:%M:%S
    # earliest="12/11/2012:20:00:00" latest="15/11/2012:20:00:00"
    # sp.fetch()
    # sp.fetch_wait()
    # sp.async_export()
    # print(sp.output)
    # print(sp.export())
    # print(sp.get_in_progress_jobs())
