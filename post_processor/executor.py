from libs.loader import Loader
from libs.prometheus_client import Prometheus
import requests
from libs import shell


class Executor:
    def __init__(self, template, post_data, git_path_location, clone_location, **kwargs):
        self.post_data = post_data
        self.template = template
        self.git_path_location = git_path_location
        self.clone_location = clone_location
        self.kwargs = kwargs
        self.connector = self.load_postprocessor()

    def load_postprocessor(self):
        #  prometheus_handler, requests, shell, location
        _loader = Loader(self.template, False)
        connector = _loader.load_dynamic_executor_file()
        #  prometheus_handler, requests, shell, location
        return connector(Prometheus, requests, shell, self.git_path_location, self.clone_location,
                         **self.kwargs) if connector else None

    def execute(self, *args, **kwargs):
        if not self.connector:
            return kwargs
        try:
            return self.connector.execute(*args, **kwargs)
        except AttributeError:
            return kwargs

    def validate(self, *args, **kwargs):
        errors = []
        if not self.connector:
            return errors
        try:
            return self.run_validation_methods(errors, kwargs)
        except AttributeError:
            return errors

    def run_validation_methods(self, errors, kwargs):
        methods = dir(self.connector)
        for method in methods:
            if "validate_" in method:
                method_obj = getattr(self.connector, method)
                if not isinstance(getattr(type(self.connector), method, None), property):
                    status, property_name, error = method_obj(kwargs)
                    if not status:
                        errors.append({"property": property_name, "error": error})
        return errors


if __name__ == "__main__":
    from template_engine.template_handler import get_latest_templates

    _post_data = {
        "global_data_vars": {
            "min": 20,
            "max": 50,
            "namespace": "mx-single-profile"

        },
        "create_pull_request": True,
        "apps_meta_data": [
            {
                "app_id": "mx-colony-service-prod-primary"
            }

        ]
    }
    # get_latest_templates(force_pull_required=True)
    post_processor = Executor("wcnp_alerts.yaml", _post_data,
                              "international-tech/intl-sre/golden-signals/rules/production/wcnp/app-owner-alerts",
                              "/Users/<USER>/git/progress/juno_1712036258/mms-config")
    erros = post_processor.validate()
    data = post_processor.execute()
    print(data)
    print(erros)
