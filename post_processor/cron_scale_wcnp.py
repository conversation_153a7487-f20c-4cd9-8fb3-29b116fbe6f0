import os


class PostProcessor:
    def __init__(self, prometheus_handler, requests, shell, location):
        """
        args: Always would be leaf nodes
        """
        self.prometheus_handler = prometheus_handler()
        self.requests = requests
        self.shell = shell
        self.location = location
        self.files = self.shell.get_files(location)

    def execute(self, *args, **kwargs):
        return [os.path.join(self.location, _file) for _file in self.files]


if __name__ == "__main__":
    # from common.teap_handler.teap_data_handler import TeapDataHand<PERSON>
    # from libs.oneOps import OneOps
    #
    # teap = TeapDataHandler().teap_data
    # data = teap.get_apps_by_platform_and_tier(platform="cosmosdb", tier="one")
    # _data = data[2]
    from libs.prometheus_client import Prometheus
    from pre_processor.attributes_handler import Validator
    import requests as requests

    post_data = {
        "global_data_vars": {
            "min": 20,
            "max": 50,
            "namespace": "mx-single-profile"

        },
        "create_pull_request": True,
        "apps_meta_data": [
            {
                "app_id": "mx-colony-service-prod-primary"
            }

        ]
    }

    p = PostProcessor(Prometheus, Validator, requests, post_data)
    print(p.execute())
