#!/bin/bash

#
# Start, Stop or Restart APP
#

source $HOME/.bash_profile
TIMESTAMP=`date "+%Y-%m-%d %H:%M:%S"`



startAgent() {
printOutput "Starting Agent in the background..."
cd /data/copy_images && nohup python rsync_agent_v3.py flush_cache_again --market ea --debug >/dev/null 2>&1 &
sleep 14
isAgentRunning

}

startAgentIfNotStarted(){
  printTask "Checking Whether APP is running or not"
  sleep 15
  isAgentRunning
  if [ $? -eq 1 ];
    then
        printOutputNewLine  "\nAgent is not running.. Starting it."
        startAgent
        isAgentRunning
    else
        printOutputNewLine  "\nStart Agent ignored"
        return 0
    fi

}

isAgentRunning() {
    printOutput "Checking if Agent is running.."
    sleep 15
    ps aux | grep "python" |grep "flush_cache_again" | grep -v grep
    if [ $? -eq 0 ];
    then
        printOutputNewLine  "\nAgent is Running."
        return 0
    else
        printOutputNewLine  "\nAgent is not running.."
        return 1
    fi
}

stopAgent() {
    printOutputNewLine  "\nStopping App."
    for pid in `ps aux | grep "python" |grep "flush_cache_again"| grep -v grep | awk '{print $2}'`
    do
        kill -9 $pid
        sleep 15
    done
}


restartApp() {
    stopAgent
    sleep 20
    startAgent
}

printOutput(){
echo  " $1"
}

printOutputNewLine(){
printf  "$1\n"
}

printTask(){
printf  "*********************************************************\n"
printOutputNewLine  "${TIMESTAMP} $1"
printf  "*********************************************************\n"
}

printUsage() {
    echo "\nUsage: ./deploy_agent.sh [start/stop/restart/status/daemon]\n"
    exit 1
}


# MAIN
if [ $# -ne 1 ];then
    printUsage
fi

if [ $1 = 'start' ];
then
    startAgentIfNotStarted
elif [ $1 = 'stop' ];
then
    stopAgent
elif [ $1 = 'restart' ];
then
    restartApp
elif [ $1 == 'status' ];
then
    isAgentRunning
elif [ $1 = 'daemon' ];
then
    startAgentIfNotStarted
else
    printOutput "\nError: Unrecognized Argument: $@"
    printUsage
fi