import logging, subprocess, os, time
from collections import namedtuple
import requests, json, argparse
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
agent_location = "{}/rsync_agent".format(BASE_DIR)
MASTER_LEDGER_FILE = "{}/master_ledger.txt".format(agent_location)
LOCATIONS = {
    "input": "{}/input".format(agent_location),
    "raw": "{}/raw".format(agent_location),
    "inprogress": "{}/in_progress".format(agent_location),
    "completed": "{}/completed".format(agent_location),
    "config": "{}/rsync_agent_config.json".format(agent_location),
    "inventory": "{}/inventory".format(agent_location)
}
CA_RSYNC_DATA_LOCATION = "/home/<USER>/logs/rsync"
# CA_RSYNC_DATA_LOCATION = "/Users/<USER>/Downloads/canada_rsync"
CA_RSYNC_FILE_FORMAT = "rsync-log-step-nfs-1260279955-1-1464997206.prod." \
                       "walmartca.prod-edc01.prod.walmart.com-{file_date}"
STATES = {
    'UNPROCESSED': "not_processed",
    'PROCESSED': "processed",
    'FAILED': "failed"
}

LEDGER_FILE = "{location}/image_files_processor_{timestamp}.txt"
JUNO_BASE_URL = "https://juno.api.stg.walmart.com"
# JUNO_BASE_URL = "http://127.0.0.1:8000"
FILES_LOCATION = "/data/Backup/product-images/img_large"
MX_BACK_UP_LOCATION = "/data/Backup/product-images/img_large"
# MX_BACK_UP_LOCATION = "/Users/<USER>/Downloads/backup"
AKAMAI_FLUSH_URL = "/api/sre/flush/v2/akamai/urls/"
URL = "{}/api/sre/flush/v1/pipeline/".format(JUNO_BASE_URL)
RE_FLUSH_CACHE_URL = "{}/api/sre/flush/v2/akamai/urls/".format(JUNO_BASE_URL)
JSON_OUTPUT_FILE = "{inventory_json}/{file_name}.json"
AKAMAI_THRESHOLD_BODY_SIZE = 50000
REPROCESS_CACHE_FLUSH_TIME_INTERVAL = 15
REPROCESS_CACHE_FLUSH_ELIGIBILITY_COUNT = 2
SSHResult = namedtuple('SSHResult',
                       ["command",
                        "return_code",
                        "pid",
                        "stdout",
                        "stderr",
                        "is_bg_process"
                        ])


def _sub_process(command):
    process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    return process


def bash(command, read_lines=False):
    """
     Executes Bash command in Local machine or localhost
    Args:
        command: Executes the command on local system
        read_lines:

    Returns:

    """
    try:
        shell_results = _sub_process(command)
        (stdout, stderr) = shell_results.communicate()
        if read_lines:
            response = _result(command, shell_results.returncode, shell_results.pid,
                               stdout.decode("utf-8").splitlines(),
                               stderr, is_bg_process=False)
        else:
            response = _result(command, shell_results.returncode, shell_results.pid, stdout.decode("utf-8"),
                               stderr, is_bg_process=False)

        return response
    except Exception as e:
        logger.error('Unable to run the command in with command %s and exception is %s' % (command, e))
        logger.exception(e)


def _result(command, return_code, pid, stdout, stderr, is_bg_process=False):
    result = None
    try:
        result = SSHResult(command, return_code, pid, stdout, stderr, is_bg_process)
    except Exception as e:
        logger.error('Unable to run the command in with command %s and exception is %s' % (command, e))
    return result


class RsyncClient:
    """
    It handles image files to flush the cache and post the image files to i5 image optimize queue.
    """

    def __init__(self, market="od"):
        self.market = market
        self.files_map = dict()
        self.json_files_map = dict()

    def __call__(self, *args, **kwargs):
        RsyncClient.task_logger("Starting Rsync Client Process")
        RsyncClient.create_mandatory_locations()
        RsyncClient.command_logger("Reading config file")
        config = RsyncClient.get_config()
        logger.info("config details are {}".format(config))

        inprogress_location = LOCATIONS.get("inprogress")
        RsyncClient.command_logger(
            "Processing in-progress files, which were failed to process {}".format(inprogress_location))
        self.process_files(inprogress_location, config, processing_in_progress_location_data=True)

        input_location = LOCATIONS.get("input")
        RsyncClient.command_logger("Processing input files {}".format(input_location))
        self.process_files(input_location, config, processing_in_progress_location_data=False)

        RsyncClient.task_logger("Finished Rsync Client Process")

    def process_files(self, input_location, config, processing_in_progress_location_data):
        files, files_map = self.get_latest_files(input_location)
        logger.info("About to process files are {}: {}".format(input_location, len(files)))
        for _file in files:
            self.process_ledgers(files_map.get(_file), config.get("sleep_interval", 60),
                                 config.get("input_img_size", 100),
                                 processing_in_progress_location_data=processing_in_progress_location_data)

    def process_input_files_and_normalize(self, input_location):
        files, files_map = self.get_latest_files(input_location)
        if len(files) == 0:
            logger.info("No files exits in input folder, exiting execution")
            exit(1)
        self.files_map = files_map
        logger.info("About to process files are {}: {}".format(input_location, len(files)))
        for _file in files:
            logger.info("Processing,File {}".format(_file))
            self.process_user_input_file(_file)
            logger.info("Finished Processing,File {}".format(_file))

    @staticmethod
    def create_mandatory_locations():
        logger.info("Creating necessary locations")
        for name, loc in LOCATIONS.items():
            logger.info("Creating location {}".format(name))
            bash("mkdir -p {}".format(loc))

    @staticmethod
    def get_config():
        logger.info("Config location is {}".format(LOCATIONS.get("config")))
        config_file = LOCATIONS.get("config")
        with open(config_file) as f:
            return json.load(f)

    def call_rest_api(self, files, config):
        """
        For a given location, it fetch all the files and calls the i5 cache flush and i5 optimize api.
        """

        if config.get("stop_api_call", False):
            logger.warning("Stop api config set to True, not making any calls to JUNO")
            return False
        logger.info("Image files processing are {}".format(files))
        _data = {"images": files, "banner": self.market}
        _data.update(config.items())
        # data = {"images": files, "banner": self.market, **config}
        headers = {'Content-type': 'application/json', 'User-Agent': 'WMTPerformance'}
        # data = json.dumps(data)
        logger.info("Rest API, post data {}".format(_data))
        resp = requests.post(URL, data=json.dumps(_data), headers=headers, verify=False)
        if resp.ok:
            logger.info("Successfully got the response from Juno, processed images")
            logger.info("Response body {}".format(resp.json()))
            return True
        else:
            logger.error("Failed get the 200 rc from Juno, failed processed images")
            logger.info("Response body {}".format(resp.json()))
            return False

    def call_rest_api_v2(self, files, config):
        """
        For a given location, it fetch all the files and calls the i5 cache flush and i5 optimize api.
        """

        if config.get("stop_api_call", False):
            logger.warning("Stop api config set to True, not making any calls to JUNO")
            return False
        logger.info("Image files processing are {}".format(files))
        _data = {"images": files, "banner": self.market}
        _data.update(config.items())
        # data = {"images": files, "banner": self.market, **config}
        headers = {'Content-type': 'application/json', 'User-Agent': 'WMTPerformance'}
        # data = json.dumps(data)
        logger.info("Rest API, post data {}".format(_data))
        logger.info("Rest API, URL {}".format(URL))
        logger.info("Rest API, headers {}".format(headers))
        resp = requests.post(URL, data=json.dumps(_data), headers=headers, verify=False)
        data = resp.json()
        akamai_urls = data.get("body", dict()).get("results", dict()).get("akamai_urls", list())
        logger.debug("Response body {}".format(data))
        logger.debug("Akamai flush URL's {}".format(akamai_urls))
        if resp.ok:
            logger.info("Successfully got the response from Juno, processed images")
            return True, akamai_urls
        else:
            logger.error("Failed get the 200 rc from Juno, failed processed images")
            return False, True,akamai_urls

    def call_reprocess_cache_flush_api(self, urls, config):
        """
        For a given location, it fetch all the files and calls the i5 cache flush and i5 optimize api.
        """

        if config.get("stop_api_call", False):
            logger.warning("Stop api config set to True, not making any calls to JUNO")
            return False
        _data = {"urls": urls, "banner": self.market}
        # data = {"images": files, "banner": self.market, **config}
        headers = {'Content-type': 'application/json', 'User-Agent': 'WMTPerformance'}
        # data = json.dumps(data)
        logger.debug("Rest API, post data {}".format(_data))
        resp = requests.post(RE_FLUSH_CACHE_URL, data=json.dumps(_data), headers=headers, verify=False)
        data = resp.json()
        if resp.ok:
            logger.info("Successfully got the response from Juno, processed images")
            logger.debug("Response body {}".format(resp.json()))
            return True, data
        else:
            logger.error("Failed get the 200 rc from Juno, failed processed images")
            logger.debug("Response body {}".format(resp.json()))
            return False, data

    @staticmethod
    def handle_after_file_processing_tasks(src_loc, dst_loc, file):
        src_file_with_loc = "{}/{}".format(src_loc, file)
        dst_file_with_loc = "{}/{}".format(dst_loc, file)
        bash("mv {} {}".format(src_file_with_loc, dst_file_with_loc))

    def update_previous_file_pointer(self, pointer, file_name):
        config = self.get_config()
        config.update({"previous_file_pointer": {"pointer": pointer, "file_name": file_name}})
        with open(LOCATIONS.get("config"), 'w') as f:
            json.dump(config, f, indent=4)

    def get_previous_file_pointer(self, file_name):
        try:
            previous_line_numer = self.get_config().get("previous_file_pointer", dict())
            if previous_line_numer.get("file_name", '') == file_name:
                return previous_line_numer.get("pointer")
            return 1
        except Exception as e:
            logger.exception(e)
            return 1

    def handle_image_files(self, file_with_location, sleep_interval, count):
        # Create ledger file
        # RsyncClient.create_ledger(location)
        while not RsyncClient.is_all_files_processed(file_with_location=file_with_location):
            status = False
            # get files
            _files = RsyncClient.get_un_processed_files(file_with_location, count)
            logger.info("Processing , sub set of the files {}".format(_files))
            config = dict()
            # call rest api
            all_api_calls_failed = list()
            for i in range(4):
                logger.info("Calling rest api, for iteration {}".format(i))
                try:
                    config = self.get_config()
                    if self.market == "ca":
                        config.update({"banner": "ca"})
                    status = self.call_rest_api(_files, config)
                except requests.exceptions.ConnectionError as e:
                    logger.warning("Unable to connect to API", e)
                    status = False
                except Exception as e:
                    logger.exception(e)
                    status = False
                all_api_calls_failed.append(status)
                if status:
                    logger.info("Rsync job sleeping for {} sec".format(config.get("api_retry_sleep_interval", 60)))
                    time.sleep(config.get("api_retry_sleep_interval", 60))
                    break
            if len(all_api_calls_failed) == 4:
                logger.warning("All API calls are failing, existing the execution. Will resume in 10 minutes")
                exit(1)
            # update file
            if not status:
                RsyncClient.update_ledger_with_status(file_with_location, _files, STATES.get("FAILED"))
            else:
                RsyncClient.update_ledger_with_status(file_with_location, _files, STATES.get("PROCESSED"))
        logger.info("All files are processed")

    @staticmethod
    def get_all_image_files_from_input_file(file_with_location):
        f = open(file_with_location, "r")
        _files = list()
        logger.debug("Reading file of {}".format(file_with_location))
        for line in f.readlines():
            logger.debug("Line {}".format(line))
            tokens = line.split(",")
            logger.debug("For line {}, tokens are {}".format(line,tokens))
            if tokens[1].rstrip("\n") == STATES.get("UNPROCESSED"):
                _files.append(tokens[0])
        return _files

    def handle_image_files_v2(self, image_files):
        _modified_json_file = None
        config = dict()
        # call rest api
        all_api_calls_failed = list()
        akamai_urls = list()
        for i in range(4):
            logger.info("Calling rest api, for iteration {}".format(i))
            try:
                config = self.get_config()
                if self.market == "ca":
                    config.update({"banner": "ca"})
                status, akamai_urls = self.call_rest_api_v2(image_files, config)
            except requests.exceptions.ConnectionError as e:
                logger.warning("Unable to connect to API", e)
                status = False
            except Exception as e:
                logger.exception(e)
                status = False
            all_api_calls_failed.append(status)
            if status:
                return status, akamai_urls
            logger.info("Rsync job sleeping for {} sec".format(config.get("api_retry_sleep_interval", 60)))
        if len(all_api_calls_failed) == 4:
            logger.warning("All API calls are failing, existing the execution. Will resume in 10 minutes")
            exit(1)

    def handle_reprocessing_akamai_cache_flush(self, urls):
        all_api_calls_failed = list()
        for i in range(4):
            logger.info("Calling rest api, for iteration {}".format(i))
            try:
                config = self.get_config()
                if self.market == "ca":
                    config.update({"banner": "ca"})
                status, akamai_urls = self.call_reprocess_cache_flush_api(urls, config)
            except requests.exceptions.ConnectionError as e:
                logger.warning("Unable to connect to API", e)
                status = False
            except Exception as e:
                logger.exception(e)
                status = False
            all_api_calls_failed.append(status)
            if status:
                return status
        if len(all_api_calls_failed) == 4:
            logger.warning("All API calls are failing, existing the execution. Will resume in 10 minutes")
            exit(1)

    @staticmethod
    def is_file_exits_in_master_ledger(file_name):
        status = RsyncClient.check_file_or_dir_exists(file_name=MASTER_LEDGER_FILE)
        if not status:
            return False
        if not status:
            bash("touch {}".format(file_name))
        command = "grep {} {}|wc -l".format(file_name, MASTER_LEDGER_FILE)
        size = bash(command)
        if int(size.stdout.rstrip("\n")) == 0:
            return False
        logger.info("{} already exits in {} ".format(file_name, MASTER_LEDGER_FILE))
        return True

    @staticmethod
    def _get_files(location):
        """
        Gets all files in a given location
        """
        common = "cd {} && ls".format(location)

        # Check dir, exits
        dir_exists = RsyncClient.check_file_or_dir_exists(dir_name=location)
        if not dir_exists:
            logger.info("Location/dir does not exits {}".format(dir_exists))
            return list()
        res = bash(common, read_lines=True)
        return res.stdout

    def create_ledger(self, locations):
        if self.market == "ca":
            self.create_ledger_ca()
        else:
            self.create_ledger_mx(locations)
        self.devide_raw_files_into_multiple_chunk_files(LOCATIONS.get("raw"), chunk_size=50)

    def reprocess_hard_ledger(self, hours):
        time_minutes = int(hours) * 60
        if self.market == "ca":
            akamai_cache_flush_eligible_file_names = RsyncClient.file_changes_happened_ca(time_minutes)
            logger.info("Number of files processing are {}".format(akamai_cache_flush_eligible_file_names))
            self.write_backup_data_ca(akamai_cache_flush_eligible_file_names)

        else:

            akamai_cache_flush_eligible_file_names = self.file_changes_happend(time_minutes)
            logger.info("Number of files processing are {}".format(akamai_cache_flush_eligible_file_names))
            self.write_backup_data(akamai_cache_flush_eligible_file_names)

    def file_changes_happend(self, time_minutes):

        cmd = "cd {} && find . -printf '%p %C@\n'".format(MX_BACK_UP_LOCATION)
        logger.info("cmd {}".format(cmd))
        res = bash(cmd, read_lines=True)
        akamai_cache_flush_eligible_file_names = list()
        if res.return_code != 0:
            return akamai_cache_flush_eligible_file_names
        for _file in res.stdout:
            tokens = _file.split(" ")
            # file_name,epoch_time,process_count
            _epoch_time_diff = RsyncClient.epoch_time_diff(float(tokens[1]), time.time())
            _count = REPROCESS_CACHE_FLUSH_ELIGIBILITY_COUNT
            _interval = REPROCESS_CACHE_FLUSH_TIME_INTERVAL
            if _epoch_time_diff < time_minutes:
                akamai_cache_flush_eligible_file_names.append(tokens[0])
        return akamai_cache_flush_eligible_file_names

    @staticmethod
    def file_changes_happened_ca(time_minutes):

        cmd = "cd {} && find . -printf '%p %C@\n'".format(CA_RSYNC_DATA_LOCATION)
        logger.info("cmd {}".format(cmd))
        res = bash(cmd, read_lines=True)
        akamai_cache_flush_eligible_file_names = list()
        if res.return_code != 0:
            return akamai_cache_flush_eligible_file_names
        for _file in res.stdout:
            tokens = _file.split(" ")
            # file_name,epoch_time,process_count
            _epoch_time_diff = RsyncClient.epoch_time_diff(float(tokens[1]), time.time())
            _count = REPROCESS_CACHE_FLUSH_ELIGIBILITY_COUNT
            _interval = REPROCESS_CACHE_FLUSH_TIME_INTERVAL
            if _epoch_time_diff < time_minutes and "rsync-log-step-nfs-" in tokens[0]:
                akamai_cache_flush_eligible_file_names.append(tokens[0])
        return akamai_cache_flush_eligible_file_names

    def write_backup_data(self, files):
        bash("mkdir -p {}".format(LOCATIONS.get("raw")))
        _file_with_loc = LEDGER_FILE.format(location=LOCATIONS.get("raw"), timestamp=int(time.time() * 1000))
        logger.info("write_backup_data {}".format(_file_with_loc))
        with open(_file_with_loc, "a") as f:
            for _file in files:
                f.write("{},{}\n".format(_file.strip("./"), STATES.get("UNPROCESSED")))
        self.devide_raw_files_into_multiple_chunk_files(LOCATIONS.get("raw"), chunk_size=50)

    def write_backup_data_ca(self, files):
        bash("mkdir -p {}".format(LOCATIONS.get("raw")))
        _file_with_loc = LEDGER_FILE.format(location=LOCATIONS.get("raw"), timestamp=int(time.time() * 1000))
        logger.info("write_backup_data {}".format(_file_with_loc))
        total_lines = 1
        for _file in files:
            previous_line_numer = 1
            file_with_location = "{}/{}".format(CA_RSYNC_DATA_LOCATION, _file)
            common_line_num = "wc -l {}".format(file_with_location)
            res = bash(common_line_num)
            if res.return_code == 0:
                total_lines = int(res.stdout.rstrip("\n").split()[0])
            else:
                logger.error("Unable to get total lines from the file, {}".format(res.stderr))
                exit(1)
            sed_command = 'sed -n "{},{}p" {}'.format(previous_line_numer, total_lines, file_with_location)
            res = bash(sed_command, read_lines=True)
            images_to_process_are = list()
            if res.return_code in (1, 0):
                images_to_process_are = res.stdout
            bash("mkdir -p {}".format(LOCATIONS.get("raw")))
            _file_with_loc = LEDGER_FILE.format(location=LOCATIONS.get("raw"), timestamp=int(time.time() * 1000))
            with open(_file_with_loc, "a") as f:
                for _img in images_to_process_are:
                    if _img.endswith(".jpg"):
                        f.write("{},{}\n".format(_img, STATES.get("UNPROCESSED")))
        self.devide_raw_files_into_multiple_chunk_files(LOCATIONS.get("raw"), chunk_size=50)

    def create_ledger_mx(self, locations):
        bash("mkdir -p {}".format(LOCATIONS.get("raw")))
        _file_with_loc = LEDGER_FILE.format(location=LOCATIONS.get("raw"), timestamp=int(time.time() * 1000))
        f = open(_file_with_loc, "a")
        for _loc in locations:
            _loc = _loc.rstrip().strip()
            files = RsyncClient._get_files(_loc)
            logger.info("Image files processing are {}".format(files))
            logger.info("Creating ledger file {}".format(_file_with_loc))
            for _file in files:
                f.write("{},{}\n".format(_file, STATES.get("UNPROCESSED")))
        f.close()
        return

    def create_ledger_ca(self):
        date_command = "date +%Y-%m-%d"
        total_lines = 0
        file_date = bash(date_command).stdout.rstrip("\n")
        file_format = CA_RSYNC_FILE_FORMAT.format(file_date=file_date)
        previous_line_numer = self.get_previous_file_pointer(file_format)
        common_line_num = "wc -l {}/{}".format(CA_RSYNC_DATA_LOCATION, file_format)
        res = bash(common_line_num)
        if res.return_code == 0:
            total_lines = int(res.stdout.rstrip("\n").split()[0])
        else:
            logger.error("Unable to get total lines from the file, {}".format(res.stderr))
            exit(1)
        sed_command = 'sed -n "{},{}p" {}/{}'.format(previous_line_numer, total_lines, CA_RSYNC_DATA_LOCATION,
                                                     file_format)
        res = bash(sed_command, read_lines=True)
        images_to_process_are = list()
        if res.return_code in (1, 0):
            images_to_process_are = res.stdout
        bash("mkdir -p {}".format(LOCATIONS.get("raw")))
        _file_with_loc = LEDGER_FILE.format(location=LOCATIONS.get("raw"), timestamp=int(time.time() * 1000))
        with open(_file_with_loc, "a") as f:
            for _img in images_to_process_are:
                if _img.endswith(".jpg"):
                    f.write("{},{}\n".format(_img, STATES.get("UNPROCESSED")))
        self.update_previous_file_pointer(total_lines, file_format)
        return

    @staticmethod
    def get_latest_files(location):
        os.chdir(location)
        files = filter(os.path.isfile, os.listdir(location))
        files_for_map = filter(os.path.isfile, os.listdir(location))
        file_map = {os.path.join(location, f): f for f in files_for_map}
        files = [os.path.join(location, f) for f in files]  # add path to each file
        files.sort(key=lambda x: os.path.getmtime(x))
        return files, file_map

    def process_ledgers(self, file_name, sleep_interval, count, processing_in_progress_location_data=False):
        source_file = "{}/{}".format(LOCATIONS.get("input"), file_name)
        dst_file = "{}/{}".format(LOCATIONS.get("inprogress"), file_name)
        if not processing_in_progress_location_data:
            bash("mv {} {}".format(source_file, dst_file))

        self.handle_image_files(dst_file, sleep_interval, count)
        status = self.is_all_files_processed(dst_file)
        if status:
            RsyncClient.handle_after_file_processing_tasks(LOCATIONS.get("inprogress"), LOCATIONS.get("completed"),
                                                           file_name)

    def devide_raw_files_into_multiple_chunk_files(self, input_location, chunk_size):
        files, files_map = self.get_latest_files(input_location)
        logger.info("About to process files are {}: {}".format(input_location, len(files)))
        for _file in files:
            RsyncClient.split_raw_files_to_multiple(files_map.get(_file), chunk_size)

    @staticmethod
    def find_last_n_minutes_ago_changed_files(location, n):
        _cmd = "cd {} && find . -mmin -{} -type f ".format(location, n)
        tail_command = " -exec ls {} +"
        cmd = _cmd + tail_command
        res = bash(cmd, read_lines=True)
        if res.return_code == 0:
            return res.stdout
        return list()

    def reprocess_n_hours_ago_data(self, hours):
        """
        Sends messages to Kafka
        Flush cache again
        """
        location = LOCATIONS.get("completed")
        _min = int(hours) * 60
        files = RsyncClient.find_last_n_minutes_ago_changed_files(location, _min)
        for _file in files:
            new_file = _file.strip("./")
            cmd = "mv {0}/{1} {2}/reprocess_{1}".format(location, new_file, LOCATIONS.get("input"))
            logger.info(cmd)
            bash(cmd)

    def flush_again(self, hours):
        _min = int(hours) * 60
        akamai_cache_flush_eligible_file_names = self._flush_again(_min)
        logger.info("Following files are eligible {}".format(akamai_cache_flush_eligible_file_names))
        futures = list()
        for _file in akamai_cache_flush_eligible_file_names:
            logger.info("Calling cache flush api for file {}".format(_file))
            pool = ThreadPoolExecutor(10)
            futures.append(pool.submit(self.process_cache_flush_by_file_name, _file))
        for future in futures:
            try:
                response = future.result()
                logger.info(response)

            except Exception as e:
                pass

    def _flush_again(self, minutes):
        """
        Sends messages to Kafka
        Flush cache again
        """
        akamai_cache_flush_eligible_file_names = list()
        inventory_data = RsyncClient.get_inventory_data(MASTER_LEDGER_FILE)
        for _file in inventory_data:
            if not _file.strip().rstrip():
                logger.warning("Empty line,ignoring")
                continue
            tokens = _file.split(",")
            # file_name,epoch_time,process_count
            _epoch_time_diff = RsyncClient.epoch_time_diff(float(tokens[1]), time.time())

            if _epoch_time_diff < minutes:
                logger.info("File start time {} , end time {}, Flush eligible. File {}".format(tokens[1], time.time(),
                                                                                               tokens[0]))
                akamai_cache_flush_eligible_file_names.append(tokens[0])
        return akamai_cache_flush_eligible_file_names

    @staticmethod
    def split_raw_files_to_multiple(file_name, chunk_size):

        RsyncClient._split(file_name, chunk_size)
        source_file = "{}/{}".format(LOCATIONS.get("raw"), file_name)
        logger.info("Removing large file {}".format(source_file))
        bash("rm -f {}".format(source_file))

    @staticmethod
    def chunks(lst, n):
        """Yield successive n-sized chunks from lst."""
        for i in range(0, len(lst), n):
            yield lst[i:i + n]

    @staticmethod
    def _split(file_name, chunk_size=100):
        logger.info("Started: Splitting large file {} in to multiple files ".format(file_name))
        _file_with_location = os.path.join(LOCATIONS.get("raw"), file_name)
        with open(_file_with_location, "r") as f:
            _chunks = RsyncClient.chunks(f.readlines(), int(chunk_size))
            for index, _chunk in enumerate(_chunks):
                _file_name = "chunk_{}_{}".format(index, file_name)
                logger.info("splitting check file name {}".format(_file_name))
                _name_with_loc = os.path.join(LOCATIONS.get("input"), _file_name)
                with open(_name_with_loc, "a") as f:
                    for row in _chunk:
                        f.write("{}".format(row))
        logger.info("Finished: Splitting large file {} in to multiple files ".format(file_name))
        return

    def process_user_input_file(self, file_name):
        """
        Takes the file from input and gets all files from input file,
        Create a list
        Send it to rest API.

        Once response is received , it saves to /inventory folder and will update master_ledger.txt file.
        Args:
            file_name:

        Returns:

        """
        _modified_json_file = None
        dst_file = "{}/{}".format(LOCATIONS.get("inprogress"), self.files_map.get(file_name))
        _image_files = RsyncClient.get_all_image_files_from_input_file(file_name)
        logger.debug("_image_files size: {} for {}".format(len(_image_files), file_name))
        # empty file move s to competed folder and returns True. Else retuen Flase
        file_empty_status = self.handle_empty_file(_image_files, file_name)
        logger.error("file_empty_status: {}".format(file_empty_status))
        if file_empty_status:
            return _modified_json_file

        status, akamai_urls = self.handle_image_files_v2(_image_files)
        logger.debug("Juno API response status (Posting images to Optimize queue and Akamai flush) {}".format(status))
        if status:
            # time.sleep(config.get("api_retry_sleep_interval", 60))
            # inventory_json}/{file_nana
            # _modified_json_file = "{}/{}.json".format(LOCATIONS.get("inventory"), self.files_map.get(file_name))
            _modified_json_file = RsyncClient.format_json_name(self.files_map.get(file_name))

            # write akamai_urls to inventory/json file
            RsyncClient.write_json_file(_modified_json_file, akamai_urls)

            # move input/file to completed/file
            logger.info("move {} to {}".format(file_name, dst_file))
            bash("mv {} {}".format(file_name, dst_file))
        else:
            logger.exception("handle_image_files_v2 api failed")

        return _modified_json_file

    def handle_empty_file(self, _image_files, file_name):
        if len(_image_files) == 0:
            logger.info("File is empty {}, so moving to completed folder ".format(file_name))
            dst_file = "{}/{}".format(LOCATIONS.get("completed"), self.files_map.get(file_name))
            # move input/file to completed/file
            logger.info("move {} to {}".format(file_name, dst_file))
            bash("mv {} {}".format(file_name, dst_file))
            return True
        else:
            logger.debug("Image files count is non zero: {}".format(file_name))
            return False

    def process_and_normalize_akamai_flush(self):
        RsyncClient.task_logger("Starting Rsync Client Process")
        RsyncClient.create_mandatory_locations()
        RsyncClient.command_logger("Reading config file")
        config = RsyncClient.get_config()
        logger.info("config details are {}".format(config))
        input_location = LOCATIONS.get("input")
        RsyncClient.command_logger("Processing input files {}".format(input_location))
        self.process_input_files_and_normalize(input_location)
        RsyncClient.task_logger("Finished Rsync Client Process")

    def get_all_akamai_cache_flush_eligible_files(self):
        akamai_cache_flush_eligible_file_names = list()
        inventory_data = RsyncClient.get_inventory_data(MASTER_LEDGER_FILE)
        for _file in inventory_data:
            if not _file.strip().rstrip():
                logger.warning("Empty line,ignoring")
                continue
            tokens = _file.split(",")
            # file_name,epoch_time,process_count
            _epoch_time_diff = RsyncClient.epoch_time_diff(float(tokens[1]), time.time())
            _count = REPROCESS_CACHE_FLUSH_ELIGIBILITY_COUNT
            _interval = REPROCESS_CACHE_FLUSH_TIME_INTERVAL
            if int(tokens[2].rstrip("\n")) < _count and _epoch_time_diff >= _interval:
                akamai_cache_flush_eligible_file_names.append(tokens[0])
        return akamai_cache_flush_eligible_file_names

    @staticmethod
    def format_json_name(_file):
        return JSON_OUTPUT_FILE.format(inventory_json=LOCATIONS.get("inventory"),
                                       file_name=_file)

    def update_inventory_completed_files_to_master_ledger(self):
        files, files_map = self.get_latest_files(LOCATIONS.get("inprogress"))
        for _file in files:
            _modified_json_file = RsyncClient.format_json_name(files_map.get(_file))
            RsyncClient.add_item_to_master_ledger(_modified_json_file)
            # move input/file to completed/file
            dst_loc_file = os.path.join(LOCATIONS.get("completed"), files_map.get(_file))
            logger.info("move {} to {}".format(_file, dst_loc_file))
            bash("mv {} {}".format(_file, dst_loc_file))

    def reprocess_akamai_flush(self):
        self.update_inventory_completed_files_to_master_ledger()
        akamai_cache_flush_eligible_file_names = self.get_all_akamai_cache_flush_eligible_files()
        for _file in akamai_cache_flush_eligible_file_names:
            logger.info("Calling cache flush api for file {}".format(_file))
            self.process_cache_flush_by_file_name(_file)
        self.update_master_ledger_with_status(akamai_cache_flush_eligible_file_names)

    @staticmethod
    def add_item_to_master_ledger(file_name):
        """
        If file exits, then don't create new one. Return
        Args:
            file_name:

        Returns:

        """
        logger.info("Updating master_ledger.txt with file {} and timestamp".format(file_name))
        status = RsyncClient.is_file_exits_in_master_ledger(file_name)
        if status:
            return True
        logger.info("Getting unprocessed files {}".format(file_name))
        with open(MASTER_LEDGER_FILE, "a") as f:
            f.write("{},{},{}\n".format(file_name, time.time(), 1))
        logger.info("Finished Updating master_ledger.txt with file {} and timestamp".format(file_name))

    def process_cache_flush_by_file_name(self, file_name):
        """ It uses, produced file of cache flush urls, it contains all the urls as list"""
        self.process_akamai_urls_chunk(file_name)

    def process_akamai_urls_chunk(self, file_name):
        """
        Akamai supports 50K characters in per flush by akamai
        Returns:

        """
        urls = RsyncClient.read_json_file(file_name)
        if len(urls) == 0:
            logger.info("Not processing akamai flush, urls size is zero")
            return
        self.handle_reprocessing_akamai_cache_flush(urls=urls)

    @staticmethod
    def read_json_file(file_name):
        try:
            with open(file_name) as f:
                return json.load(f)
        except Exception as e:
            logger.warning("unable to read json_file {}".format(file_name))
            return list()

    @staticmethod
    def write_json_file(file_name, data):
        """
        write akamai_urls to inventory/file_name json file.
        Args:
            file_name:
            data:

        Returns:

        """
        try:
            logger.info("Write, api response to inventory file {}".format(file_name))
            logger.debug("Data which is writing is {}".format(data))
            status = RsyncClient.check_file_or_dir_exists(file_name=file_name)
            if status:
                bash("rm -rf {}".format(file_name))
            with open(file_name, 'a') as f:
                json.dump(data, f)
            logger.info("Finished Writing, api response to inventory/{}".format(file_name))
        except Exception as e:
            logger.exception(e)

    @staticmethod
    def epoch_time_diff(start_time, end_time):
        """
        Epoch time diff in minutes
        Returns:

        """
        diff_in_min = float(end_time - start_time) / 60
        return diff_in_min

    @staticmethod
    def get_un_processed_files(file_with_location, count):
        logger.info("Getting unprocessed files {}".format(file_with_location))
        f = open(file_with_location, "r")
        about_to_process = list()
        for line in f.readlines():
            logger.info("line {}".format(line))
            if count == len(about_to_process):
                return about_to_process
            else:
                tokens = line.split(",")
                logger.info(tokens)
                if tokens[1].rstrip("\n") == STATES.get("UNPROCESSED"):
                    about_to_process.append(tokens[0])
        logger.info("Count of unprocessed files {}".format(about_to_process))
        f.close()
        return about_to_process

    @staticmethod
    def get_inventory_data(file_with_location):
        f = open(file_with_location, "r")
        data = f.readlines()
        f.close()
        return data

    @staticmethod
    def update_ledger_with_status(file_with_location, processed_files, status):
        logger.info("Updating processed file {}".format(processed_files))
        inventory_data = RsyncClient.get_inventory_data(file_with_location)
        f = open(file_with_location, "w")
        for _file in inventory_data:
            tokens = _file.split(",")
            if tokens[0] in processed_files:
                f.write("{},{}\n".format(tokens[0], status))
            else:
                f.write("{}".format(_file))
        f.close()
        logger.info("Finished Updating processed file {}".format(processed_files))

    @staticmethod
    def update_master_ledger_with_status(akamai_cache_flush_eligible_file_names):
        f = None
        try:
            logger.info("Started Updating MATER LEDGER file".format(MASTER_LEDGER_FILE))
            inventory_data = RsyncClient.get_inventory_data(MASTER_LEDGER_FILE)
            with open(MASTER_LEDGER_FILE, "w") as f:
                for _row in inventory_data:
                    tokens = _row.split(",")
                    logger.debug("Check,{} is eligible to flush the cache".format(tokens[0]))
                    if tokens[0] in akamai_cache_flush_eligible_file_names:
                        logger.info("{} eligible ".format(tokens[0]))
                        f.write("{},{},{}\n".format(tokens[0], time.time(), int(tokens[2].rstrip("\n")) + 1))
                    else:
                        logger.debug("{} not eligible ".format(tokens[0]))
                        f.write("{}".format(_row))
        except Exception as e:
            logger.exception(e)

        logger.info("Finished Updating MATER LEDGER file".format(MASTER_LEDGER_FILE))

    @staticmethod
    def is_all_files_processed(file_with_location):
        logger.info("Checking , weather all the unprocessed files are processed or not")
        command = "grep {} {}|wc -l".format(STATES.get("UNPROCESSED"), file_with_location)
        unprocessed_files = bash(command).stdout
        if int(unprocessed_files.rstrip("\n")) == 0:
            logger.info("Found unprocessed files")
            return True
        logger.info("Not Found unprocessed files")
        return False

    @staticmethod
    def check_file_or_dir_exists(file_name=None, dir_name=None):
        """
        Checks weather dir or file exits
        """
        if dir_name:
            command = "[ -d {} ] && echo 'true' || echo 'false'".format(dir_name)
        else:
            command = "[ -f {} ] && echo 'true' || echo 'false'".format(file_name)
        status = bash(command=command)
        # Dir might be soft link, to handle that
        if status.stdout.strip() == "false" and dir_name:
            command = " find -L {} -type l".format(dir_name)
            status = bash(command=command)
            if status.return_code == 0:
                return True
        if status.stdout.strip() == "true":
            return True
        else:
            return False

    @staticmethod
    def task_logger(message):
        logger.info("===========================================================================")
        logger.info(message)
        logger.info("===========================================================================")

    @staticmethod
    def command_logger(message):
        logger.info("****************** {} ******************".format(message))


def do_not_process_twice(name):
    cmd = "ps -ef|grep '{}'|grep python|grep -v grep|wc -l".format(name)
    res = bash(command=cmd)
    if res.return_code == 0:
        if int(res.stdout.strip().rstrip()) == 2:
            logger.warning("Process is already running {}".format(name))
            exit()


def check_arg():
    parser = argparse.ArgumentParser()
    subparsers = parser.add_subparsers(help='commands')

    args_start = subparsers.add_parser('execute', help='Execute any command ')
    args_start.set_defaults(which='execute')

    # args_start.add_argument('-loc', '--locations', metavar='\b', required=True, help='Locations, comma separated ')
    args_start.add_argument('-m', '--market', metavar='\b', required=True, help='market ')
    args_start.add_argument('-d', '--debug', action='store_true', help='Verbose mode')

    args_save = subparsers.add_parser('save', help='Save all image details to inventory file')
    args_save.set_defaults(which='save')
    args_save.add_argument('-m', '--market', metavar='\b', required=True, help='market ')
    args_save.add_argument('-d', '--debug', action='store_true', help='Verbose mode')
    args_save.add_argument('-loc', '--locations', metavar='\b', required=True, help='Locations, comma separated ')

    execute_v2 = subparsers.add_parser('execute_v2', help='execute_v2 any command ')
    execute_v2.set_defaults(which='execute_v2')

    # args_start.add_argument('-loc', '--locations', metavar='\b', required=True, help='Locations, comma separated ')
    execute_v2.add_argument('-m', '--market', metavar='\b', required=True, help='market ')
    execute_v2.add_argument('-d', '--debug', action='store_true', help='Verbose mode')

    flush_cache_again = subparsers.add_parser('flush_cache_again', help='execute_v2 any command ')
    flush_cache_again.set_defaults(which='flush_cache_again')

    # args_start.add_argument('-loc', '--locations', metavar='\b', required=True, help='Locations, comma separated ')
    flush_cache_again.add_argument('-m', '--market', metavar='\b', required=True, help='market ')
    flush_cache_again.add_argument('-d', '--debug', action='store_true', help='Verbose mode')

    reprocess_n_hours_data = subparsers.add_parser('reprocess', help='execute_v2 any command ')
    reprocess_n_hours_data.set_defaults(which='reprocess')

    # args_start.add_argument('-loc', '--locations', metavar='\b', required=True, help='Locations, comma separated ')
    reprocess_n_hours_data.add_argument('-m', '--market', metavar='\b', required=True, help='market ')
    reprocess_n_hours_data.add_argument('-ha', '--hours-ago', metavar='\b', required=True,
                                        help='Provide hours ago, Ex 3 ')
    flush_cache_again_hours_ago = subparsers.add_parser('flush_cache_again_hours_ago', help='execute_v2 any command ')
    flush_cache_again_hours_ago.set_defaults(which='flush_cache_again_hours_ago')

    # args_start.add_argument('-loc', '--locations', metavar='\b', required=True, help='Locations, comma separated ')
    flush_cache_again_hours_ago.add_argument('-m', '--market', metavar='\b', required=True, help='market ')
    flush_cache_again_hours_ago.add_argument('-ha', '--hours-ago', metavar='\b', required=True,
                                             help='Provide hours ago, Ex 3 ')
    flush_cache_again_hours_ago.add_argument('-d', '--debug', action='store_true', help='Verbose mode')
    # reprocess_hard

    reprocess_hard = subparsers.add_parser('reprocess_hard', help='execute_v2 any command ')
    reprocess_hard.set_defaults(which='reprocess_hard')

    # args_start.add_argument('-loc', '--locations', metavar='\b', required=True, help='Locations, comma separated ')
    reprocess_hard.add_argument('-m', '--market', metavar='\b', required=True, help='market ')
    reprocess_hard.add_argument('-ha', '--hours-ago', metavar='\b', required=True,
                                help='Provide hours ago, Ex 3 ')
    reprocess_hard.add_argument('-d', '--debug', action='store_true', help='Verbose mode')
    # create parameters
    # args_save.add_argument('-loc', '--locations', metavar='\b', required=True, help='Locations, comma separated ')

    return parser.parse_args()


if __name__ == '__main__':
    import sys, os, datetime

    args = check_arg()
    if args.debug:
        log_level = logging.DEBUG
    else:
        log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(log_level)
    # log_format = '%(asctime)s  %(lineno)4d %(levelname)10s: %(message)s'
    log_format = '%(asctime)s %(filename)25s %(lineno)3d %(levelname)10s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setLevel(level=logging.DEBUG)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)

    filename = "{}-{}-{}.log".format(args.which, os.path.basename(__file__).replace('.py', ''),
                                     datetime.datetime.now().strftime('%Y-%m-%d'))
    _path = os.path.join(agent_location, "logs")
    bash("mkdir -p {}".format(_path))
    fh = logging.FileHandler(filename=os.path.join(_path, filename))
    fh.setLevel(log_level)
    fh.setFormatter(fmt=formatter)
    logger.addHandler(fh)

    # Additional configuration for error logging
    error_filename = "rsync_error.log".format(datetime.datetime.now().strftime('%Y-%m-%d'))
    error_fh = logging.FileHandler(filename=os.path.join(_path, error_filename))
    error_fh.setLevel(logging.ERROR)
    error_fh.setFormatter(fmt=formatter)
    logger.addHandler(error_fh)

    rsync = RsyncClient(market=args.market.strip().rstrip())
    if args.which == 'execute':
        rsync()
    elif args.which == 'save':
        locations = args.locations.split(",")
        if len(locations) > 0:
            logger.info("User provided , location {}".format(locations))
        else:
            logger.error("User is not provided location")
            exit(1)
        do_not_process_twice("save")
        rsync.create_ledger(locations)
    elif args.which == 'execute_v2':
        """
        Process all new files, 
                        - Check input dir and process and move to in-progress
                        - image optimization (No)
                        - Akamai cache flush (yes)
                        - Never updates master ledger, reason is multiple methods should not update a file/table. 
                          Design pattern, also in file system there is no locking of file, so if we allow multiple 
                          methods to operate on file, may cause data inconsistency 
        """
        do_not_process_twice("execute_v2")
        rsync.process_and_normalize_akamai_flush()
    elif args.which == 'flush_cache_again':
        """
        Re flush the cache only using Master inventory file, 
                 - image optimization (No)
                 - Akamai cache flush (yes)
        """
        do_not_process_twice("flush_cache_again")
        rsync.reprocess_akamai_flush()
    elif args.which == 'reprocess':
        """ 
        Gets the images from Completed folder, sometime files might be deleted in it, so better use reprocess_hard , 
        it does 
                 - image optimization (yes)
                 - Akamai cache flush (yes)
        """
        do_not_process_twice("reprocess")
        rsync.reprocess_n_hours_ago_data(args.hours_ago.strip().rstrip())
    elif args.which == 'flush_cache_again_hours_ago':
        """
        Re flush the cache only using Master inventory file, , 
         - image optimization (No)
         - Akamai cache flush (yes)
        """
        do_not_process_twice("flush_cache_again_hours_ago")
        rsync.flush_again(args.hours_ago.strip().rstrip())
    elif args.which == 'reprocess_hard':
        """ 
        Gets the images from Backup and process , it does 
         - image optimization (yes)
         - Akamai cache flush (yes)
        """
        do_not_process_twice("reprocess_hard")
        rsync.reprocess_hard_ledger(args.hours_ago.strip().rstrip())
