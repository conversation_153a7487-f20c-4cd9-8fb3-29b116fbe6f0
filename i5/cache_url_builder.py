from i5.agent.rsync_agent import Rsync


def get_files_cahnged(tenant):
    normalized_i5_urls = set()
    rsync = Rsync()
    data = rsync.get_recently_changed_data("/data", "5")
    for uri in data:
        final_url = build_tenant_cache_url(tenant, uri)
        normalized_i5_urls.add(final_url)

    return normalized_i5_urls


def build_tenant_cache_url(tenant, uri):
    if tenant == "od":
        return ""
