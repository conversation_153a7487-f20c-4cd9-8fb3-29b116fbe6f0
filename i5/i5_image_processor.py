#!/usr/bin/python3
import requests, os
from bs4 import BeautifulSoup
import logging
import urllib.parse as urlparse
from urllib.parse import urlencode
from concurrent.futures import ThreadPoolExecutor
import re

logger = logging.getLogger(__name__)
I5_MX_ORIGIN = "i5.walmartimages.com.mx"
URLS = {
    "ea": "https://www.walmart.com.mx",
    "od": "https://super.walmart.com.mx",
    "bodega_ea": "",
    "ca": "https://www.walmart.ca"
}
I5_ORIGINS = {
    "mx": I5_MX_ORIGIN,
    "od": I5_MX_ORIGIN,
    "ea": I5_MX_ORIGIN,
    "ca": "i5.walmartimages.ca"
}
I5_REGEX = {
    "MX": {
        "regex_pattern": r"https://i5\.walmartimages\.com.mx/(.*?)(?=['\" ])",
        "base_url": "https://i5.walmartimages.com.mx"
    },
    "CA": {
        "regex_pattern": r"https://i5\.walmartimages\.ca/(.*?)(?=['\" ])",
        "base_url": "https://i5.walmartimages.ca"
    }
}
IMG_URLS = {
    "od": "https://i5.walmartimages.com.mx/gr/images/product-images/img_large",
    "ea": "https://i5.walmartimages.com.mx/gm/images/product-images/img_large",
    "ca": "https://i5.walmartimages.ca"
}
IMG_URL_MAPPER = {

}
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
TEAP_DIR = os.path.join(BASE_DIR, 'config')


def load_size_config():
    size_pair = None
    size_dict = dict()
    try:
        _file = f"{TEAP_DIR}/all_img_sizes.csv"
        size_list = open(_file).read().splitlines()
        # size config is optional, if we have, add to dict
        for line in size_list:
            size_pair = line.split(",")
            size_dict[size_pair[0]] = size_pair[1]
    except FileNotFoundError as e:
        return size_dict

    return size_dict


def extract_urls(upc, size_dict, banner=None, scrolling_required=True):
    results = list()
    if banner in URLS:
        logger.info(f"User is  provided banner or origin {banner}, so urls are extracting for {banner}")
        if scrolling_required:
            origin = URLS.get(banner)
        else:
            origin = IMG_URLS.get(banner)
        results.append(extract_i5_image_links(upc, origin, size_dict, scrolling_required, banner))
    else:
        logger.info(f"User Not provided banner or origin {banner}, so urls are extracting for all banners")
        results = process_all_banners(upc, size_dict)
    return results


def process_all_banners(ups, size_dict):
    result = list()
    for key, value in URLS.items():
        if value:
            result.append(extract_i5_image_links(ups, value, size_dict))
    return result


def extract_i5_image_links(upc, origin, size_dict, scrolling_required=True, banner="mx"):
    logger.info(f"Extracting i5 image urls for upc {upc} and banner {origin}")
    _results = list()
    results = {"origin": origin, "upc": upc}
    original_urls, variation_data_urls = get_urls_with_size(upc, origin,size_dict,scrolling_required=scrolling_required,banner=banner)
    results["data"] = variation_data_urls
    results["original_urls"] = original_urls
    results["status"] = True
    return results


def build_i5_url(upc, origin):
    url = f"{origin}/{upc}"
    # results = {"origin": origin, "upc": upc, "data": [url], "original_urls": [url]}
    return [url]


def process_request(url, retires=5):
    status = False
    headers = {'User-Agent': 'WMTPerformance'
               }
    proxies = {
        "http": "http://sysproxy.wal-mart.com:8080",
        "https": "http://sysproxy.wal-mart.com:8080",
    }
    for retry in range(retires):
        try:
            response = requests.get(url, headers=headers, proxies=proxies, verify=False)
            if response.status_code == 200:
                return True, response
            else:
                return False, response.status_code
        except Exception as e:
            logger.exception(e)
            return status, str(e)
    return status, None


def get_urls_with_size1(response, size_dict=None):
    original_urls = list()
    urls = list()
    try:
        htmldata = response.text
        soup = BeautifulSoup(htmldata, 'html.parser')

        for i in soup.find_all('img'):
            if "i5.walmartimages.com.mx" in i['src']:
                i5_url = i['src'] + "&odnUpScale=1"
                original_urls.append(i5_url)
                if size_dict:
                    for size in size_dict:
                        new_sizes = {"odnHeight": size, "odnWidth": size_dict[size]}
                        url_parts = list(urlparse.urlparse(i5_url))
                        query = dict(urlparse.parse_qsl(url_parts[4]))
                        query.update(new_sizes)
                        url_parts[4] = urlencode(query)
                        i5_url2 = urlparse.urlunparse(url_parts)
                        urls.append(i5_url2)
    except Exception as e:
        logger.exception(e)
    return original_urls


def get_urls_with_size(upc=None, origin=None, size_dict=None, scrolling_required=True, banner="mx"):
    """
    response:  response of i5 image body, it is for MX
    upc: it is uri , not really upc. it is for CA
    origin: for canada purpose

    Note: upc and origin are for CA
    """
    try:
        if scrolling_required:
            if banner == "ca":
                url = f"{origin}/en/ip/{upc.strip().rstrip()}"
            else:
                url = f"{origin}/ip/abc/{upc.strip().rstrip()}"
            i5_origin = I5_ORIGINS.get(banner)
            status, response = process_request(url)
            if status:
                original_urls = extract_urls_from_html_page_re(response,banner)
                variation_data_urls = add_all_pixel_variations(original_urls, size_dict)
            else:
                original_urls = list()
                variation_data_urls = list()
        else:
            original_urls = build_i5_url(upc, origin)
            variation_data_urls = add_all_pixel_variations(original_urls, size_dict)
            # variation_data_urls_without_odnd_upscale_parm = add_query_param_to_url(variation_data_urls,
            #                                                                        ["odnUpScale=1"])
            # variation_data_urls_without_odnd_bg_parm = add_query_param_to_url(variation_data_urls,
            #                                                                   ["odnBg=FFFFFF"])

        return original_urls, variation_data_urls
    except Exception as e:
        logger.exception(e)


def canada_urls(upc):
    """
    get canada i5 origin urls
    """
    try:
        # Assuming I5_ORIGINS and origin are correctly defined and used
        i5_origin = URLS.get('ca')  # Fallback to origin if ca key is not found
        url = f"{i5_origin}/en/ip/{upc.strip()}"

        status, response = process_request(url)
        if status:
            original_urls = extract_urls_from_html_page_re(response, "ca")
        else:
            original_urls = []

        return original_urls
    except Exception as e:
        logger.exception(e)
        return []  # Explicitly return an empty list in case of an exception

def extract_img_from_html_page(response, i5_origin):
    img_data = list()
    soup = BeautifulSoup(response.text, 'html.parser')
    for i in soup.find_all('img'):
        if i5_origin in i.get('src'):
            img_data.append(i['src'])
        elif i5_origin in i.get('srcset'):
            img_data.append(i['srcset'])
    return img_data


def extract_urls_from_html_page_re(response, region):
    # Convert region to uppercase to ensure case-insensitive matching
    region_upper = region.upper()

    if region_upper not in I5_REGEX:
        region_upper = 'MX'

    # Lookup the regex pattern and base URL for the given region
    regex_pattern = I5_REGEX[region_upper]["regex_pattern"]
    base_url = I5_REGEX[region_upper]["base_url"]

    # Find all matches in the HTML content
    matches = re.findall(regex_pattern, response.text, re.IGNORECASE)

    # Convert matches to a set and back to a list to ensure uniqueness
    unique_urls = list(set(matches))

    # Prepend the base URL to each match
    extracted_urls = [f"{base_url}/{match}" for match in unique_urls]
    pattern = r"\?odnHeight=[^ ]*"

    # Replace the matched pattern with an empty string in each URL
    cleaned_urls = [re.sub(pattern, "", url) for url in extracted_urls]

    return list(set(cleaned_urls))

def add_all_pixel_variations(urls, size_dict=None):
    _urls = list()
    for url in urls:
        try:
            if size_dict:
                for size in size_dict:
                    new_sizes = {"odnHeight": size, "odnWidth": size_dict[size]}
                    # new_sizes = {"odnHeight": size, "odnWidth": size_dict[size], "odnBg": 'FFFFFF'}
                    url_parts = list(urlparse.urlparse(url))
                    query = dict(urlparse.parse_qsl(url_parts[4]))
                    query.update(new_sizes)
                    url_parts[4] = urlencode(query)
                    i5_url2 = urlparse.urlunparse(url_parts)
                    _urls.append(i5_url2)
        except Exception as e:
            logger.exception(e)
    # if len(_urls) == 0 and not size_dict:
    #     _urls = urls
    return _urls


def get_uri_excluding_query_params(url):
    url_parts = list(urlparse.urlparse(url))
    return url_parts[2].strip("/")


def process_image_urls_with_size(original_urls, size_dict):
    urls = list()
    try:

        for i5_url in original_urls:
            for size in size_dict:
                new_sizes = {"odnHeight": size, "odnWidth": size_dict[size]}
                url_parts = list(urlparse.urlparse(i5_url))
                query = dict(urlparse.parse_qsl(url_parts[4]))
                query.update(new_sizes)
                url_parts[4] = urlencode(query)
                i5_url2 = urlparse.urlunparse(url_parts)
                urls.append(i5_url2)
    except Exception as e:
        logger.exception(e)
    return urls, original_urls


def write_to_file(file, target_url):
    with open(file, 'a') as f:
        f.write("{}\n".format(target_url))


def process_data(ups_list, origins=None, pall_calls_size=5, process_all_pixel_variations=False,
                 scrolling_required=True):
    logger.info("Extracting i5 image urls parallel for all upcs")
    result = list()
    size_dict = load_size_config()
    # for ups in ups_list:
    #     result.extend(extract_urls(ups, size_dict, origins))
    if not process_all_pixel_variations:
        size_dict = None
    if not scrolling_required:
        pall_calls_size = len(ups_list)
    data_per_threads = [ups_list[i:i + pall_calls_size] for i in range(0, len(ups_list), pall_calls_size)]
    logger.info("Splitting total ups i5 mage fetch to multiple slices {}".format(len(data_per_threads)))
    for i, slice_data in enumerate(data_per_threads):
        logger.info("Processing slice {}/{}".format(i, len(data_per_threads)))
        futures = list()
        pool = ThreadPoolExecutor(len(slice_data))
        for ups in slice_data:
            futures.append(pool.submit(extract_urls, ups, size_dict, origins, scrolling_required))
        for future in futures:
            try:
                result.extend(future.result())
            except Exception as e:
                logger.exception(e)
        logger.info("Finished Processing slice {}/{}".format(i, len(data_per_threads)))
    logger.info("Processing All slices")
    return result


def process_and_validate(ups_list, origins=None, process_all_pixel_variations=False, scrolling_required=True):
    i5_all_dimensions_urls, i5_original_urls, failed_fetch_upcs = list(), list(), list()
    # ups_list, origins=None, pall_calls_size=5, process_all_pixel_variations=False,
    #                  scrolling_required=True
    results = process_data(ups_list, origins, pall_calls_size=5,
                           process_all_pixel_variations=process_all_pixel_variations,
                           scrolling_required=scrolling_required)
    for upc_with_origin in results:
        try:
            if upc_with_origin.get("status"):
                i5_all_dimensions_urls.extend(upc_with_origin.get("data", list()))
                i5_original_urls.extend(upc_with_origin.get("original_urls", list()))
            else:
                failed_fetch_upcs.append(upc_with_origin.get("upc", list()))
        except Exception as e:
            logger.exception(e)

    return i5_all_dimensions_urls, i5_original_urls, failed_fetch_upcs


def process_and_validate_images(images, origin=None):
    failed_upc_origin_details = list()
    all_i5_urls = list()
    i5_original_urls = list()
    size_dict = load_size_config()
    urls = build_image_url_using_image(images, origin)
    urls_without_odnd_parm = build_image_url_using_image(images, origin, ignore_odnd_parm=True)
    results = process_image_urls_with_size(urls, size_dict)

    all_i5_urls.extend(results[0])
    i5_original_urls.extend(results[1])

    total_urls_count = len(all_i5_urls)
    after_removing_duplicate_urls_count = len(set(all_i5_urls))
    logger.info(f"Total i5 urls to process is {total_urls_count}")
    logger.info(f"After removing duplicate urls, urls to process are {after_removing_duplicate_urls_count}")
    logger.info(f"URL's to be process are  {all_i5_urls}")
    return all_i5_urls, failed_upc_origin_details, i5_original_urls, urls_without_odnd_parm, total_urls_count, \
           after_removing_duplicate_urls_count


def _build_image_url_using_image(image, banner, ignore_odnd_parm=False):
    base_url = IMG_URLS.get(banner)
    if ignore_odnd_parm:
        return f"{base_url}/{image}?odnBg=FFFFFF"
    return f"{base_url}/{image}?odnBg=FFFFFF&odnUpScale=1"


def build_image_url_using_image(images, banner, ignore_odnd_parm=False):
    data = list()
    for image in images:
        data.append(_build_image_url_using_image(image, banner, ignore_odnd_parm))
    return data


if __name__ == '__main__':
    # process_and_validate(["00316843034978", "00316843034979"], origins="ea")
    # data = process_and_validate(
    #     ["003168430349783", "00316843034979"])
    # print("Finished Processing")
    # process_and_validate_images(["00750054400226L1.jpg"], "ea")
    # add_all_pixel_variations(urls=[
    #     "https://i5.walmartimages.ca/images/Large/885/578/6000206885578.jpg?odnHeight=290&odnWidth=290&odnBg=FFFFFF"])
    # size_dict = load_size_config()
    # extract_urls(upc="6000191268858", size_dict=size_dict, banner="ca", scrolling_required=True)

    size_dict = load_size_config()
    # extract_urls(upc="6000191268858", size_dict=size_dict, banner="ca", scrolling_required=True)
    res = extract_urls(upc="6000201577618", size_dict=size_dict, banner="ca", scrolling_required=True)
    # res = extract_urls(upc="00770201803789", size_dict=size_dict, banner="ea", scrolling_required=True)

    print(res[0]['original_urls'])