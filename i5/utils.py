from urllib.parse import urlencode, urlparse, urlunparse, parse_qs, parse_qsl
from settings import CONFIG_DIR, URI_EXTRA_PARAMS


def normalize_process_urls(urls, extra_params):
    _urls = add_query_param_to_url(urls, extra_params.get("add", dict()))
    _urls = remove_query_param_to_url(_urls, extra_params.get("rm", list()))
    return _urls


def remove_query_param_to_url(urls, query_params: list):
    """
    query_params=["odnBg"]
    """
    if len(query_params) == 0:
        return urls

    _urls = list()
    for url in urls:
        u = urlparse(url)
        query = parse_qs(u.query, keep_blank_values=True)
        for _query in query_params:
            query.pop(_query, None)
        u = u._replace(query=urlencode(query, True))
        _urls.append(urlunparse(u))
    return _urls


def add_query_param_to_url(urls, query_params: list):
    """
    query_params={"odnBg":"FFFFFF"}
    """
    if len(query_params) == 0:
        return urls
    _urls = list()
    for url in urls:
        url_parts = list(urlparse(url))
        query = dict(parse_qsl(url_parts[4]))
        query.update(query_params)
        url_parts[4] = urlencode(query)
        i5_url2 = urlunparse(url_parts)
        _urls.append(i5_url2)
    return _urls


def get_additional_params(configs, banner):
    if not configs:
        return URI_EXTRA_PARAMS.get(banner)

    if configs.get(banner, dict()):
        return configs.get(banner)
    else:
        return URI_EXTRA_PARAMS.get(banner)
