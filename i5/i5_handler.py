import logging
import time
import copy

import requests

from i5.i5_image_processor import process_and_validate, get_uri_excluding_query_params
from i5.i5_image_processor import get_urls_with_size, load_size_config, canada_urls
from urllib.parse import urlencode, urlparse, urlunparse, parse_qs, parse_qsl
from i5.cache_flush import flush
from i5.kafka_producer import PublishToKafka
from settings import CONFIG_DIR, URI_EXTRA_PARAMS
from i5.utils import normalize_process_urls, get_additional_params

logger = logging.getLogger(__name__)
DEFAULT_BROKER = [
    "kafka-420262885-1-1795129945.scus.kafka-v2-taas-shared4-prod.ms-df-messaging.prod-az-southcentralus-2.prod.us.walmart.net:9093",
    "kafka-420262885-2-1795129948.scus.kafka-v2-taas-shared4-prod.ms-df-messaging.prod-az-southcentralus-2.prod.us.walmart.net:9093",
    "kafka-420262885-3-1795129951.scus.kafka-v2-taas-shared4-prod.ms-df-messaging.prod-az-southcentralus-2.prod.us.walmart.net:9093",
    "kafka-420262885-4-1795129954.scus.kafka-v2-taas-shared4-prod.ms-df-messaging.prod-az-southcentralus-2.prod.us.walmart.net:9093",
    "kafka-420262885-5-1795129957.scus.kafka-v2-taas-shared4-prod.ms-df-messaging.prod-az-southcentralus-2.prod.us.walmart.net:9093",
    "kafka-420262885-6-1795129960.scus.kafka-v2-taas-shared4-prod.ms-df-messaging.prod-az-southcentralus-2.prod.us.walmart.net:9093",
    "kafka-420262885-7-1795129963.scus.kafka-v2-taas-shared4-prod.ms-df-messaging.prod-az-southcentralus-2.prod.us.walmart.net:9093",
    "kafka-420262885-8-1795129966.scus.kafka-v2-taas-shared4-prod.ms-df-messaging.prod-az-southcentralus-2.prod.us.walmart.net:9093",
    "kafka-420262885-9-1795129969.scus.kafka-v2-taas-shared4-prod.ms-df-messaging.prod-az-southcentralus-2.prod.us.walmart.net:9093",
    "kafka-420262885-10-1795129972.scus.kafka-v2-taas-shared4-prod.ms-df-messaging.prod-az-southcentralus-2.prod.us.walmart.net:9093",
    "kafka-420262885-11-1795129975.scus.kafka-v2-taas-shared4-prod.ms-df-messaging.prod-az-southcentralus-2.prod.us.walmart.net:9093",
    "kafka-420262885-12-1795129978.scus.kafka-v2-taas-shared4-prod.ms-df-messaging.prod-az-southcentralus-2.prod.us.walmart.net:9093",
    "kafka-420262885-13-1795129981.scus.kafka-v2-taas-shared4-prod.ms-df-messaging.prod-az-southcentralus-2.prod.us.walmart.net:9093",
    "kafka-420262885-14-1795129984.scus.kafka-v2-taas-shared4-prod.ms-df-messaging.prod-az-southcentralus-2.prod.us.walmart.net:9093",
    "kafka-420262885-15-1795129987.scus.kafka-v2-taas-shared4-prod.ms-df-messaging.prod-az-southcentralus-2.prod.us.walmart.net:9093"
]
# DEFAULT_TOPIC = "APM0014550-asset-optimization-pipeline-PROD-mx"
# "APM0014550-asset-optimization-pipeline-PROD-ca"
DEFAULT_TOPIC = "APM0014550-asset-optimization-pipeline-PROD-ca"
# DEFAULT_TOPIC = "APM0014550-asset-optimization-pipeline-PROD-mx"
CERT_DIR = CONFIG_DIR
DEFAULT_PASS = "Walmart@12345"
OPTIMIZER_URL = "http://app-886542063-1-914472606.stg.timescaledb.gieo-perf.prod-az-southcentralus-19.prod.us.walmart.net:8080/api/optimize/publish"

IMG_URLS = {
    "od": "https://i5.walmartimages.com.mx/gr/images/product-images/img_large",
    "ea": "https://i5.walmartimages.com.mx/gm/images/product-images/img_large",
    "sams": "https://i5.walmartimages.com.mx/samsmx/images/product-images/img_large",
    "ca": "https://i5.walmartimages.ca"
}
IMG_URLS_SECONDARY = {
    "ea": ["https://i5.walmartimages.com.mx/mg/gm/1p/images/product-images/img_large",
           "https://i5-mx.walmartimages.com/mg/gm/1p/images/product-images/img_large"]
}

IMG_URLS_NEW = {
    "od": "https://i5-mx.walmartimages.com/gr/images/product-images/img_large",
    "ea": "https://i5-mx.walmartimages.com/gm/images/product-images/img_large",
    "ca": "https://i5.walmartimages.ca",
    "sams": "https://i5-mx.walmartimages.com/samsmx/images/product-images/img_large"
}


def post_urls_kafka(urls_list, topic):
    producer = PublishToKafka(topic=topic, broker=DEFAULT_BROKER, cert_path=CERT_DIR,
                              password=DEFAULT_PASS, ssl_ca_file="CARoot.pem",
                              # ssl_cert_file="certificate.pem",
                              ssl_cert_file="RSAkey.pem",
                              ssl_key_file="RSAkey.pem")
    producer.publish(urls_list)


def post_urls_kafka_v2(urls_list, topic):
    market = "mx"
    if "APM0014550-asset-optimization-pipeline-PROD-ca" == topic:
        market = "ca"

    DEFAULT_SIZE = 300
    data_per_threads = [urls_list[i:i + DEFAULT_SIZE] for i in range(0, len(urls_list), DEFAULT_SIZE)]
    for index, _slice in enumerate(data_per_threads):
        logger.info("Calling Optimize api")
        response = requests.post(url=OPTIMIZER_URL, json={"urls": _slice, "banner": market}, verify=False)
        logger.info(response)


def process_upcs(upcs: list, banner=None, ignore_kafka_call=False, process_all_pixel_variations=False,
                 scrolling_required=True, sleep_interval=15, images=None, v2=True, default_url_size=50, config=None,
                 required_torbit_flush=False):
    logger.info(f"Processing {upcs}")
    i5_all_dimensions_urls, i5_original_urls, failed_fetch_upcs = process_and_validate(
        upcs, banner, process_all_pixel_variations=process_all_pixel_variations, scrolling_required=scrolling_required)

    optimized_urls = handle_kafka_optimize_queue(copy.deepcopy(i5_all_dimensions_urls), ignore_kafka_call, banner,
                                                 config)
    logger.info("==========================================================================")
    logger.info(f"Total num of urls flushing are {len(i5_all_dimensions_urls)} , number of upcs/imgs {upcs}")
    logger.info("==========================================================================")
    if images and banner != "ca":
        i5_all_dimensions_urls = filter_matched_images(images, i5_all_dimensions_urls)

    aka_status, torbit_status, aka_urls, torbit_urls = flush(i5_all_dimensions_urls, banner,
                                                             sleep_interval=sleep_interval, v2=v2,
                                                             default_url_size=default_url_size, config=config,
                                                             required_torbit_flush=required_torbit_flush)

    return {
        "optimized_urls": optimized_urls,
        "akamai_urls": aka_urls,
        "torbit_urls": torbit_urls,
        "original_urls": i5_original_urls
    }


def filter_matched_images(images, urls):
    filtered_urls = list()
    for url in urls:
        for imag in images:
            if imag in url:
                filtered_urls.append(url)
    return filtered_urls


def handle_kafka_optimize_queue(urls, ignore_kafka_call, banner, config):
    kafka_extra_params = get_additional_params(config, banner).get("optimize_queue")
    _urls = normalize_process_urls(urls, kafka_extra_params)
    if not ignore_kafka_call:
        if banner == "ca":
            topic = "APM0014550-asset-optimization-pipeline-PROD-ca"
        else:
            topic = "APM0014550-asset-optimization-pipeline-PROD-mx"
        # post_urls_kafka(_urls, topic=topic)
        post_urls_kafka_v2(_urls, topic=topic)
    return _urls


def get_upcs(images, banner, is_images):
    logger.info(f"Processing {images}")
    if banner == "ca" and is_images:
        upcs = list()
        # only , if user provides full url
        for url in images:
            upcs.append(get_uri_excluding_query_params(url))
    else:
        upcs = list(set(list(set(get_upcs_from_img_urls(images)))))

    return upcs


def process_mx_images(images, banner, v2=True, default_url_size=50, config=None, required_torbit_flush=False,
                      ignore_kafka_call=False, sleep_interval=10):
    origins = list()
    i5_all_dimensions_urls = list()
    original_urls = list()
    size_dict = load_size_config()
    primary_origin = IMG_URLS.get(banner)
    origins.append(primary_origin)
    secondary_origin = IMG_URLS_SECONDARY.get(banner)
    if secondary_origin:
        origins.extend(secondary_origin)
    
    new_origin = IMG_URLS_NEW.get(banner)
    origins.append(new_origin)

    accumulated_original_urls = list()

    for origin in origins:
        for img in images:
            original_urls, all_dimensions_urls = get_urls_with_size(upc=img, origin=origin, size_dict=size_dict,
                                                                    scrolling_required=False, banner="mx")
            i5_all_dimensions_urls.extend(all_dimensions_urls)
            accumulated_original_urls.extend(original_urls)

    optimized_urls = handle_kafka_optimize_queue(copy.deepcopy(i5_all_dimensions_urls), ignore_kafka_call, banner,
                                                 config)
    new_origin_urls = None
    if new_origin:
        new_origin_urls = [url for url in i5_all_dimensions_urls if url.startswith(new_origin)]
        i5_all_dimensions_urls = [url for url in i5_all_dimensions_urls if not url.startswith(new_origin)]

    aka_status, torbit_status, aka_urls, torbit_urls = flush(i5_all_dimensions_urls, banner,
                                                             sleep_interval=sleep_interval, v2=v2,
                                                             default_url_size=default_url_size, config=config,
                                                             required_torbit_flush=required_torbit_flush)

    # Flush URLs with the new origin separately
    if new_origin and new_origin_urls:
        aka_status_new, torbit_status_new, aka_urls_new, torbit_urls_new = flush(new_origin_urls, "us",
                                                                                 sleep_interval=sleep_interval, v2=v2,
                                                                                 default_url_size=default_url_size,
                                                                                 config=config,
                                                                                 required_torbit_flush=required_torbit_flush)

        aka_status = aka_status and aka_status_new
        torbit_status = torbit_status and torbit_status_new
        aka_urls.extend(aka_urls_new)
        torbit_urls.extend(torbit_urls_new)

    return {
        "aka_status": aka_status,
        "torbit_status": torbit_status,
        "optimized_urls": optimized_urls,
        "akamai_urls": aka_urls,
        "torbit_urls": torbit_urls,
        "original_urls": accumulated_original_urls
    }


def process_urls(images: list, banner, process_all_pixel_variations=False, scrolling_required=True, sleep_interval=15,
                 is_images=False, v2=True, default_url_size=50, config=None, required_torbit_flush=False):
    logger.info(f"Processing {images}")

    images = list(set(images))
    upcs = get_upcs(images, banner, is_images)

    if not is_images:
        images = None
    data = process_upcs(upcs, banner, ignore_kafka_call=False,
                        process_all_pixel_variations=process_all_pixel_variations,
                        scrolling_required=scrolling_required, sleep_interval=sleep_interval, images=images, v2=v2,

                        default_url_size=default_url_size, config=config,
                        required_torbit_flush=required_torbit_flush)

    return data


def get_upcs_from_img_urls(images):
    return [img[:14] for img in images]


def _pull_all_canada_images(upc):
    pass


def pull_all_canada_images(upcs):
    url = "http://ca-catalog-services-prod.walmart.com/api"
    headers = {"Content-Type": "application/json"}
    body = {
        "query": "query getSKU($ids: [String!]!) { skus(ids: $ids) {id name{en_CA fr_CA} description{en_CA fr_CA} longDescription{en_CA fr_CA} type category{id} product{id} visibility onHandQuantity startDate consumerItemNumber maxOrderQuantity gtins variants{name{en_CA fr_CA} value{en_CA fr_CA}} badges{id name backgroundColor foregroundColor priority displayName{en_CA fr_CA}} images{thumbnail{url alt{en_CA fr_CA}} small{url alt{en_CA fr_CA}} large{url alt{en_CA fr_CA}} enlarged{url alt{en_CA fr_CA}} isPrimaryImage isNutritionalLabel} shipping{type height length width lwhUom weight weightUom options isShipAlone isShipInFactoryCarton isShipViaAir isOrmd isHazmat numberOfBoxes} pickup{cpc byBox siteToStore} return{isReturnable returnPeriod isCallTag} preOrder{isPreOrderable preOrderDate} grocery{isPerishable isSoldByWeight itemPickingZone maxWeight minWeight isBulkWeight pickerDescription}}}",
        "variables": {
            "ids": upcs
        },
        "operationName": "getSKU"
    }
    result_urls = list()
    response = requests.post(url=url, json=body, headers=headers)
    if response.ok:
        data = response.json()
        for sku in data.get("data", dict()).get("skus", dict()):
            for imges in sku.get("images"):
                for imags_category, category_data in imges.items():
                    if isinstance(category_data, dict):
                        if category_data.get("url"):
                            result_urls.append(category_data.get("url"))

    scrapping_urls = []
    for upc in upcs:
        scrapping_urls.extend(canada_urls(upc))
    result_urls.extend(scrapping_urls)
    return list(set(result_urls))


if __name__ == '__main__':
    from logging import handlers
    import os

    log_level = logging.INFO
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    log_format = '%(asctime)s %(filename)18s %(funcName)33s %(lineno)3d %(levelname)10s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)

    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(log_level)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)

    file_handler = handlers.RotatingFileHandler(
        filename=os.path.join(os.path.basename(__file__).replace('py', 'log')),
        maxBytes=1048576,
        backupCount=5)
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(fmt=formatter)
    logger.addHandler(file_handler)
    # Canada
    imgs = ["00750100312323",
            "00750100312343",
            "00750100312424",
            "00750100312432",
            "00750100312434",
            "00750955287821",
            "00750955287822",
            "00750955287820",
            "00750955287825",
            "00750955287827",
            "00750955287819",
            "00360054256594",
            "00750955287823",
            "00789970619201",
            "00750955287824",
            "00004155407184",
            "00690239582106",
            "00750955275023",
            "00360053160974",
            "00750955287819",
            "00750955287498",
            "00360052402212",
            "00002529300292",
            "00002529300365"]
    urls = ["https://i5.walmartimages.ca/images/Thumbnails/577/490/6000206577490.jpg"]
    # process_urls(images=urls, banner="ca", process_all_pixel_variations=True, scrolling_required=False,
    #              sleep_interval=15,
    #              is_images=True, v2=True, default_url_size=50, config=dict())
    # data = process_urls(images=urls, banner="od", is_images=True)
    # # data = process_urls(imgs, banner="od", scrolling_required=True, process_all_pixel_variations=True)
    # print(data)
    # imgs = ["00693598571836L1.jpg","00695385690508l.jpg"]
    # process_urls(imgs, banner="od", scrolling_required=True, process_all_pixel_variations=True)
    # urls = [
    #     'https://i5.walmartimages.com.mx/gr/images/product-images/img_large/00693598571836L1.jpg?odnBg=FFFFFF&odnUpScale=1&odnHeight=100&odnWidth=100',
    #     'https://i5.walmartimages.com.mx/gr/images/product-images/img_large/00693598571836L1.jpg?odnBg=FFFFFF&odnUpScale=1&odnHeight=88&odnWidth=88'
    # ]
    # post_urls_kafka(urls)
    #     urls = ["https://i5.walmartimages.ca/images/Large/577/464/6000206577464.jpg?odnHeight=580&odnWidth=580&odnBg=FFFFFF",
    # "https://i5.walmartimages.ca/images/Large/577/461/6000206577461.jpg?odnHeight=580&odnWidth=580&odnBg=FFFFFF",
    # "https://i5.walmartimages.ca/images/Large/339/115/6000204339115.jpg?odnHeight=580&odnWidth=580&odnBg=FFFFFF",
    #
    # "https://i5.walmartimages.ca/images/Thumbnails/113/021/6000207113021.jpg",
    # "https://i5.walmartimages.ca/images/Thumbnails/113/021/6000207113021.jpg?odnHeight=180&odnWidth=180&odnBg=FFFFFF",
    # "https://i5.walmartimages.ca/images/Thumbnails/113/025/6000207113025.jpg",
    # "https://i5.walmartimages.ca/images/Thumbnails/113/025/6000207113025.jpg?odnHeight=180&odnWidth=180&odnBg=FFFFFF",
    # "https://i5.walmartimages.ca/images/Large/113/025/6000207113025.jpg?odnHeight=290&odnWidth=290",
    # "https://i5.walmartimages.ca/images/Large/113/025/6000207113025.jpg?odnHeight=290&odnWidth=290&odnBg=FFFFFF",
    # "https://i5.walmartimages.ca/images/Enlarge/073/659/6000207073659.jpg",
    # "https://i5.walmartimages.ca/images/Enlarge/073/659/6000207073659.jpg?odnHeight=80&odnWidth=80&odnBg=FFFFFF",
    # "https://i5.walmartimages.ca/images/Enlarge/047/944/6000207047944.jpg?odnHeight=612&odnWidth=612&odnBg=FFFFFF",
    # "https://i5.walmartimages.ca/images/Enlarge/047/944/6000207047944.jpg?odnHeight=612&odnWidth=612&odnBg=FFFFFF"]
    #     handle_urls(urls,banner="ca")
    # pull_all_canada_images(["6000206120193"])
    ulrs = [
        # 'https://i5.walmartimages.com.mx/gr/images/product-images/img_large/00750188264325L.jpg?odnHeight=100&odnWidth=100&odnBg=FFFFFF&odnUpScale=1',
        # 'https://i5.walmartimages.com.mx/gr/images/product-images/img_large/00750188264325L.jpg?odnHeight=104&odnWidth=104&odnBg=FFFFFF&odnUpScale=1',
        # 'https://i5.walmartimages.com.mx/gr/images/product-images/img_large/00750188264325L.jpg?odnHeight=120&odnWidth=120&odnBg=FFFFFF&odnUpScale=1',
        # 'https://i5.walmartimages.com.mx/gr/images/product-images/img_large/00750188264325L.jpg?odnHeight=128&odnWidth=128&odnBg=FFFFFF&odnUpScale=1',
        'https://i5.walmartimages.com.mx/gr/images/product-images/img_large/00750188264325L.jpg?odnHeight=132&odnWidth=132&odnBg=FFFFFF&odnUpScale=1'
    ]
    post_urls_kafka(ulrs, "APM0014550-asset-optimization-pipeline-PROD-mx")
