import json
from kafka import KafkaProducer
import logging

logger = logging.getLogger(__name__)

DEFAULT_SIZE = 100


class PublishToKafka:
    def __init__(self, topic, broker, cert_path, password, ssl_ca_file, ssl_cert_file, ssl_key_file):
        self.password = password
        self.cert_path = cert_path
        self.broker = broker
        self.topic = topic
        self.ssl_ca_file = ssl_ca_file
        self.ssl_cert_file = ssl_cert_file
        self.ssl_key_file = ssl_key_file

    def publish(self, urls):
        if urls is None or urls is None:
            return

        data_per_threads = [urls[i:i + DEFAULT_SIZE] for i in range(0, len(urls), DEFAULT_SIZE)]
        logger.info(f"URLs splitted into {data_per_threads} chunks ")
        try:
            logger.info("Creating Kafka producer")
            logger.info(f"CERT_DIR {self.cert_path}")
            kafka_producer = KafkaProducer(bootstrap_servers=self.broker,
                                           security_protocol="SSL",
                                           # security_protocol="SSLv3_method",
                                           ssl_check_hostname=False,
                                           ssl_cafile="{}/{}".format(self.cert_path, self.ssl_ca_file),
                                           ssl_certfile="{}/{}".format(self.cert_path, self.ssl_cert_file),
                                           ssl_keyfile="{}/{}".format(self.cert_path, self.ssl_key_file),
                                           ssl_password=self.password)
            logger.info("Total messages are sliced into {}".format(len(data_per_threads)))
            for index, _slice in enumerate(data_per_threads):
                logger.info("Processing slice of message {}/{}".format(index, len(data_per_threads)))
                self.publish_message(kafka_producer, _slice)
            kafka_producer.close()
            logger.info("All messages are processed")
            logger.info("Closed Kafka producer")
        except Exception as ex:
            logger.exception(str(ex))

    def publish_message(self, kafka_producer, urls):
        data = {'urls': urls, "params": {
            "update": "1"
        }}
        logger.info(f"Posting urls to i5 kafka queue {urls}")
        json_data = json.dumps(data)
        message = bytes(json_data, encoding='utf-8')
        try:
            record_metadata = kafka_producer.send(self.topic, message)
            queue_count = kafka_producer.flush()
            logger.info(f"Finished Posting urls to i5 kafka queue")
        except Exception as ex:
            logger.exception(str(ex))


if __name__ == '__main__':
    # urls = list()
    # urls.append("https://test.walmart.mx/00750630630721L.jpg")
    # urls.append("https://test.walmart.mx/00750630630731L.jpg")
    # urls.append("https://test.walmart.mx/00750630630741L.jpg")
    urls = []
    producer = PublishToKafka()
    producer.publish(_url_list=urls)
