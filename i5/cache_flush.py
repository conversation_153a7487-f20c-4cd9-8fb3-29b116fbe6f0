#!/usr/bin/python3
import json
import logging
import time, re, math

import requests
import urllib3
from slack_bot.slack import send_message
from i5.utils import normalize_process_urls, get_additional_params

logger = logging.getLogger(__name__)
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

TORBIT_HEADER = {'Content-type': 'application/json', 'Accept': 'application/json'}
TORBIT_URL = "https://api.nucleus-torbit.walmart.com/deletecache/url"

TORBIT_URL_V2 = "https://api.nucleus-torbit.walmart.com/api/deletecache/url"

AKAMAI_HEADER = {'Content-type': 'application/json', 'Accept': 'application/json', 'tenant': 'mx'}
AKAMAI_URL = "https://api.nucleus-akamai.walmart.com/api/deletecache/url"
AKAMAI_CP_FLUSH_URI = "https://api.nucleus-akamai.walmart.com/api/invalidatecache/cpcode"

rex_img_extractor = ".*img_large\/(.*\.jpg)\?.*"
rex_compiler = re.compile(rex_img_extractor)

proxies = {
    "http": "http://sysproxy.wal-mart.com:8080",
    "https": "http://sysproxy.wal-mart.com:8080",
}


def _flush_torbit_cache(urls_to_flush):
    data = torbit_purge_cache_body(urls_to_flush)
    try:
        response = requests.post(TORBIT_URL, headers=TORBIT_HEADER, json=data, verify=False)
        if response.status_code == 200 or response.status_code == 201:
            return True
        logger.error("Torbit cache flush api is failing , throwing {}".format(response.status_code))
        return False
    except Exception as e:
        logger.exception(e)


def _flush_torbit_cache_v2(urls_to_flush):
    data = torbit_purge_cache_body(urls_to_flush)
    try:
        TORBIT_HEADER.update({"svc": "torbit"})
        response = requests.post(TORBIT_URL_V2, headers=TORBIT_HEADER, json=data, verify=False)
        # response1 = requests.post(TORBIT_URL_V2, headers=TORBIT_HEADER, data=json.dumps(data), verify=False)
        if response.status_code == 200 or response.status_code == 201:
            return True
        logger.error("Torbit cache flush api is failing , throwing {} and response is {}".format(response.status_code,
                                                                                                 response.text))
        return False
    except Exception as e:
        logger.exception(e)


def torbit_purge_cache_body(urls_to_flush):
    data = {
        "purge_request": {
            "cdns": {
                'torbit': True,
                'akamai': False,
                'limelight': False,
                'limelight_metadata': '',
                'cloudflare': False,
                'torbit_distcache': False,
                'zycada': False,
                'fastly': False
            },
            'methods': {
                'http': False,
                'https': True
            },
            'tags': None,
            'fa_surrogate_change': False
        }
    }
    # TODO: if urls is string, then create list and add the item
    data["purge_request"]["urls"] = urls_to_flush
    return data


def _flush_akamai_cache(urls_to_flush, banner):
    # TODO: if urls is string, then create list and add the item
    payload = build_akamia_payload(urls_to_flush)
    try:
        if banner == "ca":
            AKAMAI_HEADER.update({'tenant': 'ca'})
        elif banner == "us":
            AKAMAI_HEADER.update({'tenant': 'us'})
        else:
            AKAMAI_HEADER.update({'tenant': 'mx'})
        response = requests.post(AKAMAI_URL, headers=AKAMAI_HEADER, json=payload, verify=False, proxies=proxies,
                                 timeout=25)
        if response.status_code == 200 or response.status_code == 201:
            return True
        logger.error(f"Error occurred while processing Akamai cache flush. Check response {response}")
        return False

    except Exception as e:
        logger.exception(e)
        return False


def _flush_akamai_cp_flush(cp_codes, banner):
    # TODO: if urls is string, then create list and add the item
    payload = build_akamia_payload(cp_codes)
    try:
        if banner == "ca":
            AKAMAI_HEADER.update({'tenant': 'ca'})
        response = requests.post(AKAMAI_CP_FLUSH_URI, headers=AKAMAI_HEADER, json=payload, verify=False)
        if response.status_code == 200 or response.status_code == 201:
            return True
        return False

    except Exception as e:
        logger.exception(e)
        return False


def flush_akamai_cache(urls_to_flush, banner, retries=10):
    aka_status = False
    for retry in range(retries):
        logger.info(f"Processing akamia cache flush request for iter {retry}")
        aka_status = _flush_akamai_cache(urls_to_flush, banner)
        if aka_status:
            logger.info(f"Akamai cache flush Success for iter {retry}")
            return aka_status
        continue
    return aka_status


def flush_akamai_cp_flush(cp_codes, banner, retries=5):
    aka_status = False
    for retry in range(retries):
        logger.info(f"Processing akamia cache flush request for iter {retry}")
        aka_status = _flush_akamai_cp_flush(cp_codes=cp_codes, banner=banner)
        if aka_status:
            logger.info(f"akamia cache flush Success for iter {retry}")
            return aka_status
        continue
    return aka_status


def flush_torbit_cache(urls_to_flush, retries=5, v2=True):
    torbit_status = False
    for retry in range(retries):
        logger.info(f"Processing Trobit cache flush request for iter {retry}")
        if v2:
            torbit_status = _flush_torbit_cache_v2(urls_to_flush)
        else:
            torbit_status = _flush_torbit_cache(urls_to_flush)
        if torbit_status:
            logger.info(f"Trobit cache flush Success for iter {retry}")
            return torbit_status
        send_message("juno_logs", f"User called ,Failed. Retry count {retry}")
        continue
    return torbit_status


def build_akamia_payload(urls):
    payload_akamai = dict()
    payload_akamai["objects"] = urls
    return payload_akamai


def flush(urls, banner, default_url_size=50, retries=5, sleep_interval=5, v2=True, config=None,
          required_torbit_flush=False):
    aka_status = list()
    torbit_status = list()
    aka_urls = list()
    torbit_urls = list()
    if not urls:
        return aka_status, torbit_status, aka_urls, torbit_urls
    logger.info("Total number of ulrs are flushing are {}".format(len(urls)))
    data_per_threads = [urls[i:i + default_url_size] for i in range(0, len(urls), default_url_size)]
    logger.info("Total number of urls are sliced into".format(len(data_per_threads)))

    _torbit_params = get_additional_params(config, banner).get("torbit")
    akamai_params = get_additional_params(config, banner).get("akamai")

    if required_torbit_flush:
        for index, slice_of_data in enumerate(data_per_threads):
            torbit_flush_url = normalize_process_urls(slice_of_data, _torbit_params)
            logger.info("Processing tobit/akamai flush slice {}/{}".format(index, len(data_per_threads)))
            imgs = extract_img_from_uri(slice_of_data)
            _torbit_status = flush_torbit_cache(torbit_flush_url, retries, v2=v2)
            torbit_urls.extend(torbit_flush_url)

            logger.info(f"`Torbit cache flush api is {_torbit_status}`")

            torbit_status.append(_torbit_status)
            logger.info("Torbit cache flush status is {}".format(_torbit_status))

            time.sleep(sleep_interval)

    status_akamai_torbit = is_torbit_akamai_extra_params_different(_torbit_params, akamai_params)
    if not status_akamai_torbit:
        aka_flush_url = normalize_process_urls(urls, akamai_params)
    else:
        aka_flush_url = normalize_process_urls(urls, _torbit_params)
    # form_torbit_slack_msg(urls, slice_of_data, index, data_per_threads, imgs, _torbit_status, banner)
    _aka_status = validate_size_and_flush_cache(aka_flush_url, banner)
    aka_status.extend(_aka_status)
    aka_urls.extend(aka_flush_url)
    return aka_status, torbit_status, aka_urls, torbit_urls


def validate_size_and_flush_cache(aka_flush_url, banner):
    supported_cache_flush_payload_size = 45000
    aka_status = list()
    supported_urls_size_per_request = 0
    list_chunked = list()
    required_chunk_split = False
    one_url_size = len(aka_flush_url[0])
    urls_length = len(aka_flush_url) * one_url_size
    if urls_length > supported_cache_flush_payload_size:
        required_chunk_split = True
    if required_chunk_split:
        supported_urls_size_per_request = math.floor(supported_cache_flush_payload_size / one_url_size)
    if supported_urls_size_per_request > 0 and required_chunk_split:
        list_chunked = [aka_flush_url[i:i + supported_urls_size_per_request] for i in
                        range(0, len(aka_flush_url), supported_urls_size_per_request)]
    if len(list_chunked) > 0:
        for _slice in list_chunked:
            _aka_status = flush_akamai_cache(_slice, banner, retries=4)
            logger.info("Akamai cache flush status is {}".format(_aka_status))
            aka_status.append(_aka_status)
    else:
        _aka_status = flush_akamai_cache(aka_flush_url, banner, retries=4)
        logger.info("Akamai cache flush status is {}".format(_aka_status))
        aka_status.append(_aka_status)
    return aka_status


def is_torbit_akamai_extra_params_different(torbit_params, akamai_params):
    if torbit_params == akamai_params:
        return True
    return False


def extract_img_from_uri(urls):
    imgs = set()
    for url in urls:
        img = None
        try:
            img = rex_compiler.findall(url)[0]
        except Exception as e:
            logger.exception(e)
            logger.info("Regex failed, ignoring for all other urls")
            break
        if img:
            imgs.add(img)
    return imgs


def form_torbit_slack_msg(urls, slice_of_data, index, data_per_threads, imgs, torbit_status, banner):
    torbit_status = "Success" if torbit_status else "Failed"
    msg = f"Juno calling ,torbit cache flush call ({banner}). \n Total URL to flush {len(urls)}. Processing flush group of " \
          f"({index} of {len(data_per_threads)}). Group size {len(slice_of_data)}.\n Images flushed are " \
          f"{' , '.join(imgs)}.\n Status is {torbit_status}"
    send_message("juno_logs", f"```{msg}```")


if __name__ == '__main__':
    urls = [
        'https://i5.walmartimages.com.mx/gr/images/product-images/img_large/00694209790473L.jpg?odnHeight=100&odnWidth=100&odnBg=FFFFFF']
    flush([
        "https://i5.walmartimages.com.mx/gr/images/product-images/img_large/00750630621576L.jpg?odnHeight=2000&odnWidth=2000&odnBg=FFFFFF"],
        banner="od", v2=True)
    # data = {'purge_request': {'cdns': {'torbit': True, 'akamai': False, 'limelight': False, 'limelight_metadata': '',
    #                                    'cloudflare': False, 'torbit_distcache': False,
    #                                    'zycada': False, 'fastly': False},
    #                           'methods': {'http': False, 'https': True}, 'tags': None, 'fa_surrogate_change': False,
    #                           'urls': ['https://i5.walmartimages.com.mx/gr/images/product-images/img_large/00694209790473L.jpg?odnHeight=100&odnWidth=100&odnBg=FFFFFF']}}
    # TORBIT_HEADER.update({"svc": "torbit"})
    # response = requests.post(TORBIT_URL_V2, headers=TORBIT_HEADER, json=data, verify=False)
    # print()
    # flush(
    #     ["https://i5.walmartimages.ca/images/Large/885/578/6000206885578.jpg?odnHeight=290&odnWidth=290&odnBg=FFFFFF"])
