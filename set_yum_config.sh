# Set Global Vars
PROXY="$1"
PATH="${PATH}:/bin:/sbin:/usr/bin:/usr/sbin:/usr/local/bin:/opt/puppetlabs/bin"

function add_yum_repos {
  HTTPPROXY="$1"
    # Add Internal WMT Yum Repos
  find /etc/yum.repos.d -name "CentOS*" -delete
  find /etc/yum.repos.d -name "epel*" -delete
  find /etc/yum.repos.d -name "Walmart*" -delete

cat <<'EOF' > /etc/yum.repos.d/CentOS-Base.repo
# CentOS-Base.repo
#

[base]
name=CentOS-$releasever - Base
baseurl=https://repository.cache.cloud.wal-mart.com/content/repositories/centos-$releasever/os/$basearch/
gpgcheck=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-$releasever
proxy=_none_
EOF

cat <<'EOF' > /etc/yum.repos.d/CentOS-updates.repo
# CentOS-updtes.repo
#

[updates]
name=CentOS-$releasever - Updates
baseurl=https://repository.cache.cloud.wal-mart.com/content/repositories/centos-$releasever/updates/$basearch/
gpgcheck=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-$releasever
proxy=_none_
EOF

cat <<'EOF' > /etc/yum.repos.d/CentOS-extras.repo
# CentOS-Base.repo
#

#additional packages that may be useful
[extras]
name=CentOS-$releasever - Extras
baseurl=https://repository.cache.cloud.wal-mart.com/content/repositories/centos-$releasever/extras/$basearch/
gpgcheck=1
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-$releasever
proxy=_none_
EOF

cat <<'EOF' > /etc/yum.repos.d/CentOS-plus.repo
# CentOS-Base.repo
#

#additional packages that extend functionality of existing packages
[centosplus]
name=CentOS-$releasever -     aq
baseurl=https://repository.cache.cloud.wal-mart.com/content/repositories/centos-$releasever/centosplus/$basearch/
gpgcheck=1
enabled=0
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-CentOS-$releasever
proxy=_none_
EOF

cat <<'EOF' > /etc/yum.repos.d/epel.repo
# EPEL.repo
#

[epel]
name=Extra Packages for Enterprise Linux $releasever - $basearch
baseurl=https://repository.cache.cloud.wal-mart.com/content/repositories/fedoraproject-epel-$releasever/$basearch/
enabled=1
gpgcheck=0
gpgkey=file:///etc/pki/rpm-gpg/RPM-GPG-KEY-EPEL-$releasever
proxy=_none_
EOF


  # Clean and remake Yum cache
  yum clean all
 # rm -rf /var/cache/yum
  yum -y makecache
}

function set_proxy {
  package_provider="$1"
  provider_conf_file="$2"
  echo "proxy=${PROXY}" >> "$provider_conf_file"
}

 # Main Program
function main {
  # Set Package Provider Proxy
  package_provider="rpm"
  provider_conf_file="/etc/yum.conf"
 # set_proxy "$package_provider" "$provider_conf_file"

  add_yum_repos "$PROXY"
}

main
