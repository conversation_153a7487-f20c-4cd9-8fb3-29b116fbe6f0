from slack_sdk.rtm_v2 import RTMClient
from slack_bot.slack import WMTSlack
from slack_bot.pattern_analyser import Pattern<PERSON>nalyzer
import logging

NONAME = "*****************************************************"
# NONAME ="******************************************************"

wm_slack = WMTSlack(token=NONAME)
rtm = RTMClient(token=NONAME, proxy="http://sysproxy.wal-mart.com:8080")


@rtm.on("message")
def handle(client: RTMClient, event: dict):
    pa = PatternAnalyzer(event)
    try:
        # True,Git,access
        (status, error_message), action_type, action = pa.action_executor()
        _status = "success" if status else "failed"
        logging.info(f"status:{status}, action_type:{action_type}, action:{action}")
        if status:
            client.web_client.chat_postMessage(channel=pa.channel_id,
                                               text=f"Hi <@{pa.user}>! {action_type} {action} {_status}",
                                               thread_ts=pa.thread_ts)
        else:
            client.web_client.chat_postMessage(channel=pa.channel_id,
                                               text=f"Hi <@{pa.user}>! {action_type} {action} {_status}, "
                                                    f"reason {error_message}",
                                               thread_ts=pa.thread_ts)
    except Exception as e:
        pass
        #client.web_client.chat_postMessage(channel=pa.channel_id,
        #                                   text=f"Hi <@{pa.user}>! exception occurred {e}",
        #                                   thread_ts=pa.thread_ts)

    # if 'git' in event['text']:
    #     channel_id = event['channel']
    #     thread_ts = event['ts']
    #     user = event['user']  # This is not username but user ID (the format is either U*** or W***)
    #     wm_slack.send_message(channel=channel_id, text=f"Hi <@{user}>! From Mag", thread_ts=thread_ts)


def main():
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    log_format = '%(asctime)s %(filename)25s %(lineno)3d %(levelname)10s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    formatter = logging.Formatter(fmt=log_format, datefmt=date_format)
    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(logging.INFO)
    stream_handler.setFormatter(fmt=formatter)
    logger.addHandler(stream_handler)
    rtm.start()


if __name__ == "__main__":
    main()