from git_service.git import GitApi
from libs.oneOps import OneOps
import logging

logger = logging.getLogger(__name__)


class GitExecutor:
    def __init__(self, text):
        self.text = text

    def __call__(self, **kwargs):
        try:
            git = GitApi()
            user, org, repo = self.metadata_processor()
            if user and org and repo:
                return git.provide_access(user, org, repo)
            return False, "Not provided command properly, Example \n" \
                          "`@Friday git access user_id:vn55oi3 git_url:https://gecgithub01.walmart.com/intl-ecomm-svcs/luke`"

        except Exception as e:
            logger.exception(e)
            return False

    def metadata_processor(self):
        # "git access user_id:m0c00jt git_url:https://gecgithub01.walmart.com/intl-ecomm-svcs/luke"
        tokens = self.text.split("access")
        if len(tokens) >= 2:
            tokens = tokens[1].split(" ")
            user_string = None
            git_url = None
            for token in tokens:
                if "user_id" in token:
                    user_string = token
                elif "git_url" in token:
                    git_url = token
            if not user_string or not git_url:
                return None, None, None
            user = user_string.split("user_id:")[-1]
            git_url = git_url.split("git_url:")[-1]
            url = git_url.replace("<", '').replace(">", '')
            status, org, repo = GitExecutor.get_org_and_repo_html_url_from_git_ssh_url(url)
            if status:
                return user, org, repo
        return None, None, None

    @staticmethod
    def get_org_and_repo_html_url_from_git_ssh_url(git_url):
        try:
            repo = git_url.replace("https://", '')
            tokens = repo.split("/")
            if len(tokens) == 3:
                return True, tokens[1], tokens[2]
            return False, 0, 0
        except Exception as e:
            logger.exception(e)
            return False, 0, 0


class OneopsExecutor:
    def __init__(self, text):
        self.text = text

    def __call__(self, **kwargs):
        try:
            user, market = self.metadata_processor()
            if market == "ca":
                market = "walmartca"
            else:
                market = "mexicoecomm"
            one = OneOps(org=market.strip())
            return one.give_access(user_id=user)
        except Exception as e:
            logger.exception(e)
            return False, e

    def metadata_processor(self):
        # "oneops access user_id:m0c00jt market:ca"
        tokens = self.text.split("access")
        if len(tokens) >= 2:
            tokens = tokens[1].split(" ")
            user_string = None
            market = None
            for token in tokens:
                if "user_id" in token:
                    user_string = token
                elif "market" in token:
                    market = token
            if not user_string or not market:
                return None, None
            user = user_string.split("user_id:")[-1]
            market = market.split("market:")[-1]
            market = market.replace(' ', '')
            return user, market

        return None, None


class SreExecutor:
    def __init__(self, text):
        self.text = text

    def __call__(self, **kwargs):
        pass

    def metadata_processor(self):
        # "oneops access user_id:m0c00jt market:ca"
        tokens = self.text.split("access")
        if len(tokens) >= 2:
            tokens = tokens[1].split(" ")
            user_string = None
            market = None
            for token in tokens:
                if "user_id" in token:
                    user_string = token
                elif "market" in token:
                    market = token
            if not user_string or not market:
                return None, None
            user = user_string.split("user_id:")[-1]
            market = market.split("market:")[-1]
            market = market.replace(' ', '')
            return user, market

        return None, None


if __name__ == "__main__":
    pass
    # give_access_to_oneops("oneops access user_id:m0c00jt234 market:ca")
    # get_org_and_repo_html_url_from_git_ssh_url("https://gecgithub01.walmart.com/intl-ecomm-svcs/luke")
    # ======================================================================================================
    #              SRE WCNP Tasks
    # ======================================================================================================
    # 1. sre task wcnp action:dashboards match item service
    # 2. sre task wcnp action:dashboards namespace:item-service
    # 3. sre task wcnp action:dashboards namespace:item-service app_id:item-service
    # 4. sre task wcnp action:health <check with last week and previous day> namespace:item-service app_id:item-service
    # 5. sre task wcnp action:sanity <qa> namespace:item-service app_id:item-service
    # 6. sre task wcnp action:infra <gives, num pods, clouds, min,max cpu; min,max memory>
    # namespace:item-service app_id:item-service
    # 7. sre task wcnp action:lb <lb health, thoughput distibution> namespace:item-service app_id:item-service
    # 8. sre task wcnp action:app <xmatters, slack and team dl> namespace:item-service app_id:item-service
    # 9. sre task wcnp action:slo <alert breaches last 3 hours> namespace:item-service app_id:item-service
    # 10. sre task wcnp action:perf <runs performance> namespace:item-service app_id:item-service
    # 11. sre task wcnp action:n2xx <Analyzes Splunk logs for non 2XX> namespace:item-service app_id:item-service
    # 12. sre task wcnp action:4xx <Analyzes Splunk logs for 4xx> namespace:item-service app_id:item-service
    # 13. sre task wcnp action:5xx <Analyzes Splunk logs for 5xx> namespace:item-service app_id:item-service
    # 14. sre task wcnp action:bot <gives true client ips with occurrences> namespace:item-service app_id:item-service
    # 15. sre task wcnp action:alert <given alert id it analysis> namespace:item-service app_id:item-service
    # 16. sre task wcnp action:alerts <gets all alerts> namespace:item-service app_id:item-service
    # 17. sre task wcnp action:create_alert <creates alerts >
    # 18. sre task wcnp action:create_choas <creates choas experiments >
    # 19. sre task wcnp action:canary <currently how many canary analysis are running >
    # ======================================================================================================
    #              SRE onCall Tasks
    # ======================================================================================================
    # 19. sre task oncall action:page team:team_name or with match <lists only> for page should provide exact name
    # 19. sre task oncall action:slo  get all SLA breaches
    # ======================================================================================================
    #              SRE DevOps Tasks
    # ======================================================================================================
    # 19. sre task devops action:is_canary_imp <is canary iplimented >
    # 19. sre task devops action:experiments   <get all canary exp>
    # 19. sre task devops action:activeactive <get all active active>
    # 19. sre task devops action:activepassive <get all active active>
    # 19. sre task devops action:check_alerts <check wether alerts configures or not>
    # 19. sre task devops action:horizontal <is horizpntal scaling is implimented>