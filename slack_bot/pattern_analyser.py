import logging
from slack_bot.executors import GitExecutor, OneopsExecutor

logger = logging.getLogger(__name__)


class PatternAnalyzerException(Exception):
    """Base class for other exceptions"""
    pass


class PatternAnalyzer(object):
    def __init__(self, event, **kwargs):
        try:
            if "user" in event and "text" in event:
                self.event = event
                self.channel_id = self.event['channel']
                self.thread_ts = self.event['ts']
                self.user = self.event['user']
                self.text = self.event['text']
            else:
                logger.info("User not invoked the message, ignoring")
        except Exception as e:
            logger.info("User not invoked the message, ignoring")
            logger.exception(e)

    def generic(self):
        if 'Hello' in self.event['text']:
            channel_id = self.event['channel']
            thread_ts = self.event['ts']
            user = self.event['user']  # This is not username but user ID (the format is either U*** or W***)
            # wm_slack.send_message(channel=channel_id, text=f"Hi <@{user}>! From Mag", thread_ts=thread_ts)

    def action_executor(self):
        try:
            if not hasattr(self, 'text'):
                logger.info("text attribute does not found")
                return
            if "user_id" in self.text and "access" in self.text:
                if "git" in self.text and "git_url" in self.text:
                    logger.info("Executing Jira Task")
                    git = GitExecutor(self.text)
                    return git(), "git", "access"
                elif "oneops" in self.text and "market" in self.text:
                    logger.info("Executing OneOps Task")
                    oneops = OneopsExecutor(self.text)
                    return oneops(), "oneOps", "access"
                elif "sre" in self.text:
                    logger.info("Executing Ops Task")
            else:
                logger.info("Pattern not matching, ignoring")
        except Exception as e:
            logger.exception("Exception occurred while processing",e)

