import logging, time
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError

logger = logging.getLogger(__name__)
NONAME = "*****************************************************"
USERID = "m0c00jt"


class SlackException(Exception):
    """Base class for other exceptions"""
    pass


class WMTSlack(object):
    def __init__(self, token=None, **kwargs):
        # super(WMTSlack, self).__init__(token,**kwargs)
        if not token:
            token = NONAME

        self.client = WebClient(token=token, proxy="http://sysproxy.wal-mart.com:8080")

    def send_message(self, channel, text=None, blocks=None, user=None, thread_ts=None):
        response = None
        if not text and not blocks:
            raise SlackException("User not provide either text or blocks values")

        message = {"channel": channel}

        if blocks:
            message.update({"blocks": blocks})
        else:
            message.update({"text": text})

        if user:
            message.update({"user": user})
        if thread_ts:
            message.update({"thread_ts": thread_ts})
        try:
            if user:
                response = self.client.chat_postEphemeral(**message)
            else:
                response = self.client.chat_postMessage(**message)
        except SlackApiError as e:
            logger.exception(e.response["error"])
            if e.response.status_code == 429:
                # The `Retry-After` header will tell you how long to wait before retrying
                delay = int(e.response.headers['Retry-After'])
                logger.warning(f"Rate limited. Retrying in {delay} seconds")
                time.sleep(delay)
                response = self.send_message(channel, text=text, blocks=blocks, user=user, thread_ts=thread_ts)
            else:
                # other errors
                raise e
        return response

    def upload(self, channel, file_with_location, title, initial_comment):
        try:
            response = self.client.files_upload_v2(
                channel=channel,
                file=file_with_location,
                title=title,
                initial_comment=initial_comment
            )
        except SlackApiError as e:
            logger.exception(e.response["error"])
            if e.response.status_code == 429:
                # The `Retry-After` header will tell you how long to wait before retrying
                delay = int(e.response.headers['Retry-After'])
                logger.warning(f"Rate limited. Retrying in {delay} seconds")
                time.sleep(delay)
                response = self.upload(channel, file_with_location, title, initial_comment)
            else:
                # other errors
                raise e
        return response


def send_message(channel, message):
    try:
        slack = WMTSlack()
        slack.send_message(channel, text=message)
    except Exception as e:
        logger.info(f"Slack error occurred {e}")


if __name__ == "__main__":
    slack = WMTSlack()
    slack.send_message("juno_logs", text="enabled slack alert")
    # blocb = {
    #     "type": "section",
    #     "text": {
    #         "type": "mrkdwn",
    #         "text": "New Paid Time Off request from <example.com|Fred Enriquez>\n\n<https://example.com|View request>"
    #     }
    # }
    # slack.send_message("juno-wcnp-deployments", blocks=blocb)
