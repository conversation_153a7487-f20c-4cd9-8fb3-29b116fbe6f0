import time, os, logging
from rest_framework.response import Response
from rest_framework.decorators import api_view
from libs.prometheus_client import Prometheus
from libs.monitoring import monitor_gslbs
from libs.oneClick import process_gslbs
from slack_bot.slack import send_message


logger = logging.getLogger(__name__)


@api_view(['GET'])
def mms_dashboards(request, namespace):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        pr = Prometheus()
        return Response({"ok": True, "body": pr.get_all_dashboards(namespace)}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": e}}, status=400)


@api_view(['GET'])
def mms_oneops_dashboards(request, org, assembly):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        pr = Prometheus()
        return Response({"ok": True, "body": pr.get_all_oneops_dashboards(org, assembly)}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": e}}, status=400)


@api_view(['GET'])
def get_all_oneops_assemblies(request, org):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        pr = Prometheus()
        return Response({"ok": True, "body": pr.get_all_assemblies_platforms_env(org)}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": e}}, status=400)


@api_view(['GET'])
def mms_dashboards_with_app_id(request, namespace, app_id):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        pr = Prometheus()
        return Response({"ok": True, "body": pr.get_all_dashboards(namespace, app_id)}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": e}}, status=400)


@api_view(['GET'])
def mms_app_data(request, namespace, app_id, minutes_ago):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        prometheus = Prometheus()
        end_time, start_time = Prometheus.get_last_n_minutes_epoch_times(minutes_ago)
        data = prometheus.get_app_stats(namespace, app_id, start_time=start_time, end_time=end_time)

        return Response({"ok": True, "body": data}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": e}}, status=400)


@api_view(['GET'])
def monitor_mx_gslbs(request):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        url = "https://gecgithub01.walmart.com/raw/SRE/production-playbooks/main/yaml-playbooks/international-tech/" \
              "intl-mexico/mexico-gtm-failover/disable-azure-scus.yml?token=GHSAT0AAAAAAAAC7RFQEIS3D5LGTRBLLEFSZCQWAVA"
        lbs = process_gslbs(url)
        results = monitor_gslbs(lbs)

        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": e}}, status=400)


@api_view(['GET'])
def monitor_canada_gslbs(request):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        url = "https://gecgithub01.walmart.com/raw/SRE/production-playbooks/main/yaml-playbooks/international-tech/" \
              "intl-canada/canada-gtm-failover/disable-azure-scus.yml?token=GHSAT0AAAAAAAAC7RFQPCBTB3LFAPCHF2LGZCQ2BQA"
        lbs = process_gslbs(url)
        results = monitor_gslbs(lbs)

        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": e}}, status=400)


@api_view(['GET'])
def dashboards_for_mx_gslbs(request):
    try:
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        url = "https://gecgithub01.walmart.com/raw/SRE/production-playbooks/main/yaml-playbooks/international-tech/" \
              "intl-mexico/mexico-gtm-failover/disable-azure-scus.yml?token=GHSAT0AAAAAAAAC7RFQEIS3D5LGTRBLLEFSZCQWAVA"
        lbs = process_gslbs(url)
        results = list()
        for lb in lbs:
            results.append({"gslb": lb, "dashboard": Prometheus.build_gslb_graph(lb)})
        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": e}}, status=400)


@api_view(['GET'])
def dashboards_canada_gslbs(request):
    send_message("juno_logs", f"User called , {request.get_full_path()}")
    try:
        url = "https://gecgithub01.walmart.com/raw/SRE/production-playbooks/main/yaml-playbooks/international-tech/" \
              "intl-canada/canada-gtm-failover/disable-azure-scus.yml?token=GHSAT0AAAAAAAAC7RFQPCBTB3LFAPCHF2LGZCQ2BQA"
        send_message("juno_logs", f"User called , {request.get_full_path()}")
        lbs = process_gslbs(url)
        results = list()
        for lb in lbs:
            results.append({"gslb": lb, "dashboard": Prometheus.build_gslb_graph(lb)})

        return Response({"ok": True, "body": results}, status=200)
    except Exception as e:
        return Response({"ok": False, "body": {"message": e}}, status=400)
